(function() {
	var LOAD_ING = "加载中，请稍候...";
	var LOAD_MORE = "点击加载更多";
	var LOAD_ALL = "已加载完";
	var NET_ERR = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否数字
	function isNumber(_obj) {
		return isParameters(_obj) && typeof _obj === "number";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (G.headTheme != getPrefs("headTheme") && G.headTheme != "transparent") {
			o.pageParam.headTheme = G.headTheme;
		}
		if (
			G.appTheme != getPrefs("appTheme" + G.sysSign) &&
			G.appTheme != (G.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出actionSheet
	function actionSheet(_param, _callback) {
		var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
		o = setNewJSON(o, _param);
		var oldButton = o.buttons || [],
			newButton = [];
		oldButton.forEach(function(item) {
			newButton.push(isObject(item) ? item : {name: item});
		});
		var actionSheetBox = {
			title: o.title,
			cancel: o.cancelTitle,
			data: newButton,
			active: o.active,
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			},
			pageClose: function pageClose() {
				_param.pageClose && _param.pageClose();
			}
		};

		G.actionSheetPop = actionSheetBox;
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//弹出地区选择
	function openAreas(_param, _callback) {
		var areasBox = {
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		G.areasPop = setNewJSON(areasBox, _param);
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	//获取通讯录栏目
	function getColumnAddressBook(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "choose/tabs",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: "choosetabs",
				name: "栏目"
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	//获取通讯录分组
	function getGroupAddressBook(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "choose/tree",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: "choosetree",
				name: "分组"
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	//获取通讯录群组列表
	function getListAddressGroup(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "chatGroup/list",
				param: setNewJSON({}, _param.param),
				tag: "chatGroupList",
				name: "列表"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.id = _eItem.id || "";
						item.name = _eItem.groupName || "";
						item.url = _eItem.groupImg;
						item.conversationType = "GROUP";
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	// 获取所有地区 //area/tree
	function getSysAreas(_param, _callback) {
		var otherUrl = (_param || {}).url;
		ajax(
			{u: otherUrl || appUrl() + "login/areas", areaId: getPrefs("sys_platform")},
			otherUrl || "areas",
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					setPrefs(otherUrl ? "sys_allAreas" : "sys_areas", JSON.stringify(data));
					areaNotice({key: ""});
				}
				_callback(ret, err);
			},
			"所有地区",
			"post",
			{
				body: JSON.stringify({query: {isUsing: 1}})
			}
		);
	}

	//地区切换通知
	function areaNotice(extra) {
		[
			"module",
			"news1",
			"news2",
			"my",
			"negotiable",
			"addressBook",
			"workstation"
		].forEach(function(_eItem) {
			sendEvent({name: "areaChange_" + _eItem, extra: extra});
		});
	}

	//获取融云群或用户信息
	function getBaseChatInfos(_item, callback) {
		var _id = _item.targetId;
		var _type = _item.conversationType;
		var _force = _item.force;
		if (_id.indexOf(chatHeader()) == 0) {
			_id = _id.split(chatHeader())[1];
		}
		var nTask = getItemForKey("task_" + _id, G.chatInfoTask);
		if (nTask) {
			nTask.callback.push(callback);
			return;
		}
		if (!_item.index) {
			getAllChatInfo();
		}
		var nowItem = getItemForKey(_id, G.chatInfos, "id");
		if (!nowItem || _force) {
			nTask = {key: "task_" + _id, callback: []};
			G.chatInfoTask.push(nTask);
			nTask.callback.push(callback);
			ajax(
				{
					u:
						appUrl() + (_type == "PRIVATE" ? "user/infoByAccount" : "chatGroup/info")
				},
				"infoByChat" + _id + "_" + _id,
				function(ret, err) {
					var item = null;
					if (ret && ret.code == "200") {
						if (_type == "PRIVATE") {
							var data = ret.data || [];
							var headImg = "",
								photo = "";
							data.forEach(function(_nItem) {
								if (_nItem.headImg && !headImg) {
									headImg = _nItem.headImg;
								}
								if (_nItem.photo && !photo) {
									photo = _nItem.photo;
								}
							});
							data = data[0];
							item = {
								id: data.accountId,
								name: data.userName,
								url: headImg || photo || ""
							};
						} else {
							var data = ret.data || {};
							item = {
								id: data.id,
								businessCode: data.businessCode,
								name: data.groupName,
								url: data.groupImg || "",
								memberUserIds: data.memberUserIds || []
							};
						}
						setAllChatInfo(item);
					}
					nTask = getItemForKey("task_" + _id, G.chatInfoTask);
					for (var i = 0; i < nTask.callback.length; i++) {
						var call = nTask.callback[i];
						call && call(item, ret, err);
					}
					delItemForKey("task_" + _id, G.chatInfoTask);
				},
				"\u83B7\u53D6\u4FE1\u606F" + _id,
				"post",
				{
					body: JSON.stringify({accountId: _id, detailId: _id, isSelectList: 1})
				}
			);
		} else {
			callback && callback(nowItem);
		}
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//移动事件 不左滑关闭屏幕
	function touchmove() {
		G.nTouchmove = true;
		clearTimeout(G.touchmoveTask);
		G.touchmoveTask = setTimeout(function() {
			G.nTouchmove = false;
		}, 1000);
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//列表无数据处理
	function dealData(_type, _this, ret) {
		if (!_type) {
			_this.data.listData = [];
			_this.data.emptyBox.type = ret ? "1" : "2";
			_this.data.emptyBox.text =
				ret && ret.code != 200 ? ret.message || ret.data : "";
		} else {
			_this.data.emptyBox.text = ret
				? ret.code == 200
					? LOAD_ALL
					: ret.message || ret.data
				: NET_ERR;
		}
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//获取地区参数
	function getAreaForKey(_key, _all) {
		var rItem = null;
		var areas = JSON.parse(getPrefs("sys_areas") || "[]");
		if (_all || !areas.length) {
			areas = JSON.parse(getPrefs("sys_allAreas") || "[]");
		}
		if (areas.length) {
			rItem = getItemForKey(_key, areas, "id");
			if (rItem) {
				rItem.name =
					rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
			}
		}
		return rItem || {};
	}

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//打开通用新增
	function openWin_add(_param) {
		openWin("mo_add", "../mo_add/mo_add.stml", _param);
	}

	//打开聊天
	function openWin_chat(_param) {
		var openPage = "mo_chat";
		var myParam = {
			conversationType: _param.conversationType,
			targetId: _param.targetId || _param.id
		};

		if (_param.paramMore) {
			myParam = setNewJSON(myParam, _param.paramMore);
		}
		openWin(
			openPage + myParam.targetId,
			"../" + openPage + "/" + openPage + ".stml",
			myParam
		);
	}

	//打开代表信息
	function openWin_npcinfo(_item) {
		var openPage = _item.id ? "mo_npcinfo_details" : "mo_npcinfo_list";
		var param = {};
		param.code = module9.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module9.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				size: G.appFontSize + (this.props.size || 0),
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: this.props.type == 3
						? "fangxingxuanzhongfill"
						: "yuanxingxuanzhongfill"
					: this.props.type == 3
					? "fangxingweixuanzhong"
					: "danxuan_weixuanzhong",
				color: this.props.checked ? this.props.color || G.appTheme : "#999"
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
			this.data = {
				esrc: appUrl() + "img/default_user_head.jpg",
				asrc: null,
				bsrc: ""
			};
			this.compute = {
				monitor: function() {
					var url = isObject(this.props.src) ? this.props.src.url : this.props.src;
					if (this.data.asrc != url || !this.data.asrc) {
						this.data.asrc = url || this.data.esrc;
						this.data.bsrc = "";
					}
				}
			};
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.error = function() {
			this.data.bsrc = this.data.esrc;
		};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{s: this.monitor, class: "xy_100"},
				apivm.h("z-image", {
					round: true,
					mode: "scaleToFill",
					thumbnail: "false",
					src: showImg(this.data.bsrc || this.data.asrc, "150x150-compress-"),
					onError: this.error
				})
			);
		};

		return ZAvatar;
	})(Component);
	apivm.define("z-avatar", ZAvatar);

	var ItemAddressGroup = /*@__PURE__*/ (function(Component) {
		function ItemAddressGroup(props) {
			Component.call(this, props);
		}

		if (Component) ItemAddressGroup.__proto__ = Component;
		ItemAddressGroup.prototype = Object.create(Component && Component.prototype);
		ItemAddressGroup.prototype.constructor = ItemAddressGroup;
		ItemAddressGroup.prototype.openDetail = function(e) {
			if (this.props.select) {
				var nItem = getItemForKey(this.props.item.id, this.props.listSelect, "id");
				if (nItem) {
					if (!nItem.readonly) {
						delItemForKey(nItem, this.props.listSelect, "id");
					}
				} else {
					this.props.listSelect.push(this.props.item);
				}
			} else {
				openWin_chat(this.props.item);
			}
			stopBubble(e);
		};
		ItemAddressGroup.prototype.isSelectValue = function(_item, _type) {
			var selectItem = getItemForKey(_item.id, this.props.listSelect, "id");
			if (_type == "color") {
				return selectItem && !selectItem.readonly;
			}
			return selectItem;
		};
		ItemAddressGroup.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{onClick: this.openDetail, class: "address_group_item flex_row"},
				apivm.h(
					"view",
					{style: loadConfigurationSize(30) + "margin-right:11px;"},
					apivm.h("z-avatar", {src: showImg(this.props.item)})
				),
				apivm.h(
					"view",
					{
						style:
							"flex:1;flex-wrap: wrap;flex-direction: row;align-items: flex-start;"
					},
					this.props.item.name.split("").map(function(nItem, nIndex) {
						return apivm.h(
							"text",
							{
								style:
									loadConfiguration(1) +
									"margin:2px 0;font-weight: 600;color: " +
									(this$1.props.search &&
									this$1.props.search.result &&
									this$1.props.search.result.indexOf(nItem) > -1
										? G.appTheme
										: "#333") +
									";"
							},
							nItem
						);
					})
				),
				this.props.select &&
					apivm.h("z-radio", {
						checked: this.isSelectValue(this.props.item),
						size: 4,
						color: this.isSelectValue(this.props.item, "color") ? G.appTheme : "#999"
					})
			);
		};

		return ItemAddressGroup;
	})(Component);
	ItemAddressGroup.css = {
		".address_group_item": {minHeight: "70px", padding: "5px 16px"}
	};
	apivm.define("item-address-group", ItemAddressGroup);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.inputBlur = function(e) {
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color: "#999",
								size: G.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G.appFontSize + 14) +
									"px;color:#999;" +
									loadConfiguration()
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style": "color:#ccc;",
							class: "z_input_input flex_w",
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								isParameters(this.props.readonly)
									? this.props.readonly
									: false,
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"}
	};
	apivm.define("z-input", ZInput);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var YSuspendedBtns = /*@__PURE__*/ (function(Component) {
		function YSuspendedBtns(props) {
			Component.call(this, props);
		}

		if (Component) YSuspendedBtns.__proto__ = Component;
		YSuspendedBtns.prototype = Object.create(Component && Component.prototype);
		YSuspendedBtns.prototype.constructor = YSuspendedBtns;
		YSuspendedBtns.prototype.itemclick = function(_item) {
			this.fire("click", _item);
		};
		YSuspendedBtns.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "suspendedBtn_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";bottom:" +
						(safeArea().bottom + 98) +
						"px;"
				},
				apivm.h(
					"view",
					{style: "flex-direction:column-reverse;"},
					this.props.dataMore.data.map(function(item, index) {
						return [
							(isParameters(item.show) ? item.show : true) &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemclick(item);
										},
										class: "suspendedBtn_item",
										style:
											"margin-bottom:" +
											(index ? 10 : 0) +
											"px;" +
											loadConfigurationSize(item.bgSize || 28) +
											"background: " +
											(item.bg == "appTheme" ? G.appTheme : item.bg || G.appTheme) +
											";" +
											item.iStyle
									},
									item.type == "img"
										? apivm.h("image", {
												src: item.src,
												class: "xy_100",
												mode: "aspectFill",
												alt: ""
										  })
										: item.type == "text"
										? [
												item.src &&
													apivm.h("a-iconfont", {
														style: "" + (item.style || ""),
														name: item.src,
														color:
															item.color == "appTheme" ? G.appTheme : item.color || "#fff",
														size:
															G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
													}),
												apivm.h(
													"text",
													{
														style:
															loadConfiguration(-2) +
															"color:" +
															(item.color == "appTheme" ? G.appTheme : item.color || "#fff") +
															";"
													},
													item.value
												)
										  ]
										: apivm.h("a-iconfont", {
												style: "" + (item.style || ""),
												name: item.src,
												color: item.color == "appTheme" ? G.appTheme : item.color || "#fff",
												size:
													G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
										  })
								)
						];
					})
				)
			);
		};

		return YSuspendedBtns;
	})(Component);
	YSuspendedBtns.css = {
		".suspendedBtn_box": {position: "absolute", zIndex: "999", right: "16px"},
		".suspendedBtn_item": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center",
			boxShadow: "0px 4px 12px 1px rgba(24,64,118,0.15)",
			borderTopLeftRadius: "54px",
			borderTopRightRadius: "54px",
			borderBottomRightRadius: "54px",
			borderBottomLeftRadius: "54px"
		}
	};
	apivm.define("y-suspended-btns", YSuspendedBtns);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var AddressGroup = /*@__PURE__*/ (function(Component) {
		function AddressGroup(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				pageParam: {},

				emptyBox: {
					type: "load",
					text: ""
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				listData: [],

				inputBox: {
					show: true,
					value: "",
					placeholder: "请输入关键词"
				},

				suspendedBtn: {
					show: true,
					data: [
						{show: true, key: "add", value: "", type: "icon", src: "xinzeng", size: 5}
					]
				},

				select: false, //是否选择模式
				listSelect: []
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							setTimeout(
								function() {
									this$1.baseInit();
								},
								platform() == "app" ? 50 : 0
							);
						} else {
							this.baseclose();
						}
					}
					if (this.props.dataMore.refresh == 1) {
						this.props.dataMore.refresh = 0;
						this.pageRefresh();
					}
				}
			};
		}

		if (Component) AddressGroup.__proto__ = Component;
		AddressGroup.prototype = Object.create(Component && Component.prototype);
		AddressGroup.prototype.constructor = AddressGroup;
		AddressGroup.prototype.baseInit = function() {
			var dm = this.props.dataMore;
			this.data.pageParam = this.props.pageParam || dm.pageParam || {};
			this.data.select = this.data.pageParam.select || false; //是否选择模式 默认false
			getItemForKey("add", this.data.suspendedBtn.data).show = !this.data.select;
			this.pageRefresh();
		};
		AddressGroup.prototype.pageRefresh = function() {
			this.getData();
		};
		AddressGroup.prototype.baseclose = function() {};
		AddressGroup.prototype.penetrate = function() {};
		AddressGroup.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		AddressGroup.prototype.getData = function(_type) {
			var this$1 = this;

			var postParam = {
				pageNo: !_type ? 1 : this.data.pageNo,
				pageSize:
					!_type && this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize,
				keyword: this.data.inputBox.value,
				isMine: 1
			};

			getListAddressGroup({param: postParam}, function(ret) {
				this$1.data.inputBox.result = this$1.data.inputBox.value;
				var data = ret ? ret.dealWith || [] : [];
				if (!isArray(data) || !data.length) {
					dealData(_type, this$1, ret);
					return;
				}
				if (!_type) {
					this$1.data.listData = data;
				} else {
					this$1.data.listData = this$1.data.listData.concat(data);
				}
				this$1.data.emptyBox.type = "";
				this$1.data.emptyBox.text =
					data.length >= postParam.pageSize ? LOAD_MORE : LOAD_ALL;
				this$1.data.pageNo =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1;
				this$1.data.refreshPageSize =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
					this$1.data.pageSize;
			});
		};
		AddressGroup.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		AddressGroup.prototype.keyCallback = function(ref) {
			var detail = ref.detail;

			switch (detail.key) {
				case "add":
					openWin_add({paramType: "createGroup", title: "创建群组"});
					break;
			}
		};
		AddressGroup.prototype.confirmBtn = function() {
			this.props.dataMore.pageParam.value = this.data.listSelect;
			this.fire("click", this.props.dataMore);
			this.closePage();
		};
		AddressGroup.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:" +
						(!this.props.dataMore.full ? "rgba(0,0,0,0.4)" : "transparent") +
						";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style:
						"display:" +
						(!this.props.dataMore.full ? "flex" : "none") +
						";height:20%;"
				}),
				apivm.h(
					"view",
					{
						class: "flex_h",
						style:
							"background: " +
							(!this.props.dataMore.full ? "#FFF" : "transparent") +
							";border-radius:" +
							(!this.props.dataMore.full ? "10px 10px" : "0px 0px") +
							" 0px 0px;",
						onClick: function() {
							return this$1.penetrate();
						}
					},
					apivm.h(
						"view",
						{style: "display:" + (!this.props.dataMore.full ? "flex" : "none") + ";"},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "群聊"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								this.data.select
									? apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.confirmBtn();
												},
												class: "header_btn"
											},
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
												},
												"确定"
											)
									  )
									: null
							)
						),
						apivm.h("z-divider", null)
					),
					apivm.h(
						"y-scroll-view",
						{_this: this, refresh: true},
						apivm.h(
							"view",
							{style: "padding: 4px 16px"},
							apivm.h("z-input", {
								dataMore: this.data.inputBox,
								onConfirm: function() {
									return this$1.getData(0);
								},
								onClean: function() {
									return this$1.getData(0);
								}
							})
						),
						apivm.h(
							"view",
							null,
							!this.data.emptyBox.type &&
								this.data.listData.map(function(item, index) {
									return [
										apivm.h("item-address-group", {
											search: this$1.data.inputBox,
											_this: this$1,
											select: this$1.data.select,
											listSelect: this$1.data.listSelect,
											item: item,
											onRefresh: function() {
												return this$1.getData(0);
											}
										}),
										apivm.h(
											"view",
											{style: "padding-left:56px;"},
											apivm.h("z-divider", null)
										)
									];
								})
						),
						apivm.h(
							"view",
							null,
							this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
						),
						apivm.h("z-empty", {
							_this: this,
							dataMore: this.data.emptyBox,
							onRefresh: this.pageRefresh
						})
					),
					apivm.h("y-suspended-btns", {
						dataMore: this.data.suspendedBtn,
						onClick: this.keyCallback
					})
				)
			);
		};

		return AddressGroup;
	})(Component);
	apivm.define("address-group", AddressGroup);

	// 复制
	function copyText(_text, _callback) {
		console.log("copy:" + _text);
		if (platform() == "app") {
			api.require("clipBoard").set(
				{
					value: _text
				},
				function(ret, err) {
					_callback && _callback(ret, err);
				}
			);
		} else if (platform() == "web") {
			htmlCopyText(_text, function(ret) {
				_callback && _callback(ret, null);
			});
		} else if (platform() == "mp") {
			wx.setClipboardData({
				data: _text,
				success: function success(ret) {
					_callback && _callback(ret, null);
				},
				fail: function fail(err) {
					_callback && _callback(null, err);
				}
			});
		}
	}

	// web复制
	function htmlCopyText(_text, callback) {
		var success = true;
		var textarea = document.createElement("textarea");
		textarea.value = _text;
		document.body.appendChild(textarea);
		textarea.select();
		textarea.setSelectionRange(0, textarea.value.length); // 兼容 iOS 设备
		try {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(_text).then(
					function() {
						success = true;
					},
					function(err) {
						success = false;
					}
				);
			} else {
				var input = document.createElement("input");
				input.setAttribute("value", _text);
				document.body.appendChild(input);
				input.select();
				input.setSelectionRange(0, input.value.length); // 兼容 iOS 设备
				success = document.execCommand("copy");
				document.body.removeChild(input);
			}
		} catch (err) {
			success = false;
		} finally {
			document.body.removeChild(textarea);
		}
		callback && callback(success);
	}

	var ZTag = /*@__PURE__*/ (function(Component) {
		function ZTag(props) {
			Component.call(this, props);
		}

		if (Component) ZTag.__proto__ = Component;
		ZTag.prototype = Object.create(Component && Component.prototype);
		ZTag.prototype.constructor = ZTag;
		ZTag.prototype.getTagStyle = function() {
			var color = this.props.color || G.appTheme;
			var rc = "",
				rbg = "",
				rbor = "";
			switch (this.props.type) {
				case "2":
					rc = color;
					rbg = "#FFF";
					rbor = colorRgba(color);
					break;
				case "3":
					rc = "#FFF";
					rbg = color;
					rbor = color;
					break;
				default:
					rc = color;
					rbg = colorRgba(color, 0.1);
					rbor = "transparent";
					break;
			}

			return (
				"color:" +
				rc +
				";background:" +
				rbg +
				";border: 1px solid " +
				rbor +
				";border-radius: " +
				(this.props.roundSize || 2) +
				"px;"
			);
		};
		ZTag.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "flex-direction:row;"},
				apivm.h(
					"text",
					{
						id: "" + (this.props.id || ""),
						class: "tag_box " + (this.props.class || ""),
						style:
							"" +
							loadConfiguration((this.props.size || 0) - 2) +
							this.getTagStyle() +
							(this.props.style || "")
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZTag;
	})(Component);
	ZTag.css = {".tag_box": {padding: "1px 10px"}};
	apivm.define("z-tag", ZTag);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				dotClose: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.dotClose = true;
							setTimeout(function() {
								this$1.data.dotClose = false;
							}, 300);
						} else {
							G.actionSheetPop.pageClose();
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.closePage = function() {
			if (this.data.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			if (this.data.dotClose || _item.disabled) {
				return;
			}
			_item.buttonIndex = _index + 1;
			G.actionSheetPop.callback(_item);
			this.closePage();
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h("view", {
						onClick: function() {
							return this$1.closePage();
						},
						class: "actionSheet_cancel"
					}),
					apivm.h(
						"view",
						{
							class: this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "flex-shrink: 0;"
						},
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					apivm.h(
						"scroll-view",
						{
							class: !this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "height: auto;background: #FFF;flex-shrink: 1;",
							"scroll-y": true
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						(this.props.dataMore.data || []).map(function(item, index) {
							return (
								(isParameters(item.show) ? item.show : true) && [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.itemClick(item, index);
											},
											class: "actionSheet_item",
											style:
												"justify-content:" +
												(item.justify || "center") +
												";opacity: " +
												(item.disabled ? "0.3" : "1") +
												";"
										},
										apivm.h(
											"view",
											null,
											item.icon &&
												apivm.h("a-iconfont", {
													style: "margin-right:10px;",
													name: item.icon,
													color: (isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333",
													size:
														G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
												})
										),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-1) +
													";color:" +
													((isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333")
											},
											item.name
										)
									),
									index != this$1.props.dataMore.data.length - 1 &&
										!this$1.props.dataMore.cancel &&
										apivm.h(
											"view",
											{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
											apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
										)
								]
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-shrink: 0;"},
						this.props.dataMore.cancel &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "actionSheet_item",
									style:
										"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + ";color:#333"},
									this.props.dataMore.cancel || "取消"
								)
							)
					),
					apivm.h("view", {
						style:
							"background:#fff;flex-shrink: 0;padding-bottom:" +
							safeArea().bottom +
							"px;"
					})
				]
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_cancel": {flex: "1", minHeight: "20%", flexShrink: "0"},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "5px 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var ZBreadcrumb = /*@__PURE__*/ (function(Component) {
		function ZBreadcrumb(props) {
			Component.call(this, props);
			this.data = {
				oldData: "",
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var data = this.props.dataMore.data;
					if (isArray(data) && this.data.oldData != JSON.stringify(data)) {
						if (this.data.oldData) {
							setTimeout(function() {
								this$1.tabClick(data[data.length - 1]);
							}, 30);
						}
						this.data.oldData = JSON.stringify(data);
					}
				}
			};
		}

		if (Component) ZBreadcrumb.__proto__ = Component;
		ZBreadcrumb.prototype = Object.create(Component && Component.prototype);
		ZBreadcrumb.prototype.constructor = ZBreadcrumb;
		ZBreadcrumb.prototype.tabClick = function(_item) {
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;

			var nLevel = [];
			for (var i = 0; i < this.props.dataMore.data.length; i++) {
				var item = this.props.dataMore.data[i];
				nLevel.push(item);
				if (item.key == _item.key) {
					break;
				}
			}
			this.props.dataMore.data = nLevel;

			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view = "breadcrumb_" + _item.key;
		};
		ZBreadcrumb.prototype.nTouchmove = function() {
			touchmove();
		};
		ZBreadcrumb.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1, list) {
						return apivm.h(
							"view",
							{
								id: "breadcrumb_" + item$1.key,
								style:
									"display: inline-block;margin-left:" +
									(!index$1 ? "16" : "0") +
									"px;margin-right:" +
									(index$1 == list.length - 1 ? "16" : "0") +
									"px;",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"view",
								{class: "flex_row"},
								apivm.h(
									"view",
									{style: "display:" + (index$1 ? "flex" : "none") + ";"},
									apivm.h("a-iconfont", {
										style: "margin:0 8px;transform: rotate(180deg) scale(0.7,0.7);",
										name: "fanhui1",
										color: index$1 ? "#999" : "rgba(0,0,0,0)",
										size: G.appFontSize - 4
									})
								),
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(this$1.props.size) +
											";color:" +
											(index$1 == list.length - 1 && index$1 ? "#333" : "#999")
									},
									item$1.value
								)
							)
						);
					})
				)
			);
		};

		return ZBreadcrumb;
	})(Component);
	apivm.define("z-breadcrumb", ZBreadcrumb);

	var ZTabsTwo = /*@__PURE__*/ (function(Component) {
		function ZTabsTwo(props) {
			Component.call(this, props);
			this.data = {
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (
						!this.show &&
						this.props.dataMore.data.length &&
						api.systemType == "ios"
					) {
						this.show = true;
						setTimeout(function() {
							document.getElementById(this$1.data.scrollView.id) &&
								document
									.getElementById(this$1.data.scrollView.id)
									.scrollTo({position: "upper", animated: false});
						}, 390);
					}
				},
				position: function() {},
				animated: function() {}
			};
		}

		if (Component) ZTabsTwo.__proto__ = Component;
		ZTabsTwo.prototype = Object.create(Component && Component.prototype);
		ZTabsTwo.prototype.constructor = ZTabsTwo;
		ZTabsTwo.prototype.tabClick = function(_item) {
			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			if (this.props.dotDefault && this.props.dataMore.key == _item.key) {
				this.props.dataMore.key = "";
				this.fire("change", {key: this.props.dataMore.key});
				return;
			}
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;
			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view =
				(this.props.id || "") + "_two_" + _item.key;
		};
		ZTabsTwo.prototype.nTouchmove = function() {
			touchmove();
		};
		ZTabsTwo.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							{
								id: (this$1.props.id || "") + "_two_" + item$1.key,
								class: "tabs_two_item",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								style:
									"width:" +
									(this$1.props.average
										? 100 / this$1.props.dataMore.data.length + "%"
										: "auto") +
									";",
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"view",
								{
									class: "tabs_two_ibox",
									style:
										"background:" +
										(this$1.props.dataMore.key == item$1.key
											? this$1.props.color || G.appTheme
											: this$1.props.dColor || "#F8F8F8") +
										";"
								},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(this$1.props.size) +
											";color:#" +
											(this$1.props.dataMore.key == item$1.key ? "fff" : "666") +
											";"
									},
									item$1.value
								)
							)
						);
					})
				)
			);
		};

		return ZTabsTwo;
	})(Component);
	ZTabsTwo.css = {
		".tabs_two_item": {
			flexDirection: "row",
			justifyContent: "center",
			display: "inline-block",
			padding: "8px 5px",
			textAlign: "center"
		},
		".tabs_two_ibox": {
			flexDirection: "row",
			justifyContent: "center",
			display: "inline-block",
			padding: "2px 8px",
			borderRadius: "2px"
		}
	};
	apivm.define("z-tabs-two", ZTabsTwo);

	var Areas = /*@__PURE__*/ (function(Component) {
		function Areas(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				area: {key: "", value: ""},
				areas: [],

				areaFirst: {index: 0, data: []},
				areaSecond: {index: -1, data: []},
				areaThird: {index: -1, data: []},
				sourceData: [],

				search: {value: "", placeholder: "搜索关键词"},
				searchShow: false,
				searchData: []
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							setTimeout(
								function() {
									this$1.baseInit();
								},
								platform() == "app" ? 50 : 0
							);
						}
					}
				}
			};
		}

		if (Component) Areas.__proto__ = Component;
		Areas.prototype = Object.create(Component && Component.prototype);
		Areas.prototype.constructor = Areas;
		Areas.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			var areas = JSON.parse(
				(dm.all ? getPrefs("sys_allAreas") : "") || getPrefs("sys_areas") || "[]"
			);
			if (areas.length) {
				var sourceData = areas.concat(areas[0].children);
				sourceData[0].children = [];
				if (JSON.stringify(this.data.sourceData) != JSON.stringify(sourceData)) {
					this.data.sourceData = sourceData;
					this.data.areaFirst.index = 0;
					this.data.areaSecond.index = -1;
					this.data.areaSecond.data = [];
					this.data.areaThird.index = [];
				}
			}
			this.data.areaFirst.data = this.data.sourceData;

			getSysAreas(
				{
					url: dm.all ? appUrl() + "area/tree" : "",
					areaId: dm.all ? getPrefs("sys_platform") : ""
				},
				function(ret) {
					if (ret && ret.code == 200 && isArray(ret.data) && ret.data.length > 0) {
						var _data = ret.data;
						this$1.data.sourceData = _data.concat(_data[0].children);
						this$1.data.sourceData[0].children = [];
						this$1.data.areaFirst.data = this$1.data.sourceData;
					}
				}
			);

			this.data.area.key = dm.key || (!dm.dt ? areaId() : "");
			if (this.data.area.key) {
				this.data.area.value = getAreaForKey(
					this.data.area.key,
					this.props.dataMore.all
				).name;
			}
		};
		Areas.prototype.penetrate = function() {};
		Areas.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		Areas.prototype.caSearchInput = function(e) {
			if (this.data.search.value) {
				this.searchStart(this.data.sourceData, 0);
				this.data.searchData.sort(function(a, b) {
					return b.weights - a.weights;
				});
			}
		};
		Areas.prototype.searchStart = function(_list, _start) {
			if (!isParameters(_list)) {
				return;
			}
			if (_start == 0) {
				this.data.searchData = [];
			}
			_start++;
			var searchArr = this.data.search.value.split("");
			for (var i = 0; i < _list.length; i++) {
				var weights = 0;
				for (var j = 0; j < searchArr.length; j++) {
					if (_list[i].name.indexOf(searchArr[j]) != -1) {
						weights++;
					}
				}
				if (weights > 0) {
					_list[i].weights = weights;
					this.data.searchData.push(_list[i]);
				}
				if (_list[i].children && _list[i].children.length) {
					this.searchStart(_list[i].children, _start);
				}
			}
		};
		Areas.prototype.cckAreaItem = function(_item, _index, _level) {
			if (_level != 1 && (!isArray(_item.children) || !_item.children.length)) {
				_level = -1;
			}
			switch (_level) {
				case 1:
					this.data.areaFirst.index = _index;
					this.data.areaSecond.index = -1;
					this.data.areaSecond.data = this.data.areaFirst.data[
						this.data.areaFirst.index
					].children;
					break;
				case 2:
					this.data.areaSecond.index = _index;
					this.data.areaThird.index = -1;
					this.data.areaThird.data = this.data.areaSecond.data[
						this.data.areaSecond.index
					].children;
					break;
				case 3:
					break;
				default:
					if (this.data.area.key == _item.id) {
						toast("当前地区已是【" + _item.name + "】");
						return;
					}
					this.switchArea(_item);
					break;
			}
		};
		Areas.prototype.switchArea = function(_item) {
			var this$1 = this;

			var nowInfo = getAreaForKey(_item.id);
			var extra = {
				id: _item.id,
				key: _item.id,
				name: _item.name,
				value: _item.name
			};
			if (!nowInfo.id) {
				sendEvent({name: "areaChange_" + this.props.dataMore.all, extra: extra});
			} else if (!this.props.dataMore.ds) {
				setPrefs("sys_aresId", _item.id);
				areaNotice(extra);
			}
			G.areasPop.callback(extra);
			if (!this.props.dataMore.dt) {
				toast("切换成功");
			}
			setTimeout(function() {
				this$1.closePage();
			}, 500);
		};
		Areas.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:15%;flex-shrink: 0;"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "地区切换"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{class: "flex_h"},
							apivm.h(
								"view",
								{class: "now_box"},
								this.data.area.key
									? apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h(
												"text",
												{style: loadConfiguration(2) + "color:#333;"},
												"当前选择："
											),
											apivm.h(
												"text",
												{style: loadConfiguration(2) + "color:#333;font-weight: 600;"},
												this.data.area.value
											)
									  )
									: null
							),
							apivm.h(
								"view",
								{style: "padding:4px 16px;"},
								apivm.h("z-input", {
									dataMore: this.data.search,
									onInput: this.caSearchInput
								})
							),
							apivm.h(
								"view",
								{style: "height:1px;flex:1;flex-direction:row;"},
								this.data.areaFirst.data.length > 0 &&
									apivm.h(
										"scroll-view",
										{
											class: "flex_w",
											style:
												"background:#F4F5F7;max-width: " + (G.appFontSize + 17) * 3 + "px;",
											"scroll-y": true
										},
										(Array.isArray(this.data.areaFirst.data)
											? this.data.areaFirst.data
											: Object.values(this.data.areaFirst.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, 1);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaFirst.index
															? "#FFFFFF"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight:" +
															(index$1 == this$1.data.areaFirst.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												),
												apivm.h("a-iconfont", {
													name: "xiangzuo",
													color:
														index$1 == this$1.data.areaFirst.index ? G.appTheme : "#ccc",
													style: "transform: rotate(180deg);",
													size: G.appFontSize - 2
												})
											);
										})
									),
								this.data.areaFirst.data.length > 0 &&
									apivm.h(
										"scroll-view",
										{class: "flex_w", "scroll-y": true},
										apivm.h(
											"view",
											{
												class: "areas_item",
												onClick: function() {
													return this$1.cckAreaItem(
														this$1.data.areaFirst.data[this$1.data.areaFirst.index],
														-1,
														-1
													);
												}
											},
											apivm.h(
												"text",
												{class: "areas_item_text", style: "" + loadConfiguration(2)},
												this.data.areaFirst.data[this.data.areaFirst.index].name
											)
										),
										(Array.isArray(this.data.areaSecond.data)
											? this.data.areaSecond.data
											: Object.values(this.data.areaSecond.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, 2);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaSecond.index
															? "#F4F5F7"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight: " +
															(index$1 == this$1.data.areaSecond.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												),
												isArray(item$1.children) &&
													item$1.children.length > 0 &&
													apivm.h("a-iconfont", {
														name: "xiangzuo",
														color:
															index$1 == this$1.data.areaSecond.index ? G.appTheme : "#ccc",
														style: "transform: rotate(180deg);",
														size: G.appFontSize - 2
													})
											);
										})
									),
								this.data.areaSecond.index > -1 &&
									apivm.h(
										"scroll-view",
										{class: "flex_w", "scroll-y": true},
										apivm.h(
											"view",
											{
												class: "areas_item",
												onClick: function() {
													return this$1.cckAreaItem(
														this$1.data.areaSecond.data[this$1.data.areaSecond.index],
														-1,
														-1
													);
												}
											},
											apivm.h(
												"text",
												{class: "areas_item_text", style: "" + loadConfiguration(2)},
												this.data.areaSecond.data[this.data.areaSecond.index].name,
												"(本级)"
											)
										),
										(Array.isArray(this.data.areaThird.data)
											? this.data.areaThird.data
											: Object.values(this.data.areaThird.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, -1);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaThird.index
															? "#FFFFFF"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight: " +
															(index$1 == this$1.data.areaThird.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												)
											);
										})
									),
								apivm.h(
									"view",
									{
										class: "page_box",
										style: "display:" + (this.data.search.value ? "flex" : "none") + ";"
									},
									apivm.h(
										"scroll-view",
										{class: "xy_100 search_box", "scroll-y": true},
										(Array.isArray(this.data.searchData)
											? this.data.searchData
											: Object.values(this.data.searchData)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, -1);
													},
													class: "search_item"
												},
												item$1.name.split("").map(function(nItem, nIndex) {
													return [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(2) +
																	"color: " +
																	(this$1.data.search.value.indexOf(nItem) != -1
																		? G.appTheme
																		: "#333") +
																	";"
															},
															nItem
														)
													];
												})
											);
										})
									)
								)
							)
						)
					)
			);
		};

		return Areas;
	})(Component);
	Areas.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".now_box": {padding: "10px 16px"},
		".areas_item": {width: "100%", padding: "15px 6px 15px 16px"},
		".areas_item_text": {color: "#333"},
		".search_box": {
			padding: "10px 0",
			width: "100%",
			height: "100%",
			backgroundColor: "#FFF"
		},
		".search_item": {padding: "15px 16px", flexDirection: "row", flexWrap: "wrap"}
	};
	apivm.define("areas", Areas);

	var YSelectUser = /*@__PURE__*/ (function(Component) {
		function YSelectUser(props) {
			Component.call(this, props);
		}

		if (Component) YSelectUser.__proto__ = Component;
		YSelectUser.prototype = Object.create(Component && Component.prototype);
		YSelectUser.prototype.constructor = YSelectUser;
		YSelectUser.prototype.clean = function(_item, _index, e) {
			if (!this.props.hasDel || _item.readonly) {
				return;
			}
			delItemForKey(_item.targetId, this.props.data, "targetId");
			this.fire("change", this.props.data);
			stopBubble(e);
		};
		YSelectUser.prototype.nTouchmove = function() {
			touchmove();
		};
		YSelectUser.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"scroll-view",
				{
					class: "flex_row",
					style:
						"" + (platform() != "app" ? "display: block;white-space: nowrap;" : ""),
					"scroll-x": true,
					"scroll-y": false
				},
				isArray(this.props.data) &&
					this.props.data.map(function(item, index) {
						return apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.clean(item, index);
								},
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchStart: this$1.nTouchmove,
								style:
									"width:" +
									((G.appFontSize + 1) * 3 + 15) +
									"px;" +
									(platform() != "app" ? "display: inline-block;" : "")
							},
							apivm.h(
								"view",
								{class: "selects_item xy_center"},
								apivm.h(
									"view",
									{style: "padding:5px;"},
									apivm.h(
										"view",
										{style: loadConfigurationSize(29)},
										apivm.h("z-avatar", {src: showImg(item)})
									),
									this$1.props.hasDel && !item.readonly
										? apivm.h(
												"view",
												{
													class: "selects_clean",
													onClick: function(e) {
														return this$1.clean(item, index, e);
													}
												},
												apivm.h("a-iconfont", {
													name: "qingkong",
													color: "#7F7F7F",
													size: G.appFontSize + 2
												})
										  )
										: null
								),
								apivm.h(
									"text",
									{
										class: "selects_name",
										style:
											"" +
											loadConfiguration() +
											loadConfigurationSize(50, "w") +
											"height:" +
											(G.appFontSize + 5) +
											"px;"
									},
									item.name
								)
							)
						);
					})
			);
		};

		return YSelectUser;
	})(Component);
	YSelectUser.css = {
		".selects_item": {width: "100%", padding: "0px 2px", overflow: "hidden"},
		".selects_name": {
			color: "#333",
			marginTop: "1px",
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			textAlign: "center"
		},
		".selects_clean": {
			position: "absolute",
			zIndex: "999",
			right: "-5px",
			top: "-5px",
			padding: "5px"
		}
	};
	apivm.define("y-select-user", YSelectUser);

	var AddressBook = /*@__PURE__*/ (function(Component) {
		function AddressBook(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				pageParam: {},
				area: {key: "", value: ""},
				hideAreas: false, //隐藏地区切换
				select: false, //是否选择模式

				inputBox: {
					show: true,
					value: "",
					result: "",
					placeholder: "请输入关键词"
				},

				listSelect: [],

				emptyBox: {
					type: "load",
					text: ""
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				listData: [],
				groupList: [],

				tabBox: {key: "", data: []},
				level: {key: "0", data: [{key: "0", value: "全部"}]},

				oftenList: [] //常用
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							setTimeout(
								function() {
									this$1.baseInit();
								},
								platform() == "app" ? 50 : 0
							);
						} else {
							this.baseclose();
						}
					}
					if (this.props.dataMore.refresh == 1) {
						this.props.dataMore.refresh = 0;
						this.pageRefresh();
					}
				},
				getGroupList: function() {
					var nowList = [],
						showList = [];
					if (this.data.level.key && this.data.groupList.length) {
						if (this.data.level.key == "0") {
							nowList = this.data.groupList;
						} else {
							var nowShowList = getItemForKey(
								this.data.level.key,
								this.data.groupList,
								"id"
							);
							if (nowShowList) {
								nowList = nowShowList.children;
							}
						}
					}
					(nowList || []).forEach(function(_eItem) {
						showList.push(_eItem);
					});
					return showList || [];
				},
				showSelect: function() {
					var show = false;
					if (this.data.select && this.data.listData.length) {
						show = true;
					}
					return show;
				}
			};
		}

		if (Component) AddressBook.__proto__ = Component;
		AddressBook.prototype = Object.create(Component && Component.prototype);
		AddressBook.prototype.constructor = AddressBook;
		AddressBook.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			this.data.pageParam = this.props.pageParam || dm.pageParam || {};
			this.data.select = this.data.pageParam.select || false; //是否选择模式 默认false
			this.data.hideAreas = this.data.pageParam.hideAreas || false; //是否隐藏选择地区 默认false
			this.tabCodes =
				this.data.pageParam.tabCodes ||
				(this.data.select ? "relationBooks" : "relationBooksTemp");
			this.data.listSelect = this.data.pageParam.value || []; //已经选择的用户 默认空
			this.max = this.data.pageParam.max; //最大选择人数
			addEventListener("areaChange_addressBook", function(ret, err) {
				if (ret.value.key) {
					this$1.data.area.key = ret.value.key;
					this$1.data.level.key = this$1.data.level.data[0].key;
					this$1.data.level.data = [this$1.data.level.data[0]];
					this$1.pageRefresh();
				}
				this$1.data.area.value = getAreaForKey(this$1.data.area.key).name;
			});
			this.data.area.key = areaId();
			this.data.area.value = getAreaForKey(this.data.area.key).name;
			this.pageRefresh();
		};
		AddressBook.prototype.pageRefresh = function() {
			this.getColumn(1);
			this.getData(0);
		};
		AddressBook.prototype.baseclose = function() {};
		AddressBook.prototype.penetrate = function() {};
		AddressBook.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		AddressBook.prototype.getColumn = function(_type) {
			var this$1 = this;

			getColumnAddressBook(
				{
					param: {
						tabCodes: [this.tabCodes]
					},
					areaId: this.data.area.key
				},
				function(ret, err) {
					hideProgress();
					var data = ret ? ret.data || [] : [];
					if (isArray(data) && data.length > 0) {
						data = data[0].chooseLabels || [];
					}
					var nowList = [];
					var oldKey = this$1.data.tabBox.key;
					if (isArray(data) && data.length > 0) {
						for (var i = 0; i < data.length; i++) {
							var id = data[i].labelCode;
							var name = data[i].name;
							var item = {value: name, key: id};
							nowList.push(item);
						}
						if (
							!this$1.data.tabBox.key ||
							!getItemForKey(this$1.data.tabBox.key, nowList)
						) {
							this$1.data.tabBox.key = nowList.length ? nowList[0].key : "";
						}
						this$1.tabChange(0, oldKey);
						this$1.data.tabBox.data = nowList;
					} else {
						this$1.data.tabBox.key = "";
						this$1.data.tabBox.data = [];
						this$1.data.listData = [];
						this$1.data.emptyBox.type = ret ? "1" : "2";
						this$1.data.emptyBox.text =
							ret && ret.code != 200 ? ret.message || ret.data : "";
					}
				}
			);
		};
		AddressBook.prototype.getGroupData = function() {
			var this$1 = this;

			getGroupAddressBook(
				{
					param: {
						tabCode: this.tabCodes,
						labelCode: this.data.tabBox.key
					},
					areaId: this.data.area.key
				},
				function(ret, err) {
					hideProgress();
					var data = ret ? ret.data || [] : [];
					this$1.setGroupData(data);
					data.unshift({id: "", name: "所有人员"});
					this$1.data.groupList = data;
					this$1.data.listData = [];
					this$1.data.emptyBox.type = "";
					this$1.data.emptyBox.text = "";
				}
			);
		};
		AddressBook.prototype.setGroupData = function(_list) {
			var this$1 = this;

			_list.forEach(function(_eItem) {
				_eItem.id = _eItem.code;
				if (isArray(_eItem.children) && _eItem.children.length) {
					this$1.setGroupData(_eItem.children);
				}
			});
		};
		AddressBook.prototype.getData = function(_type) {
			var this$1 = this;

			if (!_type) {
				ajax(
					{u: appUrl() + "relationBookMember/oftenList"},
					"oftenList",
					function(ret, err) {
						this$1.data.oftenList = ret ? ret.data || [] : [];
					},
					"常用集合",
					"post",
					{
						body: JSON.stringify({})
					}
				);
			}
			if (!this.data.tabBox.key) {
				return;
			}
			if (this.data.inputBox.value) {
				this.data.level.data = [this.data.level.data[0]];
				this.data.level.key = this.data.level.data[0].key;
				this.data.groupList = [];
			} else if (this.data.tabBox.key == "often") {
				this.data.level.data = [this.data.level.data[0]];
				this.data.level.key = this.data.level.data[0].key;
				this.data.groupList = [];
				this.data.inputBox.result = "";
			} else if (this.data.level.key == "0") {
				this.data.inputBox.result = "";
				this.getGroupData();
				return;
			}
			var url = appUrl() + "relationBookMember/users";
			if (this.data.select) {
				url = appUrl() + "choose/users";
			}
			var postParam = {
				keyword: this.data.inputBox.value,
				isOpen: 1,
				tabCode: this.tabCodes,
				labelCode: this.data.tabBox.key,
				nodeId:
					this.data.inputBox.value || this.data.tabBox.key == "often"
						? ""
						: this.data.level.key || "", //有搜索时不看分级
				relationBookId:
					this.data.inputBox.value || this.data.tabBox.key == "often"
						? ""
						: this.data.level.key || ""
			};

			ajax(
				{u: url, areaId: this.data.area.key},
				"choose/users",
				function(ret, err) {
					hideProgress();
					this$1.data.inputBox.result = this$1.data.inputBox.value;
					var data = ret ? ret.data || [] : [];
					if (!isArray(data) || !data.length) {
						dealData(_type, this$1, ret);
						return;
					}
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.id = _eItem.userId || _eItem.id || ""; //id
						item.targetId = _eItem.accountId;
						item.name = _eItem.userName || "";
						item.url = _eItem.headImg || _eItem.photo;
						item.position = _eItem.position || "";
						item.mobile = _eItem.mobile || "";
						item.areaId = this$1.data.area.key;
						if (
							this$1.data.pageParam.dotShowUser &&
							getItemForKey(_eItem.accountId, this$1.data.pageParam.dotShowUser)
						) {
							return;
						}
						item.often = getItemForKey(item.id, this$1.data.oftenList) ? true : false;
						nowList.push(item);
					});
					if (this$1.data.pageParam.hideSelf) {
						nowList = nowList.filter(function(item, index) {
							return item.targetId != G.userId;
						});
					}
					this$1.listDataAll = nowList;
					this$1.data.emptyBox.type = "";
					this$1.showListData();
				},
				"分组用户",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		AddressBook.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.showListData();
			}
		};
		AddressBook.prototype.showListData = function() {
			var this$1 = this;

			if (!this.data.listData.length) {
				this.data.refreshPageSize = 0;
			}
			this.data.listData = this.listDataAll.filter(function(item, index) {
				return (
					index <
					(this$1.data.refreshPageSize > this$1.data.pageSize
						? this$1.data.refreshPageSize
						: this$1.data.pageSize)
				);
			});
			this.data.pageNo =
				Math.ceil(this.data.listData.length / this.data.pageSize) + 1;
			this.data.refreshPageSize = this.data.pageNo * this.data.pageSize;
			this.data.emptyBox.text =
				this.data.listData.length >= this.listDataAll.length ? LOAD_ALL : LOAD_MORE;
		};
		AddressBook.prototype.tabChange = function(_type, dot) {
			if (_type == 1) {
				this.data.inputBox.value = "";
				this.data.level.key = this.data.level.data[0].key;
				this.data.level.data = [this.data.level.data[0]];
			}
			if (dot != this.data.tabBox.key) {
				this.data.listData = [];
				this.data.emptyBox.type = "load";
				this.data.emptyBox.text = "";
				this.getData(0);
			}
		};
		AddressBook.prototype.openAreas = function() {
			openAreas({key: this.data.area.key}, function(ret) {});
		};
		AddressBook.prototype.groupClick = function(_item) {
			this.data.level.data.push({key: _item.id, value: _item.name});
		};
		AddressBook.prototype.isSelectValue = function(_item, _type) {
			var selectItem = getItemForKey(
				_item.targetId,
				this.data.listSelect,
				"targetId"
			);
			if (_type == "color") {
				return selectItem && !selectItem.readonly;
			}
			return selectItem;
		};
		AddressBook.prototype.checkAllHasCancel = function() {
			var hasCancel = true;
			var listLength = this.data.listData.length;
			for (var i = 0; i < listLength; i++) {
				var _item = this.data.listData[i];
				var nItem = getItemForKey(_item.targetId, this.data.listSelect, "targetId");
				if (!nItem) {
					return false;
				}
			}
			return hasCancel;
		};
		AddressBook.prototype.checkAll = function() {
			if (this.checkAllHasCancel()) {
				var nList = this.data.listSelect.slice();
				this.data.listData.forEach(function(_item, index) {
					var nItem = getItemForKey(_item.targetId, nList, "targetId"); //找出这个对象看在不在
					if (nItem && !nItem.readonly) {
						//为非不可删除时	才能删除 否则不能动
						delItemForKey(nItem, nList, "targetId");
					}
				});
				this.data.listSelect = nList;
			} else {
				var nList = this.data.listSelect.slice();
				for (var i = 0; i < this.data.listData.length; i++) {
					var _item = this.data.listData[i];
					var nItem = getItemForKey(
						_item.targetId,
						this.data.listSelect,
						"targetId"
					);
					if (!nItem) {
						if (isNumber(this.max) && this.max > 0 && nList.length >= this.max) {
							toast("最多选择" + this.max + "个");
							break;
						}
						nList.push(_item);
					}
				}
				this.data.listSelect = nList;
			}
		};
		AddressBook.prototype.userClick = function(_item) {
			this.iTouchmove();
			if (this.data.select) {
				var nItem = getItemForKey(_item.targetId, this.data.listSelect, "targetId");
				if (nItem) {
					if (!nItem.readonly) {
						delItemForKey(nItem, this.data.listSelect, "targetId");
					}
				} else {
					if (
						isNumber(this.max) &&
						this.max > 0 &&
						this.data.listSelect.length >= this.max
					) {
						toast("最多选择" + this.max + "个");
						return;
					}
					this.data.listSelect.push(_item);
				}
			} else {
				if (!_item.targetId) {
					alert(
						{
							msg: _item.name + "\n" + _item.mobile + "\n" + _item.position,
							buttons: ["复制电话", "关闭"]
						},
						function(ret) {
							if (ret.buttonIndex == "1") {
								copyText(_item.mobile, function(ret, err) {
									toast(ret ? "复制成功" : "复制失败");
								});
							}
						}
					);
					return;
				}
				openWin_npcinfo({
					id: _item.targetId,
					areaId: _item.areaId,
					reqType: "account"
				});
			}
		};
		AddressBook.prototype.confirmBtn = function() {
			var dm = this.props.dataMore;
			dm.pageParam.value = this.data.listSelect;
			this.fire("click", dm);
			dm.callback && dm.callback(dm);
			this.closePage();
		};
		AddressBook.prototype.itemLongpress = function(_item) {
			var this$1 = this;

			var buttons = [
				{
					key: "often",
					name: (_item.often ? "移除" : "收藏为") + "常用联系人",
					icon: ""
				},
				{key: "copy", name: "复制名字", icon: ""}
			];

			actionSheet(
				{
					title: "提示",
					buttons: buttons,
					dotClose: true
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					if (_index <= buttons.length) {
						switch (ret.key) {
							case "often":
								_item.often = !_item.often;
								ajax(
									{u: appUrl() + "relationBookMember/setOften"},
									"setOften",
									function(ret, err) {
										if (this$1.data.tabBox.key == "often") {
											this$1.getData(0);
										}
									},
									"设置",
									"post",
									{
										body: JSON.stringify({
											userIds: [_item.id],
											isOften: _item.often ? 1 : 0
										})
									}
								);

								break;
							case "copy":
								copyText(_item.name, function(ret, err) {
									toast(ret ? "复制成功" : "复制失败");
								});
								break;
						}
					}
				}
			);
		};
		AddressBook.prototype.iTouchstart = function(_item) {
			var this$1 = this;

			if (platform() != "web") {
				return;
			}
			this.iTouchmove();
			this.itemTask = setTimeout(function() {
				this$1.itemLongpress(_item);
				this$1.iTouchmove();
			}, 500);
		};
		AddressBook.prototype.iTouchmove = function() {
			clearTimeout(this.itemTask);
		};
		AddressBook.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:" +
						(!this.props.dataMore.full ? "rgba(0,0,0,0.4)" : "transparent") +
						";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style:
						"display:" +
						(!this.props.dataMore.full ? "flex" : "none") +
						";height:15%;"
				}),
				apivm.h(
					"view",
					{
						class: "flex_h",
						style:
							"background: " +
							(!this.props.dataMore.full ? "#FFF" : "transparent") +
							";border-radius:" +
							(!this.props.dataMore.full ? "10px 10px" : "0px 0px") +
							" 0px 0px;",
						onClick: function() {
							return this$1.penetrate();
						}
					},
					apivm.h(
						"view",
						{class: "watermark_box"},
						!this.props.dataMore.full &&
							G.watermark &&
							apivm.h("image", {
								class: "xy_100",
								src: G.watermark,
								mode: "aspectFill",
								thumbnail: "false"
							})
					),
					apivm.h(
						"view",
						{style: "display:" + (!this.props.dataMore.full ? "flex" : "none") + ";"},
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "通讯录"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								this.data.select
									? apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.confirmBtn();
												},
												class: "header_btn"
											},
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
												},
												"确定"
											)
									  )
									: null
							)
						),
						apivm.h("z-divider", null)
					),
					apivm.h(
						"view",
						{class: "flex_h"},

						apivm.h(
							"view",
							null,
							!this.data.hideAreas &&
								G.systemtTypeIsPlatform &&
								apivm.h(
									"view",
									{class: "flex_row", style: "padding:7px 16px;"},
									apivm.h(
										"text",
										{
											style: loadConfiguration(-2) + "color:#999;flex:1;margin-right:20px;"
										},
										"以下数据来自："
									),
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.openAreas();
											},
											class: "flex_row"
										},
										apivm.h(
											"text",
											{style: loadConfiguration(1) + "color: #333;"},
											this.data.area.value
										),
										apivm.h(
											"view",
											{style: "margin-left:4px;"},
											apivm.h("a-iconfont", {
												name: "gengduogongneng",
												color: "#333",
												size: G.appFontSize
											})
										)
									)
								)
						),
						apivm.h(
							"view",
							null,
							this.data.listSelect.length > 0 &&
								apivm.h(
									"view",
									{style: "padding:10px;"},
									apivm.h(
										"text",
										{style: loadConfiguration(2) + "color:#333;padding:0 6px;"},
										"已选(",
										this.data.listSelect.length,
										")"
									),
									apivm.h(
										"view",
										{style: "margin-top:10px;"},
										apivm.h("y-select-user", {data: this.data.listSelect, hasDel: true})
									)
								)
						),

						apivm.h(
							"view",
							{style: "padding:10px 16px;border-top:10px solid rgba(0,0,0,0.05);"},
							apivm.h("z-input", {
								dataMore: this.data.inputBox,
								onConfirm: function() {
									return this$1.getData(0);
								},
								onClean: function() {
									return this$1.getData(0);
								}
							})
						),

						apivm.h(
							"view",
							{style: "padding:0 7px;"},
							this.data.tabBox.data.length > 0 &&
								apivm.h("z-tabs-two", {
									dataMore: this.data.tabBox,
									onChange: function() {
										return this$1.tabChange(1);
									}
								})
						),

						apivm.h(
							"view",
							{class: "flex_row", style: "padding:10px 16px 10px 0;"},
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h("z-breadcrumb", {
									size: -2,
									dataMore: this.data.level,
									onChange: this.tabChange
								})
							),
							this.showSelect
								? apivm.h(
										"text",
										{
											onClick: function() {
												return this$1.checkAll();
											},
											style:
												loadConfiguration(-2) + "color:" + G.appTheme + ";margin:0 16px;"
										},
										this.checkAllHasCancel() ? "取消" : "",
										"全选"
								  )
								: null
						),
						apivm.h("z-divider", null),
						apivm.h(
							"y-scroll-view",
							{_this: this, refresh: true},
							apivm.h(
								"view",
								null,
								this.getGroupList.map(function(item, index) {
									return [
										apivm.h(
											"view",
											{
												class: "address_item",
												onClick: function() {
													return this$1.groupClick(item);
												}
											},
											apivm.h(
												"text",
												{style: loadConfiguration(1) + "color: #333;flex:1;"},
												item.name
											),
											apivm.h("a-iconfont", {
												style: "margin:0 10px;transform: rotate(180deg);",
												name: "fanhui1",
												color: "#666",
												size: G.appFontSize - 4
											})
										),
										apivm.h("z-divider", null)
									];
								})
							),
							apivm.h(
								"view",
								null,
								!this.data.emptyBox.type && [
									apivm.h(
										"view",
										null,
										this.data.listData.map(function(item, index) {
											return [
												apivm.h(
													"view",
													{
														class: "address_item",
														onLongpress: function() {
															return this$1.itemLongpress(item);
														},
														onClick: function() {
															return this$1.userClick(item);
														},
														onTouchStart: function() {
															return this$1.iTouchstart(item);
														},
														onTouchMove: this$1.iTouchmove
													},
													apivm.h(
														"view",
														{style: loadConfigurationSize(14) + "margin-right:15px;"},
														apivm.h("z-avatar", {src: showImg(item)})
													),
													apivm.h(
														"view",
														{
															style:
																"flex:1;flex-wrap: wrap;flex-direction: row;align-items: flex-start;"
														},
														item.name.split("").map(function(nItem, nIndex) {
															return apivm.h(
																"text",
																{
																	style:
																		loadConfiguration(1) +
																		"margin:2px 0;color: " +
																		(this$1.data.inputBox &&
																		this$1.data.inputBox.result &&
																		this$1.data.inputBox.result.indexOf(nItem) > -1
																			? G.appTheme
																			: "#333") +
																		";"
																},
																nItem
															);
														}),
														apivm.h(
															"view",
															null,
															!item.targetId &&
																apivm.h(
																	"z-tag",
																	{
																		roundSize: 20,
																		style: "margin-left:12px;margin-top:3px;",
																		type: "2",
																		color: "#666",
																		size: -3
																	},
																	"系统外用户"
																)
														)
													),
													apivm.h(
														"view",
														null,
														item.often &&
															apivm.h("a-iconfont", {
																style: "margin:0 10px;",
																name: "shoucangfill",
																color: "#F6621B",
																size: G.appFontSize
															})
													),
													apivm.h(
														"view",
														null,
														this$1.data.select &&
															apivm.h("z-radio", {
																checked: this$1.isSelectValue(item),
																size: 4,
																color: this$1.isSelectValue(item, "color") ? G.appTheme : "#999"
															})
													)
												),
												apivm.h("z-divider", null)
											];
										})
									)
								]
							),
							apivm.h(
								"view",
								null,
								this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
							),
							apivm.h("z-empty", {
								_this: this,
								dataMore: this.data.emptyBox,
								onRefresh: this.pageRefresh
							})
						)
					)
				)
			);
		};

		return AddressBook;
	})(Component);
	AddressBook.css = {
		".address_item": {
			minHeight: "52px",
			padding: "0 16px",
			flexDirection: "row",
			alignItems: "center"
		}
	};
	apivm.define("address-book", AddressBook);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var Root = /*@__PURE__*/ (function(Component) {
		function Root(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				MG: !this.props.dataMore ? G : null,
				emptyBox: {
					type: "load",
					text: ""
				},

				pageNo: 1,
				pageSize: 20,
				refreshPageSize: 0,
				listData: [],

				fixedData: [
					{type: "PRIVATE", name: "从通讯录中选择"},
					{type: "GROUP", name: "从群聊中选择"}
				],

				checkData: [],

				addressBook: {
					show: false,
					pageParam: {
						select: true
					}
				},

				addressGroup: {
					show: false,
					pageParam: {
						select: true
					}
				}
			};
		}

		if (Component) Root.__proto__ = Component;
		Root.prototype = Object.create(Component && Component.prototype);
		Root.prototype.constructor = Root;
		Root.prototype.onShow = function() {
			G.onShowNum++;
		};
		Root.prototype.baseInit = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			//获取历史列表消息
			addEventListener("rongCloud_getConversationListByCount", function(ret) {
				var methodParam = ret.value.param;
				ret = ret.value.success;
				console.log("消息列表：" + JSON.stringify(ret));
				var data = ret ? ret.result || [] : [];
				if (!isArray(data) || !data.length) {
					if (methodParam.startTime == this$1.fristDate) {
						this$1.data.listData = [];
						this$1.data.emptyBox.type = ret ? "1" : "2";
						this$1.data.emptyBox.text = ret ? "暂无消息" : "";
					} else {
						this$1.data.emptyBox.text = ret ? LOAD_ALL : NET_ERR;
					}
					return;
				}
				var nowList = [],
					allList = [];
				data.forEach(function(_eItem) {
					if (
						methodParam.startTime == this$1.fristDate ||
						!getItemForKey(_eItem.targetId, this$1.data.listData, "targetId")
					) {
						if (platform() == "app") {
							_eItem.dotDisturb = _eItem.notificationStatus != "NOTIFY";
						}
						_eItem.url = this$1.getChatInfo(_eItem, "url");
						_eItem.showName = this$1.getChatInfo(_eItem, "name");
						nowList.push(_eItem);
					}
				});
				if (methodParam.startTime != this$1.fristDate) {
					allList = this$1.data.listData.concat(nowList);
				} else {
					allList = nowList;
				}
				allList.sort(function(a, b) {
					return b.isTop - a.isTop;
				});
				this$1.data.listData = allList;

				this$1.data.emptyBox.type = "";
				this$1.data.emptyBox.text =
					nowList.length >= methodParam.count ? LOAD_MORE : LOAD_ALL;
				this$1.data.refreshPageSize =
					this$1.data.listData.length > this$1.data.pageSize
						? this$1.data.listData.length
						: this$1.data.pageSize;
				this$1.getChatInfos("targetId");
			});
			addEventListener("chat_refresh_mo_chat_msg", function(ret) {
				this$1.pageRefresh();
			});
			this.pageRefresh();
		};
		Root.prototype.pageRefresh = function() {
			this.getData(0);
		};
		Root.prototype.close = function() {
			if (this.data.addressBook.show || this.data.addressGroup.show) {
				return;
			}
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		Root.prototype.getData = function(_type) {
			var param = {
				typeList: ["private", "group"],
				count: !_type
					? this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize
					: this.data.pageSize,
				startTime:
					!this.data.listData.length || !_type
						? this.fristDate
						: this.data.listData[this.data.listData.length - 1].sentTime,
				order: 0
			};

			console.log("消息参数：" + JSON.stringify(param));
			sendEvent({
				name: "rongCloud",
				extra: {type: "getConversationListByCount", param: param}
			});
			sendEvent({
				name: "rongCloud",
				extra: {
					type: "getUnreadCountByConversationTypes",
					monitor: "",
					param: {conversationTypes: ["PRIVATE", "GROUP"], containBlocked: false}
				}
			});
		};
		Root.prototype.loadMore = function() {
			if (
				this.data.emptyBox.text == LOAD_MORE ||
				this.data.emptyBox.text == NET_ERR
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		Root.prototype.openOther = function(_item) {
			if (_item.type == "PRIVATE") {
				this.data.addressBook.show = true;
			} else {
				this.data.addressGroup.show = true;
			}
		};
		Root.prototype.openDetails = function(_item) {
			var nItem = getItemForKey(_item.targetId, this.data.checkData, "targetId");
			if (nItem) {
				delItemForKey(nItem, this.data.checkData, "targetId");
			} else {
				this.data.checkData.push(_item);
			}
		};
		Root.prototype.getChatInfos = function(_key) {
			var this$1 = this;

			this.data.listData.forEach(function(_item, _index) {
				getBaseChatInfos(
					{
						targetId: _item[_key],
						conversationType: _item.conversationType,
						index: _index
					},
					function(ret) {
						if (!ret) {
							return;
						}
						this$1.refreshList();
					}
				);
				if (_item.conversationType == "GROUP") {
					getBaseChatInfos(
						{targetId: _item["senderUserId"], conversationType: "PRIVATE", index: 1},
						function(ret) {
							if (!ret) {
								return;
							}
							this$1.refreshList();
						}
					);
				}
			});
		};
		Root.prototype.refreshList = function() {
			var this$1 = this;

			var data = JSON.parse(JSON.stringify(this.data.listData)),
				nowList = [];
			data.forEach(function(_eItem) {
				_eItem.url = this$1.getChatInfo(_eItem, "url");
				_eItem.showName = this$1.getChatInfo(_eItem, "name");
				nowList.push(_eItem);
			});
			this.data.listData = nowList;
		};
		Root.prototype.getChatInfo = function(_item, _key, _showKey) {
			var chatId = _item[_showKey || "targetId"].split(chatHeader())[1];
			var nowItem = getItemForKey(chatId, G.chatInfos, "id");
			return nowItem ? nowItem[_key] : "";
		};
		Root.prototype.isCheck = function(_item) {
			var selectItem = getItemForKey(
				_item.targetId,
				this.data.checkData,
				"targetId"
			);
			return selectItem ? true : false;
		};
		Root.prototype.send = function(_again) {
			var this$1 = this;

			if (!this.data.checkData.length) {
				return;
			}
			switch (this.data.pageParam.fType) {
				case "sendImageMessage":
					if (
						platform() == "app" &&
						!_again &&
						this.data.pageParam.fImagePath.indexOf("http") == 0
					) {
						api.imageCache(
							{
								url: this.data.pageParam.fImagePath,
								thumbnail: false
							},
							function(ret, err) {
								console.log("图片缓存:" + JSON.stringify(ret));
								var url = ret.url;
								this$1.data.pageParam.fImagePath = url;
								this$1.send(true);
							}
						);
						return;
					}
					break;
			}

			this.data.checkData.forEach(function(_eItem) {
				var param = {
					conversationType: _eItem.conversationType,
					targetId: _eItem.targetId,
					extra: ""
				};

				switch (this$1.data.pageParam.fType) {
					case "sendRichContentMessage":
						param.title = this$1.data.pageParam.fName;
						param.description =
							this$1.data.pageParam.description ||
							this$1.data.pageParam.fName + "," + this$1.data.pageParam.fId;
						if (param.title == "[文件]") {
							var chatGroupId = param.targetId.split(chatHeader())[1];
							var fileId = param.description.split(",")[1];
							if (param.conversationType == "PRIVATE") {
								chatGroupId =
									Number(G.userId) > Number(chatGroupId) ? G.userId : chatGroupId;
							}
							ajax(
								{u: appUrl() + "chatGroupFile/add"},
								"chatGroupFile/add" + chatGroupId,
								function(ret, err) {},
								"添加聊天文件",
								"post",
								{
									body: JSON.stringify({
										form: {fileId: fileId, chatGroupId: chatGroupId}
									})
								}
							);
						}
						break;
					case "sendImageMessage":
						param.imagePath = this$1.data.pageParam.fImagePath;
						param.isFull = true;
						break;
					case "sendTextMessage":
						param.text = this$1.data.pageParam.fText;
						break;
				}

				console.log(this$1.data.pageParam.fType + ":" + JSON.stringify(param));
				sendEvent({
					name: "rongCloud",
					extra: {type: this$1.data.pageParam.fType, param: param}
				});
			});
			toast("已发送");
			this.data.checkData = [];
			sendEvent(this.data.pageParam.callback);
			setTimeout(function() {
				this$1.close();
			}, 800);
		};
		Root.prototype.callbackBook = function(e) {
			var this$1 = this;

			this.data.addressBook.pageParam.value.forEach(function(_eItem) {
				this$1.data.checkData.push({
					conversationType: "PRIVATE",
					targetId: chatHeader() + _eItem.targetId
				});
			});
			this.send();
		};
		Root.prototype.callbackGroup = function(e) {
			var this$1 = this;

			this.data.addressGroup.pageParam.value.forEach(function(_eItem) {
				this$1.data.checkData.push({
					conversationType: "GROUP",
					targetId: chatHeader() + _eItem.id
				});
			});
			this.send();
		};
		Root.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{_this: this, dataMore: this.props.dataMore, more: true},
				apivm.h("view", null),
				apivm.h(
					"view",
					{style: "height:100%;"},
					apivm.h(
						"view",
						{class: "header_btn"},
						apivm.h(
							"z-button",
							{
								disabled: !this.data.checkData.length,
								style: "padding:2px 12px;",
								color: G.appTheme,
								size: -2,
								onClick: function() {
									return this$1.send();
								}
							},
							"发送"
						)
					)
				),
				apivm.h("view", null),
				apivm.h(
					"y-scroll-view",
					{_this: this, refresh: true},
					apivm.h(
						"view",
						null,
						(Array.isArray(this.data.fixedData)
							? this.data.fixedData
							: Object.values(this.data.fixedData)
						).map(function(item$1, index$1) {
							return apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.openOther(item$1);
									},
									class: "forward_item flex_row"
								},
								apivm.h(
									"text",
									{style: loadConfiguration() + "color: #333;flex:1;"},
									item$1.name
								),
								apivm.h("a-iconfont", {
									style: "transform: rotate(-90deg);",
									name: "xiangxia1",
									color: "#666",
									size: G.appFontSize
								})
							);
						})
					),
					apivm.h(
						"text",
						{
							style:
								loadConfiguration() +
								"padding: 10px 16px;color: #333;border-top:10px solid rgba(0,0,0,0.05);"
						},
						"最近聊天"
					),
					apivm.h(
						"view",
						null,
						!this.data.emptyBox.type &&
							this.data.listData.map(function(item, index) {
								return [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.openDetails(item);
											},
											class: "forward_item flex_row"
										},
										apivm.h("z-radio", {
											checked: this$1.isCheck(item),
											color: G.appTheme,
											type: "1",
											size: 8
										}),
										apivm.h(
											"view",
											{style: loadConfigurationSize(22) + "margin:0 15px"},
											apivm.h("z-avatar", {src: showImg(item)})
										),
										apivm.h(
											"text",
											{style: loadConfiguration() + "color: #333;flex:1;"},
											item.showName
										)
									)
								];
							})
					),
					apivm.h(
						"view",
						null,
						this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
					),
					apivm.h("z-empty", {
						_this: this,
						dataMore: this.data.emptyBox,
						onRefresh: this.pageRefresh
					})
				),
				apivm.h("address-book", {
					dataMore: this.data.addressBook,
					onClick: this.callbackBook
				}),
				apivm.h("address-group", {
					dataMore: this.data.addressGroup,
					onClick: this.callbackGroup
				})
			);
		};

		return Root;
	})(Component);
	Root.css = {".forward_item": {padding: "10px 16px", minHeight: "59px"}};
	apivm.define("root", Root);
	apivm.render(apivm.h("root", null), "body");
})();
