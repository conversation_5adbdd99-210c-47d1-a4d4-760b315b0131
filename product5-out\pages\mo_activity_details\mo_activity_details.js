(function() {
	var G = {
		pageWidth:
			api.platform == "web"
				? api.winWidth > api.winHeight
					? 600
					: api.winWidth
				: api.winWidth,
		refreshPageSize: 0, //返回当前页刷新列表的条数
		dotRefsresh: false, // 返回当前页是否不刷新
		showSkeleton: true, //是否展示骨架屏
		seachText: "", //搜索词
		seachPlaceholder: "请输入搜索内容", //搜索提示
		firstAjax: false, //首次网络请求是否成功
		dotCloseListener: false, //当前页面不要划动返回
		hasCloseListener: false, //不管其它页面 直接添加关闭监听

		appName: "",
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		careMode: false, //是否启用了关怀模式
		htmlStyle: "", //html级别设置style 置灰等操作
		htmlClass: "", //html级别设置class
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		viewappearFrist: true, //是否首次进入页面

		touchmoveTask: null, //划动元素时 禁用页面划动返回事件
		nTouchmove: false,

		isAppReview: false, //app是否上架期间 隐藏和显示部分功能

		touchmove: function touchmove() {
			G.nTouchmove = true;
			G.touchmoveTask && clearTimeout(G.touchmoveTask);
			G.touchmoveTask = setTimeout(function() {
				G.nTouchmove = false;
			}, 1000);
		},
		//通用组件 start=====================================================

		imagePreviewer: {
			//全局图片预览组件
			show: false,
			imgs: [],
			activeIndex: 0,
			type: 1
		},

		openImgPreviewer: function openImgPreviewer(_param) {
			if (_param === void 0) {
				_param = {};
			}
			if ((_param.imgs || []).length <= 0) {
				return;
			}
			G.imagePreviewer.activeIndex = _param.index || 0;
			G.imagePreviewer.imgs = _param.imgs;
			G.imagePreviewer.show = true;
			T.sendEvent("updatePage");
		},

		areasBox: {
			//地区切换弹窗组件
			show: false,
			type: "half", //full全屏打开  half半屏打开
			pageParam: null
		},

		openAreas: function openAreas(_param, _callback) {
			if (_param === void 0) {
				_param = {};
			}
			G.areasBox.pageParam = _param;
			G.areasBox.show = true;
			T.addEventListener("base_areas_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_areas_callback");
			});
			T.sendEvent("updatePage");
		},

		alertBox: {
			// 确认提示框
			show: false,
			title: "",
			content: "",
			richText: false,
			input: false,
			textarea: false,
			placeholder: "",
			cancel: {show: false, text: "取消", color: "#333333"},
			sure: {show: true, text: "确定", color: "appTheme"}
		},

		alert: function alert(_param, _callback) {
			var o = {title: "", msg: "", buttons: ["确定"]};
			if (T.isObject(_param)) {
				o = T.setNewJSON(o, _param);
			} else {
				o.msg = T.isParameters(_param) ? _param : "";
			}
			G.alertBox.title = o.title;
			G.alertBox.content = (o.msg || o.content || "").toString();
			G.alertBox.input = o.input;
			G.alertBox.textarea = o.textarea;
			G.alertBox.placeholder = o.placeholder;
			G.alertBox.richText = o.richText;
			G.alertBox.cancel.show = o.buttons.length > 1;
			G.alertBox.cancel.text = o.buttons[1];
			G.alertBox.sure.text = o.buttons[0];
			G.alertBox.show = true;
			T.addEventListener("base_alert_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_alert_callback");
			});
			T.sendEvent("updatePage");
		},

		actionSheetBox: {
			show: false,
			cancel: false,
			title: "",
			active: null,
			data: []
		},

		actionSheet: function actionSheet(_param, _callback) {
			var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
			o = T.setNewJSON(o, _param);
			G.actionSheetBox.title = o.title;
			G.actionSheetBox.cancel = o.cancelTitle;
			var oldButton = o.buttons || [],
				newButton = [];
			oldButton.forEach(function(item) {
				newButton.push(T.isObject(item) ? item : {value: item});
			});
			G.actionSheetBox.data = newButton;
			G.actionSheetBox.active = _param.active;
			G.actionSheetBox.dotClose = _param.dotClose;
			G.actionSheetBox.show = true;
			T.addEventListener("base_actionSheet_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_actionSheet_callback");
			});
			T.sendEvent("updatePage");
		},
		//通用组件 end=====================================================

		installed: function installed(_this) {
			var _this2 = this;
			if (_this.props && _this.props.dataMore);
			else {
				G.fitWidth();
				G.changeConfiguration(_this);
				G.appGrayscale();
				G.initOther();
				T.addEventListener("index_login_ok", function(ret, err) {
					G.initOther();
				});
				//字体刷新
				T.addEventListener("changeConfiguration", function(ret, err) {
					G.changeConfiguration(_this);
				});
				//地区刷新监听
				T.addEventListener("areaChange", function(ret, err) {
					var notifas = ["module", "news", "my", "negotiable", "area"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "areaChange_" + _eItem, extra: ret.value});
					});
				});
				//红点刷新
				T.addEventListener("unreadChange", function(ret, err) {
					var notifas = ["module", "my"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "unreadChange_" + _eItem, extra: ret.value});
					});
				});
				if (
					T.isFunction(_this.close) &&
					!T.isParameters(_this.props.pageParam) &&
					!T.isParameters(_this.props.dataMore)
				) {
					T.addEventListener("keyback", function(ret, err) {
						if (G.imagePreviewer.show) {
							G.imagePreviewer.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show) {
							if (G.alertBox.cancel.show) {
								G.alertBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						if (G.actionSheetBox.show) {
							if (G.actionSheetBox.cancel) {
								G.actionSheetBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						_this2.close(_this);
					});
					T.addEventListener("swiperight", function(ret, err) {
						if (G.nTouchmove) {
							return;
						}
						if (G.imagePreviewer.show) {
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show && G.alertBox.cancel.show) {
							G.alertBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.actionSheetBox.show) {
							G.actionSheetBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						_this2.close(_this);
					});
				}
				setTimeout(function() {
					_this2.setHeader(_this);
				}, 10);
			}
			try {
				_this.init();
			} catch (e) {
				console.log(e);
			}
		},
		close: function close(_this) {
			_this.close();
		},
		setHeader: function setHeader(_this) {
			var title = _this.data.title;
			// console.log("=================="+title);
			// console.log(_this.props);
			// console.log(_this.data);
			if (!title) {
				return;
			}
			if (T.platform() == "web") {
				if (window.parent) {
					window.parent.document.title = title;
				} else {
					document.title = title;
				}
			} else if (T.platform() == "mp") {
				wx.setNavigationBarTitle({
					title: title
				});
			}
		},
		//多端页面显示回调 app、h5、小程序
		onShow: function onShow(_this) {
			var _this3 = this;
			if (_this.props.dataMore) {
				return;
			}
			if (G.viewappearFrist) {
				G.viewappearFrist = false;
				return;
			}
			console.log("返回了当前页面：");
			T.sendEvent({name: "changeConfiguration"});
			if (G.areaId != T.getPrefs("sys_aresId")) {
				G.areaId = T.getPrefs("sys_aresId") || "";
				T.sendEvent({name: "areaChange", extra: {key: G.areaId}});
			}
			setTimeout(function() {
				_this3.setHeader(_this);
			}, 10);
			if (_this.getData) {
				//返回刷新一下
				_this.getData(false, {refresh: 1});
			}
		},
		//初始化后其它配置
		initOther: function initOther() {
			G.areaId = T.getPrefs("sys_aresId") || "";
			G.systemtTypeIsPlatform = T.getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.appName = T.getPrefs("sys_systemName") || "";
			G.uId = T.getPrefs("sys_Id") || "";
			G.userId = T.getPrefs("sys_UserID") || "";
			G.userName = T.getPrefs("sys_UserName") || "";
			G.userImg = T.getPrefs("sys_AppPhoto") || "";
			G.specialRoleKeys = JSON.parse(T.getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				G.getItemForKey("dc_admin", G.specialRoleKeys) ||
				G.getItemForKey("admin", G.specialRoleKeys);
			G.v = T.getPrefs("sys_appVersion") || "";
			if (T.platform() == "app") {
				G.isAppReview =
					JSON.parse(T.getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
		},
		//全局配置
		changeConfiguration: function changeConfiguration(_this) {
			G.appFont =
				T.getPrefs("appFont") && T.getPrefs("appFont") != "0"
					? T.getPrefs("appFont")
					: "heitiSimplified";
			G.appFontSize = Number(
				T.getPrefs("appFontSize") && T.getPrefs("appFontSize") != "0"
					? T.getPrefs("appFontSize")
					: "16"
			);
			G.appTheme =
				T.pageParam(_this).appTheme ||
				T.getPrefs("appTheme" + (myjs.iszx ? "zx" : "rd")) ||
				(myjs.iszx ? "#3088FE" : "#C61414");
			var headTheme =
				_this.data.headTheme ||
				T.pageParam(_this).headTheme ||
				T.getPrefs("headTheme") ||
				"#FFF";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			if (T.platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
			}
			_this.update();
			T.sendEvent("updatePage");
		},
		//是否全局置灰
		appGrayscale: function appGrayscale() {
			var appGrayscale = T.getPrefs("appGrayscale") || "0";
			if (T.platform() == "app");
			else {
				// G.htmlStyle = "filter:"+(appGrayscale == 1?'grayscale(1)':'none')+";";//小程序不知道为啥style没用
				G.htmlClass = appGrayscale == 1 ? "filterGray" : "filterNone";
			}
		},
		//展示图片
		showImg: function showImg(_item) {
			var baseUrl = T.isObject(_item) ? _item.url || "" : _item || "";
			baseUrl = G.showAllSystemImg(baseUrl); //先显示系统图片
			if (
				baseUrl.indexOf("http") == 0 &&
				baseUrl.indexOf("http://127.0.0.1") != 0 &&
				baseUrl.indexOf(myjs.tomcatAddress()) != 0
			) {
				//是链接 不是小程序本地链接 不是处理过的链接
				if (myjs.proxy && T.platform() != "app" && baseUrl.indexOf("https") != 0) {
					baseUrl = myjs.tomcatAddress() + "utils/proxyPic?" + baseUrl;
				}
			}
			return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
		},
		showAllSystemImg: function showAllSystemImg(_url) {
			return !_url ||
				_url.indexOf("http") == 0 ||
				_url.indexOf("/") == 0 ||
				_url.indexOf("../") == 0
				? _url
				: myjs.appUrl() + "image/" + _url;
		},
		//图片处理
		cacheImg: function cacheImg(_item, _thumbnail, _url, _priority) {
			if (!T.isObject(_item) || !T.isParameters(_item.url)) return; //没有传对象 或者没有url的时候不处理
			var baseUrl = _item.webImg || _url || _item.url || ""; //存储当前缓存地址
		},
		//字体配置
		loadConfiguration: function loadConfiguration(_changeSize) {
			return (
				"font-size:" +
				((G.appFontSize || 0) + (_changeSize || 0)) +
				"px;font-family:" +
				G.appFont +
				";"
			);
		},
		//宽度配置
		loadConfigurationSize: function loadConfigurationSize(_changeSize, _who) {
			var changeSize = _changeSize || 0,
				returnCss = "",
				cssWidth,
				cssHeight;
			if (T.isArray(_changeSize)) {
				cssWidth = "width:" + (G.appFontSize + (_changeSize[0] || 0)) + "px;";
				cssHeight = "height:" + (G.appFontSize + (_changeSize[1] || 0)) + "px;";
			} else {
				cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
				cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
			}
			if (!_who) {
				returnCss = cssWidth + cssHeight;
			} else {
				returnCss = _who == "w" ? cssWidth : cssHeight;
			}
			return returnCss;
		},
		//获取item	只有一层级的时候 会返回 当前index	_i
		getItemForKey: function getItemForKey(_value, _list, _key, _child) {
			var hasChild = false;
			if (!T.isParameters(_list)) return;
			var listLength = _list.length;
			for (var i = 0; i < listLength; i++) {
				var listItem = _list[i];
				if (T.isArray(listItem)) {
					hasChild = true;
					var result = G.getItemForKey(_value, listItem, _key, true);
					if (result) return result;
				} else {
					if (!T.isObject(listItem)) {
						if (listItem === _value) {
							return listItem;
						}
					} else {
						if (T.isArray(listItem[_key || "key"])) {
							hasChild = true;
							var result = G.getItemForKey(
								_value,
								listItem[_key || "key"],
								_key,
								true
							);
							if (result) {
								listItem["_i"] = i;
								return listItem;
							}
						} else if (listItem[_key || "key"] === _value) {
							listItem["_i"] = i;
							return listItem;
						}
					}
					if (
						T.isObject(listItem) &&
						listItem.children &&
						T.isArray(listItem.children)
					) {
						hasChild = true;
						var result = G.getItemForKey(_value, listItem.children, _key, true);
						if (result) return result;
					}
				}
			}
			if (!_child && !hasChild) return false;
		},
		//在集合中删除第一个参数obj和index都可以或对比字符串	第二个传入集合	第三个为对比key
		delItemForKey: function delItemForKey(_obj, _list, _key) {
			if (T.isTargetType(_obj, "number") && _obj < _list.length) {
				_list.splice(_obj, 1);
			} else {
				var contrastObj = !T.isObject(_obj) ? _obj : _obj[_key || "key"];
				for (var i = 0; i < _list.length; i++) {
					if (
						(!T.isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) ==
						contrastObj
					) {
						_list.splice(i, 1);
						G.delItemForKey(_obj, _list, _key);
						break;
					}
				}
			}
		},
		//是否显示顶部
		showHeader: function showHeader(_this) {
			return T.platform() == "app" || T.pageParam(_this).showHeader;
		},
		//适配多端状态栏
		headerTop: function headerTop() {
			return T.platform() == "app" ? T.safeArea().top : 0;
		},
		//底部可视区域
		footerBottom: function footerBottom(_bottom) {
			return _bottom ? T.safeArea().bottom : 0;
		},
		//适配pc页打开宽度
		fitWidth: function fitWidth() {
			if (
				T.platform() == "web" &&
				document.documentElement.clientWidth > document.documentElement.clientHeight
			) {
				$("body").style.width = "100%";
				$("body").style.maxWidth = "600px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		},
		//获取当前主题色相对应前景色
		getHeadThemeRelatively: function getHeadThemeRelatively(_this) {
			var theme =
				(_this && _this.data && _this.data.headTheme
					? _this.data.headTheme || ""
					: "") || G.headTheme;
			return theme && T.isColorDarkOrLight(theme) == "dark" ? "#FFF" : "#333";
		},
		//转换成html格式
		convertRichText: function convertRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			var textList = value.split("\n");
			var str = "";
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
				if (addText) {
					str += "<p>" + addText + "</p>";
				}
			}
			return str;
		},
		//清空html格式
		clearRichText: function clearRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			//坑爹的后台管理了 & 符号
			value = value.replace(/&amp;/g, "&");
			// 空格处理
			value = value.replace(/(&nbsp;)/g, " ");
			// 换行处理
			value = value.replace(/<br\/?[^>]*>/g, "\n");

			value = value.replace(/<\/[p|div|h1|h2|h3|h4|h5|h6]>/g, "\n");
			value = value.replace(/<\/?[^>]*>/g, "");
			return value;
		},
		//阻止冒泡事件
		stopBubble: function stopBubble(e) {
			if (!e) return;
			if (T.platform() == "web") {
				e.preventDefault();
				e.stopPropagation();
			} else if (T.platform() == "mp") {
				e.$_canBubble = false;
			}
		},
		getTagColor: function getTagColor(_key) {
			if (_key === void 0) {
				_key = "";
			}
			var tagColors = [
				{key: "未处理", value: "#F6631C"},
				{key: "未开始", value: "#F6631C"},

				{key: "签到中", value: "#F6931C"},
				{key: "报名中", value: "#F6931C"},
				{key: "进行中", value: "#F6931C"},

				{key: "请假通过", value: "#50C614"},
				{key: "请假待审批", value: "#F6931C"},
				{key: "请假中", value: "#F6931C"},
				{key: "已参与", value: G.appTheme},
				{key: "待审核", value: "#F6931C"},
				{key: "已通过", value: "#50C614"},
				{key: "有效", value: G.appTheme},
				{key: "待审查", value: "#F6931C"},
				{key: "人大交办中", value: "#F6931C"},
				{key: "政协交办中", value: "#F6931C"},
				{key: "政府交办中", value: "#F6931C"},
				{key: "党委交办中", value: "#F6931C"},
				{key: "两院交办中", value: "#F6931C"},
				{key: "法院交办中", value: "#F6931C"},
				{key: "检察院交办中", value: "#F6931C"},
				{key: "转参阅件", value: "#C61414"},
				{key: "办理中", value: "#F6931C"},
				{key: "重新办理", value: "#F6931C"},
				{key: "已答复", value: "#50C614"},
				{key: "已办结", value: "#559FFF"},
				{key: "A类", value: "#F6931C"},
				{key: "B类", value: "#1A74DA"},
				{key: "待受理", value: "#F6931C"},
				{key: "已受理", value: "#50C614"},
				{key: "已回复", value: "#50C614"},
				{key: "待交付审议", value: "#F6931C"},
				{key: "专委会审议中", value: "#F6931C"},
				{key: "已上传相关资料", value: "#50C614"},
				{key: "留存", value: "#F6931C"},
				{key: "采用", value: "#50C614"}
			];

			var tagColor = G.getItemForKey(_key, tagColors);
			return tagColor ? tagColor.value : "#666666";
		},
		//获取文件类型 并返回数据
		getFileInfo: function getFileInfo(_name) {
			if (_name === void 0) {
				_name = "";
			}
			var name = _name.toLocaleLowerCase(),
				fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
			try {
				if (name.indexOf(".") != -1)
					name = name.split(".")[name.split(".").length - 1];
				switch (name) {
					case "xlsx":
					case "xlsm":
					case "xlsb":
					case "xltx":
					case "xltm":
					case "xls":
					case "xlt":
					case "et":
					case "csv":
					case "uos": //excel格式
						fileInfo.name = "file-excel-fill";
						fileInfo.color = "#00bd76";
						fileInfo.type = "excel";
						fileInfo.convertType = "0";
						break;
					case "doc":
					case "docx":
					case "docm":
					case "dotx":
					case "dotm":
					case "dot":
					case "xps":
					case "rtf":
					case "wps":
					case "wpt":
					case "uot": //word格式
						fileInfo.name = "file-word-fill";
						fileInfo.color = "#387efa";
						fileInfo.type = "word";
						fileInfo.convertType = "0";
						break;
					case "pdf": //pdf格式
						fileInfo.name = "file-pdf-fill";
						fileInfo.color = "#e9494a";
						fileInfo.type = "pdf";
						fileInfo.convertType = "20";
						break;
					case "ppt":
					case "pptx":
					case "pps":
					case "pot":
					case "pptm":
					case "potx":
					case "potm":
					case "ppsx":
					case "ppsm":
					case "ppa":
					case "ppam":
					case "dps":
					case "dpt":
					case "uop": //ppt
						fileInfo.name = "file-ppt-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "ppt";
						fileInfo.convertType = "0";
						break;
					case "bmp":
					case "gif":
					case "jpg":
					case "pic":
					case "png":
					case "tif":
					case "jpeg":
					case "jpe":
					case "icon":
					case "jfif":
					case "dib": //图片格式 case 'webp':
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "image";
						fileInfo.convertType = "440";
						break;
					case "txt": //文本
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#2696ff";
						fileInfo.type = "txt";
						fileInfo.convertType = "0";
						break;
					case "rar":
					case "zip":
					case "7z":
					case "tar":
					case "gz":
					case "jar":
					case "ios": //压缩格式
						fileInfo.name = "file-zip-fill";
						fileInfo.color = "#a5b0c0";
						fileInfo.type = "compression";
						fileInfo.convertType = "19";
						break;
					case "mp4":
					case "avi":
					case "flv":
					case "f4v":
					case "webm":
					case "m4v":
					case "mov":
					case "3gp":
					case "rm":
					case "rmvb":
					case "mkv":
					case "mpeg":
					case "wmv": //视频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#e14a4a";
						fileInfo.type = "video";
						fileInfo.convertType = "450";
						break;
					case "mp3":
					case "m4a":
					case "amr":
					case "pcm":
					case "wav":
					case "aiff":
					case "aac":
					case "ogg":
					case "wma":
					case "flac":
					case "alac":
					case "wma":
					case "cda": //音频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#8043ff";
						fileInfo.type = "voice";
						fileInfo.convertType = "660";
						break;
					case "folder": //文件夹
						fileInfo.name = "folder-2-fill";
						fileInfo.color = "#ffd977";
						fileInfo.type = "folder";
						break;
				}
			} catch (e) {
				console.log(e.message);
			}
			return fileInfo;
		},
		//获取文件大小
		getFileSize: function getFileSize(_fileSize) {
			if (!_fileSize && _fileSize != 0) return "";
			try {
				var size1 = parseFloat((_fileSize / 1024 / 1024).toFixed(1));
				var size2 = parseFloat((_fileSize / 1024).toFixed(1));
				if (size1 >= 1) {
					return size1 + "MB";
				} else if (size2 >= 1) {
					return size2 + "KB";
				} else {
					return parseInt(_fileSize) + "B";
				}
			} catch (e) {
				return _fileSize;
			}
		},
		//选择文件并上传
		chooseFile: function chooseFile(_this, _item, callback) {
			var max = T.isNumber(_item.max) ? _item.max : 0;
			if (T.platform() == "app") {
				if (T.systemType() == "ios") {
					if (!T.confirmPer("storage", "chooseFile")) {
						//存储权限
						T.addEventListener("storage" + "Per_" + "chooseFile", function(ret, err) {
							T.removeEventListener("storage" + "Per_" + "chooseFile");
							if (ret.value.granted) {
								G.chooseFile(_this, _item, callback);
							}
						});
						return;
					}
				} else {
					if (!api.require("zyRongCloud").hasAllFilesPermission()) {
						T.alert(
							{
								title: "提示",
								msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
								buttons: ["确定", "取消"]
							},
							function(ret) {
								if (ret.buttonIndex == "1") {
									api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
										if (ret.status) {
											G.chooseFile(_this, _item, callback);
										}
									});
								}
							}
						);
						return;
					}
				}
				var fileBrowser = api.require("fileBrowser");
				fileBrowser.open({}, function(ret, err) {
					fileBrowser.close();
					setTimeout(function() {
						_item.url = ret.url;
						G.uploadFile(_this, _item, function(ret) {
							callback && callback(ret);
						});
					}, 500);
				});
			} else if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.type = "file";
				h5Input.accept = "";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.click();
				h5Input.onchange = function() {
					var listLength =
						max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = h5Input.files[j];
							G.uploadFile(_this, nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				};
			} else if (T.platform() == "mp") {
				wx.chooseMessageFile({
					count: max != 0 ? max : 9,
					type: "file",
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							(function(j) {
								var nItem = JSON.parse(JSON.stringify(_item));
								nItem.url = res.tempFiles[j];
								G.uploadFile(_this, nItem, function(ret) {
									callback && callback(ret);
								});
							})(i);
						}
					}
				});
			}
		},
		//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
		uploadFile: function uploadFile(_this, _item, callback) {
			if (_item._fileAjax || _item.module == "-noUpload")
				//有传过 或者明确不传
				return;
			_item._fileAjax = true; //是否请求过	有就不再请求
			_item.state = 1;
			if (_item.showToast) {
				T.showProgress("上传中");
			}
			var nCallack = function nCallack(ret, err) {
				T.hideProgress();
				var code = ret ? ret.code : "";
				if (code == 200) {
					var data = ret.data || {};
					_item.state = 2;
					_item.uploadId = data.id;
					_item.otherInfo = data;
				} else {
					_item.state = 3;
					_item.error = ret ? ret.message || ret.data : err.data || "";
				}
				callback && callback(_item);
			};
			if (T.platform() == "mp") {
				wx.uploadFile({
					url: myjs.tomcatAddress() + "utils/proxy",
					filePath: _item.url.path,
					name: "file",
					header: {
						"Content-Type": "multipart/form-data",
						"u-login-areaId": myjs.areaId(_this),
						Authorization: T.getPrefs("sys_token") || ""
					},

					formData: {
						BASE_URL: myjs.appUrl() + "file/upload",
						BASE_TYPE: "file",
						fileName:
							_item.url.name ||
							_item.url.path.substring(_item.url.path.lastIndexOf("/") + 1)
					},

					success: function success(res) {
						nCallack(JSON.parse(res.data), null);
					},
					fail: function fail(err) {
						nCallack(null, JSON.parse(err.data));
					}
				});
			} else {
				T.ajax(
					{u: myjs.appUrl() + "file/upload", _this: _this, web: _item.web},
					"file/upload" + _item.url,
					nCallack,
					"上传附件",
					"post",
					{
						files: {file: _item.url},
						values: {a: 1}
					},
					{
						"content-type": "file" //安卓附件不能传 得用默认的
					}
				);
			}
		},
		getAreaForKey: function getAreaForKey(_key, _dotAll) {
			var rItem = null;
			var areas = JSON.parse(T.getPrefs("sys_areas") || "[]");
			if (!_dotAll || !areas.length) {
				areas = JSON.parse(T.getPrefs("sys_allAreas") || "[]");
			}
			if (areas.length) {
				rItem = G.getItemForKey(_key, areas, "id");
				if (rItem) {
					rItem.name =
						rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
				}
			}
			return rItem || {};
		},
		showTextSize: function showTextSize(_text, _size, _middle) {
			if (_size && _text) {
				_text =
					_text.length > _size
						? _middle
							? _text.substring(0, _size / 2) +
							  "..." +
							  _text.substring(_text.length - _size / 2)
							: _text.substring(0, _size) + "..."
						: _text;
			}
			return _text;
		},
		ajaxAlert: function ajaxAlert(_param, _this, _callback) {
			var _this4 = this;
			var param = {
				title: "提示",
				msg: _param.msg || "",
				buttons: _param.buttons || ["确定", "取消"]
			};

			if (_param.alertParam) {
				param = T.setNewJSON(param, _param.alertParam);
			}
			T.alert(param, function(ret) {
				if (ret.buttonIndex == "1") {
					_this4.ajaxProcess(_param, _this, _callback);
				}
			});
		},
		ajaxProcess: function ajaxProcess(_param, _this, _callback) {
			if (!_param.dotProgress) T.showProgress(_param.toast);
			T.ajax(
				{u: _param.url, _this: _this},
				"ajaxProcess",
				function(ret) {
					if (!_param.dotProgress) T.hideProgress();
					if (!_param.dotToast) T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					if (ret && ret.code == "200") {
						_callback && _callback(ret);
					}
				},
				"\u64CD\u4F5C",
				"post",
				{
					body: JSON.stringify(_param.param)
				}
			);
		}
	};

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G$1.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G$1.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G$1.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G$1.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G$1.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G$1.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G$1.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (
			G$1.headTheme != getPrefs("headTheme") &&
			G$1.headTheme != "transparent"
		) {
			o.pageParam.headTheme = G$1.headTheme;
		}
		if (
			G$1.appTheme != getPrefs("appTheme" + G$1.sysSign) &&
			G$1.appTheme != (G$1.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G$1.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//打开附件预览
	function openWin_filePreviewer(_param) {
		openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G$1.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G$1.chatInfos));
		return G$1.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G$1.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G$1.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G$1.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		[].forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	//全局页面引用变量
	var G$1 = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G$1.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	// import dayjs from "./dayjs";
	// import { MD5 } from './crypto-ts.js';
	/**
	 * 封装和适配 api相关所有接口 和一些别的
	 */
	var T = {};
	T.NET_ERR = "网络不小心断开了";
	T.NET_OK = "操作成功";
	T.NET_NO = "操作失败，请重试";
	T.JK_ERR = "接口异常，请联系技术";
	T.LOAD_ING = "加载中，请稍候...";
	T.LOAD_MORE = "点击加载更多";
	T.LOAD_ALL = "已加载完";
	T.LOAD_NO = "暂无数据";
	T.LOAD_NOT = "页面尚未加载完成，请刷新重试";

	T.isParameters = function(obj) {
		return obj != null && obj != undefined;
	};
	T.isTargetType = function(obj, typeString) {
		return typeof obj === typeString;
	};
	T.isNumber = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "number");
	};
	T.isObject = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "object");
	};
	T.isArray = function(obj) {
		return T.isParameters(obj) && toString.apply(obj) === "[object Array]";
	};
	T.isFunction = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "function");
	};

	T.setNewJSON = function(obj, newobj, _ifReplace) {
		obj = obj || {};
		newobj = newobj || {};
		var returnObj = {};
		for (var key in obj) {
			returnObj[key] = obj[key];
		}
		for (var key in newobj) {
			if (_ifReplace && returnObj.hasOwnProperty(key)) {
				//是否不替换前者 默认替换
				continue;
			}
			returnObj[key] = newobj[key];
		}
		return returnObj;
	};

	T.getNum = function() {
		return Math.round(Math.random() * 1000000000);
	};

	T.trim = function(str) {
		if (String.prototype.trim) {
			return str == null ? "" : String.prototype.trim.call(str);
		} else {
			return str.replace(/(^\s*)|(\s*$)/g, "");
		}
	};
	T.trimAll = function(str) {
		return str.replace(/\s*/g, "");
	};
	T.removeTag = function(str) {
		if (!str) return str;
		return T.decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	};
	T.decodeCharacter = function(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	};

	T.platform = function() {
		try {
			return api.platform || "";
		} catch (e) {
			return "";
		}
	};

	T.rebootApp = function() {
		if (T.platform() == "web") {
			window.location.reload();
		} else if (T.platform() == "mp") {
			wx.reLaunch({url: "/pages/index/index"});
		} else {
			api.rebootApp();
		}
	};

	T.appName = function() {
		try {
			return myjs.appName || "";
		} catch (e) {
			return "";
		}
	};

	T.systemType = function() {
		try {
			return api.systemType;
		} catch (e) {
			return "";
		}
	};

	T.pageParam = function(_this) {
		try {
			var pageParam =
				(_this && _this.props ? _this.props.pageParam : null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(T.getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (T.platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	};

	T.safeArea = function() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	};

	T.setPrefs = function(key, value) {
		if (!T.isParameters(value)) {
			T.removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {}
	};

	T.getPrefs = function(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			return "";
		}
	};

	T.removePrefs = function(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {}
	};

	T.addEventListener = function(name, callback) {
		var keyback = function keyback(ret, err) {
			T.isFunction(callback) && callback(ret, err);
		};
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			if (!window.baseEventList) {
				window.baseEventList = [];
			}
			if (G.getItemForKey(name, window.baseEventList)) {
				G.delItemForKey(name, window.baseEventList);
			}
			window.baseEventList.push({
				key: name,
				value: keyback
			});
		} else {
			api.addEventListener({name: name}, keyback);
		}
	};

	T.removeEventListener = function(name) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			G.delItemForKey(name, window.baseEventList);
		} else {
			api.removeEventListener({name: name});
		}
	};

	T.sendEvent = function(name, extra) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			var pageframes = window.parent.document.getElementsByTagName("iframe");
			for (var i = 0; i < pageframes.length; i++) {
				if (T.isArray(pageframes[i].contentWindow.baseEventList)) {
					var sendItem = G.getItemForKey(
						T.isObject(name) ? name.name : name,
						pageframes[i].contentWindow.baseEventList
					);
					if (sendItem) {
						sendItem.value({value: T.isObject(name) ? name.extra : extra});
					}
				}
			}
		} else {
			try {
				api.sendEvent(T.isObject(name) ? name : {name: name, extra: extra});
			} catch (e) {}
		}
	};

	T.removeLaunchView = function() {
		try {
			api.removeLaunchView();
		} catch (e) {}
	};

	T.setScreenOrientation = function(orientation) {
		try {
			api.setScreenOrientation({orientation: orientation});
		} catch (e) {}
	};

	T.cancelAjax = function(name) {
		try {
			api.cancelAjax({tag: name});
		} catch (e) {}
	};

	T.ajax = function(url, tag, callback, logText, method, data, header) {
		if (header === void 0) {
			header = {};
		}
		T.cancelAjax(tag);
		var getUrl = url; //请求链接
		var frequency = 0; //网络异常 重复请求次数
		var dataType = "json"; //返回数据类型
		var cacheType = ""; //请求类型
		var paramData = {};
		var areaId = "";
		var timeout = 0;
		var isWeb = "";
		if (T.isObject(url)) {
			getUrl = url.u; //请求链接
			dataType = url.dt || "json"; //
			cacheType = url.t || ""; //
			frequency = url.frequency || 0;
			paramData = url.paramData || {};
			areaId = url.areaId || myjs.areaId(url._this);
			timeout = url.timeout || 0;
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method ? method : "get",
			cache: false,
			timeout: 50,
			dataType: dataType,
			data: T.isObject(data) ? data : {},
			headers: T.setNewJSON(
				{
					"u-login-areaId": areaId,
					Authorization: T.getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header
			)
		};

		o = T.setNewJSON(o, paramData);
		if (T.platform() == "web") {
			delete o.tag;
		}
		if (o.url.indexOf("push/rongCloud") != -1) {
			//融云接口
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					myjs.chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = myjs.chatEnvironment();
			}
		}
		if (
			isWeb &&
			JSON.stringify(o.data) != "{}" &&
			(o.data.body || JSON.stringify(o.data.values) != "{}")
		) {
			//公众端通用 过期时间不传token且置空public_token
			var webToken = "";
			if (
				T.getPrefs("tokenEndTime") &&
				new Date().getTime() < T.getPrefs("tokenEndTime")
			) {
				webToken = T.getPrefs("public_token") || "";
			} else {
				T.removePrefs("public_token");
			}
			(o.headers.Authorization = header.Authorization || webToken || ""),
				(o.headers["u-terminal"] = "PUBLIC");
			// var isBody = o.data.body?true:false;
			// var postParam = isBody?JSON.parse(o.data.body):o.data.values;
			// var signParam = {};
			// function getParam(_obj){
			// 	// console.log(JSON.stringify(_obj));
			// 	for (var key in _obj) {
			// 		var kValue = _obj[key];
			// 		if(T.isObject(kValue) && !T.isArray(kValue)){
			// 			getParam(kValue);
			// 		}else{
			// 			kValue = T.isArray(kValue)?kValue.join(","):kValue;
			// 			if (signParam.hasOwnProperty(key)) {
			// 				signParam[key] += (signParam[key]?',':'') + kValue;
			// 			}else{
			// 				signParam[key] = kValue;
			// 			}
			// 		}
			// 	}
			// }
			// getParam(postParam);
			// var signStr = T.sort_ascii(signParam,"#");
			// postParam.clientId = M.clientId;
			// postParam.token = isWeb;
			// postParam.timestamp = dayjs().valueOf();
			// postParam.nonce = "zyrd";
			// postParam.sign = MD5(signStr + "#" + M.clientId + isWeb + postParam.timestamp + postParam.nonce).toString().toUpperCase()
			// if(isBody){
			// 	o.data.body = JSON.stringify(postParam);
			// }
		}
		var oldContentType = o.headers["content-type"];
		if (myjs.proxy && T.platform() != "app" && o.url.indexOf("https") != 0) {
			//小程序 使用代理 T.platform() == "mp" &&
			var oldUrl = o.url;
			var proxyUrl = myjs.tomcatAddress() + "utils/proxy";
			if (oldUrl.indexOf("?") != -1) {
				o.url = proxyUrl + oldUrl.substring(oldUrl.indexOf("?"));
				oldUrl = oldUrl.substring(0, oldUrl.indexOf("?"));
			} else {
				o.url = proxyUrl;
			}
			o.url +=
				(o.url.indexOf("?") != -1
					? o.url.substring(o.url.indexOf("?")) == "?"
						? ""
						: "&"
					: "?") +
				"BASE_URL=" +
				oldUrl;
			o.url += "&BASE_TYPE=" + oldContentType;
			o.headers["content-type"] = "application/x-www-form-urlencoded";
		}
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (T.platform() == "app" && logText) {
			if (o.method == "post") {
				console.log(logText + "post【" + frequency + "】：" + JSON.stringify(o));
			} else {
				console.log(logText + "get【" + frequency + "】：" + JSON.stringify(o));
			}
		}
		try {
			var cbFun = function cbFun(ret, err) {
				// if(T.isObject(url) && T.isParameters(url._this.ajax)){
				// 	url._this.ajax = true;
				// }
				if (T.isFunction(callback)) {
					if (err) {
						try {
							ret = JSON.parse(err.msg);
							err = null;
						} catch (e) {
							ret = JSON.parse(JSON.stringify(err));
							err = null;
						}
					}
					if (err) {
						// if (frequency > 0) {
						// 	var frequencyUrl = url;
						// 	frequencyUrl.frequency--;
						// 	T.ajax(frequencyUrl, tag, callback, logText, method, data, header);
						// 	return;
						// }
					}
					if (T.platform() == "app" && logText) {
						if (ret)
							console.log("得到" + logText + "返回结果ret：" + JSON.stringify(ret));
						if (err)
							console.log("得到" + logText + "返回结果err：" + JSON.stringify(err));
					}
					if (ret) {
						ret.message = ret.message || ret.msg || "";
						var errcode = ret.code || "";
						if ((errcode == 302 || errcode == 2) && cacheType != "login") {
							//令牌失效
							T.hideProgress();
							T.sendEvent({
								name: "index",
								extra: {type: "verificationToken", errmsg: ret.message}
							});
							// return;
						}
					}
					callback(ret, err, true);
				}
			};
			setTimeout(function() {
				if (T.platform() == "web") {
					var xhr = new XMLHttpRequest();
					xhr.open(o.method, o.url);
					for (var header in o.headers) {
						xhr.setRequestHeader(header, o.headers[header]);
					}
					var sendValue = "";
					if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
						var dValue = o.data.values || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue +=
								(!sendValue ? "" : "&") +
								vItem +
								"=" +
								encodeURIComponent(dValue[vItem]);
						}
					} else if (oldContentType.indexOf("file") != -1) {
						sendValue = new FormData();
						var dValue = o.data.values || {};
						var fileValue = o.data.files || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
						}
						for (var vItem in fileValue) {
							sendValue.append(vItem, fileValue[vItem]);
						}
					} else {
						sendValue = o.data.body || JSON.stringify(o.data.values); //encodeURIComponent web加了之后 不能传递json了
					}
					xhr.send(sendValue);
					xhr.onreadystatechange = function() {
						if (xhr.readyState === XMLHttpRequest.DONE) {
							if (xhr.responseText) {
								var response = this.responseText;
								if (o.dataType == "json") {
									var isJSON = false;
									try {
										response = JSON.parse(response);
										isJSON = true;
									} catch (e) {
										isJSON = false;
									}
									if (isJSON) {
										cbFun(response, null);
									} else {
										cbFun(null, response);
									}
								} else {
									cbFun(response, null);
								}
							} else {
								cbFun(null, {});
							}
						}
					};
				} else {
					api.ajax(o, cbFun);
				}
			}, timeout);
		} catch (e) {
			console.log(e);
		}
	};

	T.openWin = function(name, url, pageParam, _this, allowEdit, _more) {
		var delay = 0;
		url = T.handleSYSLink(url, _this); //先处理跳转链接
		var o = {
			name: name,
			url: T.platform() == "web" ? url.substring(url.lastIndexOf("/") + 1) : url,
			pageParam: pageParam ? pageParam : {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: delay,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (T.isObject(_more)) {
			o = T.setNewJSON(o, _more);
		}
		o.pageParam.headTheme =
			(_this && _this.data && _this.data.headTheme
				? _this.data.headTheme || ""
				: "") ||
			G.headTheme ||
			"";
		o.pageParam.appTheme = G.appTheme || "";
		o.pageParam.areaId = o.pageParam.areaId || myjs.areaId(_this);
		o.pageParam.v = G.v;
		if (o.pageParam.paramSaveKey) {
			T.setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		api.openWin(o);
	};

	T.closeWin = function(_param) {
		var o = {};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				T.removePrefs(api.pageParam.paramSaveKey);
			}
			api.closeWin(o);
		} catch (e) {}
	};

	T.clearCache = function(callback) {
		var o = {};
		try {
			api.clearCache(o, function(ret, err) {
				T.isFunction(callback) && callback(ret, err);
			});
		} catch (e) {}
	};

	T.toast = function(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: "middle",
			global: false
		};

		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.msg = T.isParameters(_param) ? _param : "";
			o.location = location || "middle";
			o.global = global;
		}
		o.msg = o.msg.toString();
		try {
			api.toast(o);
		} catch (e) {}
	};

	T.showProgress = function(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.title = T.isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		try {
			api.showProgress(o);
		} catch (e) {}
	};

	T.hideProgress = function() {
		try {
			api.hideProgress();
		} catch (e) {}
	};

	T.alert = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ["确定"]
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = T.isParameters(_param)?_param:"";
		// }
		// o.msg = o.msg.toString();
		// try{
		// 	api.alert(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.confirm = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ['确定', '取消']
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = _param;
		// }
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.confirm(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.actionSheet = function(_param, _callback) {
		G.actionSheet(_param, _callback);
		// var o = {
		// 	title: '请选择',
		// 	cancelTitle: '取消',
		// 	destructiveTitle: "",
		// };
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.actionSheet(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.getPicture = function(_param, callback) {
		if (!callback) {
			T.toast("请先设置callback");
			return;
		}
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = T.setNewJSON(o, _param);
		try {
			if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				//ios拍照需要加到真实dom能才进onchange
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (T.platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (T.platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!T.confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					//相机相册权限
					T.addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						T.removeEventListener(preName + "Per_" + "getPicture");
						if (ret.value.granted) {
							T.getPicture(_param, callback);
						}
					});
					return;
				}
				if (o.sourceType == "camera" || o.userOne) {
					api.getPicture(o, function(ret, err) {
						T.isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	};

	T.hasPermission = function(one_per) {
		if (T.platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				//判断一堆时 就自己看	一般是一个一个判断
				T.alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	};

	T.requestPermission = function(one_per, callback, _fName) {
		if (T.platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				//把结果 发监听过去
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					T.sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				T.isFunction(callback) && callback(ret, err);
			});
		}
	};

	T.confirmPer = function(perm, _fName, _reason) {
		if (T.platform() == "app") {
			var has = T.hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				T.confirm(
					{
						title: "无法使用" + hintWord[perm],
						msg: T.systemType() == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret, err) {
						if (1 == ret.buttonIndex) {
							T.requestPermission(perm, null, _fName);
						} else {
							T.sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	};

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	T.handleSYSLink = function(_link, _this, myParam) {
		if (_link === void 0) {
			_link = "";
		}
		// if(_link.indexOf("http") != 0){
		// 	return _link;
		// }
		myParam = myParam || {};
		//index.html?token={{token}}&userId={{userId}}
		_link = _link.replace("{{tomcatAddress}}", myjs.tomcatAddress());
		_link = _link.replace("{{shareAddress}}", myjs.shareAddress());
		_link = _link.replace(
			"{{token}}",
			encodeURIComponent(T.getPrefs("sys_token"))
		); //当前app登录用户的token，例如：bearer eyJhbGciOiJ...
		_link = _link.replace("{{sysUrl}}", myjs.appUrl()); //当前app请求系统地址，例如：http://**************:54386/lzt/
		_link = _link.replace("{{areaId}}", myParam.areaId || myjs.areaId(_this)); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", myjs.iszx); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			//是app内页面 带上特有参数如果没有
			if (_link.indexOf("sysUrl-zy-") == -1) {
				//没有带地址
				_link += "-zyz-sysUrl-zy-" + myjs.appUrl();
			}
			if (_link.indexOf("sysAreaId-zy-") == -1) {
				//没有带地区
				_link += "-zyz-sysAreaId-zy-" + (myParam.areaId || myjs.areaId(_this));
			}
			if (_link.indexOf("iszx-zy-") == -1) {
				//没有带人大政协判断
				_link += "-zyz-iszx-zy-" + myjs.iszx;
			}
			if (_link.indexOf("appTheme-zy-") == -1) {
				//没有带主题
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			}
			if (_link.indexOf("careMode-zy-") == -1) {
				//没有唯一标识
				_link += "-zyz-careMode-zy-" + G.careMode;
			}
		}
		return _link;
	};

	//按照ascii 排序
	T.sort_ascii = function(obj, _default) {
		var arr = [];
		var num = 0;
		for (var i in obj) {
			arr[num] = i;
			num++;
		}
		var sortArr = arr.sort();
		var str = "";
		for (var _i = 0; _i < sortArr.length; _i++) {
			var sValue = obj[sortArr[_i]];
			str +=
				sortArr[_i] +
				"=" +
				(T.isTargetType(sValue, "number")
					? sValue
					: T.isObject(sValue)
					? JSON.stringify(sValue)
					: sValue || _default) +
				"&";
		}
		var char = "&";
		str = str.replace(new RegExp("^\\" + char + "+|\\" + char + "+$", "g"), "");
		return str;
	};

	/** 判断颜色属于深色还是浅色*/
	T.isColorDarkOrLight = function(hexcolor) {
		try {
			var colorrgb = T.colorRgb(hexcolor);
			var colors = colorrgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
			var red = colors[1];
			var green = colors[2];
			var blue = colors[3];
			var brightness;
			brightness = red * 299 + green * 587 + blue * 114;
			brightness = brightness / 255000;
			if (brightness >= 0.5) {
				return "light";
			} else {
				return "dark";
			}
		} catch (e) {
			return "";
		}
	};

	//16进制颜色转化为RGB颜色
	T.colorRgb = function(str) {
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var sColor = str.toLowerCase();
		if (sColor && reg.test(sColor)) {
			if (sColor.length === 4) {
				var sColorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
				}
				sColor = sColorNew;
			}
			var sColorChange = [];
			for (var i = 1; i < 7; i += 2) {
				sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
			}
			return "rgb(" + sColorChange.join(",") + ")";
		} else {
			return sColor;
		}
	};

	/** 16进制颜色 转换成rgba颜色	可设置透明 */
	T.colorRgba = function(_color, _alpha) {
		if (!_color) return;
		// 16进制颜色值的正则
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		// 把颜色值变成小写
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(T.isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	};

	var myjs = {};

	//修改时 需同步修改_zy/myjs.js
	myjs.iszx = false;

	myjs.proxy = false;

	myjs.appName = (myjs.iszx ? "政协" : "人大") + "平台版";

	myjs.appUrl = function() {
		// if(T.platform() == "web"){//适配正式测试 不用重复切换
		// 	switch(location.hostname){
		// 		case "*************":
		// 			return "http://*************:810/lzt/";
		// 		case "***************":
		// 			return "http://***************:54386/lzt/";
		// 	}
		// }
		return (
			T.getPrefs("sys_appUrl") ||
			(myjs.iszx
				? "https://productpc.cszysoft.com:20170/lzt/"
				: "https://productpc.cszysoft.com:20169/lzt/")
		);
	};

	myjs.chatHeader = function() {
		return (
			T.getPrefs("sys_chatHeader") || "platform5" + (myjs.iszx ? "zx" : "rd")
		);
	};

	myjs.chatEnvironment = function() {
		return T.getPrefs("sys_chatEnvironment") || "1";
	};

	myjs.tomcatAddress = function() {
		return (
			T.getPrefs("sys_tomcatAddress") ||
			(T.platform() == "web"
				? window.location.protocol == "https:"
					? "https://cszysoft.com:9091/"
					: "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		); //http://**********:8080/ http://cszysoft.com:9090/ https://cszysoft.com:9091/
	};

	myjs.shareAddress = function(_type) {
		if (_type == 1 && T.platform() != "mp") {
			return "../../";
		}
		return (
			T.getPrefs("sys_shareAddress") ||
			(T.platform() == "web"
				? window.location.protocol.indexOf("http") == 0
					? window.location.protocol
					: "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(myjs.iszx ? "platform5zx/" : "platform5rd/")
		);
	};

	//默认当前页面 area下的地区 无则传参地区
	(myjs.areaId = function(_this) {
		return (
			(_this && _this.data && _this.data.area ? _this.data.area.key || "" : "") ||
			T.pageParam(_this).areaId ||
			T.getPrefs("sys_aresId") ||
			T.getPrefs("sys_platform") ||
			""
		);
	}),
		//系统类型：（平台版：platform）（标准版：standard）
		(myjs.systemType = function(_this) {
			return (
				T.pageParam(_this).platform || T.getPrefs("sys_systemType") || "platform"
			);
		});

	myjs.clientId = "zyrdV5TestAccount";

	myjs.clientSecret = "zyrdV5TestPassword";

	myjs.hw_project_id = "0611d8333100251b2fc1c01937b8e6d9";

	myjs.hw_bucket = "zy-soft";

	myjs.hw_header = "ZY";

	var SECONDS_A_MINUTE$1 = 60;
	var SECONDS_A_HOUR$1 = SECONDS_A_MINUTE$1 * 60;
	var SECONDS_A_DAY$1 = SECONDS_A_HOUR$1 * 24;
	var SECONDS_A_WEEK$1 = SECONDS_A_DAY$1 * 7;
	var MILLISECONDS_A_SECOND$1 = 1e3;
	var MILLISECONDS_A_MINUTE$1 = SECONDS_A_MINUTE$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_HOUR$1 = SECONDS_A_HOUR$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_DAY$1 = SECONDS_A_DAY$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_WEEK$1 = SECONDS_A_WEEK$1 * MILLISECONDS_A_SECOND$1; // English locales

	var MS$1 = "millisecond";
	var S$1 = "second";
	var MIN$1 = "minute";
	var H$1 = "hour";
	var D$1 = "day";
	var W$1 = "week";
	var M$1 = "month";
	var Q$1 = "quarter";
	var Y$1 = "year";
	var DATE$1 = "date";
	var FORMAT_DEFAULT$1 = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING$1 = "Invalid Date"; // regex

	var REGEX_PARSE$1 = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT$1 = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en$1 = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart$1 = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr$1 = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart$1(hourOffset, 2, "0") +
			":" +
			padStart$1(minuteOffset, 2, "0")
		);
	};
	var monthDiff$1 = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M$1);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M$1);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor$1 = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit$1 = function prettyUnit(u) {
		var special = {
			M: M$1,
			y: Y$1,
			w: W$1,
			d: D$1,
			D: DATE$1,
			h: H$1,
			m: MIN$1,
			s: S$1,
			ms: MS$1,
			Q: Q$1
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined$1 = function isUndefined(s) {
		return s === undefined;
	};
	var U$1 = {
		s: padStart$1,
		z: padZoneStr$1,
		m: monthDiff$1,
		a: absFloor$1,
		p: prettyUnit$1,
		u: isUndefined$1
	};

	var L$1 = "en";
	var Ls$1 = {};
	Ls$1[L$1] = en$1;
	var isDayjs$1 = function isDayjs(d) {
		return d instanceof Dayjs$1;
	};
	var parseLocale$1 = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L$1;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls$1[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls$1[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls$1[name] = preset;
			l = name;
		}
		if (!isLocal && l) L$1 = l;
		return l || (!isLocal && L$1);
	};
	var dayjs$1 = function dayjs(date, c) {
		if (isDayjs$1(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs$1(cfg);
	};
	var wrapper$1 = function wrapper(date, instance) {
		return dayjs$1(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils$1 = U$1;
	Utils$1.l = parseLocale$1;
	Utils$1.i = isDayjs$1;
	Utils$1.w = wrapper$1;
	var parseDate$1 = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils$1.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE$1);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs$1 = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale$1(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate$1(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils$1;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING$1);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs$1(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs$1(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs$1(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils$1.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils$1.u(_startOf) ? _startOf : true;
			var unit = Utils$1.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils$1.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D$1);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils$1.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y$1:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M$1:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W$1: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D$1:
				case DATE$1:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H$1:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN$1:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S$1:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils$1.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M$1] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y$1] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H$1] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN$1] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S$1] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS$1] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D$1 ? this.$D + (_int - this.$W) : _int;
			if (unit === M$1 || unit === Y$1) {
				var date = this.clone().set(DATE$1, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE$1, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils$1.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils$1.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs$1(_this2);
				return Utils$1.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M$1) {
				return this.set(M$1, this.$M + number);
			}
			if (unit === Y$1) {
				return this.set(Y$1, this.$y + number);
			}
			if (unit === D$1) {
				return instanceFactorySet(1);
			}
			if (unit === W$1) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN$1] = MILLISECONDS_A_MINUTE$1),
				(_C$MIN$C$H$C$S$unit[H$1] = MILLISECONDS_A_HOUR$1),
				(_C$MIN$C$H$C$S$unit[S$1] = MILLISECONDS_A_SECOND$1),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils$1.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING$1;
			var str = formatStr || FORMAT_DEFAULT$1;
			var zoneStr = Utils$1.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils$1.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils$1.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils$1.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils$1.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils$1.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils$1.s(this.$s, 2, "0"),
				SSS: Utils$1.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT$1, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils$1.p(units);
			var that = dayjs$1(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE$1;
			var diff = this - that;
			var result = Utils$1.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y$1] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M$1] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q$1] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W$1] = (diff - zoneDelta) / MILLISECONDS_A_WEEK$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[D$1] = (diff - zoneDelta) / MILLISECONDS_A_DAY$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[H$1] = diff / MILLISECONDS_A_HOUR$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN$1] = diff / MILLISECONDS_A_MINUTE$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[S$1] = diff / MILLISECONDS_A_SECOND$1),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils$1.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M$1).$D;
		};
		_proto.$locale = function $locale() {
			return Ls$1[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale$1(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils$1.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto$1 = Dayjs$1.prototype;
	dayjs$1.prototype = proto$1;
	[
		["$ms", MS$1],
		["$s", S$1],
		["$m", MIN$1],
		["$H", H$1],
		["$W", D$1],
		["$M", M$1],
		["$y", Y$1],
		["$D", DATE$1]
	].forEach(function(g) {
		proto$1[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs$1.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs$1, dayjs$1);
			plugin.$i = true;
		}
		return dayjs$1;
	};
	dayjs$1.locale = parseLocale$1;
	dayjs$1.isDayjs = isDayjs$1;
	dayjs$1.unix = function(timestamp) {
		return dayjs$1(timestamp * 1e3);
	};
	dayjs$1.en = Ls$1[L$1];
	dayjs$1.Ls = Ls$1;
	dayjs$1.p = {};

	// 定义各模块共用参数
	function baseModule(_value, _key) {
		switch (_value) {
			case "contentInformation": //内容资料其实是资讯
			case "learningmaterials": //学习资料其实是资讯
			case "perform_promote": //履职技巧
				_value = "informationContent";
				break;
			case "activityPublish": //活动
			case "activitySign":
			case "activityJoin":
			case "activitySignNotice":
			case "activityJoinNotice":
				_value = "servantActivity";
				break;
			case "npcMemberCheckPrepare": //变更申请
			case "npcMemberCheckPass":
			case "npcMemberCheckFalse":
			case "cppccMemberCheckPass":
			case "cppccMemberCheckFalse":
				_value = "cppccMemberCheckPrepare";
				break;
			case "npc_member":
				_value = "cppcc_member";
				break;
			case "LegislationBusinessCode":
			case "submitArchivePassiveSuggest":
				_value = "legislationOpinion";
				break;
			case "study_online_1":
			case "study_publish":
				_value = "study_online";
				break;
			case "schedule_daily_notice_staff":
				_value = "schedule_daily_notice";
				break;
			case "micro_advice_reject":
			case "micro_advice_reply":
			case "micro_advice_submit":
			case "micro_advice_report":
			case "micro_advice_push_group":
			case "micro_advice_transfer":
			case "micro_advice_invite":
				_value = "min_suggest";
				break;
			case "schedule_reception_notice":
				_value = "schedule_reception";
				break;
			case "contactInteractSend":
				_value = "double_interaction";
				break;
			case "sendNodeProcessInfo":
				_value = "legislative_process";
				break;
		}

		var mySeModule = null;
		//code【模块管理中code,app中唯一】	appType【展示在business-item组件name】	businessCode【后台接口需要的code】	behaviorCode【点击详情时增加阅读量】	dotComment【通用详情不评论】
		var mybaseModule = [
			{
				name: "资讯",
				code: "5",
				appType: "news",
				businessCode: "informationContent",
				behaviorCode: "information_content"
			},
			{
				name: myjs.iszx ? "网络议政" : "意见征集",
				code: "6",
				appType: "solicitation",
				businessCode: "opinioncollect"
			},
			{name: "专题", code: "7", appType: "topic", businessCode: "infoSubject"},
			{
				name: "专题资讯",
				code: "8",
				appType: "news",
				businessCode: "informationSubject",
				dotComment: true
			},
			{
				name: (myjs.iszx ? "委员" : "代表") + "信息",
				code: "9",
				appType: "npcInfo",
				businessCode: "cppcc_member"
			},
			{
				name: (myjs.iszx ? "委员" : "代表") + "信息变更申请",
				code: "9_1",
				appType: "npcReview",
				businessCode: "cppccMemberCheckPrepare"
			},
			{
				name: "活动",
				code: "10",
				appType: "activity",
				businessCode: "servantActivity"
			},
			{name: "活动资料", code: "10_1", appType: "material", businessCode: ""},
			{
				name: "活动请假申请",
				code: "10_2",
				appType: "",
				businessCode: "activityLeavepass"
			},
			{
				name: "活动请假审批",
				code: "10_3",
				appType: "",
				businessCode: "activityLeavesub"
			},
			{name: "履职档案", code: "11", appType: "performance", businessCode: ""},
			{name: "履职足迹", code: "11_1", appType: "material", businessCode: ""},
			{
				name: "履职补录",
				code: "11_2",
				appType: "material",
				businessCode: "dutiesadditional",
				dotComment: true
			},
			{name: "履职调整", code: "11_3", appType: "", businessCode: ""},
			{
				name: myjs.iszx ? "委员说" : "圈子",
				code: "12",
				appType: "circle",
				businessCode: "styleCircle"
			},
			{
				name: "待办",
				code: "13",
				appType: "upcoming",
				businessCode: "pendingMessage"
			},
			{name: "消息", code: "14", appType: "upcoming", businessCode: "box_message"},
			{
				name: "系统消息",
				code: "14_1",
				appType: "",
				businessCode: "system",
				dotComment: true
			},
			{name: "扫一扫", code: "15", appType: "", businessCode: ""},
			{name: "云盘", code: "16", appType: "", businessCode: "pan_pubshare"},
			{
				name: "云盘共享",
				code: "16_1",
				appType: "",
				businessCode: "pan_pubshare_1"
			},
			{
				name: "个人收藏",
				code: "17",
				appType: "upcoming",
				businessCode: "",
				listFlat: true
			},
			{name: "帮助中心", code: "18", appType: "", businessCode: ""},
			{name: "分享应用", code: "19", appType: "", businessCode: ""},
			{name: "有事好商量", code: "20", appType: "negotiable", businessCode: ""},
			{
				name: "协商活动",
				code: "21",
				appType: "negotiable_activity",
				businessCode: ""
			},
			{
				name: "通知公告",
				code: "22",
				appType: "news",
				businessCode: "notification",
				dotComment: true
			},
			{name: "制度文件", code: "23", appType: "news", businessCode: ""},
			{
				name: "协商征集",
				code: "24",
				appType: "negotiable_solicitation",
				businessCode: "opinioncollect",
				onlyBusinessCode: "discussioncollect"
			},
			{name: "远程协商", code: "25", appType: "videoConference", businessCode: ""},
			{
				name: "法规规章规范性文件库",
				code: "26",
				appType: "regulations",
				businessCode: ""
			},
			{
				name: "双联互动",
				code: "27",
				appType: "double_interaction",
				businessCode: "double_interaction",
				sendBusinessCode: "voter_npc"
			},
			{
				name: "双联互动H5",
				code: "27_1",
				appType: "double_interaction",
				businessCode: "double_interaction",
				sendBusinessCode: "voter_npc"
			},
			{
				name: "委员值班",
				code: "28",
				appType: "schedule_daily_notice",
				businessCode: "schedule_daily_notice",
				doubleCode: "schedule_daily"
			},
			{
				name: "委员会客厅",
				code: "29",
				appType: "schedule_reception",
				businessCode: "schedule_reception",
				doubleCode: "schedule_reception"
			},
			{name: "投票", code: "30", appType: "vote", businessCode: "vote"},
			{
				name: "培训考试",
				code: "31",
				appType: "training",
				businessCode: "study_online"
			},
			{
				name: "成绩单",
				code: "31_1",
				appType: "transcript",
				businessCode: "study_online_1"
			},
			{
				name: "题集训练",
				code: "31_2",
				appType: "training",
				businessCode: "promote_paper"
			},
			{
				name: "训练任务",
				code: "31_3",
				appType: "training",
				businessCode: "promote_task"
			},
			{
				name: "掌上建议",
				code: "32",
				appType: "suggestion",
				businessCode: "suggestion"
			},
			{
				name: "沟通情况记录",
				code: "32_communication",
				appType: "suggestion_communication",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "答复信息",
				code: "32_reply",
				appType: "suggestion_reply",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "单位调整记录",
				code: "32_adjustRecord",
				appType: "suggestion_adjustRecord",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "申请调整记录",
				code: "32_adjustStopRecord",
				appType: "suggestion_adjustStopRecord",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "申请延期记录",
				code: "32_answerStopRecord",
				appType: "suggestion_answerStopRecord",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "掌上提案",
				code: "33",
				appType: "suggestion",
				businessCode: "proposal"
			},
			{name: "问卷调查", code: "34", appType: "survey", businessCode: "survey"},
			{
				name: "立法征询",
				code: "35",
				appType: "legislative_consultation",
				businessCode: "legislationOpinion"
			},
			{
				name: "规范性备案审查建议平台",
				code: "36",
				appType: "normative",
				businessCode: "archive"
			},
			{
				name: "掌上议案",
				code: "37",
				appType: "suggestion",
				businessCode: "motion"
			},
			{
				name: "代表参与情况",
				code: "37_communication",
				appType: "suggestion_communication",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "审议结果与报告",
				code: "37_reply",
				appType: "suggestion_reply",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "提案线索征集",
				code: "38",
				appType: "proposal_clue",
				businessCode: "proposal_clue"
			},
			{
				name: "立法联系点",
				code: "39",
				appType: "contact_point",
				businessCode: "contact_point",
				onlyBusinessCode: "legis_contact_point",
				doubleCode: "law_contact_point"
			},
			{
				name: "微建议",
				code: "40",
				appType: "min_suggest",
				businessCode: "min_suggest"
			},
			{
				name: "社情民意",
				code: "41",
				appType: "suggestion",
				businessCode: "social"
			},
			{
				name: "立法规划计划",
				code: "42",
				appType: "legislative_planning",
				businessCode: "legislative_planning"
			},
			{
				name: "立法项目库",
				code: "43",
				appType: "legislative_project_library",
				businessCode: "legislative_project_library"
			},
			{
				name: "立法进程",
				code: "44",
				appType: "legislative_process",
				businessCode: "legislative_process"
			},
			{
				name: "政协书院",
				code: "45",
				appType: "classical_learning",
				businessCode: "classical_learning"
			},
			{
				name: "联网监督",
				code: "46",
				appType: "superviseOnline",
				businessCode: "superviseOnline"
			},
			{
				name: "党建工作",
				code: "47",
				appType: "partyActivity",
				businessCode: "partyActivity"
			},
			{
				name: "履职大厅",
				code: "48",
				appType: "dutiesroom",
				businessCode: "dutiesroom"
			},
			{
				name: "履职大厅审核",
				code: "48_1",
				appType: "dutiesroomsub",
				businessCode: "dutiesroomsub"
			},
			{
				name: "履职大厅审核",
				code: "52",
				appType: "conference",
				businessCode: "conference"
			}
		];

		//有保存先获取
		var saveModule = T.getPrefs("baseModule_" + _value);
		if (saveModule) {
			mySeModule = JSON.parse(saveModule);
		} else {
			if (T.isArray(_key)) {
				for (var i = 0; i < _key.length; i++) {
					mySeModule = G.getItemForKey(_value, mybaseModule, _key[i]);
					if (mySeModule) {
						break;
					}
				}
			} else {
				mySeModule = G.getItemForKey(_value, mybaseModule, _key);
			}
		}
		return mySeModule ? mySeModule : {};
	}

	// 打开聊天 type=PRIVATE/GROUP id=userId
	function openChat(_param, _this) {
		if (_param === void 0) {
			_param = {};
		}
		var openPage = "mo_chat";
		var myParam = {
			conversationType: _param.conversationType,
			targetId: _param.targetId || _param.id
		};

		if (_param.paramMore) {
			myParam = T.setNewJSON(myParam, _param.paramMore);
		}
		T.openWin(
			openPage + myParam.targetId,
			"../" + openPage + "/" + openPage + ".stml",
			myParam,
			_this
		);
	}

	// 打开详情共用方法
	function openDetails(_param, _addParam, _this) {
		if (_param === void 0) {
			_param = {};
		}
		if (_addParam === void 0) {
			_addParam = {};
		}
		console.log(JSON.stringify(_param));
		if (_param.isDelete) {
			T.toast("该条目已删除");
			return;
		}
		var myParam = {};
		// myParam.pageType = "page";
		// myParam.title = _param.nTitle || "详情";//页面名字
		// myParam.areaId = _param.areaId || "";
		myParam.id = _param.id;
		myParam.code = _param.code;
		myParam.businessCode = _param.businessCode;
		myParam.url = _param.link; //不要写url 图片用到了这个
		if (myParam.url) {
			myParam.url = T.handleSYSLink(myParam.url, _this);
		}
		var title = _param.title;
		myParam.title =
			title && title.length > 7 ? title.substring(0, 7) + "..." : title;
		myParam = T.setNewJSON(myParam, _addParam);
		if (_param.addParam) {
			myParam = T.setNewJSON(myParam, _param.addParam);
		}
		var openPage = "mo_details";
		console.log("myParam：" + JSON.stringify(myParam));
		var nowModule = baseModule(_param.code, "code");
		switch (myParam.code) {
			case "area": //地区切换
				openPage = "mo_areas";
				break;
			case "add": //通用新增
				openPage = "mo_add";
				break;
			case "search": //通用搜索
				openPage = "mo_search";
				break;
			case "newsList": //资讯列表
				openPage = "mo_news_list";
				break;
			case "business":
				openPage = "mo_business_list";
				break;
			case "showuser":
				openPage = "mo_showuser_list";
				break;
			case "file":
				openPage = "mo_preview_file";
				break;
			case "5":
			case "8":
				break;
			case "6":
			case "24":
				break;
			case "7": //资讯专题详情
				openPage = "mo_news_topic";
				break;
			case "9":
				openPage = myParam.id ? "mo_npcinfo_details" : "mo_npcinfo_list";
				break;
			case "9_1": //代表/委员 信息审核
				openPage = myParam.id ? "mo_npcinfo_review_details" : "mo_npcinfo_review";
				break;
			case "10":
				openPage = myParam.id ? "mo_activity_details" : "mo_business_list_n";
				break;
			case "10_1": //活动资料
				break;
			case "10_2": //活动请假详情
				openPage = "mo_activity_leaveDetail";
				break;
			case "10_3": //活动请假审批 通过了就进详情
				openPage =
					_param.hasComplete == 1
						? "mo_activity_leaveDetail"
						: "mo_activity_leaveReview";
				break;
			case "11":
				openPage = myParam.id ? "mo_performance_file" : "mo_performance_file_list";
				break;
			case "11_2":
				openPage = myParam.id ? openPage : "mo_performance_repair";
				break;
			case "12":
				openPage = myParam.id ? openPage : "mo_circle";
				break;
			case "14_1":
				break;
			case "16_1": //云盘共享
				openPage = "mo_cloud_disk";
				myParam.defaultType = "share";
				break;
			case "20":
				openPage = myParam.id ? "mo_negotiable_details" : "mo_negotiable";
				break;
			case "21":
				openPage = myParam.id
					? "mo_negotiableActivity_details"
					: "mo_business_list";
				break;
			case "22": //通知公告
				openPage = myParam.id ? "mo_notice_detail" : "mo_business_list_n";
				break;
			case "23":
				break;
			case "25":
				openPage = myParam.id
					? "mo_negotiableConference_details"
					: "mo_business_list";
				break;
			case "26": //规范性文件库
				openPage = myParam.id ? "mo_regulations_details" : "mo_regulations";
				break;
			case "27": //规范性文件库
				openPage = "mo_DoubleInteractive_detail";
				break;
			case "28": //规范性文件库
				openPage = "mo_member_duty_content";
				break;
			case "29": //规范性文件库
				openPage = "mo_member_duty_content";
				myParam.type = "schedule_reception";
				break;
			case "30": //投票
				openPage = myParam.id ? "mo_vote_details" : "mo_business_list";
				break;
			case "31": //培训考试
			case "31_1": //成绩单
				openPage = myParam.id ? "mo_exam_answers" : "mo_business_list";
				if (myParam.id && myParam.id.indexOf(",") != -1) {
					var dataId = myParam.id.split(",");
					myParam.id = dataId[0];
					myParam.topicId = dataId[1];
				}
				break;
			case "31_2": //题集训练
			case "31_3": //训练任务
				openPage = myParam.id ? "mo_duty_promotion_transcript_exam_answers" : "";
				if (myParam.id && myParam.id.indexOf(",") != -1) {
					var dataId = myParam.id.split(",");
					myParam.id = dataId[0];
					myParam.topicId = dataId[1];
				}
				break;
			case "32": //建议
				openPage = myParam.id ? "mo_suggestion_details" : "mo_suggestion";
				break;
			case "33": //提案
				openPage = myParam.id ? "mo_proposal_details" : "mo_proposal";
				break;
			case "34": //问卷调查
				openPage = myParam.id ? "mo_survey_details" : "mo_survey_list";
				break;
			case "35": //立法征询
				openPage = myParam.id
					? "mo_legislative_consultation_detail"
					: "mo_legislative_consultation";
				break;
			case "36": //备案审查
				openPage = myParam.id ? "mo_normative_details" : "mo_normative";
				break;
			case "36_1": //备案审查-我的建议
				openPage = "mo_normative_suggest";
				break;
			case "37": //掌上议案
				openPage = myParam.id ? "mo_motion_details" : "mo_business_list";
				break;
			case "38": //提案线索征集
				openPage = myParam.id ? "mo_proposal_clue_details" : "mo_proposal_clue";
				break;
			case "39": //立法联系点
				openPage = myParam.id
					? "mo_legislative_point_detail"
					: "mo_legislative_point";
				break;
			case "40": //微建议
				openPage = myParam.id ? "mo_min_suggest_details" : "mo_min_suggest";
				break;
			case "41": //社情民意
				openPage = myParam.id ? "mo_social_details" : "mo_business_list";
				break;
			case "43": //立法项目库
				openPage = myParam.id
					? "mo_legislative_project_library_details"
					: "mo_legislative_project_library";
				break;
			case "44": //立法进程
				openPage = myParam.id
					? "mo_legislative_process_details"
					: "mo_legislative_process";
				break;
			case "47": //党建工作
				openPage = myParam.id ? "mo_normal_details" : "mo_party_building";
				break;
			case "48": // 履职大厅
				openPage = myParam.id
					? "mo_performance_dynamics_details"
					: "mo_performance_dynamics";
				break;
			case "48_1": // 履职大厅审核
				openPage = myParam.id
					? "mo_performance_dynamics_examine_details"
					: "mo_performance_dynamics_examine";
				break;
			case "videoconferencing_details": //视频会议详情
				openPage = "mo_videoconferencing_details";
				break;
			case "videoconferencing_Join": //加入视频会议
				openPage = "mo_videoconferencing_Join";
				break;
			case "8897": //会议系统扫码相关操作
				break;
			default:
				T.toast("请使用电脑登录系统进行处理");
				return;
		}

		if (nowModule && myParam.id) delete myParam.title;
		myParam.code = _param.ncode || _param.code;
		if (myParam.url) {
			openPage = "mo_details_url";
		}
		if (!openPage) {
			T.toast("请使用电脑登录系统进行处理");
			return;
		}
		if (T.platform() == "web" && myParam.url) {
			window.open(myParam.url);
		} else {
			T.openWin(
				openPage + (myParam.id || myParam.code),
				"../" + openPage + "/" + openPage + ".stml",
				myParam,
				_this
			);
		}

		//增加阅读量
		if (nowModule && nowModule.behaviorCode) {
			addBehaviorRecord(_param, _this);
		}
		//需要已读
		if (_param.isRedDot == 1) {
			addRedDot(_param, _this);
			if (_param.oldId) {
				//消息中心已读
				addRedDot(_param, _this, null, _param.oldBusinessCode, _param.oldId);
			}
		}
	}

	// 增加已读
	function addRedDot(_param, _this, _callback, _businessCode, _businessId) {
		var businessCode = "",
			businessId = "";
		if (_businessCode) {
			businessCode = _businessCode;
			businessId = _businessId;
		} else {
			var nowModule = baseModule(_param.code, "code");
			businessCode = nowModule.businessCode;
			businessId = _param.id;
		}
		T.ajax(
			{u: myjs.appUrl() + "redDot/sign", _this: _this},
			"redDot/sign" + businessId,
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			"设置已读",
			"post",
			{
				body: JSON.stringify({businessCode: businessCode, businessId: businessId})
			}
		);

		_param.isRedDot = "0";
	}

	// 增加阅读量
	function addBehaviorRecord(_param, _this) {
		var nowModule = baseModule(_param.code, "code");
		T.ajax(
			{
				u:
					myjs.appUrl() +
					("behavior/record/" + nowModule.behaviorCode + "/" + _param.id),
				_this: _this
			},
			"behavior/record",
			function(ret, err) {},
			"阅读",
			"post",
			{
				body: JSON.stringify({})
			}
		);
	}

	// 获取各模块详情接口
	function getModuleDetails(_param, _this, _callback) {
		var url = "";
		var postParam = {};

		postParam.detailId = _param.id;
		switch (_param.code) {
			case "file": //文件详情
				url = myjs.appUrl() + "file/info/" + _param.id;
				break;
			case "5":
			case "8":
				url = myjs.appUrl() + "newsContent/info";
				break;
			case "6": //意见征集
			case "24": //协商征集
				url = myjs.appUrl() + "opinioncollect/info";
				break;
			case "10": //活动详情
				url = myjs.appUrl() + "servantactivity/info";
				break;
			case "10_1": //活动资料
				url = myjs.appUrl() + "activitydoc/info";
				break;
			case "11_2": //履职补录
				url = myjs.appUrl() + "performdutiesaddit/info";
				break;
			case "12": //圈子
				url = myjs.appUrl() + "styleCircle/info";
				break;
			case "14_1": //系统消息
				url = myjs.appUrl() + "boxMessage/info";
				break;
			case "20": //协商计划
				url = myjs.appUrl() + "consultPlan/info";
				break;
			case "21": //协商活动
				url = myjs.appUrl() + "consultActivity/info";
				break;
			case "22": //通知公告
				url = myjs.appUrl() + "notification/info";
				break;
			case "23": //协商制度
				url = myjs.appUrl() + "consultRegulation/info";
				break;
			case "25": //远程协商
				url = myjs.appUrl() + "consultActivityConference/info";
				break;
			case "26": //规范性文件库
				url = myjs.appUrl() + "open_api/openStatute/app/info";
				break;
			case "27": //规范性文件库
				url = myjs.appUrl() + "officeOnlineTopic/info";
				break;
			case "28": //委员值班
				url = myjs.appUrl() + "officeOnlineTopic/info";
				break;
			case "29": //委员会客厅
				url = myjs.appUrl() + "officeOnlineTopic/info";
				break;
			case "30": //投票
				url = myjs.appUrl() + "voteTopic/info";
				break;
			case "32": //建议
				url = myjs.appUrl() + "suggestion/info";
				postParam.isOpenWithLock = _param.isOpenWithLock;
				break;
			case "33": //提案
				url = myjs.appUrl() + "proposal/info";
				postParam.isOpenWithLock = _param.isOpenWithLock;
				break;
			case "34": //问卷调查
				url = myjs.appUrl() + "survey/info";
				break;
			case "35": //立法征询
				url = myjs.appUrl() + "legislationOpinion/info";
				break;
			case "36": //备案审查
				url = myjs.appUrl() + "open_api/openStatute/app/info";
				break;
			case "37": //掌上议案
				url = myjs.appUrl() + "motion/info";
				break;
			case "38": //提案线索征集
				url = myjs.appUrl() + "proposalClue/info";
				break;
			case "39": //立法联系点
				url = myjs.appUrl() + "lawConsultRegulation/info";
				break;
			case "40": //微建议
				url = myjs.appUrl() + "microAdvice/info";
				break;
			case "41": //社情民意
				url = myjs.appUrl() + "socialInfo/info";
				break;
			case "42": //立法规划计划详情
				url = myjs.appUrl() + "lawPlanningPlan/info";
				break;
			case "43": //立法项目库
				url = myjs.appUrl() + "lawProject/info";
				break;
			case "44": //立法进程
				url = myjs.appUrl() + "lawProcessInfo/info";
				break;
			case "45": //政协书院
				url = myjs.appUrl() + "syReadingNotes/info";
				break;
			case "47": //党建工作
				url = myjs.appUrl() + "partyActivity/info";
				break;
			case "48": //履职动态
				url = myjs.appUrl() + "performdutiesroom/info";
				break;
			case "48_1": //履职动态审核
				url = myjs.appUrl() + "performdutiesroom/info";
				break;
			default:
				_callback(null, "未携带code");
				return;
		}

		T.ajax(
			{u: url, _this: _this},
			_param.tag || "details" + _param.id,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					//得出外部链接
					_param.link =
						data.contentType == 2 ? data.linkUrl || data.externalLinks || "" : "";
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(postParam)
			},
			_param.header
		);
	}

	//打开扫码
	function openScan(_param, _this, _callback) {
		if (_param === void 0) {
			_param = {};
		}
		function scanResult(_content) {
			if (_content.indexOf(myjs.chatHeader() + "|") != -1) {
				//如果是内部扫码 HeNanZXJavaapp|login|http://aa.com/
				var params = _content.split("|");
				if (params.length < 3) {
					T.alert(
						{title: "识别结果为：", msg: _content, buttons: ["关闭"]},
						function(ret, err) {}
					);
					return;
				}
				switch (params[1]) {
					case "login": //登录
						T.showProgress("登录中");
						T.ajax(
							{
								u:
									myjs.appUrl() + "scanCodeLogin/receipt/appToken?qrCodeId=" + params[2],
								_this: _this
							},
							"appToken",
							function(ret, err) {
								T.hideProgress();
								T.toast(ret ? ret.message || ret.data : T.NET_ERR);
								if (ret && ret.code == 200) {
									_callback && _callback();
								}
							},
							params[1]
						);
						break;
					case "activityCode": //活动签到码	platform5zx|activityCode|580930599619919872|7961|430000
						var url = "",
							param = {
								form: {
									activityId: params[2]
								},

								signInCommand: params[3]
							};

						url = myjs.appUrl() + "activityperson/join";
						T.showProgress("签到中");
						T.ajax(
							{u: url, _this: _this},
							"activityperson/join",
							function(ret, err) {
								T.hideProgress();
								T.toast(ret ? ret.message || ret.data : T.NET_ERR);
								if (ret && ret.code == 200) {
									_callback && _callback();
								}
							},
							params[1],
							"post",
							{
								body: JSON.stringify(param)
							}
						);

						break; //扫群二维码
					case "groupQr":
						var _id = params[2];
						T.showProgress();
						T.ajax(
							{u: myjs.appUrl() + "chatGroup/info", _this: _this},
							"chatGroup/info" + _id,
							function(ret, err) {
								T.hideProgress();
								var data = ret ? ret.data || {} : {};
								if (!ret || ret.code != "200" || !data.id) {
									T.toast("获取群信息失败，请稍候重试");
									return;
								}
								var memberUserIds = data.memberUserIds || [];
								if (G.getItemForKey(G.userId, memberUserIds)) {
									openChat(
										{conversationType: "GROUP", targetId: myjs.chatHeader() + data.id},
										_this
									);
									return;
								}
								T.showProgress("加入中");
								T.ajax(
									{u: myjs.appUrl() + "chatGroup/edit", _this: _this},
									"chatGroup/edit" + _id,
									function(ret, err) {
										T.hideProgress();
										if (!ret || ret.code != "200") {
											T.toast("加入群组失败，请稍候重试");
											return;
										}
										openChat(
											{
												conversationType: "GROUP",
												targetId: myjs.chatHeader() + data.id,
												paramMore: {joinType: "groupQr"}
											},
											_this
										);
									},
									"\u52A0\u5165\u7FA4\u7EC4" + _id,
									"post",
									{
										body: JSON.stringify({
											form: {groupName: data.groupName, id: data.id},
											memberUserIds: memberUserIds.concat([G.userId]),
											ownerUserId: data.ownerUserId
										})
									}
								);
							},
							"\u83B7\u53D6\u7FA4\u4FE1\u606F" + _id,
							"post",
							{
								body: JSON.stringify({detailId: _id})
							}
						);

						break;
					case "toWygzsSignApp": //代表联络站/委员工作室 活动扫码签到
						var url = "";
						if (params.length > 4) {
							url = params[2] + "app/wygzsApp/operationActivityUserByUser?";
						} else {
							T.alert(
								{title: "提示", msg: "后台未配置有误,请联系管理员", buttons: ["关闭"]},
								function(ret, err) {}
							);
							return;
						}

						var paramObj = {activityId: params[4], type: 2, status: 1}; //type:2 签到操作 , status: 1签到
						T.showProgress("签到中");
						T.ajax(
							{u: url, _this: _this},
							"app/wygzsApp/operationActivityUserByUser?",
							function(ret, err) {
								T.hideProgress();
								T.toast(ret ? ret.message || ret.data : T.NET_ERR);
								if (ret && ret.code == 200) {
									var workstationsUrl =
										params[3] +
										"/#/activitiesDetail?id=" +
										params[4] +
										"&token={{token}}&areaId={{areaId}}";
									setTimeout(function() {
										openDetails(
											{code: "8897", link: workstationsUrl, title: "详情"},
											null,
											_this
										);
									}, 1500);
								}
							},
							"代表联络站扫码签到接口",
							"post",
							{
								values: paramObj
							},
							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);

						break;
					case "zhtmeetingSignIn": //会议签到--智会通
						var url = "";
						if (params.length > 3) {
							url = params[2] + "appMeetSign/tosignin?";
						} else {
							T.alert(
								{title: "提示", msg: "后台未配置有误,请联系管理员", buttons: ["关闭"]},
								function(ret, err) {}
							);
							return;
						}
						var paramObj = {
							meetId: params[3],
							activityId: params[3],
							conferenceId: params[3],
							dataId: params[3],
							signInType: "qrCode",
							userId: G.userId,
							type: "signIn"
						};

						T.showProgress("签到中");
						T.ajax(
							{u: url, _this: _this},
							"appMeetSign/tosignin?",
							function(ret, err) {
								T.hideProgress();
								T.toast(ret ? ret.message || ret.data : T.NET_ERR);
								// if(ret&&ret.code==200){

								// }
							},
							"会议系统签到接口",
							"post",
							{
								values: paramObj
							},
							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);

						break;
					default:
						T.alert(
							{title: "识别结果为：", msg: _content, buttons: ["关闭"]},
							function(ret, err) {}
						);
						break;
				}
			} else if (_content.indexOf("http") == 0) {
				openDetails({code: "5", link: _content, title: ""}, null, _this);
			} else {
				T.alert({title: "识别结果为：", msg: _content, buttons: ["关闭"]}, function(
					ret,
					err
				) {});
			}
		}
		if (T.platform() == "app") {
			var zyHmsScan = api.require("zyHmsScan");
			if (!zyHmsScan) {
				T.toast("未绑定扫码模块，请联系开发");
				return;
			}
			var preName = "camera";
			if (
				!T.confirmPer(
					preName,
					"getPicture",
					"用于打开摄像头并扫码，若取消将无法使用扫一扫功能"
				)
			) {
				//相机权限
				T.addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
					T.removeEventListener(preName + "Per_" + "getPicture");
					if (ret.value.granted) {
						openScan(_param, _this, _callback);
					}
				});
				return;
			}
			zyHmsScan.openDefaultView({}, function(ret) {
				if (ret.status) {
					setTimeout(function() {
						scanResult(ret.result);
					}, 300);
				}
			});
		} else if (T.platform() == "mp") {
			wx.scanCode({
				success: function success(ret) {
					setTimeout(function() {
						scanResult(ret.result);
					}, 300);
				}
			});
		}
	}

	// 收藏/取消收藏
	function optionCollect(_param, _this, _callback) {
		var businessCode = _param.code;
		if (_param.module == "2") {
			businessCode = "contentInformation";
		} else if (_param.module == "6") {
			businessCode = "learningmaterials";
		} else if (_param.module == "7") {
			businessCode = "perform_promote";
		}
		var param = {
			businessCode: businessCode,
			businessId: _param.id,
			theme: _param.theme,
			ids: _param.ids,
			folderId: _param.folderId || "0"
		};

		if (_param.collect) {
			param = {form: param};
		}
		T.ajax(
			{
				u: myjs.appUrl() + ("favorite/" + (_param.collect ? "add" : "dels")),
				_this: _this
			},
			"favorite/add",
			function(ret, err) {},
			(_param.collect ? "" : "取消") + "\u6536\u85CF",
			"post",
			{
				body: JSON.stringify(param)
			}
		);
	}

	// 是否收藏
	function isCollect(_param, _this, _callback) {
		var businessCode = _param.code;
		if (_param.module == "2") {
			businessCode = "contentInformation";
		} else if (_param.module == "6") {
			businessCode = "learningmaterials";
		} else if (_param.module == "7") {
			businessCode = "perform_promote";
		}
		var param = {
			businessCode: businessCode
		};

		T.ajax(
			{u: myjs.appUrl() + "favorite/findBusinessIds", _this: _this},
			"findBusinessIds",
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || [] : [];
				_callback && _callback(G.getItemForKey(_param.id, data));
			},
			"\u662F\u5426\u6536\u85CF",
			"post",
			{
				body: JSON.stringify(param)
			}
		);
	}

	// 复制
	function copyText(_text, _callback) {
		console.log("copy:" + _text);
		if (T.platform() == "app") {
			api.require("clipBoard").set(
				{
					value: _text
				},
				function(ret, err) {
					_callback && _callback(ret, err);
				}
			);
		} else if (T.platform() == "web") {
			htmlCopyText(_text, function(ret) {
				_callback && _callback(ret, null);
			});
		} else if (T.platform() == "mp") {
			wx.setClipboardData({
				data: _text,
				success: function success(ret) {
					_callback && _callback(ret, null);
				},
				fail: function fail(err) {
					_callback && _callback(null, err);
				}
			});
		}
	}

	// web复制

	// web复制
	function htmlCopyText(_text, callback) {
		var success = true;
		var textarea = document.createElement("textarea");
		textarea.value = _text;
		document.body.appendChild(textarea);
		textarea.select();
		textarea.setSelectionRange(0, textarea.value.length); // 兼容 iOS 设备
		try {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(_text).then(
					function() {
						success = true;
					},
					function(err) {
						success = false;
					}
				);
			} else {
				var input = document.createElement("input");
				input.setAttribute("value", _text);
				document.body.appendChild(input);
				input.select();
				input.setSelectionRange(0, input.value.length); // 兼容 iOS 设备
				success = document.execCommand("copy");
				document.body.removeChild(input);
			}
		} catch (err) {
			success = false;
		} finally {
			document.body.removeChild(textarea);
		}
		callback && callback(success);
	}

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var ZSearch = /*@__PURE__*/ (function(Component) {
		function ZSearch(props) {
			Component.call(this, props);
			this.data = {
				propValue: ""
			};
			this.compute = {
				id: function() {
					return this.props.id || "input";
				}
			};
		}

		if (Component) ZSearch.__proto__ = Component;
		ZSearch.prototype = Object.create(Component && Component.prototype);
		ZSearch.prototype.constructor = ZSearch;
		ZSearch.prototype.installed = function() {
			var this$1 = this;
			if (
				T.isParameters(this.props.dataMore) ? this.props.dataMore.autofocus : false
			) {
				//是否自动获取焦点
				setTimeout(function() {
					$("#" + this$1.id).focus();
				}, 150);
			}
		};
		ZSearch.prototype.inputConfirm = function() {
			var this$1 = this;

			$("#" + this.id).blur();
			setTimeout(function() {
				this$1.fire("confirm", {});
			}, 0);
		};
		ZSearch.prototype.inputIng = function() {
			var this$1 = this;

			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.input = this.props.dataMore.input.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			setTimeout(function() {
				this$1.fire("input", {});
			}, 0);
		};
		ZSearch.prototype.inputBlur = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("blur", {});
			}, 0);
		};
		ZSearch.prototype.inputFocus = function() {
			var this$1 = this;

			$("#" + this.id).focus();
			setTimeout(function() {
				this$1.fire("focus", {});
			}, 0);
		};
		ZSearch.prototype.clean = function() {
			var this$1 = this;

			if (!this.i) {
				this.props.dataMore.input = "";
				this.i = 1;
			} else {
				this.props.dataMore.input = " ".repeat(this.i++ % 2);
				this.props.dataMore.input = " ".repeat(this.i++ % 2);
			}
			this.inputIng();
			setTimeout(function() {
				this$1.fire("clean", {});
			}, 0);
		};
		ZSearch.prototype.openLeftFilters = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("leftFilters", {});
			}, 0);
		};
		ZSearch.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "z_search_box " + (this.props.class || ""),
					style:
						"\n\tborder-top-left-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-top-right-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-bottom-left-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-bottom-right-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tbackground: " +
						(this.props.bg || "#F4F5F7") +
						";\n\tjustify-content: " +
						(this.props.justify ||
							(this.props.type == "1" ? "center" : "flex-start")) +
						";\n\t" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					{style: "width:auto;height:auto;"},
					T.isParameters(this.props.dataMore) &&
						this.props.dataMore.leftFilters &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.openLeftFilters();
								},
								style:
									"margin-left:-5px;margin-right:15px;flex-direction:row;align-items: center;"
							},
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(1) +
										"font-weight: 400;color: #333;margin-right:4px;"
								},
								this.props.leftFiltersText
							),
							apivm.h("a-iconfont", {
								name: "xiangxia",
								color: "#333",
								size: G.appFontSize - 4
							}),
							apivm.h("view", {
								style: "width:1px;height:24px;background:#E5E7E8;margin-left:8px;"
							})
						)
				),
				apivm.h(
					"view",
					{style: "width:auto;height:auto;"},
					(!T.isParameters(this.props.dataMore) ||
						(!this.props.dataMore.dotIcon && !this.props.dataMore.leftFilters)) &&
						apivm.h(
							"view",
							{class: "z_search_input_icon", style: "margin-right:10px;"},
							apivm.h("a-iconfont", {
								class: "z_search_icon",
								name: "sousuo",
								color: "#999",
								size: G.appFontSize - 2
							})
						)
				),
				this.props.type == "1"
					? apivm.h(
							"text",
							{
								class: "z_search_text",
								style:
									"color:#999;line-height: " +
									(G.appFontSize + 2) +
									"px;" +
									G.loadConfiguration(-2)
							},
							this.props.placeholder
					  )
					: [
							apivm.h("input", {
								id: this.id,
								style:
									"" + G.loadConfiguration() + (this.props.dataMore.inputStyle || ""),
								"placeholder-style": "color:#999;",
								class: "z_search_input",
								type: this.props.inputType || "text",
								placeholder: this.props.placeholder,
								onInput: function(e) {
									if (typeof this$1 != "undefined") {
										this$1.props.dataMore.input = e.target.value;
									} else {
										this$1.data.this.props.dataMore.input = e.target.value;
									}
									this$1.inputIng(e);
								},
								maxlength: this.props.dataMore.maxlength,
								"confirm-type": this.props.confirmType || "search",
								"keyboard-type": this.props.keyboardType || "default",
								onConfirm: this.inputConfirm,
								onBlur: this.inputBlur,
								onFocus: this.inputFocus,
								value:
									typeof this == "undefined"
										? this.data.this.props.dataMore.input
										: this.props.dataMore.input
							}),
							this.props.dataMore.input &&
								!this.props.dataMore.dotCleanIcon &&
								apivm.h(
									"view",
									{
										onClick: this.clean,
										class: "z_search_input_icon",
										style: "padding: 5px;padding-left:0;margin-right: -10px;"
									},
									apivm.h("a-iconfont", {
										name: "qingkong",
										color: "#666",
										size: G.appFontSize
									})
								)
					  ]
			);
		};

		return ZSearch;
	})(Component);
	ZSearch.css = {
		".z_search_box": {
			width: "100%",
			height: "100%",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "2px 15px"
		},
		".z_search_text": {marginLeft: "4px"},
		".z_search_input": {
			paddingLeft: "0px",
			paddingRight: "10px",
			width: "1px",
			flex: "1",
			background: "transparent",
			borderColor: "transparent",
			color: "#333"
		},
		".z_search_input::placeholder": {color: "#999"},
		".z_search_input_icon": {
			alignItems: "center",
			justifyContent: "center",
			width: "auto",
			height: "auto"
		}
	};
	apivm.define("z-search", ZSearch);

	var ADivider = /*@__PURE__*/ (function(Component) {
		function ADivider(props) {
			Component.call(this, props);
			this.compute = {
				boxClass: function() {
					return "a-divider " + (this.props.class || "");
				},
				lineStyle: function() {
					return this.props["line-color"]
						? "border-top-color:" + this.props["line-color"] + ";"
						: "";
				},
				textStyle: function() {
					return (
						(this.props.color ? "color:" + this.props.color + ";" : "") +
						"" +
						(this.props.style || "")
					);
				}
			};
		}

		if (Component) ADivider.__proto__ = Component;
		ADivider.prototype = Object.create(Component && Component.prototype);
		ADivider.prototype.constructor = ADivider;
		ADivider.prototype.lineClass = function(position) {
			var width =
				this.props["content-position"] == position
					? "a-divider_line-width"
					: "a-divider_line-flex";
			var style = this.props.dashed
				? "a-divider_line-dashed"
				: "a-divider_line-solid";
			return "a-divider_line " + width + " " + style;
		};
		ADivider.prototype.render = function() {
			return apivm.h(
				"view",
				{class: this.boxClass, style: this.props.style || ""},
				this.props.content
					? [
							apivm.h("view", {class: this.lineClass("left"), style: this.lineStyle}),
							apivm.h(
								"text",
								{class: "a-divider_text", style: this.textStyle},
								this.props.content
							),
							apivm.h("view", {class: this.lineClass("right"), style: this.lineStyle})
					  ]
					: apivm.h("view", {class: this.lineClass("center"), style: this.lineStyle})
			);
		};

		return ADivider;
	})(Component);
	ADivider.css = {
		".a-divider": {flexDirection: "row", alignItems: "center"},
		".a-divider_line": {borderTopWidth: "1px", borderTopColor: "#F6F6F6"},
		".a-divider_line-solid": {borderTopStyle: "solid"},
		".a-divider_line-dashed": {borderTopStyle: "dashed"},
		".a-divider_line-width": {width: "10%"},
		".a-divider_line-flex": {flex: "1"},
		".a-divider_text": {
			fontSize: "14px",
			color: "#969799",
			padding: "0 16px",
			textAlign: "center",
			maxWidth: "75%"
		}
	};
	apivm.define("a-divider", ADivider);

	var ZActionsheetInput = /*@__PURE__*/ (function(Component) {
		function ZActionsheetInput(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				search: {
					input: "",
					autofocus: false,
					dotIcon: true,
					inputStyle: "text-align: center;"
				}
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.autofocus && $("#" + this.props.dataMore.key)) {
								setTimeout(function() {
									$("#" + this$1.props.dataMore.key).focus();
								}, 150);
							}
							this.data.search.input = this.props.dataMore.input;
						} else {
							//重置 不让input重复绑定 导致不返回数据
							this.data.search.input = "";
							this.data.search = JSON.parse(JSON.stringify(this.data.search));
						}
					}
				}
			};
		}

		if (Component) ZActionsheetInput.__proto__ = Component;
		ZActionsheetInput.prototype = Object.create(Component && Component.prototype);
		ZActionsheetInput.prototype.constructor = ZActionsheetInput;
		ZActionsheetInput.prototype.installed = function() {};
		ZActionsheetInput.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		ZActionsheetInput.prototype.penetrate = function() {};
		ZActionsheetInput.prototype.itemClick = function() {
			var this$1 = this;

			this.props.dataMore.input = this.data.search.input;
			setTimeout(function() {
				this$1.fire("click", this$1.props.dataMore);
				this$1.closePage();
			}, 0);
		};
		ZActionsheetInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "actionSheetInput_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "flex:1;min-height:30%;flex-shrink: 0;"
				}),
				apivm.h(
					"view",
					{
						class: "actionSheetInput_warp",
						onClick: function() {
							return this$1.penetrate();
						}
					},
					apivm.h(
						"view",
						{class: "actionSheetInput_header_warp"},
						apivm.h(
							"view",
							{class: "actionSheetInput_header_main"},
							apivm.h(
								"text",
								{
									style: G.loadConfiguration(1) + "color:#333",
									class: "actionSheetInput_header_main_text"
								},
								this.props.dataMore.value
							)
						),
						apivm.h(
							"view",
							{class: "actionSheetInput_header_left_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "actionSheetInput_header_btn"
									},
									apivm.h(
										"text",
										{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"取消"
									)
								)
							)
						),
						apivm.h(
							"view",
							{class: "actionSheetInput_header_right_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick();
										},
										class: "actionSheetInput_header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						)
					),
					apivm.h(
						"view",
						{style: "width:100%;height:1px;padding:0 16px;"},
						apivm.h("a-divider", null)
					),
					apivm.h(
						"view",
						{style: "align-items: center;min-height:300px;padding-top:70px;"},
						apivm.h(
							"view",
							null,
							this.props.dataMore.fileInfo.type == "text"
								? apivm.h(
										"text",
										{style: G.loadConfiguration(1) + "color:#333;margin:20px 4px;"},
										this.props.dataMore.fileInfo.text
								  )
								: this.props.dataMore.fileInfo.type == "img"
								? apivm.h("img", {src: "", alt: ""})
								: apivm.h("a-iconfont", {
										style: "font-weight:" + (this.props.dataMore.weight || "400") + ";",
										name: this.props.dataMore.fileInfo.name,
										color:
											this.props.dataMore.fileInfo.color == "appTheme"
												? G.appTheme
												: this.props.dataMore.fileInfo.color || "#fff",
										size:
											G.appFontSize +
											(T.isParameters(this.props.dataMore.size)
												? Number(this.props.dataMore.size)
												: 4)
								  })
						),
						apivm.h(
							"view",
							{style: "padding:20px 16px;width: 100%;"},
							this.data.show
								? apivm.h(
										"view",
										{style: "height:36px;width:100%;"},
										apivm.h("z-search", {
											id: this.props.dataMore.key,
											dataMore: this.data.search,
											onConfirm: function() {
												return this$1.itemClick();
											},
											placeholder: this.props.dataMore.placeholder
										})
								  )
								: null
						)
					)
				)
			);
		};

		return ZActionsheetInput;
	})(Component);
	ZActionsheetInput.css = {
		".actionSheetInput_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".actionSheetInput_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			height: "auto"
		},
		".actionSheetInput_header_warp": {
			height: "49px",
			flexDirection: "row",
			width: "100%",
			alignItems: "center"
		},
		".actionSheetInput_header_btn": {
			height: "49px",
			width: "auto",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".actionSheetInput_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".actionSheetInput_header_main": {
			flex: "1",
			width: "1px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "0 49px"
		},
		".actionSheetInput_header_main_text": {fontWeight: "600", maxWidth: "200px"},
		".actionSheetInput_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row"
		},
		".actionSheetInput_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row-reverse"
		}
	};
	apivm.define("z-actionSheet-input", ZActionsheetInput);

	var ZTabs = /*@__PURE__*/ (function(Component) {
		function ZTabs(props) {
			Component.call(this, props);
			this.data = {
				oldData: [],

				scrollIView: "",
				scrollIViewAnimation: true,
				itemDotBounces: this.props.dotBounces || false
			};
			this.compute = {
				itemData: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						return [];
					}
					var hasChange = false;
					if (this.data.oldData.length != this.props.dataMore.data.length) {
						hasChange = true;
					} else {
						for (var i = 0; i < this.props.dataMore.data.length; i++) {
							if (
								this.props.dataMore.data[i].key != this.data.oldData[i].key ||
								this.props.dataMore.data[i].value != this.data.oldData[i].value
							) {
								hasChange = true;
								break;
							}
						}
					}
					if (hasChange) {
						this.data.oldData = this.props.dataMore.data.concat([]);
						if (this.props.type == "4") {
							setTimeout(function() {
								this$1.tabClick(this$1.data.oldData[this$1.data.oldData.length - 1]);
							}, 30);
						}
					}
					return this.data.oldData;
				}
			};
		}

		if (Component) ZTabs.__proto__ = Component;
		ZTabs.prototype = Object.create(Component && Component.prototype);
		ZTabs.prototype.constructor = ZTabs;
		ZTabs.prototype.installed = function() {
			if (!this.props.dataMore) {
				return;
			}
			if (
				!this.props.dotDefault &&
				!G.getItemForKey(this.props.dataMore.key, this.itemData, "key")
			) {
				this.tabClick(
					G.getItemForKey(this.props.dataMore.defaultKey, this.itemData, "key") ||
						this.itemData[0]
				);
			}
		};
		ZTabs.prototype.tabClick = function(_item) {
			var this$1 = this;

			if (!_item) {
				return;
			}
			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			//不需要默认的时候 可以取消tab
			if (this.props.dotDefault && this.props.dataMore.key == _item.key) {
				this.props.dataMore.key = "";
				setTimeout(function() {
					this$1.fire("change", {key: this$1.props.dataMore.key});
				}, 0);
				return;
			}
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;
			setTimeout(function() {
				this$1.fire("change", {key: this$1.props.dataMore.key});
			}, 0);
			var nowView = this.props.id + "_tabs_" + _item.key;
			try {
				if (T.platform() == "app") {
					document.getElementById(this.props.id).scrollTo({view: nowView});
				} else if (T.platform() == "mp") {
					this.data.scrollIView = nowView;
				} else {
					document.getElementById(this.props.id).scrollTo({
						left: document.getElementById(nowView).offsetLeft,
						behavior: "smooth" //带动画划动
					});
				}
			} catch (e) {}
		};
		ZTabs.prototype.nTouchmove = function() {
			G.touchmove();
		};
		ZTabs.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style: "width:100%;height:40px;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				!this.props.type || this.props.type == 1
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab_box",
										style:
											(T.platform() != "app" ? "display: inline-block;" : "") +
											"width:" +
											(this$1.props.average
												? 100 / this$1.itemData.length + "%"
												: "auto") +
											";"
									},
									apivm.h(
										"view",
										{
											class: "zy-tab",
											style:
												"margin-left:" +
												(!index$1 ? "8" : "0") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "8" : "0") +
												"px;"
										},
										apivm.h(
											"view",
											{style: "align-items: center;"},
											apivm.h(
												"text",
												{
													style:
														G.loadConfiguration(this$1.props.size) +
														";color:#" +
														(this$1.props.dataMore.key == item$1.key ? "000" : "666") +
														";font-weight: " +
														(this$1.props.dataMore.key == item$1.key ? "8" : "4") +
														"00;",
													class: "zy-tab__text"
												},
												item$1.value
											),
											apivm.h("view", {
												class: "zy-tab__line",
												style:
													"background:" +
													(this$1.props.dataMore.key != item$1.key
														? "transparent"
														: this$1.props.bg) +
													";"
											})
										)
									)
								);
							})
					  )
					: this.props.type == 2
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type2_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type2",
											style:
												"background:" +
												(this$1.props.dataMore.key == item$1.key
													? this$1.props.bg
													: "#F8F8F8") +
												";margin-left:" +
												(!index$1 ? "16" : "5") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "5") +
												"px;"
										},
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";line-height:" +
													((this$1.props.size || 0) + 16) * 1.5 +
													"px;color:" +
													(this$1.props.dataMore.key == item$1.key ? "#FFF" : "#666"),
												class: "zy-tab-type2__text"
											},
											item$1.value
										)
									)
								);
							})
					  )
					: this.props.type == 3
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type3_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type3",
											style:
												"margin-left:" +
												(!index$1 ? "16" : "6") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "6") +
												"px;"
										},
										apivm.h(
											"view",
											{class: "zy-tab-hot_box"},
											T.isParameters(item$1.badge) &&
												item$1.badge > 0 &&
												apivm.h(
													"view",
													{class: "zy-tab-hot_warp"},
													apivm.h(
														"text",
														{
															style:
																"font-size:" + ((this$1.props.size || 0) + 16 - 6) + "px;",
															class: "zy-tab-hot_text"
														},
														item$1.badge
													)
												)
										),
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";line-height:" +
													((this$1.props.size || 0) + 16) * 1.4 +
													"px;color:" +
													(this$1.props.dataMore.key == item$1.key
														? this$1.props.bg
														: "#666"),
												class: "zy-tab-type3__text"
											},
											item$1.value
										),
										apivm.h("view", {
											class: "zy-tab-type3__line",
											style:
												"background:" +
												(this$1.props.dataMore.key != item$1.key
													? "transparent"
													: this$1.props.bg) +
												";"
										})
									)
								);
							})
					  )
					: this.props.type == 4
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type4_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type4",
											style:
												"margin-left:" +
												(!index$1 ? "16" : "0") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "0") +
												"px;"
										},
										apivm.h(
											"view",
											{style: "display:" + (index$1 ? "flex;" : "none") + ";"},
											apivm.h("a-iconfont", {
												style: "margin:0 10px;transform: rotate(180deg) scale(0.7,0.7);",
												name: "fanhui1",
												color: index$1 ? "#999" : "rgba(0,0,0,0)",
												size: G.appFontSize - 4
											})
										),
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";line-height:" +
													((this$1.props.size || 0) + 16) * 1.4 +
													"px;color:" +
													(index$1 == this$1.itemData.length - 1 && index$1 != 0
														? "#333"
														: "#999"),
												class: "zy-tab-type4__text"
											},
											item$1.value
										)
									)
								);
							})
					  )
					: this.props.type == 5
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type5_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type5",
											style:
												"border-radius:" +
												(!index$1
													? "2px 0 0 2px"
													: index$1 == this$1.itemData.length - 1
													? "0 2px 2px 0"
													: "0") +
												";z-index:" +
												(this$1.props.dataMore.key == item$1.key ? 1 : 0) +
												";background:" +
												(this$1.props.dataMore.key == item$1.key
													? this$1.props.bg
													: "#FFF") +
												";margin-left:" +
												(!index$1 ? "16" : "0") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "-1") +
												"px;border: 1px solid " +
												(this$1.props.dataMore.key == item$1.key
													? this$1.props.bg
													: "#333") +
												";"
										},
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";color:" +
													(this$1.props.dataMore.key == item$1.key ? "#FFF" : "#333") +
													";",
												class: "zy-tab-type5__text"
											},
											item$1.value
										)
									)
								);
							})
					  )
					: this.props.type == 6
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type2_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type2",
											style:
												"background:" +
												(this$1.props.dataMore.key == item$1.key
													? this$1.props.bg
													: "#F8F8F8") +
												";margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "5") +
												"px;border-radius:" +
												(this$1.props.borderRadius
													? this$1.props.borderRadius + "px"
													: "0") +
												";padding: 2px 15px; "
										},
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";line-height:" +
													((this$1.props.size || 0) + 16) * 1.5 +
													"px;color:" +
													(this$1.props.dataMore.key == item$1.key ? "#FFF" : "#666"),
												class: "zy-tab-type2__text"
											},
											item$1.value
										)
									)
								);
							})
					  )
					: []
			);
		};

		return ZTabs;
	})(Component);
	ZTabs.css = {
		".zy-tab-box": {
			flex: "1",
			height: "100%",
			flexDirection: "row",
			width: "auto",
			overflowY: "hidden !important"
		},
		".zy-tab": {
			padding: "6px 8px 4px 8px",
			flexShrink: "0",
			width: "auto",
			fontWeight: "400"
		},
		".zy-tab__text": {color: "#333", flexShrink: "0", padding: "1px 0"},
		".zy-tab__line": {
			position: "absolute",
			zIndex: "999",
			bottom: "-3px",
			width: "34px",
			maxWidth: "80%",
			height: "2px",
			borderRadius: "999px",
			background: "#333"
		},
		".zy-tab-type2_box": {flexShrink: "0", width: "auto"},
		".zy-tab-type2": {
			flexShrink: "0",
			width: "auto",
			fontWeight: "400",
			padding: "0px 8px",
			borderRadius: "2px",
			margin: "6px 5px"
		},
		".zy-tab-type2__text": {flexShrink: "0"},
		".zy-tab-type3_box": {flexShrink: "0", width: "auto"},
		".zy-tab-type3": {
			flexShrink: "0",
			width: "auto",
			padding: "9px 8px",
			margin: "17px 6px 5px",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			background: "#FFF",
			alignItems: "center"
		},
		".zy-tab-type3__text": {flexShrink: "0"},
		".zy-tab-type3__line": {
			position: "absolute",
			zIndex: "999",
			bottom: "-5px",
			width: "61%",
			height: "1px",
			borderRadius: "999px",
			background: "#333"
		},
		".zy-tab-type4_box": {flexShrink: "0", width: "auto"},
		".zy-tab-type4": {
			flexShrink: "0",
			width: "auto",
			fontWeight: "400",
			flexDirection: "row",
			alignItems: "center"
		},
		".zy-tab-type4__text": {flexShrink: "0"},
		".zy-tab-hot_box": {
			position: "absolute",
			zIndex: "999",
			top: "-7px",
			right: "-7px"
		},
		".zy-tab-hot_warp": {
			minWidth: "18px",
			minHeight: "18px",
			background: "#EF0000",
			borderRadius: "50px",
			alignItems: "center",
			justifyContent: "center",
			padding: "0 2px"
		},
		".zy-tab-hot_text": {color: "#FFF"},
		".zy-tab-type5_box": {flexShrink: "0", width: "auto"},
		".zy-tab-type5": {
			flexShrink: "0",
			width: "auto",
			fontWeight: "400",
			padding: "2px 12px",
			margin: "6px 5px"
		},
		".zy-tab-type5__text": {flexShrink: "0", fontWeight: "bold"}
	};
	apivm.define("z-tabs", ZTabs);

	var FavoriteAround = /*@__PURE__*/ (function(Component) {
		function FavoriteAround(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				ajax: false,
				listSelect: null,
				hasAddFolder: true,
				orderBys: {
					key: "updatedate",
					direction: 1 //0 顺序 1倒序
				},
				level: {key: "", defaultKey: "", data: [{key: "0", value: "全部"}]},

				listData: [
					// { id:"1", name:"XX收藏夹", fileInfo:{"name":"folder-2-fill",color:"#F6C21C",type:"folder"}, time:"2020-02-06 12:00", },
				],
				addInput: {
					show: false,
					autofocus: true,
					key: "around_add_folder_callback",
					value: "新建收藏夹",
					size: 24,
					weight: "bold",
					fileInfo: G.getFileInfo("folder"),
					input: "",
					placeholder: "新建收藏夹"
				},

				favoriteFolder: []
			};
			this.compute = {
				isShow: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.listData = [];
							this.data.listSelect = this.props.dataMore.listSelect || [];
							this.getData();
						} else {
							this.data.level.data = [this.data.level.data[0]];
						}
					}
				},
				titleName: function() {
					var name = "";
					switch (this.props.dataMore.key) {
						case "long_move":
							name =
								"移动到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_add":
							name =
								"收藏到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
					}

					return name;
				}
			};
		}

		if (Component) FavoriteAround.__proto__ = Component;
		FavoriteAround.prototype = Object.create(Component && Component.prototype);
		FavoriteAround.prototype.constructor = FavoriteAround;
		FavoriteAround.prototype.installed = function() {};
		FavoriteAround.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		FavoriteAround.prototype.penetrate = function() {};
		FavoriteAround.prototype.itemClick = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.props.dataMore.toId = this$1.data.level.key;
				this$1.props.dataMore.toItem =
					this$1.data.level.data[this$1.data.level.data.length - 1];
				this$1.props.dataMore.listSelect = this$1.data.listSelect;
				this$1.fire("click", this$1.props.dataMore);
				this$1.closePage();
			}, 0);
		};
		FavoriteAround.prototype.openFolder = function(_item) {
			this.data.level.data.push({key: _item.id, value: _item.name});
		};
		FavoriteAround.prototype.getData = function(_type) {
			var this$1 = this;

			if (!this.data.show) {
				return;
			}
			T.ajax(
				{u: myjs.appUrl() + "favoriteFolder/list", _this: this},
				"favoriteFolder",
				function(ret, err) {
					this$1.data.ajax = true;
					var code = ret ? ret.code : "";
					this$1.data.favoriteFolder =
						code == "200" && T.isArray(ret.data) ? ret.data : [];
					this$1.showFavoriteFolder();
				},
				"收藏夹列表",
				"post",
				{
					body: JSON.stringify({})
				}
			);

			this.showFavoriteFolder();
		};
		FavoriteAround.prototype.showFavoriteFolder = function() {
			var newList = [];
			if (this.data.level.key == "0") {
				newList = this.data.favoriteFolder;
			} else {
				newList =
					(
						G.getItemForKey(this.data.level.key, this.data.favoriteFolder, "id") || {}
					).children || [];
			}
			this.data.listSelect.forEach(function(_item, index) {
				G.delItemForKey(_item, newList, "id");
			});
			this.data.listData = newList;
		};
		FavoriteAround.prototype.levelChange = function(ref) {
			var detail = ref.detail;

			var nLevel = [],
				lastItem = null;
			for (var i = 0; i < this.data.level.data.length; i++) {
				lastItem = this.data.level.data[i];
				nLevel.push(lastItem);
				if (lastItem.key == detail.key) {
					break;
				}
			}
			this.data.level.data = nLevel;
			this.getData(0);
		};
		FavoriteAround.prototype.addFolder = function() {
			var this$1 = this;

			this.data.addInput.input = "";
			this.data.addInput.show = true;
			setTimeout(function() {
				$("#" + this$1.data.addInput.key).focus();
			}, 150);
		};
		FavoriteAround.prototype.keyCallback = function(ref) {
			var detail = ref.detail;

			console.log(JSON.stringify(detail));
			switch (detail.key) {
				case "around_add_folder_callback":
					this.addFolderCallback();
					break;
			}
		};
		FavoriteAround.prototype.addFolderCallback = function() {
			var this$1 = this;

			var postParam = {
				parentId: this.data.level.key || "0",
				name: this.data.addInput.input || this.data.addInput.placeholder
			};

			T.showProgress("新建中");
			T.ajax(
				{u: myjs.appUrl() + "favoriteFolder/add", _this: this},
				"favoriteFolder",
				function(ret, err) {
					T.hideProgress();
					T.toast(ret ? ret.message : T.NET_ERR);
					if (ret && ret.code == 200) {
						this$1.getData(0);
					}
				},
				"新建收藏夹",
				"post",
				{
					body: JSON.stringify({form: postParam})
				}
			);
		};
		FavoriteAround.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "cloudDiskAround_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				apivm.h(
					"view",
					{
						class: "cloudDiskAround_warp",
						onClick: function() {
							return this$1.penetrate();
						}
					},
					apivm.h(
						"view",
						{class: "cloudDiskAround_header_warp"},
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_main"},
							apivm.h(
								"text",
								{
									style: G.loadConfiguration(1) + "color:#333",
									class: "cloudDiskAround_header_main_text"
								},
								this.titleName
							)
						),
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_left_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "cloudDiskAround_header_btn"
									},
									apivm.h(
										"text",
										{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"取消"
									)
								)
							)
						),
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_right_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick();
										},
										class: "cloudDiskAround_header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						)
					),
					apivm.h(
						"view",
						{style: "width:100%;height:1px;padding:0 16px;"},
						apivm.h("a-divider", null)
					),
					apivm.h(
						"view",
						{
							style:
								"flex-direction:row;align-items: center;padding:10px 16px 10px 10px;margin-top:10px;"
						},
						apivm.h(
							"view",
							{style: "flex:1;width:1px;"},
							apivm.h("z-tabs", {
								id: "tab_cloud_disk_around",
								style: "height:" + (G.appFontSize + 5) + "px;",
								size: -2,
								dataMore: this.data.level,
								onChange: this.levelChange,
								type: 4,
								bg: "" + G.appTheme
							})
						),
						apivm.h(
							"view",
							null,
							this.data.hasAddFolder
								? apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.addFolder();
											},
											style: "margin-left:10px;flex-direction:row;align-items: center;"
										},
										apivm.h("a-iconfont", {
											style: "margin-right:6px;",
											name: "folder-add-fill",
											color: "#F6931C",
											size: G.appFontSize + 4
										}),
										apivm.h(
											"text",
											{style: G.loadConfiguration(1) + "color:#333;"},
											"新建"
										)
								  )
								: null
						)
					),
					apivm.h(
						"view",
						{style: "width:100%;height:1px;padding:0 16px;"},
						apivm.h("a-divider", null)
					),
					apivm.h(
						"scroll-view",
						{style: "flex:1;height:1px;", "scroll-y": true},
						apivm.h(
							"view",
							null,
							(Array.isArray(this.data.listData)
								? this.data.listData
								: Object.values(this.data.listData)
							).map(function(item$1, index$1, list) {
								return apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.openFolder(item$1, index$1);
										}
									},
									apivm.h(
										"view",
										{class: "folder_item"},
										apivm.h("a-iconfont", {
											name: "folder-2-fill",
											color: "#ffd977",
											size: G.appFontSize + 20
										}),
										apivm.h(
											"view",
											{class: "folder_item_body"},
											apivm.h(
												"view",
												{class: "folder_item_warp"},
												apivm.h(
													"text",
													{style: G.loadConfiguration(1) + "color: #333;flex:1;"},
													item$1.name
												),
												apivm.h(
													"view",
													{
														style:
															"width:auto;height:auto;padding:8px;transform: rotate(-90deg);"
													},
													apivm.h("a-iconfont", {
														name: "xiangxia1",
														color: "#999999",
														size: G.appFontSize
													})
												)
											),
											apivm.h("view", {class: "folder_item_line"})
										)
									)
								);
							})
						),
						apivm.h(
							"view",
							null,
							this.data.ajax &&
								!this.data.listData.length &&
								apivm.h(
									"view",
									{style: "align-items: center;padding-top:90px;"},
									apivm.h("image", {
										style: "width:200px;height:200px;",
										mode: "aspectFill",
										thumbnail: "false",
										src: myjs.shareAddress(1) + "img/icon_no_favorite.png"
									}),
									apivm.h(
										"text",
										{style: G.loadConfiguration(1) + "color: #666;margin-top:10px;"},
										"暂无收藏夹"
									)
								)
						)
					)
				),

				apivm.h("z-actionSheet-input", {
					dataMore: this.data.addInput,
					onClick: this.keyCallback
				})
			);
		};

		return FavoriteAround;
	})(Component);
	FavoriteAround.css = {
		".cloudDiskAround_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".cloudDiskAround_warp": {
			flex: "1",
			height: "1px",
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".cloudDiskAround_header_warp": {
			height: "49px",
			flexDirection: "row",
			width: "100%",
			alignItems: "center"
		},
		".cloudDiskAround_header_btn": {
			height: "49px",
			width: "auto",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".cloudDiskAround_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".cloudDiskAround_header_main": {
			flex: "1",
			width: "1px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "0 49px"
		},
		".cloudDiskAround_header_main_text": {
			fontWeight: "600",
			maxWidth: "200px",
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".cloudDiskAround_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row"
		},
		".cloudDiskAround_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row-reverse"
		},
		".folder_item": {
			padding: "0 16px",
			flexDirection: "row",
			alignItems: "center"
		},
		".folder_item_body": {flex: "1", marginLeft: "15px"},
		".folder_item_warp": {
			minHeight: "65px",
			padding: "5px 0",
			flexDirection: "row",
			alignItems: "center"
		},
		".folder_item_line": {borderTop: "1px solid #EEEEEE"},
		".folder_item_more": {width: "auto", height: "auto", padding: "6px"}
	};
	apivm.define("favorite-around", FavoriteAround);

	var NumberKeyboard = /*@__PURE__*/ (function(Component) {
		function NumberKeyboard(props) {
			Component.call(this, props);
			this.data = {
				numbers: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"],
				closeBase64:
					"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAtCAYAAAA5reyyAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAC4jAAAuIwF4pT92AAADLElEQVRo3u2Zz0tVQRTHP/lMreiXEEREPKIg29gmSJIQWgYVoQhtSqJalv0D0SKQdq0KClpXUNaupE0JZoZWZhL9cNNvkS5mlpbaYs7tnZ7vao83b+67Mh+43PO+9zh35jhnftwBj8fj8Xg8Hg+wHfgAzJTo1QWsjztIc9FfAkGa7+q22eBFlgM4AVQAAXBDKlxm+R35Mi3tPACsAqaA8pjrFMkoJmh9cVckB31St1GbhdruHdNyr3ARkTwJ6zRdUClZuOjKm4GDYt8CnojdBGzF9Ijz0rB1wFF53o8ZBgDqgd1iXwbeY/75J4AVwAvgujzfBuwT+x7Q6aCN1ggwaTKgtCYyA/gppfcovUq0BqU9Vr5tSm8QrUppPcq3VekXlD4gWmCzwS4G+F8R9o8cvjq9JpU9EeGTq6yvyv5S7Ma5SOFHwHFgMXBX6aeBWsy6MQzQIHAMqMSkZcg1YFj8BkWbAJoxaf/UQTucEDA7hV1zmEwKn1F6YlPYNR9dvsxFCm8AGjFj1wxmUVsG3ASGxKcZ2Ai8Ba6KtgnYL3Y78Fr5poEx4AowjlmitGCGiTqXAbRNwOwUbiT3luqI8hkWbURpLcq3RekjSk+LVh3xjrPq7xKbwt//Q/8s908Rz7WtfX7LfUou5+1zkcIPgJ1i6xTuVz7NwFoygQToAHaJ/TzLdw0mqKH/N2AHZm0YBrKCTNonhoD4Z+EoEpvCCxqXs3AKuAM8E30PUEPusSsfyoBXwG35XQ0cwszI94GHDtpojYC5Z2G9F+6mOB9Ja5V+UemJTeFxZY8pe9jiO3RZer8dFLtxLlK4E/M5qpx/e+ZJ4Bzws8DyqzD76ZAhzIy8FHjpoH1WCfCzcEGk5D5ZUCnFIaxTqqBSsrCdwkvkngYuUVqHSmn5XYrHDX/pIP5jy/mu3riDNBfVmLVX3EGKut5gzmGsYftcOKQOWEn+i+Qp4B3RJ2erMWe7+ZLC7J27yHyAsEKxAlgINZiPBDNZ+nLMGUehy54FTSVmSxaVgvVxVzAp9DI7eHvjrlSSWIY52wiD1xp3hZLIFkzw2uOuSJKpw/RGj8fj8XgWHn8A4dKLb2WSDzAAAAAASUVORK5CYII=",
				backBase64:
					"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAtCAYAAAA5reyyAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAC4jAAAuIwF4pT92AAAC/UlEQVRo3u2aO2gUURSGv1k1PlFXUHwUQZEYlUACPiM2KbQSKxEkBEtbHyDxAWqjoK2gqIjYWJpC0gQLQUWjoBJUtBOMiWJMwJiYiGtxdtlzx1lndr07w4X7wcBm59xzcv655752wOPxeDwej8dJggxjtwGHgHagsQ7+fwHvgJvA1QzzrAv7gUKK12NgQdZJ22J3yuKVruf1SCbtEu4Cbqm/nyEl9hKYBGZYivMbyCMP66j6/gxwNuWcrXEMs0fcSynuKRXzY9Yi1EoHpnivUoydRyaUAvApayFqYQtSUiXxPgPLUoyfA0aKsYeyFqNaOjHFewisSNCuHRm7ZiaM0wQcB1ZH3JsNfMXBHniAv5cSSQgo95gBYH6M/XJggso9bI7y54yAzZjijZB8xg+AMZKNlw3Aa2U7HGHjnIAbgO8qqTFgY5U+9mI+gNsRNguBF5jirYuwc0rAFsozXqkEV9boaxvmg3iAjGcArcCguvcIWFLBj1MCvlVJvQHm/qe/9cC48nkf2E55zCsAH2J8OCPgVsyyW2PJbwuVt2fjwNqY9s4IeBpzuWKTDmSM0+L1A6sStK2rgDmLvvLq86Dl/7MfGVs1ozi8NYuik3Lv+GbR7yxErKgSfkL8AYQzJbwolNxFCz4D4KnyOQQcDsXpjfHhjIAAR7AnYiNSuiVf75FZGWSXM63u3fiHH6cEBBFNi3i3Bh9NmLuRAWBeyKYNmMKcuKLK2TkBAS5hininyvYDqu0UsuuIohX4qWz7ImycFBDkwFSL2F1F21KbaWBzjG34rDGMswICXA4ldyVhuz2I4M0J7XcBJ4GdEfecFhDgPKaIPSnHD4AvOHqgWiI8Jl5LMXYD8oOV0wKCrNe0iCdSitulYjpZwppwOfcCO5BeEiDLEBtXDlgMHMRc6ly3nVAWr3acQw4eNMPFRG3tzQuIgPpthB/I4cNoBjlb5wLpvpUwAWyqRyK23gSolj7kxGYpUgUB0gNtXpPFGD3APuS3E4/H4/F4PB6Pxwp/AGH7f53PGS6/AAAAAElFTkSuQmCC"
			};
		}

		if (Component) NumberKeyboard.__proto__ = Component;
		NumberKeyboard.prototype = Object.create(Component && Component.prototype);
		NumberKeyboard.prototype.constructor = NumberKeyboard;
		NumberKeyboard.prototype.installed = function() {
			if (this.props.dataMore.isRandom) {
				this.data.numbers.sort(this.randomsort);
			}
		};
		NumberKeyboard.prototype.randomsort = function(a, b) {
			return Math.random() > 0.5 ? -1 : 1; //用Math.random()函数生成0~1之间的随机数与0.5比较，返回-1或1
		};
		NumberKeyboard.prototype.closeBoard = function(e) {
			this.fire("close", "");
		};
		NumberKeyboard.prototype.getNumber = function(_item) {
			var this$1 = this;

			if (_item == -1) {
				_item = this.data.numbers[this.data.numbers.length - 1];
			}
			if (this.props.dataMore.value.length < this.props.dataMore.codeLength) {
				this.props.dataMore.value += _item;
			}
			if (this.props.dataMore.value.length == this.props.dataMore.codeLength) {
				setTimeout(function() {
					this$1.fire("finish", "");
				}, 0);
			}
		};
		NumberKeyboard.prototype.delNumber = function(e) {
			if (this.props.dataMore.value) {
				this.props.dataMore.value = this.props.dataMore.value.substring(
					0,
					this.props.dataMore.value.length - 1
				);
			}
		};
		NumberKeyboard.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "number-keyboard_box"},
				apivm.h(
					"view",
					{class: "number-keyboard_box-item-container"},
					(Array.isArray(this.data.numbers)
						? this.data.numbers
						: Object.values(this.data.numbers)
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							{
								class: "number-keyboard_box-item",
								style:
									"display:" +
									(index$1 != this$1.data.numbers.length - 1 ? "block" : "none")
							},
							apivm.h(
								"view",
								{
									class: "number-keyboard_box-item-label",
									onClick: function() {
										return this$1.getNumber(item$1);
									}
								},
								apivm.h("text", {style: "font-size:28px;"}, item$1)
							)
						);
					}),
					apivm.h(
						"view",
						{class: "number-keyboard_box-item"},
						apivm.h(
							"view",
							{class: "number-keyboard_box-item-label", onClick: this.closeBoard},
							apivm.h("image", {
								class: "number-keyboard_box-item-ico",
								src: this.data.closeBase64,
								mode: "widthFix"
							})
						)
					),
					apivm.h(
						"view",
						{class: "number-keyboard_box-item"},
						apivm.h(
							"view",
							{
								class: "number-keyboard_box-item-label",
								onClick: function() {
									return this$1.getNumber(-1);
								}
							},
							apivm.h(
								"text",
								{style: "font-size:28px;"},
								this.data.numbers[this.data.numbers.length - 1]
							)
						)
					),
					apivm.h(
						"view",
						{class: "number-keyboard_box-item"},
						apivm.h(
							"view",
							{class: "number-keyboard_box-item-label", onClick: this.delNumber},
							apivm.h("image", {
								class: "number-keyboard_box-item-ico",
								src: this.data.backBase64,
								mode: "widthFix"
							})
						)
					)
				)
			);
		};

		return NumberKeyboard;
	})(Component);
	NumberKeyboard.css = {
		".number-keyboard_box": {
			alignItems: "center",
			width: "100%",
			backgroundColor: "#EFEFEF"
		},
		".number-keyboard_box-item-container": {
			flexFlow: "row wrap",
			justifyContent: "space-around",
			alignItems: "center",
			padding: "10px"
		},
		".number-keyboard_box-item": {
			flexBasis: "33%",
			boxSizing: "border-box",
			padding: "5px"
		},
		".number-keyboard_box-item-label": {
			display: "flex",
			backgroundColor: "#ffffff",
			padding: "5px",
			borderRadius: "5px",
			width: "100%",
			height: "48px",
			alignItems: "center",
			justifyContent: "center"
		},
		".number-keyboard_box-item-ico": {width: "60px"}
	};
	apivm.define("number-keyboard", NumberKeyboard);

	var PasswordInput = /*@__PURE__*/ (function(Component) {
		function PasswordInput(props) {
			Component.call(this, props);
			this.data = {
				cursorIndex: false,
				cursorTask: null
			};
			this.compute = {
				codeArr: function() {
					var aar = [];
					for (var index = 0; index < this.props.dataMore.codeLength; index++) {
						aar[index] = null;
					}
					return aar;
				}
			};
		}

		if (Component) PasswordInput.__proto__ = Component;
		PasswordInput.prototype = Object.create(Component && Component.prototype);
		PasswordInput.prototype.constructor = PasswordInput;
		PasswordInput.prototype.installed = function() {
			this.startCursor();
		};
		PasswordInput.prototype.codeInput = function(e) {
			this.fire("codeClick", "");
		};
		PasswordInput.prototype.startCursor = function() {
			var this$1 = this;

			this.data.cursorIndex = !this.data.cursorIndex;
			this.data.cursorTask && clearTimeout(this.data.cursorTask);
			this.data.cursorTask = setTimeout(function() {
				this$1.startCursor();
			}, 600);
		};
		PasswordInput.prototype.getCursorShow = function(index) {
			return this.data.cursorIndex && index == this.props.dataMore.value.length;
		};
		PasswordInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "password-input_container"},
				apivm.h(
					"view",
					{class: "password-input_security"},
					(Array.isArray(this.codeArr)
						? this.codeArr
						: Object.values(this.codeArr)
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							{
								class: "password-input_item",
								style: "margin-left:" + (index$1 ? "16" : "0") + "px;",
								"data-index": index$1,
								onClick: this$1.codeInput
							},
							this$1.props.dataMore.value[index$1] && !this$1.props.dataMore.mask
								? apivm.h("text", {class: "password-input_item-word--hidden"})
								: null,
							this$1.props.dataMore.value[index$1] && this$1.props.dataMore.mask
								? apivm.h(
										"text",
										{
											class: "password-input_item-word--mask",
											style: G.loadConfiguration(4) + "color: #333333;font-weight: 600;"
										},
										this$1.props.dataMore.value[index$1]
								  )
								: null,
							this$1.getCursorShow(index$1) &&
								apivm.h("view", {
									class: "password-input_item-cursor",
									style: "height: " + (G.appFontSize + 10) + "px;"
								})
						);
					})
				)
			);
		};

		return PasswordInput;
	})(Component);
	PasswordInput.css = {
		".password-input_container": {width: "100%"},
		".password-input_security": {flexFlow: "row", justifyContent: "center"},
		".password-input_item": {
			width: "48px",
			height: "48px",
			borderRadius: "2px",
			background: "#F8F8F8",
			alignItems: "center",
			justifyContent: "center"
		},
		".password-input_item-word--hidden": {
			width: "10px",
			height: "10px",
			backgroundColor: "#000",
			borderRadius: "100%"
		},
		".password-input_item-word--mask": {textAlign: "center"},
		".password-input_item-cursor": {
			position: "absolute",
			zIndex: "999",
			width: "1px",
			left: "24px",
			top: "9px",
			background: "#666"
		}
	};
	apivm.define("password-input", PasswordInput);

	var YNumInput = /*@__PURE__*/ (function(Component) {
		function YNumInput(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) YNumInput.__proto__ = Component;
		YNumInput.prototype = Object.create(Component && Component.prototype);
		YNumInput.prototype.constructor = YNumInput;
		YNumInput.prototype.installed = function() {};
		YNumInput.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		YNumInput.prototype.finish = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("finish", this$1.props.dataMore);
			}, 0);
		};
		YNumInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "numInput_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h(
					"view",
					{
						style:
							"flex:1;height:1px;align-items: center; justify-content: center;width:100%;"
					},
					apivm.h(
						"view",
						{class: "numInput_warp"},
						apivm.h(
							"view",
							{class: "numInput_title"},
							apivm.h(
								"text",
								{style: G.loadConfiguration(1) + "color: #333333;font-weight: 600;"},
								this.props.dataMore.title
							),
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "numInput_close"
								},
								apivm.h("a-iconfont", {
									name: "cuohao",
									color: "#666666",
									size: G.appFontSize + 6
								})
							)
						),
						apivm.h(
							"view",
							{style: "width:100%;padding:9px 9px 25px;"},
							apivm.h("password-input", {dataMore: this.props.dataMore})
						)
					)
				),
				apivm.h("number-keyboard", {
					dataMore: this.props.dataMore,
					onFinish: this.finish,
					onClose: function() {
						return this$1.closePage();
					}
				}),
				apivm.h("view", {
					style: "background:#EFEFEF;padding-bottom:" + T.safeArea().bottom + "px;"
				})
			);
		};

		return YNumInput;
	})(Component);
	YNumInput.css = {
		".numInput_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".numInput_warp": {
			background: "#FFFFFF",
			borderRadius: "10px 10px 10px 10px",
			minWidth: "320px",
			justifyContent: "center",
			alignItems: "center"
		},
		".numInput_title": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center",
			height: "54px",
			width: "100%",
			minWidth: "320px"
		},
		".numInput_close": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			height: "54px",
			width: "54px",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("y-num-input", YNumInput);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.zButtonClick = function(e, _props) {
			var this$1 = this;

			if (!this.props.disabled) {
				setTimeout(function() {
					this$1.fire("click", {});
				}, 0);
			}
			if (!this.props.bubble) {
				G.stopBubble(e);
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "z-button",
					style:
						"\n\t\tborder-top-left-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-top-right-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-bottom-right-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-bottom-left-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-color:" +
						this.props.color +
						";\n\t\tbackground:" +
						(this.props.plain ? "#FFF" : this.props.color) +
						";\n\t\topacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";\n\t\t" +
						this.props.style,
					onClick: function(e) {
						return this$1.zButtonClick(e, this$1.props);
					}
				},
				apivm.h(
					"text",
					{
						style:
							"\n\t\tcolor:" +
							(this.props.plain ? this.props.color : "#FFF") +
							";\n\t\t" +
							G.loadConfiguration((this.props.size || 16) - 16) +
							"\n\t"
					},
					this.props.text
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z-button": {
			padding: "8.3px 12px",
			textAlign: "center",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			display: "flex",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZTag = /*@__PURE__*/ (function(Component) {
		function ZTag(props) {
			Component.call(this, props);
		}

		if (Component) ZTag.__proto__ = Component;
		ZTag.prototype = Object.create(Component && Component.prototype);
		ZTag.prototype.constructor = ZTag;
		ZTag.prototype.getTagColor = function(_type) {
			if (this.props.bg) {
				return (
					(_type == "bg"
						? "background:" + (this.props.bg || "#F6931C")
						: "color:#FFF") + ";"
				);
			} else {
				if (this.props.plain) {
					return (
						"" +
						(_type == "bg" ? "border:1px solid " : "color:") +
						(this.props.color || "#F6931C") +
						";"
					);
				} else {
					return (
						(_type == "bg" ? "background" : "color") +
						":" +
						T.colorRgba(this.props.color || "#F6931C", _type == "bg" ? 0.1 : 1) +
						";"
					);
				}
			}
		};
		ZTag.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "" + this.getTagColor("bg"), class: "tag_box"},
				apivm.h(
					"text",
					{style: "" + this.props.style + this.getTagColor("text")},
					this.props.text
				)
			);
		};

		return ZTag;
	})(Component);
	ZTag.css = {
		".tag_box": {
			padding: "1px 10px",
			borderRadius: "2px 2px 2px 2px",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-tag", ZTag);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.dotClose) {
								setTimeout(function() {
									this$1.props.dataMore.dotClose = false;
								}, 500);
							}
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.installed = function() {};
		ZActionsheet.prototype.closePage = function() {
			if (this.props.dataMore.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
			T.sendEvent("updatePage");
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			var this$1 = this;

			if (this.props.dataMore.dotClose) {
				return;
			}
			if (_item.disabled) {
				return;
			}
			if (T.isParameters(this.props.active)) {
				this.props.active.key = _item.key;
			}
			_item.buttonIndex = _index + 1;
			setTimeout(function() {
				this$1.fire("click", _item);
				this$1.closePage();
			}, 0);
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "actionSheet_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "flex:1;min-height:30%;flex-shrink: 0;"
				}),
				apivm.h(
					"scroll-view",
					{class: "actionSheet_warp", style: "flex-shrink: 1;", "scroll-y": true},
					apivm.h(
						"view",
						null,
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(-2 + 1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					this.props.data.map(function(item, index) {
						return (
							(T.isParameters(item.show) ? item.show : true) && [
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick(item, index);
										},
										class: "actionSheet_item",
										style:
											"justify-content:" +
											(item.type ? "flex-start" : "center") +
											";opacity: " +
											(item.disabled ? "0.3" : "1") +
											";"
									},
									apivm.h(
										"view",
										null,
										T.isParameters(this$1.props.active) &&
											T.isParameters(this$1.props.active.direction) &&
											this$1.props.active.direction >= 0 &&
											item.key == this$1.props.active.key &&
											apivm.h("a-iconfont", {
												style:
													"margin-left:-25px;margin-right:10px;transform: rotate(" +
													(this$1.props.active.direction == 1 ? "0" : "180") +
													"deg);",
												name: "zhixiangxia",
												color: G.appTheme,
												size: G.appFontSize - 1
											})
									),
									apivm.h(
										"view",
										null,
										item.type == "img"
											? apivm.h("img", {src: "", alt: ""})
											: item.type == "icon"
											? apivm.h("a-iconfont", {
													style:
														"font-weight:" + (item.weight || "400") + ";margin-right:10px;",
													name: item.src,
													color: item.color || "#333",
													size:
														G.appFontSize +
														(T.isParameters(item.size) ? Number(item.size) : 4)
											  })
											: ""
									),
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(-2 + 1) +
												";color:" +
												(item.key ==
												(T.isParameters(this$1.props.active) && this$1.props.active.key)
													? G.appTheme
													: "#333")
										},
										item.value
									)
								),
								index != this$1.props.data.length - 1 &&
									!this$1.props.cancel &&
									!this$1.props.dataMore.cancel &&
									apivm.h(
										"view",
										{style: "width:100%;height:1px;padding:0 16px;"},
										apivm.h("a-divider", null)
									)
							]
						);
					})
				),
				apivm.h(
					"view",
					null,
					(this.props.cancel || this.props.dataMore.cancel) &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.closePage();
								},
								class: "actionSheet_item",
								style:
									"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration(-2) + ";color:#333"},
								this.props.dataMore.cancel || "取消"
							)
						)
				),
				apivm.h("view", {
					style: "background:#fff;padding-bottom:" + T.safeArea().bottom + "px;"
				})
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			height: "auto"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "0 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	// 加密
	function getFileAdress(_param, _callback) {
		if (_param === void 0) {
			_param = {};
		}
		//文档地址 https://www.yozodcs.com/page/help.html#help9
		T.ajax(
			{u: "https://www.yozodcs.com/fcscloud/file/http?"},
			"onlinefile",
			function(ret, err) {
				if (ret) {
					var data = (ret.data || {}).data;
					if (data) {
						T.ajax(
							{u: "https://www.yozodcs.com/fcscloud/composite/convert?"},
							"onlinefile",
							function(ret, err) {
								if (ret) {
									var viewUrl = (ret.data || {}).viewUrl;
									if (viewUrl) {
										_callback(viewUrl, ret.data);
									} else {
										_callback(null, ret.message || "打开失败，请重试");
									}
								} else {
									_callback(null, "打开失败");
								}
							},
							"生成链接",
							"post",
							{
								values: {
									srcRelativePath: data,
									convertType:
										G.getFileInfo(data.substring(data.lastIndexOf("."))).convertType ||
										"0",
									isDccAsync: 1,
									isCopy: 0,
									noCache: 0,
									fileUrl: _param.url,
									showFooter: 0, //是否显示页脚
									isHeaderBar: 0,
									htmlTitle: "详情"
								}
							},

							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);
					} else {
						_callback(null, ret.message || "打开失败，请重试");
					}
				} else {
					_callback(null, "打开失败");
				}
			},
			"在线转换",
			"post",
			{
				values: {
					fileUrl: _param.url
				}
			},

			{
				"content-type": "application/x-www-form-urlencoded"
			}
		);
	}

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				withSrc: "",
				showSrc: "",
				showPoster: "",
				showError: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.src != this.data.withSrc) {
						this.data.withSrc = this.props.src;
						this.dealWith();
					}
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.installed = function() {};
		ZVideo.prototype.dealWith = function() {
			var this$1 = this;

			console.log(
				"withSrc:" +
					this.data.withSrc +
					"-" +
					this.data.showSrc +
					"-" +
					dayjs$1().unix()
			);
			if ((this.data.withSrc + "").indexOf("http") != 0) {
				//不是http开头 说明系统内附件
				var cachePath = T.getPrefs("attach_" + this.data.withSrc);
				console.log(cachePath);
				if (
					cachePath &&
					dayjs$1().unix() - Number(cachePath.split("-attachPath-")[0]) < 86400
				) {
					this.data.showSrc = cachePath.split("-attachPath-")[1];
					this.data.showPoster =
						myjs.tomcatAddress() + "utils2/proxyVideo?" + this.data.showSrc;
					return;
				}
				var src = myjs.appUrl() + "file/preview/" + this.data.withSrc;
				getFileAdress({url: src}, function(ret, err) {
					if (ret) {
						T.ajax(
							{u: ret, dt: "text", _this: this$1},
							"onlinefile",
							function(ret, err) {
								var matchResult = ret ? ret.match(/videoFile = "([^"]+)"/) : "";
								if (matchResult) {
									this$1.data.showError = "";
									function unicodeToChinese(str) {
										return str.replace(/\\u(\w{4})/g, function(match, code) {
											return String.fromCharCode(parseInt(code, 16));
										});
									}
									var path = unicodeToChinese(matchResult[1]).replace(/\\/g, "");
									this$1.data.showSrc = path;
									this$1.data.showPoster =
										myjs.tomcatAddress() + "utils2/proxyVideo?" + this$1.data.showSrc;
									T.setPrefs(
										"attach_" + this$1.data.withSrc,
										dayjs$1().unix() + "-attachPath-" + path
									);
								} else {
									this$1.data.showError = err;
								}
							},
							"附件详情",
							"get"
						);
					} else {
						this$1.data.showError = err;
					}
				});
			} else {
				this.data.showSrc = this.data.withSrc;
				this.data.showPoster =
					myjs.tomcatAddress() + "utils2/proxyVideo?" + this.data.showSrc;
			}
		};
		ZVideo.prototype.again = function(e) {
			if (this.data.showError) {
				this.data.showError = "";
				this.dealWith();
			}
			G.stopBubble(e);
		};
		ZVideo.prototype.render = function() {
			return apivm.h(
				"view",
				{
					a: this.monitor,
					style:
						"width:100%;height: " +
						(T.platform() == "web"
							? api.winWidth > 600
								? 600
								: api.winWidth
							: api.winWidth) *
							0.52 +
						"px;" +
						this.props.style,
					class: this.props.class
				},
				this.data.showSrc
					? apivm.h("video", {
							id: this.props.id,
							controls: true,
							style: "width:100%;height:100%;object-fit: cover;",
							src: this.data.showSrc,
							poster: this.props.poster || this.data.showPoster,
							mode: "aspectFill"
					  })
					: apivm.h(
							"view",
							{
								onClick: this.again,
								style:
									"width:100%;height:100%;align-items: center;justify-content: center;"
							},
							this.data.showError != ""
								? apivm.h(
										"view",
										{
											style:
												"flex-direction:row;align-items: center;justify-content: center;"
										},
										apivm.h("a-iconfont", {
											name: "huanyuan",
											color: "#666",
											style: "font-weight: 600;margin-right:10px;",
											size: G.appFontSize
										}),
										apivm.h(
											"text",
											{style: "color:#666;" + G.loadConfiguration()},
											"视频加载失败"
										)
								  )
								: apivm.h("image", {
										style: "width:50px;height:50px;",
										src: myjs.shareAddress(1) + "img/loading.gif",
										mode: "aspectFill",
										thumbnail: "false"
								  })
					  )
			);
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false //是否展开了
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.installed = function() {};
		ZRichText.prototype.expandShow = function() {
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(T.isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (T.isObject(expText) || T.isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			var notTagText = T.removeTag(expText);
			this.data.hasExpand =
				T.isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = T.decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log(expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = T.removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nowItem = {
					label: nlabel,
					index: index,
					text: item,
					style: styleMatch ? styleMatch[1] : ""
				};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: styleMatch ? styleMatch[1] : ""
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: styleMatch ? styleMatch[1] : ""
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var tdWidth = this$1.getStyle(item.style, "width") || "150px";
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					showTexts.push(addItem);
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			G.stopBubble(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			console.log(imgs, "imgs");
			G.openImgPreviewer({
				index: G.getItemForKey(_item.index + "", imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			G.stopBubble(e);
			if (T.platform() == "web") {
				window.open(_item.href);
				return;
			}
			T.openWin(
				"mo_details_url",
				"../mo_details_url/mo_details_url.stml",
				{url: _item.href},
				this
			);
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp(_item + ":s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			G.touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{a: this.monitor, class: this.props.class},
				apivm.h(
					"view",
					null,
					(Array.isArray(this.data.listData)
						? this.data.listData
						: Object.values(this.data.listData)
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							null,
							item$1.label == "text"
								? apivm.h(
										"view",
										null,
										apivm.h(
											"text",
											{
												style:
													this$1.props.style +
													"line-height: " +
													G.appFontSize * 1.8 +
													"px;" +
													(this$1.props.detail && T.platform() != "app"
														? "text-indent: 2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item$1.text
										)
								  )
								: null,
							item$1.label == "text_a"
								? apivm.h(
										"view",
										{
											onClick: function(e) {
												return this$1.openHrefs(e, item$1, index$1);
											}
										},
										apivm.h(
											"text",
											{style: this$1.props.style + "color: blue;", class: "richText"},
											item$1.text
										)
								  )
								: item$1.label == "br"
								? apivm.h(
										"view",
										null,
										apivm.h("view", {style: "height:" + G.appFontSize + "px;"})
								  )
								: item$1.label == "img"
								? apivm.h(
										"view",
										{
											onClick: function(e) {
												return this$1.openImages(e, item$1, index$1);
											}
										},
										apivm.h(
											"view",
											{class: "richImgBox"},
											apivm.h("image", {
												class: "richImg",
												mode: "aspectFill",
												thumbnail: "false",
												src: G.showImg(item$1.src)
											})
										)
								  )
								: item$1.label == "video" || item$1.label == "source"
								? apivm.h(
										"view",
										{style: "margin:5px 0;"},
										item$1.src && apivm.h("z-video", {src: item$1.src})
								  )
								: item$1.label == "table"
								? apivm.h(
										"view",
										null,
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchStart: this$1.nTouchmove
												},
												(item$1.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item$1.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{style: "" + G.loadConfiguration(), class: "richText"},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								  )
								: apivm.h("view", null)
						);
					})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: function() {
									return this$1.expandShow();
								}
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration(-2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {alignItems: "center", justifyContent: "center"},
		".richImg": {width: "100%", maxWidth: "100%", maxHeight: "100%"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				search: {dotIcon: true, show: true, input: "", value: ""}
			};
			this.compute = {
				isShow: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.input) {
								this.data.search.input = this.props.dataMore.content;
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.installed = function() {};
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				this.fire("cancel");
			}
			T.sendEvent("updatePage");
		};
		ZAlert.prototype.closeStop = function(e) {
			G.stopBubble(e);
		};
		ZAlert.prototype.inputIng = function(e) {
			if (T.isParameters(e)) {
				this.data.search.input = e.detail.value;
			}
		};
		ZAlert.prototype.itemClick = function() {
			var this$1 = this;

			setTimeout(function() {
				if (this$1.props.dataMore.input || this$1.props.dataMore.textarea) {
					this$1.props.dataMore.content = this$1.data.search.input;
				}
				this$1.data.search.input = "";
				this$1.fire("click", this$1.props.dataMore);
				this$1.fire("sure");
				this$1.closePage(1);
			}, 0);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "alert_box",
					onClick: this.closeStop,
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				this.data.show
					? apivm.h(
							"view",
							{class: "alert_warp", onClick: this.closeStop},
							apivm.h(
								"view",
								{class: "alert_content_title"},
								this.props.dataMore.title &&
									apivm.h(
										"text",
										{class: "alert_title", style: "" + G.loadConfiguration(4)},
										this.props.dataMore.title
									)
							),
							apivm.h(
								"scroll-view",
								{class: "alert_content_box", "scroll-x": false, "scroll-y": true},
								this.props.dataMore.richText
									? apivm.h("z-rich-text", {
											style: G.loadConfiguration(1) + "color:#333;",
											nodes: this.props.dataMore.content
									  })
									: this.props.dataMore.input
									? apivm.h(
											"view",
											{style: "height:36px;width:100%;"},
											apivm.h("z-search", {
												id: "alert_input",
												dataMore: this.data.search,
												placeholder: this.props.dataMore.placeholder
											})
									  )
									: this.props.dataMore.textarea
									? apivm.h("textarea", {
											id: "alert_input",
											style:
												G.loadConfiguration(1) +
												"height: " +
												(this.props.dataMore.height || "150") +
												"px;",
											class: "alert_textarea",
											placeholder: this.props.dataMore.placeholder,
											"placeholder-style": "color:#999;",
											value: this.data.search.input,
											"confirm-type": "return",
											onInput: this.inputIng
									  })
									: apivm.h(
											"text",
											{class: "alert_content", style: "" + G.loadConfiguration(1)},
											this.props.dataMore.content
									  )
							),
							apivm.h(
								"view",
								{style: "width:100%;height:1px;padding:0 15px;"},
								apivm.h("a-divider", null)
							),
							apivm.h(
								"view",
								{class: "alert_btn_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "alert_btn_item",
										style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) +
												"color:" +
												(this.props.dataMore.cancel.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.cancel.color) +
												";"
										},
										this.props.dataMore.cancel.text
									)
								),
								apivm.h(
									"view",
									{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
									apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
								),
								apivm.h(
									"view",
									{
										onClick: this.itemClick,
										class: "alert_btn_item",
										style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) +
												"color:" +
												(this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color) +
												";"
										},
										this.props.dataMore.sure.text
									)
								)
							)
					  )
					: null
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_box": {
			position: "absolute",
			zIndex: "1001",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {margin: "30px 15px", maxHeight: "350px"},
		".alert_title": {
			color: "#333333",
			fontWeight: "bold",
			padding: "20px 20px 0",
			textAlign: "center"
		},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {flexDirection: "row", alignItems: "center"},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#eee",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		},
		".alert_textarea::placeholder": {color: "#999"}
	};
	apivm.define("z-alert", ZAlert);

	var YAttachments = /*@__PURE__*/ (function(Component) {
		function YAttachments(props) {
			Component.call(this, props);
			this.data = {};
			this.compute = {
				imgList: function() {
					var list = [];
					if (T.isArray(this.props.data) && this.props.data.length) {
						this.props.data.forEach(function(_eItem, _eIndex, _eArr) {
							var fileInfo = G.getFileInfo(_eItem.extName || "unknown");
							if (fileInfo.type != "image") {
								return;
							}
							list.push({
								id: _eItem.id,
								name: _eItem.originalFileName,
								newName: _eItem.newFileName,
								url: myjs.appUrl() + "image/" + _eItem.newFileName,
								fileInfo: fileInfo,
								dotSystemDel: _eItem.dotSystemDel
							});
						});
					}
					return list;
				},
				id: function() {},
				name: function() {},
				newName: function() {},
				url: function() {},
				fileInfo: function() {},
				dotSystemDel: function() {},
				fileList: function() {
					var list = [];
					if (T.isArray(this.props.data) && this.props.data.length) {
						this.props.data.forEach(function(_eItem, _eIndex, _eArr) {
							var fileInfo = G.getFileInfo(_eItem.extName || "unknown");
							if (fileInfo.type == "image") {
								return;
							}
							list.push({
								id: _eItem.id,
								name: _eItem.originalFileName,
								newName: _eItem.newFileName,
								url: myjs.appUrl() + "file/preview/" + _eItem.id,
								fileInfo: fileInfo,
								dotSystemDel: _eItem.dotSystemDel
							});
						});
					}
					return list;
				}
			};
		}

		if (Component) YAttachments.__proto__ = Component;
		YAttachments.prototype = Object.create(Component && Component.prototype);
		YAttachments.prototype.constructor = YAttachments;
		YAttachments.prototype.installed = function() {};
		YAttachments.prototype.openFile = function(e, _item) {
			G.stopBubble(e);
			var param = {};
			param.id = _item.id || _item.url;
			param.suffix = _item.fileInfo.type;
			param.fileSource = this.props.fileSource;
			openWin_filePreviewer(param);
		};
		YAttachments.prototype.openImages = function(e, _item, _index) {
			G.stopBubble(e);
			G.openImgPreviewer({
				index: _index,
				imgs: this.imgList.map(function(obj) {
					return obj.url;
				})
			});
		};
		YAttachments.prototype.delFile = function(e, _item, _index) {
			G.stopBubble(e);
			G.delItemForKey(_item, this.props.data, "id");
			if (!_item.dotSystemDel) {
				T.ajax(
					{u: myjs.appUrl() + "file/clear", _this: this},
					"file/clear" + _item.id,
					function(ret, err) {},
					"删除附件",
					"post",
					{
						body: JSON.stringify({fileIds: [_item.id]})
					}
				);
			}
		};
		YAttachments.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				null,
				this.imgList.length + this.fileList.length > 0
					? apivm.h(
							"view",
							{style: this.props.style},
							apivm.h(
								"view",
								null,
								this.imgList.length > 0 &&
									apivm.h(
										"view",
										{
											class: "commentSendBig_img_box",
											style:
												"margin-bottom:" + (this.fileList.length > 0 ? "1" : "") + "0px;"
										},
										(Array.isArray(this.imgList)
											? this.imgList
											: Object.values(this.imgList)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													class: "commentSendBig_img_item",
													onClick: function(e) {
														return this$1.openImages(e, item$1, index$1);
													}
												},
												apivm.h("image", {
													style: "width:100%;" + G.loadConfigurationSize(44, "h"),
													src: G.showImg(item$1),
													mode: "aspectFill"
												}),
												apivm.h(
													"view",
													{
														class: "commentSendBig_img_clean",
														style:
															"display:" + (this$1.props.type == "2" ? "flex" : "none") + ";",
														onClick: function(e) {
															return this$1.delFile(e, item$1, index$1);
														}
													},
													apivm.h("a-iconfont", {
														name: "qingkong",
														color: "rgba(0,0,0,0.65)",
														size: G.appFontSize + 2
													})
												)
											);
										})
									)
							),
							apivm.h(
								"view",
								null,
								this.fileList.length > 0 &&
									apivm.h(
										"view",
										null,
										(Array.isArray(this.fileList)
											? this.fileList
											: Object.values(this.fileList)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													class: "attachments_item",
													style:
														"margin-top:" +
														(index$1 ? 1 : 0) +
														"0px;background:" +
														(this$1.props.theme == "dark" ? "#FFF" : "transparent") +
														";",
													onClick: function(e) {
														return this$1.openFile(e, item$1, index$1);
													}
												},
												apivm.h(
													"view",
													{style: "width:auto;height:auto;margin-right:10px;"},
													apivm.h("a-iconfont", {
														name: item$1.fileInfo.name,
														color: item$1.fileInfo.color,
														size: G.appFontSize + 6
													})
												),
												apivm.h(
													"view",
													{style: "flex:1;"},
													apivm.h(
														"text",
														{
															class: "text_one" + (T.platform() == "app" ? "" : "2"),
															style:
																G.loadConfiguration() + "color: #666;word-break: break-all;"
														},
														item$1.name
													)
												),
												apivm.h(
													"view",
													null,
													this$1.props.type == "2"
														? apivm.h(
																"view",
																{
																	style: "padding:5px;margin-right:-5px;",
																	onClick: function(e) {
																		return this$1.delFile(e, item$1, index$1);
																	}
																},
																apivm.h("a-iconfont", {
																	name: "qingkong",
																	color: "#333",
																	size: G.appFontSize + 4
																})
														  )
														: null
												)
											);
										})
									)
							)
					  )
					: null
			);
		};

		return YAttachments;
	})(Component);
	YAttachments.css = {
		".attachments_item": {
			width: "auto",
			borderRadius: "4px 4px 4px 4px",
			opacity: "1",
			border: "1px solid #F4F5F7",
			padding: "2px 10px",
			minHeight: "36px",
			flexDirection: "row",
			alignItems: "center"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".commentSendBig_img_box": {flexDirection: "row", flexWrap: "wrap"},
		".commentSendBig_img_item": {
			padding: "10px 10px 0 0",
			width: "25%",
			height: "auto",
			boxSizing: "border-box"
		},
		".commentSendBig_img_clean": {
			position: "absolute",
			zIndex: "999",
			top: "1px",
			right: "3px"
		}
	};
	apivm.define("y-attachments", YAttachments);

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				style:
					(this.props.style || "") +
					";font-size: " +
					(this.props.size || 16) +
					"px;flex-shrink:0;",
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: "yuanxingxuanzhongfill"
					: "danxuan_weixuanzhong",
				color: this.props.color
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var YCloudDisk = /*@__PURE__*/ (function(Component) {
		function YCloudDisk(props) {
			Component.call(this, props);
			this.data = {
				isOpenListMore: false
			};
		}

		if (Component) YCloudDisk.__proto__ = Component;
		YCloudDisk.prototype = Object.create(Component && Component.prototype);
		YCloudDisk.prototype.constructor = YCloudDisk;
		YCloudDisk.prototype.installed = function() {};
		YCloudDisk.prototype.isSelectValue = function(_item) {
			return G.getItemForKey(_item.id, this.props.dataSelect, "id");
		};
		YCloudDisk.prototype.itemTap = function(_item) {
			var this$1 = this;

			if (this.data.isOpenListMore) {
				return;
			}
			if (
				this.props.dataMore.isSelect &&
				(this.props.dataMore.selectType == "file"
					? _item.fileInfo.type != "folder"
					: true)
			) {
				var nItem = G.getItemForKey(_item.id, this.props.dataSelect, "id");
				if (nItem) {
					if (!nItem.notDel) {
						G.delItemForKey(nItem, this.props.dataSelect, "id");
					}
				} else {
					if (_item.dotSelect) {
						T.toast(_item.dotSelectToast);
						return;
					}
					setTimeout(function() {
						this$1.props.dataSelect.push(_item);
					}, 0);
				}
				setTimeout(function() {
					this$1.fire("changeDataSelect", {});
				}, 0);
			} else {
				setTimeout(function() {
					this$1.fire("openDetails", _item);
				}, 0);
			}
		};
		YCloudDisk.prototype.itemPress = function(e, _item) {
			this.openListMore(e, _item);
		};
		YCloudDisk.prototype.openListMore = function(e, _item) {
			var this$1 = this;

			G.stopBubble(e);
			if (this.props.dataMore.isSelect) {
				return;
			}
			if (_item.dotSelect) {
				T.toast(_item.dotSelectToast);
				return;
			}
			this.data.isOpenListMore = true;
			setTimeout(function() {
				this$1.data.isOpenListMore = false;
			}, 200);
			this.props.dataSelect.push(_item);
			this.props.dataMore.isSelect = true;
			setTimeout(function() {
				this$1.fire("changeSelect", {});
				this$1.fire("changeDataSelect", {});
			}, 0);
		};
		YCloudDisk.prototype.showDot = function(_item) {
			return (
				_item.isRedDot == 1 ||
				G.getItemForKey(_item.id, this.props.dataMore.listUnread || [])
			);
		};
		YCloudDisk.prototype.openleftMore = function(e, _item) {
			var this$1 = this;

			G.stopBubble(e);
			setTimeout(function() {
				this$1.fire("leftMore", _item);
			}, 0);
		};
		YCloudDisk.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: this.props.class || "", style: this.props.style || ""},
				this.props.dataMore.mode == "list"
					? apivm.h(
							"view",
							{class: "cloudDisk_list_box"},
							(Array.isArray(this.props.data)
								? this.props.data
								: Object.values(this.props.data)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										class: "cloudDisk_list_item",
										onClick: function() {
											return this$1.itemTap(item$1);
										},
										onLongpress: function(e) {
											return this$1.itemPress(e, item$1);
										}
									},
									apivm.h(
										"view",
										null,
										this$1.props.dataMore.hasMore &&
											apivm.h(
												"view",
												{
													style: "padding:10px;",
													onClick: function(e) {
														return this$1.openleftMore(e, item$1);
													}
												},
												apivm.h("a-iconfont", {
													name: "gengduo3",
													color: "#999",
													size: G.appFontSize + 10
												})
											)
									),
									apivm.h(
										"view",
										{
											style:
												"width:auto;height:auto;padding:4px;margin-right:15px;position: relative;"
										},
										apivm.h("a-iconfont", {
											name: item$1.fileInfo.name,
											color: item$1.fileInfo.color,
											size: G.appFontSize + 20
										}),
										this$1.showDot(item$1)
											? apivm.h(
													"view",
													{
														class: "cloudDisk_list_redDot",
														style:
															"" +
															G.loadConfigurationSize(-6) +
															(G.careMode ? "top:1px;right: 1px;" : "")
													},
													" "
											  )
											: null
									),
									apivm.h(
										"view",
										{style: "flex:1;"},
										apivm.h(
											"view",
											{style: "flex-direction:row;align-items: center;min-height:65px;"},
											apivm.h(
												"view",
												{style: "flex:1;"},
												apivm.h(
													"text",
													{
														style:
															G.loadConfiguration(1) + "color: #333;word-break:break-all;",
														class: "text_one" + (T.platform() == "app" ? "" : "2")
													},
													item$1.name
												),
												this$1.props.dataMore.type != "details"
													? apivm.h(
															"view",
															{
																style: "margin-top:4px;flex-direction:row; align-items: center;"
															},
															apivm.h(
																"view",
																null,
																item$1.firstMsg
																	? apivm.h(
																			"view",
																			{style: "flex-direction:row; align-items: center;"},
																			apivm.h(
																				"text",
																				{
																					style:
																						G.loadConfiguration(-4) +
																						"color: " +
																						(item$1.fristColor || "#999") +
																						";"
																				},
																				item$1.firstMsg
																			),
																			apivm.h("view", {
																				style:
																					"width:1px;height:" +
																					(G.appFontSize - 1) +
																					"px;background:#eee;margin:0 10px;"
																			})
																	  )
																	: null
															),
															apivm.h(
																"view",
																null,
																item$1.firstIcon &&
																	item$1.firstIcon.name &&
																	apivm.h("a-iconfont", {
																		style: "margin-right:6px;",
																		name: item$1.firstIcon.name,
																		color: item$1.firstIcon.color || "#999",
																		size: G.appFontSize
																	})
															),
															apivm.h(
																"text",
																{
																	style: G.loadConfiguration(-4) + "color: #999;flex-shrink: 0;"
																},
																dayjs$1(item$1.time).format("YYYY-MM-DD HH:mm")
															),
															apivm.h(
																"view",
																null,
																T.isArray(item$1.addMsg) && item$1.addMsg.length > 0
																	? apivm.h(
																			"view",
																			{style: "flex-direction:row; align-items: center;"},
																			(Array.isArray(item$1.addMsg)
																				? item$1.addMsg
																				: Object.values(item$1.addMsg)
																			).map(function(nItem, nIndex) {
																				return apivm.h(
																					"view",
																					{style: "flex-direction:row; align-items: center;"},
																					apivm.h("view", {
																						style:
																							"width:1px;height:" +
																							(G.appFontSize - 1) +
																							"px;background:#eee;margin:0 10px;"
																					}),
																					apivm.h(
																						"text",
																						{
																							style:
																								G.loadConfiguration(-4) +
																								"color: " +
																								(nItem.color || "#999") +
																								";"
																						},
																						nItem.text
																					)
																				);
																			})
																	  )
																	: item$1.addMsg
																	? apivm.h(
																			"view",
																			{style: "flex-direction:row; align-items: center;"},
																			apivm.h("view", {
																				style:
																					"width:1px;height:" +
																					(G.appFontSize - 1) +
																					"px;background:#eee;margin:0 10px;"
																			}),
																			apivm.h(
																				"text",
																				{
																					style:
																						G.loadConfiguration(-4) +
																						"color: " +
																						(item$1.addColor || "#999") +
																						";"
																				},
																				item$1.addMsg
																			)
																	  )
																	: null
															)
													  )
													: null
											),
											apivm.h(
												"view",
												{style: "width:auto;height:auto;"},
												this$1.props.dataMore.type == "details" ||
													(this$1.props.dataMore.isSelect &&
														this$1.props.dataMore.selectType == "file" &&
														item$1.fileInfo.type == "folder")
													? apivm.h(
															"view",
															{
																style:
																	"width:auto;height:auto;padding:8px;transform: rotate(-90deg);"
															},
															apivm.h("a-iconfont", {
																name: "xiangxia1",
																color: "#999999",
																size: G.appFontSize
															})
													  )
													: this$1.props.dataMore.isSelect
													? apivm.h("z-radio", {
															style: "padding:6px;",
															checked: this$1.isSelectValue(item$1),
															size: G.appFontSize + 4,
															color: this$1.isSelectValue(item$1) ? G.appTheme : "#999"
													  })
													: apivm.h(
															"view",
															{
																style: "width:auto;height:auto;padding:8px;margin: -8px 0;",
																onClick: function(e) {
																	return this$1.openListMore(e, item$1);
																}
															},
															apivm.h("a-iconfont", {
																name: "gengduo",
																color: "#999999",
																size: G.appFontSize
															})
													  )
											)
										),
										apivm.h(
											"view",
											{style: "width:100%;height:1px;"},
											apivm.h("a-divider", null)
										)
									)
								);
							})
					  )
					: this.props.dataMore.mode == "grid"
					? apivm.h(
							"view",
							{class: "cloudDisk_list_grid_box"},
							(Array.isArray(this.props.data)
								? this.props.data
								: Object.values(this.props.data)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										class: "cloudDisk_list_grid_item",
										onClick: function() {
											return this$1.itemTap(item$1);
										},
										onLongpress: function(e) {
											return this$1.itemPress(e, item$1);
										}
									},
									apivm.h(
										"view",
										{class: "cloudDisk_list_grid_warp"},
										apivm.h(
											"view",
											{
												style:
													"width:auto;height:auto;align-items: center;margin-bottom:10px;"
											},
											apivm.h(
												"view",
												{style: "position: relative;"},
												apivm.h("a-iconfont", {
													name: item$1.fileInfo.name,
													color: item$1.fileInfo.color,
													size: G.appFontSize + 30
												}),
												this$1.showDot(item$1)
													? apivm.h(
															"view",
															{
																class: "cloudDisk_list_redDot",
																style:
																	"" +
																	G.loadConfigurationSize(-6) +
																	(G.careMode ? "top:1px;right: 1px;" : "")
															},
															" "
													  )
													: null
											)
										),
										apivm.h(
											"text",
											{
												class: "cloudDisk_list_grid_name",
												style:
													G.loadConfiguration(-2) +
													"line-height:" +
													(G.appFontSize - 2) * 1.4 +
													"px;height:" +
													(G.appFontSize - 2) * 1.4 * 2 +
													"px;"
											},
											item$1.name
										),
										apivm.h(
											"view",
											{
												style: "width:auto;height:auto;align-items: center;margin-top:8px;"
											},
											this$1.props.dataMore.isSelect
												? apivm.h("z-radio", {
														style:
															"margin:-2px 0 -" + (T.platform() == "app" ? 2 : 3) + "px 0;",
														checked: this$1.isSelectValue(item$1),
														size: G.appFontSize + 4,
														color: this$1.isSelectValue(item$1) ? G.appTheme : "#999"
												  })
												: apivm.h(
														"view",
														{
															style: "width:auto;height:auto;padding:8px;margin: -8px 0;",
															onClick: function(e) {
																return this$1.openListMore(e, item$1);
															}
														},
														apivm.h("a-iconfont", {
															name: "gengduo",
															color: "#999999",
															size: G.appFontSize
														})
												  )
										)
									)
								);
							})
					  )
					: []
			);
		};

		return YCloudDisk;
	})(Component);
	YCloudDisk.css = {
		".cloudDisk_list_item": {
			flexDirection: "row",
			alignItems: "center",
			padding: "0px 15px",
			minHeight: "65px"
		},
		".cloudDisk_list_grid_box": {
			flexDirection: "row",
			flexWrap: "wrap",
			padding: "0 2px 16px 16px"
		},
		".cloudDisk_list_grid_item": {
			width: "33.33%",
			height: "auto",
			padding: "15px 14px 0 0"
		},
		".cloudDisk_list_grid_warp": {
			padding: "9px",
			borderRadius: "4px",
			border: "1px solid #EBEBEB"
		},
		".cloudDisk_list_grid_name": {
			color: "#333333",
			WebkitLineClamp: "2",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden",
			textOverflow: "ellipsis",
			whiteSpace: "normal !important",
			textAlign: "center"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".cloudDisk_list_redDot": {
			position: "absolute",
			zIndex: "999",
			top: "2px",
			right: "2px",
			background: "#f92323",
			borderRadius: "50%",
			whiteSpace: "nowrap",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("y-cloud-disk", YCloudDisk);

	var YCloudDiskAround = /*@__PURE__*/ (function(Component) {
		function YCloudDiskAround(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				listSelect: null,
				hasAddFolder: true,
				orderBys: {
					key: "updatedate",
					direction: 1 //0 顺序 1倒序
				},
				level: {key: "", defaultKey: "", data: [{key: "0", value: "我的文件"}]},

				listData: [
					// { id:"1", name:"XX文件夹", fileInfo:{"name":"folder-2-fill",color:"#F6C21C",type:"folder"}, time:"2020-02-06 12:00", },
				],
				dataMore: {
					mode: "list", //list列表 grid宫格
					isSelect: false, //是否选择中
					selectType: "file",
					type: "details",
					hasMore: false
				},

				addInput: {
					show: false,
					autofocus: true,
					key: "around_add_folder_callback",
					value: "新建文件夹",
					size: 24,
					weight: "bold",
					fileInfo: G.getFileInfo("folder"),
					input: "",
					placeholder: "新建文件夹"
				}
			};
			this.compute = {
				isShow: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.listData = [];
							this.data.listSelect = this.props.dataMore.listSelect || [];
							this.data.level.data[0].value =
								this.props.dataMore.key == "long_shared"
									? "共享文件"
									: this.props.dataMore.key == "chat_file"
									? "文件"
									: "我的文件";
							this.data.dataMore.hasMore = this.props.dataMore.key == "chat_file";
							if (
								this.props.dataMore.key == "long_choose" ||
								this.props.dataMore.key == "chat_file"
							) {
								this.data.dataMore.type = "select";
								this.data.dataMore.isSelect = true;
								// this.orderBys.key = "filemenu";
								// this.orderBys.direction = "0";
								this.data.hasAddFolder = false;
							}
							this.getData();
						} else {
							this.data.level.data = [this.data.level.data[0]];
						}
					}
				},
				titleName: function() {
					var name = "";
					switch (this.props.dataMore.key) {
						case "long_copy":
							name =
								"复制到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_move":
							name =
								"移动到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_shared":
							name =
								"共享到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_resave":
							name =
								"转存到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_choose":
							name = "选择云盘文件";
							break;
						case "chat_file":
							name = "聊天文件";
							break;
					}

					return name;
				}
			};
		}

		if (Component) YCloudDiskAround.__proto__ = Component;
		YCloudDiskAround.prototype = Object.create(Component && Component.prototype);
		YCloudDiskAround.prototype.constructor = YCloudDiskAround;
		YCloudDiskAround.prototype.installed = function() {};
		YCloudDiskAround.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		YCloudDiskAround.prototype.penetrate = function() {};
		YCloudDiskAround.prototype.itemClick = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.props.dataMore.toId = this$1.data.level.key;
				this$1.props.dataMore.toItem =
					this$1.data.level.data[this$1.data.level.data.length - 1];
				this$1.props.dataMore.listSelect = this$1.data.listSelect;
				this$1.fire("click", this$1.props.dataMore);
				this$1.closePage();
			}, 0);
		};
		YCloudDiskAround.prototype.openDetails = function(ref) {
			var detail = ref.detail;

			// console.log(JSON.stringify(detail));
			if (detail.fileInfo.type == "folder") {
				this.data.level.data.push({key: detail.id, value: detail.name});
			}
		};
		YCloudDiskAround.prototype.openLeftMore = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			var buttons = ["打开"];
			if (detail.createBy == G.uId || detail.createBy == G.userId) {
				buttons.push("删除");
			}
			T.actionSheet(
				{
					title: "提示",
					buttons: buttons,
					dotClose: true
				},
				function(ret, err) {
					if (ret.value == "打开") {
						openWin_filePreviewer({
							id: detail.fileInfoId,
							suffix: detail.fileInfo.type,
							fileSource: "5"
						});
					} else if (ret.value == "删除") {
						G.ajaxAlert(
							{
								msg: "确定删除吗?",
								url: myjs.appUrl() + "chatGroupFile/dels",
								param: {ids: [detail.groupFileId]}
							},
							this$1,
							function(ret) {
								G.delItemForKey(
									detail.groupFileId,
									this$1.data.listSelect,
									"groupFileId"
								);
								this$1.getData();
							}
						);
					}
				}
			);
		};
		YCloudDiskAround.prototype.getData = function(_type) {
			var this$1 = this;

			if (!this.data.show) {
				return;
			}
			var url =
				myjs.appUrl() +
				(this.props.dataMore.key == "long_shared" ? "pubsharefile" : "panfile") +
				"/list";
			var tableId =
				this.props.dataMore.key == "long_shared"
					? "id_pan_pubshare_file"
					: "id_pan_file_list";
			var postParam = {
				pageNo: 1,
				pageSize: 9999,
				keyword: "",
				tableId: tableId,
				query: {fileMenu: "1", fileStatus: "0"},
				orderBys: [
					{
						columnId: tableId + "_" + this.data.orderBys.key,
						isDesc: this.data.orderBys.direction
					}
				],
				wheres: [
					{
						columnId: tableId + "_parentid",
						queryType: "EQ",
						value: this.data.level.key || "0"
					}
				]
			};

			if (this.props.dataMore.key == "long_choose") {
				delete postParam.query;
			}
			if (this.props.dataMore.key == "chat_file") {
				url = myjs.appUrl() + "chatGroupFile/list";
				postParam = {
					pageNo: 1,
					pageSize: 9999,
					keyword: "",
					query: {chatGroupId: this.props.dataMore.id}
				};
			}
			T.ajax(
				{u: url, _this: this},
				"panfile" + "/menutree",
				function(ret, err) {
					T.hideProgress();
					var code = ret ? ret.code : "";
					var data = ret ? ret.data || [] : [];
					if (T.isArray(data) && data.length) {
						var nowList = [];
						data.forEach(function(_eItem, _eIndex, _eArr) {
							//item index 原数组对象
							var item = {};
							if (this$1.props.dataMore.key == "chat_file") {
								item.groupFileId = _eItem.id;
								_eItem = _eItem.fileInfo || {};
							}
							item.id = _eItem.id || ""; //id
							item.name = _eItem.fileName || _eItem.originalFileName || ""; //标题
							item.time = _eItem.updateDate || _eItem.createDate;
							item.fileInfoId = _eItem.fileInfoId || _eItem.id;
							item.createBy = _eItem.createBy;

							item.fileInfo = G.getFileInfo(_eItem.extName || "folder");
							if (
								this$1.props.dataMore.key == "long_choose" ||
								this$1.props.dataMore.key == "chat_file"
							) {
								nowList.push(item);
							} else {
								if (!G.getItemForKey(item.id, this$1.data.listSelect, "id")) {
									nowList.push(item);
								}
							}
						});
						if (!_type) {
							this$1.data.listData = nowList;
						} else {
							this$1.data.listData = this$1.data.listData.concat(nowList);
						}
					} else {
						this$1.data.listData = [];
					}
				},
				"列表",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		YCloudDiskAround.prototype.levelChange = function(ref) {
			var detail = ref.detail;

			var nLevel = [],
				lastItem = null;
			for (var i = 0; i < this.data.level.data.length; i++) {
				lastItem = this.data.level.data[i];
				nLevel.push(lastItem);
				if (lastItem.key == detail.key) {
					break;
				}
			}
			this.data.level.data = nLevel;
			this.getData(0);
		};
		YCloudDiskAround.prototype.addFolder = function() {
			var this$1 = this;

			this.data.addInput.input = "";
			this.data.addInput.show = true;
			setTimeout(function() {
				$("#" + this$1.data.addInput.key).focus();
			}, 150);
		};
		YCloudDiskAround.prototype.keyCallback = function(ref) {
			var detail = ref.detail;

			console.log(JSON.stringify(detail));
			switch (detail.key) {
				case "around_add_folder_callback":
					this.addFolderCallback();
					break;
			}
		};
		YCloudDiskAround.prototype.addFolderCallback = function() {
			var this$1 = this;

			var postParam = {
				parentId: this.data.level.key || "0",
				fileName: this.data.addInput.input || this.data.addInput.placeholder,
				pubshareName: this.data.addInput.input || this.data.addInput.placeholder
			};

			T.showProgress("新建中");
			T.ajax(
				{
					u:
						myjs.appUrl() +
						(this.props.dataMore.key == "long_shared" ? "pubsharefile" : "panfile") +
						"/addmenu",
					_this: this
				},
				"panfile" + "/addmenu",
				function(ret, err) {
					T.hideProgress();
					T.toast(ret ? ret.message : T.NET_ERR);
					if (ret && ret.code == 200) {
						this$1.getData(0);
					}
				},
				"新建文件夹",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		YCloudDiskAround.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "cloudDiskAround_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				apivm.h(
					"view",
					{
						class: "cloudDiskAround_warp",
						onClick: function() {
							return this$1.penetrate();
						}
					},
					apivm.h(
						"view",
						{class: "cloudDiskAround_header_warp"},
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_main"},
							apivm.h(
								"text",
								{
									style: G.loadConfiguration(1) + "color:#333",
									class: "cloudDiskAround_header_main_text"
								},
								this.titleName
							)
						),
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_left_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "cloudDiskAround_header_btn"
									},
									apivm.h(
										"text",
										{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"取消"
									)
								)
							)
						),
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_right_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick();
										},
										class: "cloudDiskAround_header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						)
					),
					apivm.h(
						"view",
						{style: "width:100%;height:1px;padding:0 16px;"},
						apivm.h("a-divider", null)
					),
					apivm.h(
						"view",
						{
							style:
								"flex-direction:row;align-items: center;padding:10px 16px 10px 10px;margin-top:10px;"
						},
						apivm.h(
							"view",
							{style: "flex:1;width:1px;"},
							apivm.h("z-tabs", {
								id: "tab_cloud_disk_around",
								style: "height:" + (G.appFontSize + 5) + "px;",
								size: -2,
								dataMore: this.data.level,
								onChange: this.levelChange,
								type: 4,
								bg: "" + G.appTheme
							})
						),
						apivm.h(
							"view",
							null,
							this.data.hasAddFolder
								? apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.addFolder();
											},
											style: "margin-left:10px;flex-direction:row;align-items: center;"
										},
										apivm.h("a-iconfont", {
											style: "margin-right:6px;",
											name: "folder-add-fill",
											color: "#F6931C",
											size: G.appFontSize + 4
										}),
										apivm.h(
											"text",
											{style: G.loadConfiguration(1) + "color:#333;"},
											"新建"
										)
								  )
								: null
						)
					),
					apivm.h(
						"view",
						{style: "width:100%;height:1px;padding:0 16px;"},
						apivm.h("a-divider", null)
					),
					apivm.h(
						"scroll-view",
						{style: "flex:1;height:1px;", "scroll-y": true},
						apivm.h("y-cloud-disk", {
							data: this.data.listData,
							dataMore: this.data.dataMore,
							dataSelect: this.data.listSelect,
							onOpenDetails: this.openDetails,
							onLeftMore: this.openLeftMore
						})
					)
				),

				apivm.h("z-actionSheet-input", {
					dataMore: this.data.addInput,
					onClick: this.keyCallback
				})
			);
		};

		return YCloudDiskAround;
	})(Component);
	YCloudDiskAround.css = {
		".cloudDiskAround_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".cloudDiskAround_warp": {
			flex: "1",
			height: "1px",
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".cloudDiskAround_header_warp": {
			height: "49px",
			flexDirection: "row",
			width: "100%",
			alignItems: "center"
		},
		".cloudDiskAround_header_btn": {
			height: "49px",
			width: "auto",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".cloudDiskAround_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".cloudDiskAround_header_main": {
			flex: "1",
			width: "1px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "0 49px"
		},
		".cloudDiskAround_header_main_text": {
			fontWeight: "600",
			maxWidth: "200px",
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".cloudDiskAround_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row"
		},
		".cloudDiskAround_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row-reverse"
		}
	};
	apivm.define("y-cloud-disk-around", YCloudDiskAround);

	var YCommentSendBig = /*@__PURE__*/ (function(Component) {
		function YCommentSendBig(props) {
			Component.call(this, props);
			this.data = {
				id: this.props.id || "comment_send_big_input",
				maxlength: this.props.maxlength || 300,
				isFocus: false,

				actionSheet: {
					//长条点击更多弹窗
					show: false,
					data: [
						{key: "cloudpan", value: "我的云盘", icon: ""},
						{key: "myfile", value: "本机文件", icon: ""}
					]
				},

				aroundPage: {
					//复制移动弹窗
					show: false,
					key: "long_choose", //long_move long_copy
					listSelect: [],
					toId: ""
				}
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore.big.show != this.show) {
						this.show = this.props.dataMore.big.show;
						if (this.show) {
							setTimeout(function() {
								this$1.textwarp();
							}, 30);
						} else {
							this.data.actionSheet.show = false;
						}
					}
				}
			};
		}

		if (Component) YCommentSendBig.__proto__ = Component;
		YCommentSendBig.prototype = Object.create(Component && Component.prototype);
		YCommentSendBig.prototype.constructor = YCommentSendBig;
		YCommentSendBig.prototype.installed = function() {};
		YCommentSendBig.prototype.closePage = function() {
			var this$1 = this;

			this.props.dataMore.big.show = false;
			setTimeout(function() {
				this$1.fire("close", {});
			}, 0);
		};
		YCommentSendBig.prototype.penetrate = function() {};
		YCommentSendBig.prototype.textwarp = function() {
			$("#" + this.data.id).focus();
		};
		YCommentSendBig.prototype.keyboardheightchange = function(e) {};
		YCommentSendBig.prototype.input = function(e) {
			var this$1 = this;

			this.data.isFocus = true;
			this.props.dataMore.input = e.detail.value;
			setTimeout(function() {
				this$1.fire("input", {value: this$1.props.dataMore.input});
			}, 0);
		};
		YCommentSendBig.prototype.focus = function(e) {
			this.data.isFocus = true;
		};
		YCommentSendBig.prototype.blur = function(e) {
			this.data.isFocus = false;
		};
		YCommentSendBig.prototype.confirm = function(e) {};
		YCommentSendBig.prototype.send = function() {
			var this$1 = this;

			if (!this.props.dataMore.input.length && !this.props.dataMore.files.length) {
				T.toast(this.props.dataMore.hintToast);
				return;
			}
			setTimeout(function() {
				this$1.fire("send", {
					value: this$1.props.dataMore.input,
					files: this$1.props.dataMore.files
				});
			}, 0);
		};
		YCommentSendBig.prototype.chooseFile = function(_type) {
			var this$1 = this;

			var callback = function(ret) {
				if (ret.otherInfo) {
					this$1.props.dataMore.files.push(ret.otherInfo);
				} else {
					T.toast(ret.error);
				}
			};
			if (_type == 2) {
				//文件
				this.data.actionSheet.show = true;
			} else {
				//相机相册
				T.getPicture(
					{sourceType: _type == 1 ? "library" : "camera", destinationType: "url"},
					function(ret, err) {
						var dataUrl = ret ? ret.data || ret.base64Data : "";
						if (dataUrl) {
							var _item = {showToast: true, url: dataUrl};
							G.uploadFile(this$1, _item, callback);
						}
					}
				);
			}
		};
		YCommentSendBig.prototype.keyCallback = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			switch (detail.key) {
				case "myfile":
					G.chooseFile(this, {showToast: true}, function(ret) {
						if (ret.otherInfo) {
							this$1.props.dataMore.files.push(ret.otherInfo);
						} else {
							T.toast(ret.error);
						}
					});
					break;
				case "cloudpan":
					this.data.aroundPage.listSelect = [];
					this.data.aroundPage.show = true;
					break;
			}
		};
		YCommentSendBig.prototype.aroundCallback = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			console.log(JSON.stringify(detail));
			this.data.aroundPage.listSelect.forEach(function(_eItem, _eIndex, _eArr) {
				if (
					!G.getItemForKey(_eItem.fileInfoId, this$1.props.dataMore.files, "id")
				) {
					T.ajax(
						{u: myjs.appUrl() + "file/info/" + _eItem.fileInfoId, _this: this$1},
						"file/info" + _eItem.fileInfoId,
						function(ret, err) {
							if (ret && ret.code == 200 && ret.data) {
								ret.data.dotSystemDel = true; //云盘增加的文件不能真删除
								this$1.props.dataMore.files.push(ret.data);
							}
						},
						"附件详情",
						"get"
					);
				}
			});
		};
		YCommentSendBig.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "commentSendBig_box",
					style: "display:" + (this.props.dataMore.big.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "flex:1;"
				}),
				apivm.h(
					"view",
					{
						onClick: function() {
							return this$1.penetrate();
						},
						class: "commentSendBig_warp",
						style:
							"padding-bottom:" +
							G.footerBottom(!this.data.isFocus && this.props.bottom) +
							"px;"
					},
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.textwarp();
							},
							class: "commentSendBig_warp_text_box"
						},
						apivm.h("textarea", {
							id: this.data.id,
							style: "" + G.loadConfiguration(),
							class: "commentSendBig_warp_text",
							placeholder: this.props.dataMore.replyName || this.props.dataMore.hint,
							"auto-height": true,
							"placeholder-style": "color:#ccc;",
							maxlength: this.data.maxlength,
							value: this.props.dataMore.input,
							"confirm-type": "return",
							onKeyboardheightchange: this.keyboardheightchange,
							onInput: this.input,
							onFocus: this.focus,
							onBlur: this.blur,
							onConfirm: this.confirm
						})
					),
					apivm.h("y-attachments", {
						style: "padding: 10px 16px;",
						type: "2",
						data: this.props.dataMore.files
					}),
					apivm.h(
						"view",
						{class: "commentSendBig_warp_add_box"},
						apivm.h(
							"view",
							null,
							T.platform() != "web"
								? apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.chooseFile(0);
											}
										},
										apivm.h("a-iconfont", {
											style: "margin-right:20px;",
											name: "xiangji",
											color: "#333",
											size: G.appFontSize + 12
										})
								  )
								: null
						),
						apivm.h(
							"view",
							null,
							(G.userId
							? true
							: false)
								? apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.chooseFile(1);
											}
										},
										apivm.h("a-iconfont", {
											style: "margin-right:20px;",
											name: "tupian",
											color: "#333",
											size: G.appFontSize + 12
										})
								  )
								: null
						),
						apivm.h(
							"view",
							null,
							(G.userId
							? true
							: false)
								? apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.chooseFile(2);
											}
										},
										apivm.h("a-iconfont", {
											name: "suoyouwenjian",
											color: "#333",
											size: G.appFontSize + 12
										})
								  )
								: null
						),
						apivm.h("view", {style: "flex:1;"}),
						apivm.h(
							"text",
							{
								style: "" + G.loadConfiguration(-2),
								class: "commentSendBig_warp_maxnum"
							},
							this.props.dataMore.input.length,
							"/",
							this.props.maxlength
						),
						apivm.h("z-button", {
							onClick: function() {
								return this$1.send();
							},
							text: this.props.sendtext || "发送",
							size: G.appFontSize,
							fontstyle: "" + G.loadConfiguration(),
							style: "flex-shrink:0;padding:3px 12px;margin-left:20px;",
							color: G.appTheme
						})
					)
				),

				apivm.h("z-actionSheet", {
					dataMore: this.data.actionSheet,
					size: -2,
					cancel: true,
					data: this.data.actionSheet.data,
					onClick: this.keyCallback
				}),

				apivm.h("y-cloud-disk-around", {
					dataMore: this.data.aroundPage,
					onClick: this.aroundCallback
				})
			);
		};

		return YCommentSendBig;
	})(Component);
	YCommentSendBig.css = {
		".commentSendBig_box": {
			backgroundColor: "rgba(0,0,0,.2)",
			width: "100%",
			height: "100%",
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".commentSendBig_warp": {background: "#FFF", width: "100%", height: "auto"},
		".commentSendBig_warp_text_box": {
			padding: "16px 16px 0",
			minHeight: "110px",
			maxHeight: "250px"
		},
		".commentSendBig_warp_text": {
			width: "100%",
			height: "110px",
			borderRadius: "0",
			padding: "0",
			borderColor: "transparent"
		},
		".commentSendBig_warp_text::placeholder": {color: "#ccc"},
		".commentSendBig_warp_add_box": {
			width: "100%",
			height: "45px",
			flexDirection: "row",
			alignItems: "center",
			padding: "0 16px"
		},
		".commentSendBig_warp_maxnum": {color: "#999999", paddingTop: "8px"}
	};
	apivm.define("y-comment-send-big", YCommentSendBig);

	var YCommentSend = /*@__PURE__*/ (function(Component) {
		function YCommentSend(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) YCommentSend.__proto__ = Component;
		YCommentSend.prototype = Object.create(Component && Component.prototype);
		YCommentSend.prototype.constructor = YCommentSend;
		YCommentSend.prototype.installed = function() {};
		YCommentSend.prototype.hintclick = function() {
			var this$1 = this;

			if (this.props.dataMore.send.disabled) {
				T.toast(this.props.dataMore.hint);
				return;
			}
			setTimeout(function() {
				this$1.fire("hintclick", {});
			}, 0);
		};
		YCommentSend.prototype.cckComment = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("cckComment", {});
			}, 0);
		};
		YCommentSend.prototype.cckLike = function() {
			var this$1 = this;

			//code-35 立法征询独特业务
			if (this.props.dataMore.code == "35") {
				setTimeout(function() {
					this$1.fire("cckLike", {});
				}, 0);
				return;
			}
			if (this.props.dataMore.likeIs) {
				if (this.props.dataMore.likeNum > 0) {
					this.props.dataMore.likeNum--;
				}
			} else {
				this.props.dataMore.likeNum++;
			}
			this.props.dataMore.likeIs = !this.props.dataMore.likeIs;
			setTimeout(function() {
				this$1.fire("cckLike", {});
			}, 0);
		};
		YCommentSend.prototype.cckunLike = function() {
			var this$1 = this;

			//反对方法 回调
			setTimeout(function() {
				this$1.fire("cckunLike", {});
			}, 0);
			return;
		};
		YCommentSend.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "commentSend_box",
					style:
						"display:" +
						(this.props.dataMore.send.show && !G.isAppReview ? "flex" : "none") +
						";padding-bottom:" +
						(G.footerBottom(this.props.bottom) + 6) +
						"px;"
				},
				apivm.h(
					"view",
					{onClick: this.hintclick, class: "commentSend_hint_box"},
					apivm.h(
						"text",
						{
							style:
								G.loadConfiguration() +
								"color:" +
								(this.props.dataMore.input ? "#333" : "#999") +
								";",
							class: "" + (T.platform() == "app" ? "text_one" : "text_one2")
						},
						this.props.dataMore.input || this.props.dataMore.hint
					)
				),
				(!this.props.commentDot || !this.props.likeDot || this.props.unlikeDot) &&
					apivm.h(
						"view",
						{class: "commentSend_btn_box"},
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.cckComment();
								},
								style:
									"display:" +
									(!this.props.commentDot && !this.props.dataMore.onlyMe
										? "flex"
										: "none") +
									";",
								class: "commentSend_btn_item"
							},
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(-2) + "color:" + "#666666" + ";margin-right:5px;"
								},
								this.props.dataMore.commentNum || 0
							),
							apivm.h("a-iconfont", {
								name: "pinglun",
								color: "#666666",
								size: G.appFontSize + 4
							})
						),
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.cckLike();
								},
								style: "display:" + (!this.props.likeDot ? "flex" : "none") + ";",
								class: "commentSend_btn_item"
							},
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(-2) +
										"color:" +
										(this.props.dataMore.likeIs ? "#F6931C" : "#666666") +
										";margin-right:5px;"
								},
								this.props.dataMore.likeNum || 0
							),
							apivm.h("a-iconfont", {
								name: this.props.dataMore.likeIs ? "like-fill" : "like",
								color: this.props.dataMore.likeIs ? "#F6931C" : "#666666",
								size: G.appFontSize + 4
							})
						),
						apivm.h(
							"view",
							{
								style: "display:" + (this.props.unlikeDot ? "flex" : "none") + ";",
								class: "commentSend_btn_item",
								onClick: function() {
									return this$1.cckunLike();
								}
							},
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(-2) +
										"color:" +
										(this.props.dataMore.unlikeIs ? "#F6931C" : "#666666") +
										";margin-right:5px;"
								},
								this.props.dataMore.unlikeNum || 0
							),
							apivm.h("a-iconfont", {
								name: this.props.dataMore.unlikeIs ? "unlike-fill" : "unlike",
								color: this.props.dataMore.unlikeIs ? "#F6931C" : "#666666",
								size: G.appFontSize + 4
							})
						)
					)
			);
		};

		return YCommentSend;
	})(Component);
	YCommentSend.css = {
		".commentSend_box": {
			width: "100%",
			minHeight: "44px",
			background: "#FFF",
			flexDirection: "row",
			alignItems: "center",
			borderTop: "1px solid rgba(153,153,153,0.2)",
			padding: "6px 16px"
		},
		".commentSend_hint_box": {
			width: "1",
			flex: "1",
			height: "32px",
			padding: "0px 15px",
			justifyContent: "center",
			alignItems: "flex-start",
			backgroundColor: "#F4F5F7",
			borderRadius: "2px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".commentSend_btn_box": {
			marginLeft: "10px",
			flexDirection: "row",
			alignItems: "center",
			marginRight: "-10px"
		},
		".commentSend_btn_item": {
			padding: "5px 10px 5px 10px",
			flexDirection: "row",
			alignItems: "center"
		}
	};
	apivm.define("y-comment-send", YCommentSend);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.installed = function() {};
		ZAvatar.prototype.imgError = function(e) {};
		ZAvatar.prototype.imgLoad = function(e) {};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{class: "class||''", style: this.props.style || ""},
				apivm.h("z-image", {
					proportionMin: "0.9",
					proportionMax: "1.1",
					class: "z_avatar_img",
					thumbnail: true,
					mode: this.props.mode || "aspectFill",
					style: "border-radius: " + (this.props.radius || "50%") + ";",
					src: G.showImg(
						(T.isObject(this.props.data)
						? this.props.data.url ||
						  this.props.data.headImg ||
						  this.props.data.senderHeadImg
						: this.props.data)
							? this.props.data
							: myjs.appUrl() + "img/default_user_head.jpg"
					),
					onError: this.imgError,
					onLoad: this.imgLoad
				})
			);
		};

		return ZAvatar;
	})(Component);
	ZAvatar.css = {".z_avatar_img": {width: "100%", height: "100%"}};
	apivm.define("z-avatar", ZAvatar);

	var YComment = /*@__PURE__*/ (function(Component) {
		function YComment(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) YComment.__proto__ = Component;
		YComment.prototype = Object.create(Component && Component.prototype);
		YComment.prototype.constructor = YComment;
		YComment.prototype.installed = function() {};
		YComment.prototype.hasExpand = function() {
			return G.getItemForKey(this.props.baseid, this.props.dataMore.listExpand);
		};
		YComment.prototype.openExpand = function(e) {
			if (G.getItemForKey(this.props.baseid, this.props.dataMore.listExpand)) {
				G.delItemForKey(this.props.baseid, this.props.dataMore.listExpand);
			} else {
				this.props.dataMore.listExpand.push(this.props.baseid);
			}
			this.scrollTo({
				detail: {view: "comment_" + this.props.baseid, animated: true}
			});
			G.stopBubble(e);
		};
		YComment.prototype.click = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("click", this$1.props.data);
			}, 0);
		};
		YComment.prototype.cckLike = function(e, _item) {
			var this$1 = this;

			if (_item.likeIs) {
				if (_item.likeNum > 0) {
					_item.likeNum--;
				}
			} else {
				_item.likeNum++;
			}
			_item.likeIs = !_item.likeIs;
			setTimeout(function() {
				this$1.fire("cckLike", {
					likeIs: _item.likeIs,
					code: "comment",
					id: _item.id
				});
			}, 0);
			G.stopBubble(e);
		};
		YComment.prototype.cckReplyBig = function(e, _item) {
			this.cckReply({detail: _item});
			G.stopBubble(e);
		};
		YComment.prototype.cckReply = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			if (this.props.dataMore.send.disabled) {
				T.toast(this.props.dataMore.hint);
				return;
			}
			setTimeout(function() {
				this$1.fire("reply", detail);
			}, 0);
		};
		YComment.prototype.scrollTo = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			setTimeout(function() {
				this$1.fire("scrollTo", detail);
			}, 0);
		};
		YComment.prototype.getTagColor = function(_item) {
			if (
				_item == "我" ||
				_item == "我的单位" ||
				_item == "单位" ||
				_item == "立法联系点"
			) {
				return G.appTheme;
			} else if (_item == "群众") {
				return "#666666";
			} else if (_item == "普通用户") {
				return "#95d475";
			} else if (_item == "人大代表") {
				return "#e6a23c";
			} else {
				return "#F6931C";
			}
		};
		YComment.prototype.itemClick = function(e, _item, _index) {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("itemClick", _item);
			}, 0);
			G.stopBubble(e);
		};
		YComment.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{id: this.props.id, style: {display: !G.isAppReview ? "flex" : "none"}},
				apivm.h(
					"view",
					{style: "" + (this.props.boxstyle || "border-top: 10px solid #F8F8F8;")},
					!this.props.ischildren &&
						!this.props.dataMore.dotHint &&
						apivm.h(
							"view",
							{class: "comment_hint_box"},
							apivm.h("view", {
								class: "comment_hint_line",
								style:
									G.loadConfigurationSize(-1, "h") + "background:" + G.appTheme + ";"
							}),
							apivm.h(
								"text",
								{class: "comment_hint_text", style: "" + G.loadConfiguration(1)},
								this.props.hint || "全部评论",
								this.props.total ? "(" + this.props.total + ")" : ""
							)
						),
					(Array.isArray(this.props.data)
						? this.props.data
						: Object.values(this.props.data)
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							null,
							apivm.h(
								"view",
								{
									id: "comment_" + item$1.id,
									style:
										"display:" +
										(!this$1.props.ischildren
											? "flex"
											: this$1.hasExpand(item$1)
											? "flex"
											: index$1 < 1
											? "flex"
											: "none") +
										";padding: 0 " +
										(this$1.props.ischildren ? "10" : "16") +
										"px;"
								},
								apivm.h(
									"view",
									{
										class: "comment_item_warp",
										style:
											"width: auto;padding: " +
											(this$1.props.ischildren ? "10" : "15") +
											"px 0;" +
											(this$1.props.ischildren ? "" : "border-bottom: 1px solid #eee;"),
										onClick: function(e) {
											return this$1.itemClick(e, item$1, index$1);
										}
									},
									apivm.h(
										"view",
										{style: "margin-right:7px;width: auto;"},
										apivm.h("z-avatar", {
											style: G.loadConfigurationSize(14),
											key: item$1.refresh,
											data: item$1
										})
									),
									apivm.h(
										"view",
										{style: "flex:1;"},
										apivm.h(
											"view",
											{class: "comment_item_top"},
											apivm.h(
												"view",
												{
													style:
														"flex:1;width:1px;flex-direction:row; align-items: flex-start;flex-wrap: wrap;"
												},
												apivm.h(
													"text",
													{class: "comment_item_name", style: "" + G.loadConfiguration()},
													item$1.name
												),
												item$1.replyName
													? [
															apivm.h("a-iconfont", {
																style: "margin:0 10px;transform: rotate(180deg);",
																name: "xiangzuo",
																color: "#999999",
																size: G.appFontSize - 6
															}),
															apivm.h(
																"text",
																{class: "comment_item_name", style: "" + G.loadConfiguration()},
																item$1.replyName
															)
													  ]
													: [
															T.isArray(item$1.userRoles) &&
																item$1.userRoles.length > 0 &&
																apivm.h(
																	"view",
																	{
																		style:
																			"flex-direction:row; align-items: center;flex-shrink: 0;"
																	},
																	(Array.isArray(item$1.userRoles)
																		? item$1.userRoles
																		: Object.values(item$1.userRoles)
																	).map(function(item$1, index$1) {
																		return apivm.h(
																			"view",
																			{style: "margin-left:10px;flex-shrink: 0;"},
																			apivm.h("z-tag", {
																				style: "" + G.loadConfiguration(-2),
																				color: this$1.getTagColor(item$1),
																				text: item$1
																			})
																		);
																	})
																),
															item$1.userArea &&
																apivm.h(
																	"view",
																	{style: "margin:0 10px;flex-shrink: 0;"},
																	apivm.h("z-tag", {
																		style: "" + G.loadConfiguration(-2),
																		color: "#666",
																		text:
																			item$1.userArea.length > 8
																				? item$1.userArea.substring(0, 8) + "..."
																				: item$1.userArea
																	})
																)
													  ]
											),
											T.isParameters(item$1.checkedStatus) && item$1.checkedStatus != 1
												? apivm.h(
														"view",
														{style: "width: auto;flex-direction:row;align-items: center;"},
														apivm.h(
															"text",
															{
																style:
																	G.loadConfiguration(-2) +
																	"color:" +
																	(item$1.checkedStatus != 2 ? "#e6a23c" : "#999") +
																	";"
															},
															item$1.checkedStatus != 2 ? "审核中" : "未通过"
														)
												  )
												: !this$1.props.ischildren &&
														!this$1.props.dataMore.dotOption && [
															apivm.h(
																"view",
																{
																	style: "width: auto;flex-direction:row;align-items: center;",
																	onClick: function(e) {
																		return this$1.cckLike(e, item$1);
																	}
																},
																apivm.h(
																	"text",
																	{
																		style:
																			G.loadConfiguration(-2) +
																			"color:" +
																			(item$1.likeIs ? "#F6931C" : "#666666") +
																			";margin-right:5px;"
																	},
																	item$1.likeNum
																),
																apivm.h("a-iconfont", {
																	name: item$1.likeIs ? "like-fill" : "like",
																	color: item$1.likeIs ? "#F6931C" : "#666666",
																	size: G.appFontSize + 3
																})
															)
														]
										),
										apivm.h(
											"view",
											null,
											item$1.content
												? apivm.h(
														"text",
														{
															style: "" + G.loadConfiguration(1),
															class: "comment_item_content"
														},
														item$1.content
												  )
												: null
										),
										apivm.h("y-attachments", {
											style: "margin-top: 10px;",
											data: item$1.attachments
										}),
										apivm.h(
											"view",
											{class: "comment_item_add"},
											apivm.h(
												"text",
												{style: G.loadConfiguration(-2) + "color:#999;"},
												dayjs$1(item$1.time).format("YYYY-MM-DD HH:mm")
											),
											!this$1.props.dataMore.dotOption &&
												apivm.h(
													"text",
													{
														style: G.loadConfiguration(-2) + "margin-left:20px;color:#333;",
														onClick: function(e) {
															return this$1.cckReplyBig(e, item$1);
														}
													},
													item$1.createBy == G.userId || item$1.createBy == G.uId
														? "删除"
														: "回复"
												),
											apivm.h(
												"view",
												null,
												this$1.props.dataMore.dotDel &&
													apivm.h(
														"text",
														{
															style: G.loadConfiguration(-2) + "margin-left:20px;color:#333;",
															onClick: function(e) {
																return this$1.cckReplyBig(e, item$1);
															}
														},
														item$1.createBy == G.userId || item$1.createBy == G.uId
															? "删除"
															: ""
													)
											)
										),
										apivm.h(
											"view",
											null,
											T.isParameters(item$1.replyList) &&
												item$1.replyList.length > 0 &&
												apivm.h("y-comment", {
													baseid: item$1.id,
													dataMore: this$1.props.dataMore,
													data: item$1.replyList,
													ischildren: true,
													onScrollTo: this$1.scrollTo,
													onReply: this$1.cckReply,
													boxstyle: "padding-top:0.1rem;background: #F8F9FA;margin-top:10px;"
												})
										),
										apivm.h(
											"view",
											null,
											this$1.props.ischildren &&
												this$1.props.data.length > 1 &&
												(this$1.hasExpand(item$1)
													? index$1 == this$1.props.data.length - 1
													: true) &&
												apivm.h(
													"view",
													{
														style: "flex-direction:row; align-items: center;margin-top:10px;",
														onClick: function(e) {
															return this$1.openExpand(e);
														}
													},
													apivm.h(
														"text",
														{style: G.loadConfiguration(-2) + "color:#666;margin-right:5px;"},
														"—— ",
														this$1.hasExpand(item$1)
															? "收起"
															: "展开" + (this$1.props.data.length - 1) + "条回复"
													),
													apivm.h("a-iconfont", {
														style:
															"transform: rotate(" +
															(this$1.hasExpand(item$1) ? "180" : "0") +
															"deg);",
														name: "xiangxia1",
														color: "#666666",
														size: G.appFontSize + 3
													})
												)
										)
									)
								)
							)
						);
					})
				)
			);
		};

		return YComment;
	})(Component);
	YComment.css = {
		".comment_hint_box": {
			padding: "15px 15px 0",
			flexDirection: "row",
			alignItems: "center"
		},
		".comment_hint_line": {width: "3px", borderRadius: "10px"},
		".comment_hint_text": {
			color: "#333333",
			fontWeight: "600",
			marginLeft: "5px",
			flex: "1"
		},
		".comment_item_warp": {flexDirection: "row"},
		".comment_item_top": {flexDirection: "row", alignItems: "flex-start"},
		".comment_item_name": {fontWeight: "600", color: "#333333"},
		".comment_item_content": {
			color: "#333333",
			marginTop: "5px",
			wordBreak: "break-all"
		},
		".comment_item_add": {
			flexDirection: "row",
			alignItems: "center",
			marginTop: "10px"
		}
	};
	apivm.define("y-comment", YComment);

	var YSuspendedBtns = /*@__PURE__*/ (function(Component) {
		function YSuspendedBtns(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) YSuspendedBtns.__proto__ = Component;
		YSuspendedBtns.prototype = Object.create(Component && Component.prototype);
		YSuspendedBtns.prototype.constructor = YSuspendedBtns;
		YSuspendedBtns.prototype.installed = function() {};
		YSuspendedBtns.prototype.itemclick = function(_item) {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("click", _item);
			}, 0);
		};
		YSuspendedBtns.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "suspendedBtn_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";bottom:" +
						(T.safeArea().bottom + 128) +
						"px;"
				},
				apivm.h(
					"view",
					{style: "flex-direction:column-reverse;"},
					this.props.data.map(function(item, index) {
						return [
							(T.isParameters(item.show) ? item.show : true) &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemclick(item);
										},
										class: "suspendedBtn_item",
										style:
											"margin-bottom:" +
											(index ? 10 : 0) +
											"px;" +
											G.loadConfigurationSize(item.bgSize || 28) +
											"background: " +
											(item.bg == "appTheme" ? G.appTheme : item.bg || G.appTheme) +
											";"
									},
									item.type == "img"
										? apivm.h("image", {
												src: myjs.shareAddress(1) + "img/" + item.src + ".png",
												style: "" + (item.style || "width:20px;height: 20px;"),
												mode: "aspectFill",
												alt: ""
										  })
										: item.type == "text"
										? apivm.h(
												"text",
												{
													style:
														G.loadConfiguration(-2) +
														"color:" +
														(item.color == "appTheme" ? G.appTheme : item.color || "#fff") +
														";"
												},
												item.value
										  )
										: apivm.h("a-iconfont", {
												style:
													"font-weight:" + (item.weight || "400") + ";" + (item.style || ""),
												name: item.src,
												color: item.color == "appTheme" ? G.appTheme : item.color || "#fff",
												size:
													G.appFontSize + (T.isParameters(item.size) ? Number(item.size) : 4)
										  })
								)
						];
					})
				)
			);
		};

		return YSuspendedBtns;
	})(Component);
	YSuspendedBtns.css = {
		".suspendedBtn_box": {position: "absolute", zIndex: "999", right: "16px"},
		".suspendedBtn_item": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center",
			boxShadow: "0px 4px 12px 1px rgba(24,64,118,0.15)",
			borderTopLeftRadius: "54px",
			borderTopRightRadius: "54px",
			borderBottomRightRadius: "54px",
			borderBottomLeftRadius: "54px"
		}
	};
	apivm.define("y-suspended-btns", YSuspendedBtns);

	var ImagePreviewer = /*@__PURE__*/ (function(Component) {
		function ImagePreviewer(props) {
			Component.call(this, props);
			this.data = {
				index: 1,
				activeIndex: 0,
				indicator: false,
				statusBarStyle: ""
			};
		}

		if (Component) ImagePreviewer.__proto__ = Component;
		ImagePreviewer.prototype = Object.create(Component && Component.prototype);
		ImagePreviewer.prototype.constructor = ImagePreviewer;
		ImagePreviewer.prototype.current = function() {
			return T.platform() == "web"
				? this.props.activeIndex
				: this.data.activeIndex;
		};
		ImagePreviewer.prototype.closePre = function() {
			this.close();
		};
		ImagePreviewer.prototype.close = function() {
			G.imagePreviewer.show = false;
			T.sendEvent("updatePage");
		};
		ImagePreviewer.prototype.installed = function() {
			// 修复顶部状态栏样式
			if (this.props.type == 1) {
				// api.setStatusBarStyle({ style: "dark" });
				this.data.indicator = true;
			} else if (this.props.type == 2) {
				// api.setStatusBarStyle({ style: "white" });
				this.data.indicator = false;
			}
			this.data.activeIndex = this.props.activeIndex;
			// 滑动到指定图片显示
			if (this.props.activeIndex) {
				this.data.index = parseInt(this.props.activeIndex) + 1;
			}
		};
		ImagePreviewer.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "image-previewer-page"},
				apivm.h(
					"view",
					{style: "height:1px;flex:1;justify-content: center;"},
					apivm.h(
						"view",
						{style: "height:1px;flex:1;justify-content: center;"},
						apivm.h(
							"swiper",
							{
								onClick: function() {
									return this$1.closePre();
								},
								class: "image-previewer-swiper",
								circular: true,
								current: this.current(),
								"indicator-dots": "true",
								"indicator-color": "#737373",
								"indicator-active-color": "#ffffff"
							},
							(Array.isArray(this.props.imgs)
								? this.props.imgs
								: Object.values(this.props.imgs)
							).map(function(item$1, index$1) {
								return apivm.h(
									"swiper-item",
									{
										onClick: function() {
											return this$1.closePre();
										},
										style: "height:100%;"
									},
									apivm.h("image", {
										class: "image-previewer-img",
										src: G.showImg(item$1),
										thumbnail: "false",
										mode: "aspectFit"
									})
								);
							})
						)
					)
				)
			);
		};

		return ImagePreviewer;
	})(Component);
	ImagePreviewer.css = {
		".image-previewer-page": {height: "100%", background: "#000"},
		".image-previewer-swiper": {height: "100%"},
		".image-previewer-img": {height: "100%", width: "100%"},
		".image-previewer-header": {
			display: "flex",
			flexDirection: "row",
			alignItems: "center",
			flexWrap: "nowrap",
			padding: "0 5px"
		},
		".image-previewer-back": {
			width: "30px",
			height: "45px",
			backgroundRepeat: "no-repeat",
			backgroundPosition: "center",
			backgroundSize: "20px"
		},
		".image-previewer-close": {width: "30px", height: "45px"},
		".image-previewer-title": {
			flex: "1",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexWrap: "nowrap",
			fontSize: "18px",
			textAlign: "center",
			textOverflow: "ellipsis",
			overflow: "hidden",
			whiteSpace: "nowrap",
			color: "#fff",
			height: "45px",
			lineHeight: "45px"
		},
		".image-previewer-placeholder": {
			width: "30px",
			height: "45px",
			marginRight: "5px"
		},
		".image-previewer-right": {width: "30px", height: "45px"}
	};
	apivm.define("image-previewer", ImagePreviewer);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				showPage: false,
				initFrist: true, //首次初始化
				viewappearFrist: true, //是否首次进入页面
				initFrequency: false,
				pageParam: {},
				pageType: ""
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore) {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							if (this.data.show) {
								this.baseInit();
								setTimeout(function() {
									this$1.data.showPage = true;
								}, 80);
								console.log("base-page-param：" + JSON.stringify(this.data.pageParam));
							} else {
								this.data.showPage = false;
								if (this.data.pageParam.paramSaveKey) {
									T.removePrefs(this.data.pageParam.paramSaveKey);
								}
								this.fire("baseclose", null);
							}
						}
						//监听刷新事件
						if (this.props.dataMore.pageRefresh == 1) {
							this.props.dataMore.pageRefresh = 0;
							this.pageRefresh();
						}
					}
				}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			if (!this.props.dataMore) {
				this.baseInit();
				console.log("base-page-param：" + JSON.stringify(this.data.pageParam)); //没有dataMore 才是新页面
				// T.addEventListener('viewappear', (ret, err)=> {
				// 	// console.log("我收到了返回事件"+JSON.stringify(T.pageParam(this)));
				// 	if(this.viewappearFrist){
				// 		this.viewappearFrist = false;
				// 		return;
				// 	}
				// 	this.pageRefresh();
				// });
				T.addEventListener("updatePage", function(ret, err) {
					this$1.update();
				});
			}
		};
		YBasePage.prototype.pageRefresh = function() {
			var this$1 = this;
			setTimeout(function() {
				this$1.fire("pageRefresh");
			}, 0);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			//组件内 刚开始会调用多次 这里判断一下 300ms只返回一次
			if (this.data.initFrequency) {
				return;
			}
			this.data.initFrequency = true;
			setTimeout(function() {
				this$1.data.initFrequency = false;
			}, 300);
			this.data.pageParam = T.pageParam(this.props._this);
			if (this.data.pageParam.token) {
				T.setPrefs("sys_token", decodeURIComponent(this.data.pageParam.token));
				if (!T.getPrefs("sys_Mobile")) {
					getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
				}
			}
			if (this.data.pageParam.areaId) {
				T.setPrefs("sys_aresId", this.data.pageParam.areaId);
			}
			this.init();
			setTimeout(
				function() {
					this$1.fire("init", {first: this$1.data.initFrist});
					this$1.data.initFrist = false;
				},
				T.systemType() == "android" && !this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.init = function() {
			this.data.pageType = this.data.pageParam.pageType || "page";
		};
		YBasePage.prototype.close = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("close", null);
			}, 0);
		};
		YBasePage.prototype.penetrate = function() {};
		YBasePage.prototype.alertCallback = function(_type) {
			if (T.isNumber(_type)) {
				T.sendEvent("base_alert_callback", {buttonIndex: _type});
			} else {
				_type.detail.buttonIndex = 1;
				T.sendEvent("base_alert_callback", _type.detail);
			}
		};
		YBasePage.prototype.areaCallback = function(e) {
			T.sendEvent("base_areas_callback", e.detail);
		};
		YBasePage.prototype.actionSheetCallback = function(e) {
			T.sendEvent("base_actionSheet_callback", e.detail);
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: G.htmlClass + " base_page_warp",
					style:
						G.htmlStyle +
						";display:" +
						(this.props.dataMore ? (this.data.showPage ? "flex" : "none") : "flex") +
						";background:" +
						(this.props.dataMore && this.props.dataMore.type == "half"
							? "rgba(0,0,0,0.4)"
							: "#FFF") +
						";"
				},
				G.appTheme && [
					this.props.dataMore &&
						this.props.dataMore.type == "half" &&
						apivm.h("view", {
							onClick: function() {
								return this$1.close();
							},
							style:
								"height:" +
								(this.props.dataMore.boxAuto
									? "1px;flex:1"
									: (this.props.shadowH || "14%") + ";flex-shrink: 0;")
						}),
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.penetrate();
							},
							style:
								"background:" +
								(this.props.bg || "#FFF") +
								";height:" +
								(this.props.dataMore && this.props.dataMore.boxAuto
									? "auto;max-height:90%;"
									: "1px;flex:1") +
								";border-radius: " +
								(this.props.dataMore && this.props.dataMore.type == "half"
									? "10px 10px"
									: "0 0") +
								" 0 0;"
						},
						apivm.h(
							"view",
							{style: "flex-shrink: 0;"},
							this.props.dataMore &&
								this.props.dataMore.type == "half" &&
								!this.props.closeH && [
									apivm.h(
										"view",
										{class: "base_page_header_warp", style: "height: 49px;"},
										apivm.h(
											"view",
											{
												class: "base_page_header_main",
												style: this.props.titleStyle || "padding: 0 44px;"
											},
											this.props.titleBox
												? [this.props.children.length >= 3 ? this.props.children[2] : null]
												: [
														apivm.h(
															"text",
															{
																style: G.loadConfiguration(1) + "color:#333",
																class: "base_page_header_main_text"
															},
															G.showTextSize(this.props.title, 8, 1)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "base_page_header_left_box", style: "height:49px;"},
											this.props.back
												? [this.props.children.length >= 1 ? this.props.children[0] : null]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "base_page_header_btn"
															},
															apivm.h(
																"text",
																{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
																"取消"
															)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "base_page_header_right_box", style: "height:49px;"},
											this.props.more
												? [this.props.children.length >= 2 ? this.props.children[1] : null]
												: []
										)
									),
									apivm.h(
										"view",
										{style: "width:100%;height:1px;padding:0 16px;"},
										apivm.h("a-divider", null)
									)
								]
						),
						apivm.h(
							"view",
							{
								style:
									"width:100%;height:" +
									(this.props.dataMore && this.props.dataMore.boxAuto
										? "auto"
										: "1px;flex:1") +
									";" +
									(T.platform() != "app" ? "overflow-y: scroll;" : "")
							},

							apivm.h(
								"view",
								null,
								((this.props.dataMore &&
									(this.props.dataMore.type == "full" ||
										this.data.pageParam.pageType == "home")) ||
									!this.props.dataMore) &&
									(this.props.titleBox || this.props.back || this.props.more
										? true
										: G.showHeader(this.props._this) && !this.props.closeH) &&
									apivm.h(
										"view",
										{
											style:
												"width:100%;height:auto;padding-top:" +
												G.headerTop() +
												"px;background:" +
												((this.props._this &&
												this.props._this.data &&
												this.props._this.data.headTheme
													? this.props._this.data.headTheme || ""
													: "") || G.headTheme)
										},
										apivm.h(
											"view",
											{class: "base_page_header_warp", style: "height: 44px;"},
											apivm.h(
												"view",
												{
													class: "base_page_header_main",
													style: this.props.titleStyle || "padding: 0 44px;"
												},
												this.props.titleBox
													? [this.props.children.length >= 3 ? this.props.children[2] : null]
													: [
															apivm.h(
																"text",
																{
																	style:
																		G.loadConfiguration(4) +
																		"color:" +
																		G.getHeadThemeRelatively(this.props._this),
																	class: "base_page_header_main_text"
																},
																G.showTextSize(this.props.title, 8, 1)
															)
													  ]
											),
											apivm.h(
												"view",
												{class: "base_page_header_left_box", style: "height:44px;"},
												apivm.h(
													"view",
													{style: "height: 44px;"},
													this.props.back
														? [
																this.props.children.length >= 1 ? this.props.children[0] : null
														  ]
														: [
																apivm.h(
																	"view",
																	{
																		onClick: function() {
																			return this$1.close();
																		},
																		class: "base_page_header_btn",
																		style: {
																			display: (this.props.dataMore &&
																			this.props.dataMore.type == "full"
																			? true
																			: G.showHeader(this.props._this) &&
																			  this.data.pageType == "page")
																				? "flex"
																				: "none"
																		}
																	},
																	apivm.h("a-iconfont", {
																		name: "fanhui1",
																		color: G.getHeadThemeRelatively(this.props._this),
																		size: G.appFontSize + 1
																	})
																)
														  ]
												)
											),
											apivm.h(
												"view",
												{class: "base_page_header_right_box", style: "height:44px;"},
												this.props.more
													? [this.props.children.length >= 2 ? this.props.children[1] : null]
													: []
											)
										)
									)
							),
							apivm.h(
								"view",
								{
									style:
										"width:100%;height:" +
										(this.props.dataMore && this.props.dataMore.boxAuto
											? "auto"
											: "1px;flex:1") +
										";"
								},
								this.props.children.length >= 4 ? this.props.children[3] : null
							)
						)
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h(
							"view",
							{
								class: "suspend_box",
								style: "display:" + (G.imagePreviewer.show ? "flex" : "none") + ";"
							},
							G.imagePreviewer.show &&
								apivm.h("image-previewer", {
									imgs: G.imagePreviewer.imgs,
									activeIndex: G.imagePreviewer.activeIndex,
									type: G.imagePreviewer.type
								})
						),
						apivm.h("mo-areas", {
							dataMore: G.areasBox,
							pageParam: G.areasBox.pageParam,
							onChange: this.areaCallback
						}),
						apivm.h("z-actionSheet", {
							dataMore: G.actionSheetBox,
							data: G.actionSheetBox.data,
							active: G.actionSheetBox.active,
							onClick: this.actionSheetCallback
						}),
						apivm.h("z-alert", {
							dataMore: G.alertBox,
							onClick: this.alertCallback,
							onCancel: function() {
								return this$1.alertCallback(2);
							}
						})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		div: {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".base_page_warp": {
			width: "100%",
			height: "100%",
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0",
			background: "rgba(0,0,0,0.4)"
		},
		".base_page_header_warp": {
			flexDirection: "row",
			width: "100%",
			alignItems: "center",
			flexShrink: "0"
		},
		".base_page_header_main": {
			width: "1px",
			height: "100%",
			flex: "1",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_main_text": {fontWeight: "600", flexShrink: "0"},
		".base_page_header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".base_page_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px",
			flexDirection: "row-reverse",
			flexShrink: "0"
		},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".avm-toast": {zIndex: "999"},
		".avm-confirm-mask": {zIndex: "999"},
		".suspend_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.6)"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var ASkeleton = /*@__PURE__*/ (function(Component) {
		function ASkeleton(props) {
			Component.call(this, props);
			this.data = {};
			this.compute = {
				rows: function() {
					var row = this.props.row || 0;
					return Array.from({length: row}).fill("");
				},
				length: function() {},
				avatarClass: function() {
					return (
						"a-skeleton_avatar " +
						(this.props["avatar-shape"] == "square" ? "" : "a-skeleton_round")
					);
				},
				avatarStyle: function() {
					var size = this.props["avatar-size"];
					return size ? "width:" + size + ";height:" + size + ";" : "";
				},
				titleStyle: function() {
					var titleWidth = this.props["title-width"];
					return titleWidth ? "width:" + titleWidth + ";" : "";
				}
			};
		}

		if (Component) ASkeleton.__proto__ = Component;
		ASkeleton.prototype = Object.create(Component && Component.prototype);
		ASkeleton.prototype.constructor = ASkeleton;
		ASkeleton.prototype.beforeRender = function() {
			if (!("loading" in this.props)) {
				this.props.loading = true;
			}
		};
		ASkeleton.prototype.getChildNode = function() {
			return this.props.children.length > 0 ? this.props.children[0] : null;
		};
		ASkeleton.prototype.getRowStyle = function(index) {
			return "width:" + this.getRowWidth(index) + ";";
		};
		ASkeleton.prototype.getRowWidth = function(index) {
			var rowWidth = this.props["row-width"] || "100%";

			if (rowWidth === "100%" && index === this.props.row - 1) {
				return "60%";
			}

			if (Array.isArray(rowWidth)) {
				return rowWidth[index];
			}

			return rowWidth;
		};
		ASkeleton.prototype.render = function() {
			var this$1 = this;
			return (
				(this.props.loading &&
					apivm.h(
						"view",
						{class: "a-skeleton"},
						this.props.avatar &&
							apivm.h("view", {class: this.avatarClass, style: this.avatarStyle}),
						apivm.h(
							"view",
							{style: "flex:1"},
							this.props.title &&
								apivm.h(
									"text",
									{class: "a-skeleton_title", style: this.titleStyle},
									this.props.title
								),
							this.props.row &&
								apivm.h(
									"view",
									null,
									(Array.isArray(this.rows) ? this.rows : Object.values(this.rows)).map(
										function(item$1, index$1) {
											return apivm.h("view", {
												class: "a-skeleton_row",
												style: this$1.getRowStyle(index$1)
											});
										}
									)
								)
						)
					)) ||
				this.getChildNode()
			);
		};

		return ASkeleton;
	})(Component);
	ASkeleton.css = {
		".a-skeleton": {width: "100%", flexDirection: "row", padding: "10px 16px"},
		".a-skeleton_avatar": {
			flexShrink: "0",
			width: "32px",
			height: "32px",
			marginRight: "16px",
			backgroundColor: "#f2f3f5"
		},
		".a-skeleton_round": {borderRadius: "999px"},
		".a-skeleton_title": {
			width: "40%",
			height: "16px",
			backgroundColor: "#f2f3f5"
		},
		".a-skeleton_row": {
			marginTop: "12px",
			width: "40%",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("a-skeleton", ASkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				refreshTriggered: false, //设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发
				upperThreshold: this.props.upperThreshold || 50, //距顶部/左边多远时，触发 scrolltoupper 事件
				lowerThreshold: this.props.lowerThreshold || 50, //距底部/右边多远时，触发 scrolltolower 事件

				scrollVID: "" //滚动位置
			};
			this.compute = {
				monitor: function() {
					if (this.data.scrollVID != (this.props["scroll-into-view"] || "")) {
						this.data.scrollVID = this.props["scroll-into-view"] || "";
						if (this.data.scrollVID) {
							this.scrollTo(this.data.scrollVID);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.install = function() {};
		YScrollView.prototype.installed = function() {};
		YScrollView.prototype.onscrolltoupper = function(e) {};
		YScrollView.prototype.onscrolltolower = function(e) {
			this.fire("up", {});
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			this.fire("scroll", detail);
		};
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			this.fire("lower", {});
			this.data.refreshTriggered = true;
			setTimeout(function() {
				this$1.data.refreshTriggered = false;
			}, 150);
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			try {
				if (T.platform() != "mp") {
					var _animated = T.isParameters(this.props["scroll-with-animation"])
						? this.props["scroll-with-animation"]
						: true;
					document
						.getElementById(this.props.id)
						.scrollTo(
							T.platform() == "app"
								? {view: nowView, animated: _animated}
								: {
										top: this.getOffestValue(
											document.getElementById(nowView),
											this.props.id
										).top,
										behavior: _animated ? "smooth" : "instant"
								  }
						);
				}
			} catch (e) {}
		};
		YScrollView.prototype.getOffestValue = function(elem, parentId) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == parentId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"view",
				{a: this.monitor, style: "flex:1;height:1px;" + (this.props.style || "")},
				apivm.h(
					"scroll-view",
					{
						id: "" + this.props.id,
						style: (this.props.style || "") + "background-color:transparent;",
						class: "" + (this.props.class || ""),
						"scroll-x": T.isParameters(this.props["scroll-x"])
							? this.props["scroll-x"]
							: false,
						"scroll-y": T.isParameters(this.props["scroll-y"])
							? this.props["scroll-y"]
							: true,
						bounces: T.isParameters(this.props["bounces"])
							? this.props["bounces"]
							: true,
						"scroll-into-view":
							"" + (T.platform() == "mp" ? this.props["scroll-into-view"] || "" : ""),
						"scroll-with-animation":
							T.platform() == "mp"
								? T.isParameters(this.props["scroll-with-animation"])
									? this.props["scroll-with-animation"]
									: true
								: false,
						"refresher-enabled":
							T.platform() != "web" &&
							(T.isParameters(this.props["refresher-enabled"])
								? this.props["refresher-enabled"]
								: false),
						"refresher-threshold": T.isParameters(this.props["refresher-threshold"])
							? this.props["refresher-threshold"]
							: 65,
						"refresher-background": T.isParameters(this.props["refresher-background"])
							? this.props["refresher-background"]
							: "#FFF",
						"refresher-triggered": this.data.refreshTriggered,
						"upper-threshold": this.upperThreshold,
						"lower-threshold": this.lowerThreshold,
						onScrolltoupper: this.onscrolltoupper,
						onScrolltolower: this.onscrolltolower,
						onRefresherrefresh: this.onrefresherrefresh,
						onScroll: this.onscroll
					},
					apivm.h(
						"view",
						{style: this.props.firsts || ""},
						this.props.children || null
					),
					apivm.h(
						"view",
						null,
						T.isParameters(this.props.data)
							? [
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											style: {display: this.props.data.skeleton ? "flex" : "none"}
										},
										(Array.isArray([1, 2, 3]) ? [1, 2, 3] : Object.values([1, 2, 3])).map(
											function(item$1, index$1) {
												return apivm.h("a-skeleton", {title: true, row: "3"});
											}
										)
									),
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											style: {
												display:
													!this.props.data.skeleton &&
													!this.props.data.notList &&
													this.props.data.listLength == 0
														? "flex"
														: "none"
											}
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(-4) + "color: #CCCCCC;padding:16px;"},
											this.props.data.text || "暂无数据"
										)
									),
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											onClick: this.onscrolltolower,
											style: {
												display:
													!this.props.data.skeleton &&
													!this.props.data.notList &&
													this.props.data.listLength != 0
														? "flex"
														: "none"
											}
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(-4) + "color: #CCCCCC;padding:15px;"},
											this.props.data.text
										)
									),
									apivm.h(
										"view",
										{style: {display: !this.props.data.dotFooter ? "flex" : "none"}},
										apivm.h("view", {
											style: "padding-bottom:" + T.safeArea().bottom + "px;"
										})
									)
							  ]
							: null
					)
				)
			);
		};

		return YScrollView;
	})(Component);
	YScrollView.css = {
		".y_scroll_box": {
			alignItems: "center",
			justifyContent: "center",
			width: "100%"
		}
	};
	apivm.define("y-scroll-view", YScrollView);

	var MoActivityDetails = /*@__PURE__*/ (function(Component) {
		function MoActivityDetails(props) {
			Component.call(this, props);
			this.data = {
				G: G,
				pageParam: {},
				pageType: "",
				title: "",
				id: "",
				code: "",
				nowModule: null,
				hasShare: false,
				collect: {has: false, is: false, title: ""},

				errorTitle: "",
				dataTitle: "",
				dataSubTitle: "",
				dataContent: "",
				dataTitleAddText: [],
				dataTitleAddRightText: [],
				materialBtn: {
					//相关资料
					key: "material"
				},

				participantsBtn: {
					//参与人员
					key: "participants"
				},

				activityBtns: [
					// {
					// 	type:"btn",text:"请假",color:"#666",plain:true,
					// },
					// {
					// 	type:"btn",text:"报名",color:"#666",
					// },
				],
				dataContentUnshift: [
					// {
					// 	title:"",
					// 	content:""
					// }
				],
				dataContentAdd: [
					// {
					// 	title:"征集结果反馈",
					// 	data:[
					// 		{
					// 			title:"关于街道实施惠民项目征集情况反馈",
					// 			content:"不搞形式主义，等级评定应和调解员职业发展及待遇相一致，才能体现评级的意义，才能真正调动调解员积极性",
					// 			addText:"反馈人：张三 反馈时间：2022-05-01 12:00",
					// 		}
					// 	],
					// }
				],
				externalLinks: "", //外部链接
				suspendedBtn: {
					show: true,
					data: [
						{
							show: false,
							key: "listen",
							value: "播报",
							bg: "#FFF",
							color: "appTheme",
							type: "icon",
							src: "shengyin1",
							size: 5,
							weight: "bold"
						}
					]
				},

				attachments: [
					// {id:2,name:"《建立有效的区域发展新机制解决方案》.doc",url:""},
				],

				//评论相关参数
				comment: {
					input: "",
					files: [],
					hint: "发表评论",
					hintToast: "请输入评论内容",
					commentNum: 0,
					likeNum: 0,
					likeIs: false,
					more: false,
					send: {
						show: false,
						disabled: false
					},

					big: {
						show: false
					},

					listHint: "全部评论",

					replyId: "",
					replyName: "",

					listExpand: [] //保存展开过的数据 以供刷新
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				pageNot: {
					skeleton: true,
					notList: true,
					listLength: 0,
					type: "0",
					text: "",
					dotFooter: true,
					swa: true,
					siv: ""
				},
				listData: [
					// {
					// 	id:"1",
					// 	name:"李四",
					// 	url:"https://www.apicloud.com/image/png/be/03/be034b9b1daf1e992c801b59cc66b336.30x30.png?t=1663048336247",
					// 	content:"这里是组织方发布的新内容可以是很长的话或者一些补充的内容之类的，支持上传附件与图片。",
					// 	time:"2022-05-07 13:30",
					// 	replyList:[
					// 		{
					// 			id:"1.1",
					// 			name:"张瑞",
					// 			replyName:"",
					// 			url:"https://www.apicloud.com/image/png/be/03/be034b9b1daf1e992c801b59cc66b336.30x30.png?t=1663048336247",
					// 			content:"从政协十三次会议以来政协委员和各办理单位以及各政协委员。",
					// 			time:"2022-05-07 13:30",
					// 		},
					// 		{
					// 			id:"1.2",
					// 			name:"李四",
					// 			replyName:"张瑞",
					// 			url:"https://www.apicloud.com/image/png/be/03/be034b9b1daf1e992c801b59cc66b336.30x30.png?t=1663048336247",
					// 			content:"谢谢你的评论",
					// 			time:"2022-05-07 13:30",
					// 		},
					// 	],
					// 	likeNum:25,
					// 	likeIs:true,
					// }
				],
				alertBox: {
					show: false,
					title: "",
					content: "",
					key: "",
					ids: "",
					cancel: {show: true, text: "取消", color: "#333333"},
					sure: {show: true, text: "确定", color: "appTheme"}
				},

				sharePage: {
					//分享弹框
					show: false,
					type: "half", //full全屏打开 half半屏打开
					boxAuto: true,
					pageParam: null
				},

				floatModule: {state: "", id: ""}, //播报的状态	播报的id	 0没有任何状态	 1播放中	2已暂停	3已结束
				floatModuleContent: "",

				actionSheet: {
					//长条点击更多弹窗
					show: false,
					keyItem: null,
					data: [
						{key: "cloudpan", value: "我的云盘", icon: ""},
						{key: "myfile", value: "本机文件", icon: ""}
					]
				},

				numInput: {
					//签到码输入
					show: false,
					title: "签到口令",
					key: "command_callback",
					value: "",
					codeLength: 4,
					mask: true,
					item: null
				},

				aroundPage: {
					//复制移动弹窗
					show: false,
					key: "long_add"
				},

				hasFile: false
			};
		}

		if (Component) MoActivityDetails.__proto__ = Component;
		MoActivityDetails.prototype = Object.create(Component && Component.prototype);
		MoActivityDetails.prototype.constructor = MoActivityDetails;
		MoActivityDetails.prototype.onShow = function() {
			G.onShow(this);
		};
		MoActivityDetails.prototype.installed = function() {};
		MoActivityDetails.prototype.baseInit = function() {
			this.data.pageParam = T.pageParam(this);
			G.installed(this);
		};
		MoActivityDetails.prototype.baseclose = function() {};
		MoActivityDetails.prototype.init = function() {
			var this$1 = this;

			this.data.pageType = this.data.pageParam.pageType || "page";
			this.data.title = this.data.pageParam.title || "详情";
			this.data.id = this.data.pageParam.id || "682447359405330432"; //业务id
			this.data.code = this.data.pageParam.code || "10"; //业务code
			this.data.nowModule = baseModule(this.data.code, "code"); //获取业务数据
			this.data.pageNot.notList = this.data.pageParam.notCommentList; //不显示评论

			this.getData(0);

			//接收播报状态监听
			T.addEventListener("playState", function(ret, err) {
				this$1.data.floatModule = ret.value;
				var playItem = G.getItemForKey("listen", this$1.data.suspendedBtn.data);
				if (this$1.data.id == this$1.data.floatModule.id) {
					switch (this$1.data.floatModule.state + "") {
						case "0":
							playItem.src = "shengyin1";
							break;
						case "1":
							playItem.src = "shengyin";
							break;
						case "2":
							playItem.src = "shengyinjingyin";
							break;
						case "3":
							playItem.src = "shengyin1";
							break;
					}
				} else {
					playItem.src = "shengyin1";
				}
			});
		};
		MoActivityDetails.prototype.close = function() {
			if (this.data.alertBox.show) {
				this.data.alertBox.show = false;
				return;
			}
			if (this.data.numInput.show) {
				this.data.numInput.show = false;
				return;
			}
			if (this.data.actionSheet.show) {
				this.data.actionSheet.show = false;
				return;
			}
			if (this.data.sharePage.show) {
				this.data.sharePage.show = false;
				return;
			}
			if (this.data.comment.big.show) {
				this.data.comment.big.show = false;
				return;
			}
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				T.closeWin();
			}
		};
		MoActivityDetails.prototype.getData = function(_type) {
			var this$1 = this;
			setTimeout(function() {
				T.sendEvent({name: "index", extra: {type: "getPlayState"}});
			}, 300);
			if (this.data.nowModule.businessCode) {
				isCollect(
					{id: this.data.id, code: this.data.nowModule.businessCode},
					this,
					function(is) {
						this$1.data.collect.is = is;
					}
				);
			}

			T.ajax(
				{u: myjs.appUrl() + "activitydoc/list", _this: this},
				"activitydoc/list",
				function(ret, err) {
					var data = ret ? ret.data || [] : [];
					this$1.data.hasFile = data.length > 0;
				},
				"活动资料",
				"post",
				{
					body: JSON.stringify({
						pageNo: 1,
						pageSize: 1,
						query: {activityId: this.data.id}
					})
				}
			);

			getModuleDetails({id: this.data.id, code: this.data.code}, this, function(
				ret,
				err
			) {
				if (err == "未携带code") {
					T.toast("未携带code");
					return;
				}
				T.hideProgress();
				var data = ret ? ret.data || {} : {};
				this$1.data.errorTitle = ret
					? ret.code != 200
						? ret.message || ret.data
						: ""
					: T.NET_ERR;
				var title = data.title || data.infoTitle || "";
				var subTitle = data.subTitle || data.infoSubtitle || "";
				var content = data.content || data.infoContent || "",
					unshiftContent = "",
					pushContent = "";

				this$1.data.dataTitleAddText = [];
				this$1.data.dataTitleAddRightText = [];
				this$1.data.dataContentAdd = [];
				this$1.data.activityBtns = [];
				this$1.data.dataContentUnshift = [];
				this$1.data.comment.send.show = !this$1.data.pageParam.notComment; //是否不要评论
				this$1.data.comment.send.disabled = false;
				this$1.data.collect.has =
					this$1.data.nowModule.businessCode && !this$1.data.errorTitle;
				switch (this$1.data.code) {
					case "10":
						var activityStatusName = data.activityStatusName;
						var canSign = data.canSign;
						var needSign = data.needSign;
						var joinStatus = data.joinStatus; //nosign暂未参与 sign已报名 join已签到 leaveing请假中 leavepass请假通过 leavenopass请假未通过
						var activityForm = data.activityForm || ""; //1全员公开 2指定人公开 3指定人不公开
						var rightState =
							{
								leaveing: "请假待审批",
								leavepass: "请假通过",
								leavenopass: "请假未通过"
							}[joinStatus] || "";
						if (!rightState && activityStatusName == "已结束") {
							rightState = joinStatus == "nosign" ? "未参与" : "已参与";
						}
						this$1.data.dataTitleAddRightText.push({
							text: rightState,
							color: G.getTagColor(rightState)
						});
						if (dayjs$1().isBefore(dayjs$1(data.signupStopTime))) {
							//在报名截止之前
							activityStatusName = "报名中";
							if (canSign == 1) {
								switch (joinStatus) {
									case "nosign":
										if (activityForm != "1" || needSign == "1") {
											this$1.data.activityBtns.push({
												key: "leave",
												type: "btn",
												text: "请假",
												color: "#F6931C"
											});
										}
										this$1.data.activityBtns.push({
											key: "signup",
											type: "btn",
											text: "报名",
											color: G.appTheme
										});
										break;
									case "sign":
										this$1.data.activityBtns.push({
											key: "unsignup",
											type: "btn",
											text: "取消报名",
											color: G.appTheme,
											plain: true
										});
										if (
											dayjs$1(data.joinBeginTime).isBefore(dayjs$1()) &&
											dayjs$1().isBefore(dayjs$1(data.joinEndTime))
										) {
											activityStatusName = "签到中";
											switch (joinStatus) {
												case "sign":
													this$1.data.activityBtns.push({
														key: "signin",
														type: "btn",
														text: "签到",
														color: G.appTheme
													});
													break;
												case "join":
													this$1.data.activityBtns = [];
													this$1.data.activityBtns.push({
														type: "text",
														text:
															"已签到" +
															(dayjs$1().isBefore(dayjs$1(data.beginTime))
																? "，请等待活动正式开始！"
																: ""),
														color: "#999"
													});
													break;
											}
										}
										break;
									case "join":
										activityStatusName = "签到中";
										this$1.data.activityBtns.push({
											type: "text",
											text:
												"已签到" +
												(dayjs$1().isBefore(dayjs$1(data.beginTime))
													? "，请等待活动正式开始！"
													: ""),
											color: "#999"
										});
										break;
								}
							}
						} else if (
							dayjs$1(data.joinBeginTime).isBefore(dayjs$1()) &&
							dayjs$1().isBefore(dayjs$1(data.joinEndTime))
						) {
							//在签到中
							activityStatusName = "签到中";
							if (canSign == 1) {
								switch (joinStatus) {
									case "sign":
										this$1.data.activityBtns.push({
											key: "signin",
											type: "btn",
											text: "签到",
											color: G.appTheme
										});
										break;
									case "join":
										this$1.data.activityBtns.push({
											type: "text",
											text:
												"已签到" +
												(dayjs$1().isBefore(dayjs$1(data.beginTime))
													? "，请等待活动正式开始！"
													: ""),
											color: "#999"
										});
										break;
								}
							}
						}
						switch (joinStatus) {
							case "leaveing":
								if (dayjs$1().isBefore(dayjs$1(data.signupStopTime)) && canSign == 1) {
									this$1.data.activityBtns.push({
										key: "unleave",
										type: "btn",
										text: "撤销请假",
										color: "#F6931C",
										plain: true
									});
								}
								this$1.data.activityBtns.push({
									key: "leaveDetail",
									type: "leaveText",
									text: "请等待审批！",
									color: "#999"
								});
								break;
							case "leavepass":
								if (dayjs$1().isBefore(dayjs$1(data.signupStopTime)) && canSign == 1) {
									this$1.data.activityBtns.push({
										key: "unleave",
										type: "btn",
										text: "撤销请假",
										color: "#F6931C",
										plain: true
									});
								}
								this$1.data.activityBtns.push({
									key: "leaveDetail",
									type: "leaveText",
									text: "请假已通过！",
									color: "#999"
								});
								break;
							case "leavenopass":
								this$1.data.activityBtns.push({
									key: "leaveDetail",
									type: "leaveText",
									text: "请假未通过，请重新请假！",
									color: "#999"
								});
								if (dayjs$1().isBefore(dayjs$1(data.signupStopTime)) && canSign == 1) {
									this$1.data.activityBtns.push({
										key: "leave",
										type: "btn",
										text: "重新请假",
										color: "#F6931C"
									});
									this$1.data.activityBtns.push({
										key: "signup",
										type: "btn",
										text: "报名",
										color: G.appTheme
									});
								}
								break;
						}

						this$1.data.dataTitleAddText.push({
							text: data.activityTypeName,
							color: "#F6931C",
							type: "tag"
						});
						this$1.data.dataTitleAddText.push({
							text: activityStatusName,
							color: G.getTagColor(activityStatusName),
							type: "tag"
						});
						this$1.data.dataContentUnshift.push({
							title: "发布机构：",
							content: data.organizerName || data.organizer
						});
						this$1.data.dataContentUnshift.push({
							title: "活动开始：",
							content: dayjs$1(data.beginTime).format("YYYY-MM-DD HH:mm")
						});
						this$1.data.dataContentUnshift.push({
							key: "showCMore",
							title: "活动结束：",
							content: dayjs$1(data.endTime).format("YYYY-MM-DD HH:mm"),
							more: true,
							is: false
						});
						this$1.data.dataContentUnshift.push({
							show: false,
							title: "报名截止：",
							content: dayjs$1(data.signupStopTime).format("YYYY-MM-DD HH:mm")
						});
						this$1.data.dataContentUnshift.push({
							show: false,
							title: "签到开始：",
							content: dayjs$1(data.joinBeginTime).format("YYYY-MM-DD HH:mm")
						});
						this$1.data.dataContentUnshift.push({
							show: false,
							title: "签到结束：",
							content: dayjs$1(data.joinEndTime).format("YYYY-MM-DD HH:mm")
						});
						if (T.isArray(data.labels) && data.labels.length) {
							this$1.data.dataContentUnshift.push({
								title: "活动标签：",
								content: data.labels
									.map(function(obj) {
										return obj.activityLabel.label;
									})
									.join("、")
							});
						}
						this$1.data.dataContentUnshift.push({
							title: "活动地点：",
							content: data.address
						});
						var fileLink = data.linkDetailVos || [];
						var videoHrefs = data.infoVideo || "";
						if (T.isParameters(fileLink) && fileLink.length) {
							var useType1 = [],
								useType2 = [];
							fileLink.forEach(function(_mItem) {
								if (
									_mItem.fileId ||
									(T.isArray(_mItem.attachments) && _mItem.attachments.length)
								) {
									if (_mItem.useType == "1" && useType1.length < 3) {
										useType1.push({url: _mItem.attachments[0].newFileName});
									} else if (_mItem.useType == "2") {
										useType2.push(
											_mItem.fileId ||
												(_mItem.attachments.length ? _mItem.attachments[0].id : "")
										);
									}
								}
							});
							if (useType2.length && useType2[0]) {
								videoHrefs = useType2[0];
							}
						}
						if (videoHrefs) {
							unshiftContent += '<video src="' + videoHrefs + '"></video>';
						}
						var isScreen = data.isScreen || "1"; //是否允许截屏 默认是
						api.setScreenSecure && api.setScreenSecure({secure: isScreen == "0"});
						var isCompraise = data.isCompraise || "1"; //允许评论点赞 默认是
						this$1.data.comment.send.show = isCompraise == "1";
						this$1.data.pageNot.notList = isCompraise != "1";

						var showType = T.isParameters(data.showType) ? data.showType : 1;
						this$1.checkedStatus = showType == 1 ? "1" : "0";
						this$1.data.comment.onlyMe = showType == 0 || showType == 3;
						if (this$1.data.comment.onlyMe) {
							this$1.data.comment.listHint = "我的评论";
						}
						break;
				}

				//等内容显示出来了再把页面展示
				setTimeout(function() {
					this$1.data.pageNot.skeleton = false;
					this$1.data.dataTitle = title;
					this$1.data.dataSubTitle = subTitle;
				}, 80);
				this$1.data.dataContent = unshiftContent + content + pushContent;
				this$1.data.floatModuleContent = content;
				G.getItemForKey("listen", this$1.data.suspendedBtn.data).show =
					(this$1.data.floatModuleContent ? true : false) &&
					T.platform() == "app" &&
					api.require("voiceRecognizer");
				this$1.data.attachments = data.attachments || [];

				//有业务数据
				if (this$1.data.nowModule) {
					this$1.getCommentCount();
					//有列表评论
					if (!this$1.data.pageNot.notList) {
						this$1.getCommentData();
					}
				}
			});
		};
		MoActivityDetails.prototype.openLink = function() {
			T.openWin(
				"mo_details_url",
				"../mo_details_url/mo_details_url.stml",
				{url: this.data.externalLinks},
				this
			);
		};
		MoActivityDetails.prototype.getCommentData = function(_type) {
			var this$1 = this;

			if (!_type) {
				this.data.pageNo = 1;
			}
			var postParam = {
				pageNo: this.data.pageNo,
				pageSize: !_type
					? this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize
					: this.data.pageSize,
				businessCode: this.data.nowModule.businessCode,
				businessId: this.data.id
			};

			if (this.data.comment.onlyMe) {
				postParam.isSelectForMyself = 1;
			}
			T.ajax(
				{u: myjs.appUrl() + "comment/twoLevelTree", _this: this},
				"comment/twoLevelTree",
				function(ret, err) {
					T.hideProgress();
					this$1.data.pageNot.skeleton = false;
					var code = ret ? ret.code : "";
					var data = ret ? ret.data || [] : [];
					this$1.data.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1; //类型 列表中只有有网和无网的情况
					this$1.data.pageNot.text =
						ret && code != 200 ? ret.message || ret.data : ""; //只有接口报的异常才改文字
					if (!_type) {
						this$1.data.pageNo = 1;
					}
					if (T.isArray(data) && data.length) {
						var nowList = [];
						data.forEach(function(_eItem) {
							//item index 原数组对象
							var item = {};
							item.id = _eItem.id || ""; //id
							item.createBy = _eItem.publishAccountId || "";
							item.name = _eItem.commentUserName || "";
							item.url = _eItem.headImg || _eItem.photo;
							item.content = _eItem.commentContent || "";
							item.time = _eItem.createDate || "";
							item.likeNum = _eItem.praisesCount;
							item.likeIs = _eItem.hasClickPraises;
							item.replyName = _eItem.toCommenter || "";
							item.attachments = _eItem.fileInfos || [];
							item.userRoles = _eItem.userRoles || [];
							item.userArea = _eItem.userArea || "";
							item.checkedStatus = _eItem.checkedStatus || 0;
							item.replyList = [];
							(_eItem.children || []).forEach(function(_nItem) {
								var nItem = {};
								nItem.id = _nItem.id || ""; //id
								nItem.createBy = _nItem.publishAccountId || "";
								nItem.name = _nItem.commentUserName || "";
								nItem.url = _nItem.headImg || _nItem.photo;
								nItem.content = _nItem.commentContent || "";
								nItem.time = _nItem.createDate || "";
								nItem.likeNum = _nItem.praisesCount;
								nItem.likeIs = _nItem.hasClickPraises;
								nItem.replyName = _nItem.toCommenter || "";
								nItem.attachments = _nItem.fileInfos || [];
								nItem.userRoles = _nItem.userRoles || [];
								nItem.userArea = _nItem.userArea || "";
								nItem.checkedStatus = _nItem.checkedStatus || 0;
								nItem.replyList = [];
								item.replyList.push(nItem);
							});
							nowList.push(item);
						});
						if (!_type) {
							this$1.data.listData = nowList;
						} else {
							this$1.data.listData = this$1.data.listData.concat(nowList);
						}
						this$1.data.pageNo++;
						this$1.data.pageNot.text = !this$1.data.listData.length
							? ""
							: data.length >= postParam.pageSize
							? T.LOAD_MORE
							: T.LOAD_ALL; //当前返回的数量 等于 请求的数量 说明可能还有	少于说明没有了
						(this$1.data.refreshPageSize =
							Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
							this$1.data.pageSize),
							(this$1.data.pageNo =
								Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1);
					} else if (_type == 1) {
						//加载更多的时候 底部显示文字
						this$1.data.pageNot.text = ret
							? code == 200
								? T.LOAD_ALL
								: ret.message
							: T.NET_ERR;
					} else {
						this$1.data.listData = [];
					}
					this$1.data.pageNot.listLength = this$1.data.listData.length;
				},
				"评论列表",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		MoActivityDetails.prototype.loadMore = function() {
			if (
				(this.data.pageNot.text == T.LOAD_MORE ||
					this.data.pageNot.text == T.NET_ERR) &&
				this.data.pageNo != 1
			) {
				this.data.pageNot.text = T.LOAD_ING;
				this.getCommentData(this.data.listData.length ? 1 : 0); //列表没数据时 算下拉 有数据上拉
			}
		};
		MoActivityDetails.prototype.getCommentCount = function() {
			var this$1 = this;

			T.ajax(
				{u: myjs.appUrl() + "praises/count", _this: this},
				"praises/count",
				function(ret, err) {
					var data = ret ? ret.data || {} : {};
					this$1.data.comment.commentNum = data.commentCount || 0;
					this$1.data.comment.likeNum = data.praisesCount || 0;
					this$1.data.comment.likeIs = data.hasClickPraises || false;
				},
				"评论点赞总数",
				"post",
				{
					body: JSON.stringify({
						businessCode: this.data.nowModule.businessCode,
						businessId: this.data.id,
						isExcludeSubComment: this.data.code == "6" ? 1 : null
					})
				}
			);
		};
		MoActivityDetails.prototype.suspendedBtnClick = function(ref) {
			var detail = ref.detail;

			switch (detail.key) {
				case "listen":
					if (this.data.id == this.data.floatModule.id) {
						if (this.data.floatModule.state == "1") {
							T.sendEvent({name: "index", extra: {type: "pauseStartPlay"}});
						} else {
							T.sendEvent({name: "index", extra: {type: "reStartPlay"}});
						}
					} else {
						T.sendEvent({
							name: "index",
							extra: {
								type: "startPlay",
								floatType: this.data.code,
								id: this.data.id,
								src: this.data.floatModuleContent
							}
						});
					}
					break;
				default:
					T.toast(detail.value);
					break;
			}
		};
		MoActivityDetails.prototype.commentHintClick = function(_value) {
			this.data.comment.big.show = _value;
		};
		MoActivityDetails.prototype.scrollTo = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			var _view = detail.view;
			var _animated = detail.animated;
			var nowView = _view;
			if (T.platform() == "mp") {
				this.data.pageNot.siv = "";
				this.data.pageNot.swa = _animated;
				setTimeout(function() {
					this$1.data.pageNot.siv = nowView;
				}, 1);
			} else {
				document
					.getElementById("details_scroll_box")
					.scrollTo(
						T.platform() == "app"
							? {view: nowView, animated: _animated}
							: {
									top: this.getOffestValue(
										document.getElementById(nowView),
										"details_scroll_box"
									).top,
									behavior: _animated ? "smooth" : "instant"
							  }
					);
			}
		};
		MoActivityDetails.prototype.getOffestValue = function(elem, parentId) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == parentId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		MoActivityDetails.prototype.cckComment = function() {
			this.scrollTo({detail: {view: "details_comment_box", animated: true}});
		};
		MoActivityDetails.prototype.cckLike = function(ref) {
			var detail = ref.detail;

			var likeIs = T.isParameters(detail.likeIs)
				? detail.likeIs
				: this.data.comment.likeIs;
			var param = {
				businessCode: detail.code || this.data.nowModule.businessCode,
				businessId: detail.id || this.data.id
			};

			if (likeIs) {
				param = {form: param};
			}
			T.ajax(
				{u: myjs.appUrl() + "praises/" + (likeIs ? "add" : "dels"), _this: this},
				"praises/add",
				function(ret, err) {},
				(likeIs ? "" : "取消") + "点赞",
				"post",
				{
					body: JSON.stringify(param)
				}
			);
		};
		MoActivityDetails.prototype.cckReply = function(ref) {
			var detail = ref.detail;

			if (detail.createBy == G.userId || detail.createBy == G.uId) {
				this.data.alertBox.title = "";
				this.data.alertBox.ids = detail.id;
				this.data.alertBox.content = "确定删除所选的评论吗？";
				this.data.alertBox.key = "comment_delete_callback";
				this.data.alertBox.show = true;
			} else {
				this.data.comment.input = "";
				this.data.comment.files = [];
				this.data.comment.replyId = detail.id;
				this.data.comment.replyName = "回复" + detail.name;
				this.commentHintClick(true);
			}
		};
		MoActivityDetails.prototype.itemClick = function(ref) {
			var detail = ref.detail;

			// console.log(JSON.stringify(detail));
			this.data.actionSheet.keyItem = detail;
			this.data.actionSheet.data = [{key: "commentCopy", value: "复制", icon: ""}];

			if (detail.createBy == G.userId || detail.createBy == G.uId) {
				this.data.actionSheet.data.push({
					key: "commentDel",
					value: "删除",
					icon: ""
				});
			} else {
				this.data.actionSheet.data.push({
					key: "commentReply",
					value: "回复",
					icon: ""
				});
			}
			this.data.actionSheet.show = true;
		};
		MoActivityDetails.prototype.commentInputClose = function() {
			if (!this.data.comment.input && !this.data.comment.files.length) {
				this.data.comment.replyId = "";
				this.data.comment.replyName = "";
			}
		};
		MoActivityDetails.prototype.commentInputSend = function(_param) {
			var this$1 = this;
			if (_param === void 0) _param = {};

			var param = {
				form: {
					terminalName: "APP",
					businessCode: this.data.nowModule.businessCode,
					businessId: this.data.id,
					commentContent: this.data.comment.input,
					attachmentIds: this.data.comment.files
						.map(function(obj) {
							return obj.id;
						})
						.join(","),
					parentId: this.data.comment.replyId
				}
			};

			if (this.checkedStatus == "1") {
				param.form.checkedStatus = "1";
			}
			if (_param.isReplaceDirtyWord) {
				param.isReplaceDirtyWord = 1;
			}
			T.showProgress(this.data.comment.hint + "中");
			T.ajax(
				{u: myjs.appUrl() + "comment/add", _this: this},
				"comment/add",
				function(ret, err) {
					T.hideProgress();
					if (ret && ret.data == "DirtyWordException") {
						this$1.data.alertBox.title = "";
						this$1.data.alertBox.content =
							ret.message + "，是否继续提交？提交时会把敏感词替换成【*】";
						this$1.data.alertBox.key = "comment_callback";
						this$1.data.alertBox.show = true;
					} else {
						T.toast(
							ret
								? ret.code == 200
									? this$1.data.comment.hint + "成功"
									: ret.message || ret.data
								: T.NET_ERR
						);
						if (ret && ret.code == 200) {
							this$1.data.comment.input = "";
							this$1.data.comment.files = [];
							this$1.commentHintClick(false);
							setTimeout(function() {
								this$1.commentInputClose();
								this$1.getCommentCount();
								this$1.getCommentData();
							}, 300);
						}
					}
				},
				"评论",
				"post",
				{
					body: JSON.stringify(param)
				}
			);
		};
		MoActivityDetails.prototype.keyCallback = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			if (!T.isObject(detail)) {
				detail = this.data[detail];
			}
			console.log(JSON.stringify(detail));
			switch (detail.key) {
				case "comment_delete_callback":
					this.delCallback();
					break;
				case "commentCopy":
					copyText(this.data.actionSheet.keyItem.content, function(ret, err) {
						T.toast(ret ? "复制成功" : "复制失败");
					});
					break;
				case "commentDel":
				case "commentReply":
					this.cckReply({detail: this.data.actionSheet.keyItem});
					break;
				case "comment_callback":
					this.commentInputSend({isReplaceDirtyWord: 1});
					break;
				case "showCMore": //展开收起详情更多
					detail.is = !detail.is;
					this.data.dataContentUnshift.forEach(function(_eItem, _eIndex, _eArr) {
						if (T.isParameters(_eItem.show)) {
							_eItem.show = detail.is;
						}
					});
					break;
				case "leave": //请假
					openDetails(
						{code: "add"},
						{id: this.data.id, paramType: "activityLeave", title: "请假"},
						this
					);
					break;
				case "leaveDetail":
					openDetails({code: "10_2", id: this.data.id}, null, this);
					break;
				case "material":
					T.openWin(
						"mo_activity_material",
						"../mo_activity_material/mo_activity_material.stml",
						{id: this.data.id},
						this
					);
					break;
				case "participants":
					T.openWin(
						"mo_activity_participants",
						"../mo_activity_participants/mo_activity_participants.stml",
						{id: this.data.id},
						this
					);
					break;
				case "signup":
				case "unsignup":
				case "unleave":
					this.options(detail.key);
					break;
				case "signin":
					this.data.actionSheet.data = [
						{show: T.platform() != "web", key: "scan", value: "二维码签到", icon: ""},
						{show: true, key: "command", value: "签到口令签到", icon: ""}
					];

					this.data.actionSheet.show = true;
					break;
				case "scan":
					openScan(null, this, function(ret) {
						this$1.getData(0);
					});
					break;
				case "command":
					this.data.numInput.value = "";
					this.data.numInput.show = true;
					break;
				case "command_callback":
					this.options("signin");
					break;
			}
		};
		MoActivityDetails.prototype.delCallback = function() {
			var this$1 = this;

			var postParam = {
				ids: [this.data.alertBox.ids]
			};

			T.showProgress("操作中");
			T.ajax(
				{u: myjs.appUrl() + "comment/dels", _this: this},
				"comment/dels",
				function(ret, err) {
					T.hideProgress();
					T.toast(ret ? ret.message : T.NET_ERR);
					if (ret && ret.code == 200) {
						setTimeout(function() {
							this$1.getCommentCount();
							this$1.getCommentData();
						}, 300);
					}
				},
				"删除评论",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		MoActivityDetails.prototype.shareBtn = function() {
			this.data.sharePage.show = true;
		};
		MoActivityDetails.prototype.options = function(_key) {
			var this$1 = this;

			var url = "",
				param = {
					form: {
						activityId: this.data.id
					}
				};

			if (_key == "signup" || _key == "unsignup") {
				if (_key == "signup") {
					url = myjs.appUrl() + "activityperson/sign";
				} else {
					url = myjs.appUrl() + "activityperson/cancelsign";
				}
			} else if (_key == "signin") {
				url = myjs.appUrl() + "activityperson/join";
				param.signInCommand = this.data.numInput.value;
			} else if (_key == "unleave") {
				url = myjs.appUrl() + "activityleave/cancel";
			}
			T.showProgress("操作中");
			T.ajax(
				{u: url, _this: this},
				"activityperson/option",
				function(ret, err) {
					T.hideProgress();
					T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					if (ret) {
						if (ret.code == 200) {
							setTimeout(function() {
								this$1.data.numInput.show = false;
								this$1.getData(0);
							}, 300);
						} else {
							this$1.data.numInput.value = "";
						}
					}
				},
				_key,
				"post",
				{
					body: JSON.stringify(param)
				}
			);
		};
		MoActivityDetails.prototype.collectBtn = function() {
			if (this.data.collect.is) {
				this.data.collect.is = !this.data.collect.is;
				optionCollect({
					code: this.data.nowModule.businessCode,
					id: this.data.id,
					module: this.module,
					collect: this.data.collect.is,
					theme: this.data.collect.title || this.data.dataTitle
				});
			} else {
				this.data.aroundPage.show = true;
			}
		};
		MoActivityDetails.prototype.aroundCallback = function(ref) {
			var detail = ref.detail;

			console.log(JSON.stringify(detail));
			this.data.collect.is = !this.data.collect.is;
			optionCollect({
				code: this.data.nowModule.businessCode,
				id: this.data.id,
				module: this.module,
				collect: this.data.collect.is,
				theme: this.data.collect.title || this.data.dataTitle,
				folderId: detail.toItem.key
			});
		};
		MoActivityDetails.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{
					_this: this,
					title: this.data.title,
					dataMore: this.props.dataMore,
					pageParam: this.props.pageParam,
					more: this.data.collect.has || this.data.hasShare,
					onInit: this.baseInit,
					onClose: this.close,
					onBaseclose: this.baseclose,
					onPageRefresh: function() {
						return this$1.getData(false);
					}
				},
				apivm.h("view", {style: "width:auto;height:100%;"}),
				apivm.h(
					"view",
					{style: "width:auto;height:100%;flex-direction:row-reverse;"},
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.collectBtn();
							},
							class: "header_btn",
							style: {display: this.data.collect.has ? "flex" : "none"}
						},
						apivm.h("a-iconfont", {
							name: this.data.collect.is ? "shoucangfill" : "shoucang",
							color: this.data.collect.is
								? "#F6931C"
								: this.data.G.getHeadThemeRelatively(this),
							size: this.data.G.appFontSize + 6
						})
					),
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.shareBtn();
							},
							class: "header_btn",
							style: {display: this.data.hasShare ? "flex" : "none"}
						},
						apivm.h("a-iconfont", {
							name: "share",
							color: this.data.G.getHeadThemeRelatively(this),
							size: this.data.G.appFontSize + 3
						})
					)
				),
				apivm.h("view", null),
				apivm.h(
					"view",
					{
						style:
							"width:100%;height:" +
							(this.props.dataMore && this.props.dataMore.boxAuto
								? "auto"
								: "1px;flex:1") +
							";"
					},
					apivm.h(
						"y-scroll-view",
						{
							id: "details_scroll_box",
							style: "width:100%;height:1px;flex:1;",
							"refresher-enabled": true,
							"scroll-with-animation": this.data.pageNot.swa,
							"scroll-into-view": this.data.pageNot.siv,
							data: this.data.pageNot,
							onLower: function() {
								return this$1.getData(0);
							},
							onUp: function() {
								return this$1.loadMore();
							}
						},
						apivm.h(
							"view",
							null,
							this.data.dataTitle
								? apivm.h(
										"view",
										null,
										apivm.h(
											"view",
											null,
											apivm.h(
												"view",
												{class: "details_title_box"},
												apivm.h(
													"text",
													{
														style: "" + this.data.G.loadConfiguration(4),
														class: "details_title_text"
													},
													this.data.dataTitle
												),
												this.data.dataSubTitle &&
													apivm.h(
														"text",
														{
															style: this.data.G.loadConfiguration(1) + "margin-top:10px;",
															class: "details_title_text"
														},
														"“",
														this.data.dataSubTitle,
														"”"
													)
											),
											apivm.h(
												"view",
												{style: "flex-direction:row; align-items: center;"},
												apivm.h(
													"view",
													{class: "details_add_box"},
													(Array.isArray(this.data.dataTitleAddText)
														? this.data.dataTitleAddText
														: Object.values(this.data.dataTitleAddText)
													).map(function(item$1, index$1) {
														return apivm.h(
															"view",
															{style: "margin:10px 16px 0 0;"},
															item$1.type == "tag"
																? apivm.h(
																		"view",
																		null,
																		apivm.h("z-tag", {
																			style: "" + this$1.data.G.loadConfiguration(-2),
																			color: item$1.color,
																			text: item$1.text
																		})
																  )
																: apivm.h(
																		"text",
																		{
																			style:
																				this$1.data.G.loadConfiguration(-2) +
																				"color: " +
																				(item$1.color || "#666") +
																				";padding: 1px 0px;"
																		},
																		item$1.text
																  )
														);
													})
												),
												apivm.h(
													"view",
													{class: "details_add_right_box"},
													(Array.isArray(this.data.dataTitleAddRightText)
														? this.data.dataTitleAddRightText
														: Object.values(this.data.dataTitleAddRightText)
													).map(function(item$1, index$1) {
														return apivm.h(
															"text",
															{
																style:
																	this$1.data.G.loadConfiguration(-2) +
																	"color: " +
																	(item$1.color || "#666") +
																	";margin:5px 0 0 16px;"
															},
															item$1.text
														);
													})
												)
											),
											apivm.h(
												"view",
												{style: "padding: 10px 16px;"},
												apivm.h(
													"view",
													null,
													this.data.dataContentUnshift &&
														this.data.dataContentUnshift.length > 0 &&
														this.data.dataContentUnshift.map(function(item, index) {
															return (
																(T.isParameters(item.show) ? item.show : true) && [
																	apivm.h(
																		"view",
																		{style: "flex-direction:row;margin-bottom:10px;"},
																		apivm.h(
																			"text",
																			{
																				style:
																					this$1.data.G.loadConfiguration(-2) +
																					"color: #333;font-weight: 600;flex-shrink:0;"
																			},
																			item.title
																		),
																		apivm.h(
																			"text",
																			{
																				style: this$1.data.G.loadConfiguration(-2) + "color: #333;"
																			},
																			item.content
																		),
																		item.more
																			? apivm.h(
																					"view",
																					{
																						onClick: function() {
																							return this$1.keyCallback({detail: item});
																						},
																						style: "padding:0 5px;margin-left:10px;"
																					},
																					apivm.h("a-iconfont", {
																						style:
																							"transform: rotate(" + (item.is ? "180" : "0") + "deg);",
																						name: "xiangxia1",
																						color: this$1.data.G.appTheme,
																						size: this$1.data.G.appFontSize + 1
																					})
																			  )
																			: null
																	)
																]
															);
														})
												),
												apivm.h("z-rich-text", {
													detail: true,
													style: this.data.G.loadConfiguration(1) + "color:#333;",
													nodes: this.data.dataContent
												}),

												apivm.h(
													"view",
													null,
													this.data.hasFile &&
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.keyCallback({detail: "materialBtn"});
																},
																style:
																	"flex-direction:row; align-items: center;margin-top:10px;background: #F8F9FA;border-radius: 4px;height: 50px;padding:0 20px;"
															},
															apivm.h("a-iconfont", {
																name: "tongji2",
																color: "#A1A8AE",
																size: this.data.G.appFontSize + 20
															}),
															apivm.h(
																"view",
																{
																	style: "flex-shrink:0;flex:1;margin:0 20px;flex-direction:row;"
																},
																apivm.h(
																	"view",
																	null,
																	apivm.h(
																		"text",
																		{
																			style:
																				this.data.G.loadConfiguration(1) +
																				"color: #6E747A;font-weight: 600;"
																		},
																		"活动相关资料"
																	),
																	apivm.h("view", {
																		style:
																			"position:absolute;z-index:999;bottom:0;left:-4px;right:-11px;height:5px;background:rgba(150,150,150,0.4);"
																	})
																)
															),
															apivm.h("a-iconfont", {
																style: "transform: rotate(-90deg);",
																name: "xiangxia1",
																color: "#666",
																size: this.data.G.appFontSize - 2
															})
														)
												),

												apivm.h(
													"view",
													null,
													this.data.activityBtns &&
														this.data.activityBtns.length > 0 &&
														apivm.h(
															"view",
															{
																style: "flex-direction:row;flex-wrap: wrap;padding:10px 10px 0;"
															},
															(Array.isArray(this.data.activityBtns)
																? this.data.activityBtns
																: Object.values(this.data.activityBtns)
															).map(function(item$1, index$1) {
																return apivm.h(
																	"view",
																	{
																		style:
																			"flex-direction:row;align-items: center; justify-content: center;width:" +
																			(this$1.data.activityBtns.length == 1 ||
																			(this$1.data.activityBtns.length == 3 &&
																				item$1.type == "leaveText")
																				? "100"
																				: "50") +
																			"%;padding:5px 0;"
																	},
																	item$1.type == "btn"
																		? apivm.h("z-button", {
																				onClick: function() {
																					return this$1.keyCallback({
																						detail: item$1
																					});
																				},
																				round: true,
																				plain: item$1.plain,
																				style: "min-width:132px;padding:7px 0px;",
																				fontstyle: this$1.data.G.loadConfiguration(),
																				size: this$1.data.G.appFontSize,
																				color: item$1.color,
																				text: item$1.text
																		  })
																		: apivm.h(
																				"view",
																				{
																					style:
																						"padding:7px 0px;flex-direction:row;align-items: center;"
																				},
																				apivm.h(
																					"text",
																					{
																						style:
																							this$1.data.G.loadConfiguration() +
																							"color: " +
																							item$1.color +
																							";"
																					},
																					item$1.text
																				),
																				item$1.type == "leaveText"
																					? apivm.h(
																							"view",
																							{
																								onClick: function() {
																									return this$1.keyCallback({
																										detail: item$1
																									});
																								},
																								style:
																									"flex-direction:row;align-items: center;width: auto;"
																							},
																							apivm.h(
																								"text",
																								{
																									style:
																										this$1.data.G.loadConfiguration(-4) +
																										"color: " +
																										this$1.data.G.appTheme +
																										";"
																								},
																								"详情"
																							),
																							apivm.h("a-iconfont", {
																								style: "transform: rotate(-90deg);",
																								name: "xiangxia1",
																								color: this$1.data.G.appTheme,
																								size: this$1.data.G.appFontSize - 2
																							})
																					  )
																					: null
																		  )
																);
															})
														)
												)
											),
											apivm.h(
												"view",
												null,
												this.data.externalLinks &&
													apivm.h(
														"text",
														{
															onClick: function() {
																return this$1.openLink();
															},
															style:
																this.data.G.loadConfiguration() +
																"color: blue;padding: 10px 16px;"
														},
														"外部链接：",
														this.data.externalLinks
													)
											),
											apivm.h("y-attachments", {
												style: "padding: 15px 16px;",
												data: this.data.attachments
											}),

											apivm.h(
												"view",
												null,
												this.data.dataContentAdd.length > 0
													? apivm.h(
															"view",
															null,
															(Array.isArray(this.data.dataContentAdd)
																? this.data.dataContentAdd
																: Object.values(this.data.dataContentAdd)
															).map(function(item$1, index$1) {
																return apivm.h(
																	"view",
																	{class: "details_addContent_box"},
																	apivm.h(
																		"view",
																		{class: "details_addContent_hint_box"},
																		apivm.h("view", {
																			class: "details_addContent_hint_line",
																			style:
																				this$1.data.G.loadConfigurationSize(-1, "h") +
																				"background:" +
																				this$1.data.G.appTheme +
																				";"
																		}),
																		apivm.h(
																			"text",
																			{
																				class: "details_addContent_hint_text",
																				style: "" + this$1.data.G.loadConfiguration(1)
																			},
																			item$1.title
																		)
																	),
																	item$1.data && item$1.data.length > 0
																		? apivm.h(
																				"view",
																				null,
																				(Array.isArray(item$1.data)
																					? item$1.data
																					: Object.values(item$1.data)
																				).map(function(nItem, nIndex) {
																					return apivm.h(
																						"view",
																						{class: "details_addContent_con_box"},
																						apivm.h(
																							"text",
																							{
																								class: "details_addContent_con_title",
																								style: "" + this$1.data.G.loadConfiguration(1)
																							},
																							nItem.title
																						),
																						apivm.h("z-rich-text", {
																							style:
																								this$1.data.G.loadConfiguration(1) + "color:#333;",
																							nodes: nItem.content
																						}),
																						apivm.h("y-attachments", {
																							style: "padding: 10px 0px;",
																							data: nItem.attachments
																						}),
																						apivm.h(
																							"text",
																							{
																								class: "details_addContent_con_addText",
																								style: "" + this$1.data.G.loadConfiguration(-2)
																							},
																							nItem.addText
																						)
																					);
																				})
																		  )
																		: null
																);
															})
													  )
													: null
											)
										),
										apivm.h(
											"view",
											null,
											apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.keyCallback({detail: "participantsBtn"});
													},
													style:
														"border-top: 10px solid #F8F8F8;padding: 15px 16px; flex-direction:row; align-items: center;"
												},
												apivm.h("view", {
													style:
														this.data.G.loadConfigurationSize(-1, "h") +
														"width:3px; border-radius: 10px;background:" +
														this.data.G.appTheme +
														";"
												}),
												apivm.h(
													"text",
													{
														class: "comment_hint_text",
														style:
															this.data.G.loadConfiguration(1) +
															"color: #333333; font-weight: 600; margin-left: 5px; flex:1;"
													},
													"参与人员"
												),
												apivm.h("a-iconfont", {
													style: "transform: rotate(-90deg);",
													name: "xiangxia1",
													color: "#666",
													size: this.data.G.appFontSize - 2
												})
											)
										),
										apivm.h(
											"view",
											null,
											!this.data.pageNot.notList &&
												apivm.h("y-comment", {
													id: "details_comment_box",
													dataMore: this.data.comment,
													scrollMore: this.data.pageNot,
													hint: this.data.comment.listHint,
													data: this.data.listData,
													onScrollTo: this.scrollTo,
													onCckLike: this.cckLike,
													onReply: this.cckReply,
													onItemClick: this.itemClick
												})
										),
										apivm.h(
											"view",
											null,
											!this.data.comment.send.show &&
												apivm.h("view", {
													style:
														"padding-bottom:" +
														(!this.data.pageParam.footerH ? T.safeArea().bottom : 0) +
														"px;"
												})
										)
								  )
								: this.data.errorTitle
								? apivm.h(
										"view",
										null,
										apivm.h(
											"view",
											{style: "padding-bottom:15px;"},
											apivm.h(
												"view",
												{class: "details_title_box"},
												apivm.h(
													"text",
													{
														style: "" + this.data.G.loadConfiguration(4),
														class: "details_title_text"
													},
													this.data.errorTitle
												)
											)
										)
								  )
								: null
						)
					),

					apivm.h("y-comment-send", {
						dataMore: this.data.comment,
						commentDot: this.data.pageNot.notList,
						bottom: this.data.pageType == "page",
						onHintclick: function() {
							return this$1.commentHintClick(true);
						},
						onCckComment: this.cckComment,
						onCckLike: this.cckLike
					})
				),

				apivm.h("y-suspended-btns", {
					dataMore: this.data.suspendedBtn,
					data: this.data.suspendedBtn.data,
					onClick: this.suspendedBtnClick
				}),

				apivm.h("y-comment-send-big", {
					dataMore: this.data.comment,
					bottom: this.data.pageType == "page",
					onSend: this.commentInputSend,
					onClose: this.commentInputClose,
					maxlength: "300"
				}),

				apivm.h("z-actionSheet", {
					dataMore: this.data.actionSheet,
					size: -2,
					cancel: true,
					data: this.data.actionSheet.data,
					onClick: this.keyCallback
				}),

				apivm.h("y-num-input", {
					dataMore: this.data.numInput,
					onFinish: this.keyCallback
				}),

				apivm.h("favorite-around", {
					dataMore: this.data.aroundPage,
					onClick: this.aroundCallback
				}),

				apivm.h("z-alert", {
					dataMore: this.data.alertBox,
					onClick: this.keyCallback
				})
			);
		};

		return MoActivityDetails;
	})(Component);
	MoActivityDetails.css = {
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".details_title_box": {padding: "10px 16px 0"},
		".details_title_text": {fontWeight: "600", color: "#333333"},
		".details_add_box": {
			paddingLeft: "16px",
			flexDirection: "row",
			width: "1px",
			flex: "1"
		},
		".details_add_right_box": {paddingRight: "16px", flexDirection: "row"},
		".details_addContent_box": {
			background: "#FFFFFF",
			borderTop: "10px solid #F8F8F8"
		},
		".details_addContent_hint_box": {
			padding: "15px 15px 0",
			flexDirection: "row",
			alignItems: "center"
		},
		".details_addContent_hint_line": {width: "3px", borderRadius: "10px"},
		".details_addContent_hint_text": {
			color: "#333333",
			fontWeight: "600",
			marginLeft: "5px",
			flex: "1"
		},
		".details_addContent_con_box": {padding: "15px"},
		".details_addContent_con_title": {
			fontWeight: "600",
			color: "#333333",
			marginBottom: "6px"
		},
		".details_addContent_con_addText": {
			fontWeight: "400",
			color: "#999",
			marginTop: "6px"
		}
	};
	apivm.define("mo-activity-details", MoActivityDetails);
	apivm.render(apivm.h("mo-activity-details", null), "body");
})();
