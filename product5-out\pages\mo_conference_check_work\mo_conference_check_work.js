(function() {
	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	var LOAD_ING = "加载中，请稍候...";
	var LOAD_MORE = "点击加载更多";
	var NET_ERR = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否数字
	function isNumber(_obj) {
		return isParameters(_obj) && typeof _obj === "number";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//移除监听
	function removeEventListener(name) {
		if (
			platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			delItemForKey(name, window.baseEventList);
		} else {
			try {
				api.removeEventListener({name: name});
			} catch (e) {}
		}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出actionSheet
	function actionSheet(_param, _callback) {
		var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
		o = setNewJSON(o, _param);
		var oldButton = o.buttons || [],
			newButton = [];
		oldButton.forEach(function(item) {
			newButton.push(isObject(item) ? item : {name: item});
		});
		var actionSheetBox = {
			title: o.title,
			cancel: o.cancelTitle,
			data: newButton,
			active: o.active,
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			},
			pageClose: function pageClose() {
				_param.pageClose && _param.pageClose();
			}
		};

		G.actionSheetPop = actionSheetBox;
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	function getPicture(_param, callback) {
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = setNewJSON(o, _param);
		try {
			if (platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						if (ret.value.granted) {
							getPicture(_param, callback);
						}
						removeEventListener(preName + "Per_" + "getPicture");
					});
					return;
				}
				if (o.sourceType == "camera") {
					api.getPicture(o, function(ret, err) {
						isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	}

	function hasPermission(one_per) {
		if (platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	}

	function requestPermission(one_per, callback, _fName) {
		if (platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				isFunction(callback) && callback(ret, err);
			});
		}
	}

	function confirmPer(perm, _fName, _reason) {
		if (platform() == "app") {
			var has = hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				alert(
					{
						title: "无法使用" + hintWord[perm],
						msg: api.systemType == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret) {
						if (1 == ret.buttonIndex) {
							requestPermission(perm, null, _fName);
						} else {
							sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
	function uploadFile(_item, callback) {
		if (_item._fileAjax || _item.module == "-noUpload") return;
		_item._fileAjax = true;
		_item.state = 1;
		if (_item.showToast) {
			showProgress("上传中");
		}
		var nCallack = function nCallack(ret, err) {
			hideProgress();
			var code = ret ? ret.code : "";
			if (code == 200) {
				var data = ret.data || {};
				_item.state = 2;
				_item.uploadId = data.id;
				_item.otherInfo = data;
			} else {
				_item.state = 3;
				_item.error = ret ? ret.message || ret.data : err.data || err.body || "";
			}
			callback && callback(_item);
		};
		if (platform() == "mp") {
			wx.uploadFile({
				url: appUrl() + "file/upload",
				filePath: _item.url.path,
				name: "file",
				header: {
					"Content-Type": "multipart/form-data",
					"u-login-areaId": areaId(),
					Authorization: getPrefs("sys_token") || ""
				},

				success: function success(res) {
					nCallack(JSON.parse(res.data), null);
				},
				fail: function fail(err) {
					nCallack(null, JSON.parse(err.data));
				}
			});
		} else {
			ajax(
				{u: appUrl() + "file/upload", web: _item.web},
				"file/upload" + _item.url,
				nCallack,
				"上传附件",
				"post",
				{
					files: {file: _item.url}
				},
				{"content-type": "file"}
			);
		}
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE$1 = 60;
	var SECONDS_A_HOUR$1 = SECONDS_A_MINUTE$1 * 60;
	var SECONDS_A_DAY$1 = SECONDS_A_HOUR$1 * 24;
	var SECONDS_A_WEEK$1 = SECONDS_A_DAY$1 * 7;
	var MILLISECONDS_A_SECOND$1 = 1e3;
	var MILLISECONDS_A_MINUTE$1 = SECONDS_A_MINUTE$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_HOUR$1 = SECONDS_A_HOUR$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_DAY$1 = SECONDS_A_DAY$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_WEEK$1 = SECONDS_A_WEEK$1 * MILLISECONDS_A_SECOND$1; // English locales

	var MS$1 = "millisecond";
	var S$1 = "second";
	var MIN$1 = "minute";
	var H$1 = "hour";
	var D$1 = "day";
	var W$1 = "week";
	var M$1 = "month";
	var Q$1 = "quarter";
	var Y$1 = "year";
	var DATE$1 = "date";
	var FORMAT_DEFAULT$1 = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING$1 = "Invalid Date"; // regex

	var REGEX_PARSE$1 = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT$1 = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en$1 = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart$1 = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr$1 = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart$1(hourOffset, 2, "0") +
			":" +
			padStart$1(minuteOffset, 2, "0")
		);
	};
	var monthDiff$1 = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M$1);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M$1);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor$1 = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit$1 = function prettyUnit(u) {
		var special = {
			M: M$1,
			y: Y$1,
			w: W$1,
			d: D$1,
			D: DATE$1,
			h: H$1,
			m: MIN$1,
			s: S$1,
			ms: MS$1,
			Q: Q$1
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined$1 = function isUndefined(s) {
		return s === undefined;
	};
	var U$1 = {
		s: padStart$1,
		z: padZoneStr$1,
		m: monthDiff$1,
		a: absFloor$1,
		p: prettyUnit$1,
		u: isUndefined$1
	};

	var L$1 = "en";
	var Ls$1 = {};
	Ls$1[L$1] = en$1;
	var isDayjs$1 = function isDayjs(d) {
		return d instanceof Dayjs$1;
	};
	var parseLocale$1 = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L$1;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls$1[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls$1[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls$1[name] = preset;
			l = name;
		}
		if (!isLocal && l) L$1 = l;
		return l || (!isLocal && L$1);
	};
	var dayjs$1 = function dayjs(date, c) {
		if (isDayjs$1(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs$1(cfg);
	};
	var wrapper$1 = function wrapper(date, instance) {
		return dayjs$1(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils$1 = U$1;
	Utils$1.l = parseLocale$1;
	Utils$1.i = isDayjs$1;
	Utils$1.w = wrapper$1;
	var parseDate$1 = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils$1.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE$1);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs$1 = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale$1(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate$1(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils$1;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING$1);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs$1(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs$1(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs$1(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils$1.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils$1.u(_startOf) ? _startOf : true;
			var unit = Utils$1.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils$1.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D$1);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils$1.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y$1:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M$1:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W$1: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D$1:
				case DATE$1:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H$1:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN$1:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S$1:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils$1.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M$1] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y$1] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H$1] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN$1] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S$1] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS$1] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D$1 ? this.$D + (_int - this.$W) : _int;
			if (unit === M$1 || unit === Y$1) {
				var date = this.clone().set(DATE$1, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE$1, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils$1.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils$1.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs$1(_this2);
				return Utils$1.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M$1) {
				return this.set(M$1, this.$M + number);
			}
			if (unit === Y$1) {
				return this.set(Y$1, this.$y + number);
			}
			if (unit === D$1) {
				return instanceFactorySet(1);
			}
			if (unit === W$1) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN$1] = MILLISECONDS_A_MINUTE$1),
				(_C$MIN$C$H$C$S$unit[H$1] = MILLISECONDS_A_HOUR$1),
				(_C$MIN$C$H$C$S$unit[S$1] = MILLISECONDS_A_SECOND$1),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils$1.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING$1;
			var str = formatStr || FORMAT_DEFAULT$1;
			var zoneStr = Utils$1.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils$1.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils$1.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils$1.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils$1.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils$1.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils$1.s(this.$s, 2, "0"),
				SSS: Utils$1.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT$1, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils$1.p(units);
			var that = dayjs$1(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE$1;
			var diff = this - that;
			var result = Utils$1.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y$1] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M$1] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q$1] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W$1] = (diff - zoneDelta) / MILLISECONDS_A_WEEK$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[D$1] = (diff - zoneDelta) / MILLISECONDS_A_DAY$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[H$1] = diff / MILLISECONDS_A_HOUR$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN$1] = diff / MILLISECONDS_A_MINUTE$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[S$1] = diff / MILLISECONDS_A_SECOND$1),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils$1.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M$1).$D;
		};
		_proto.$locale = function $locale() {
			return Ls$1[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale$1(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils$1.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto$1 = Dayjs$1.prototype;
	dayjs$1.prototype = proto$1;
	[
		["$ms", MS$1],
		["$s", S$1],
		["$m", MIN$1],
		["$H", H$1],
		["$W", D$1],
		["$M", M$1],
		["$y", Y$1],
		["$D", DATE$1]
	].forEach(function(g) {
		proto$1[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs$1.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs$1, dayjs$1);
			plugin.$i = true;
		}
		return dayjs$1;
	};
	dayjs$1.locale = parseLocale$1;
	dayjs$1.isDayjs = isDayjs$1;
	dayjs$1.unix = function(timestamp) {
		return dayjs$1(timestamp * 1e3);
	};
	dayjs$1.en = Ls$1[L$1];
	dayjs$1.Ls = Ls$1;
	dayjs$1.p = {};

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//打开图片预览
	function openWin_imgPreviewer(_param) {
		G.imgPreviewerPop = {
			show: true,
			index: _param.index,
			imgs: _param.imgs
		};

		console.log(JSON.stringify(G.imgPreviewerPop));
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var PreviewerImg = /*@__PURE__*/ (function(Component) {
		function PreviewerImg(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				frameName: "previewerImg"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.props.dataMore.watermark = G.watermark;
							this.baseInit();
						} else {
							if (platform() == "app" && this.UIPhotoViewer) {
								this.UIPhotoViewer.close();
							}
						}
					}
					if (this.data.show) {
						if (this.nowParam != JSON.stringify(this.props.dataMore)) {
							this.nowParam = JSON.stringify(this.props.dataMore);
							this.sendMessage();
						}
					}
				}
			};
		}

		if (Component) PreviewerImg.__proto__ = Component;
		PreviewerImg.prototype = Object.create(Component && Component.prototype);
		PreviewerImg.prototype.constructor = PreviewerImg;
		PreviewerImg.prototype.baseInit = function() {
			var this$1 = this;

			var data = this.props.dataMore;
			switch (platform()) {
				case "app":
					if (G.watermark) {
						//有水印使用web
						addEventListener(this.data.frameName + "_msg", function() {
							this$1.closePage();
						});
						api.setFrameClient({frameName: this.data.frameName}, function(ret, err) {
							if (ret.state == 2) {
								this$1.isLoading = true;
								setTimeout(function() {
									this$1.sendMessage();
								}, 400);
							}
						});
						return;
					}
					this.UIPhotoViewer = api.require("UIPhotoViewer");
					this.UIPhotoViewer.open(
						{
							images: data.imgs,
							activeIndex: data.index,
							gestureClose: true,
							bgColor: "#000"
						},
						function(ret, err) {
							switch (ret.eventType) {
								case "click":
									this$1.closePage();
									break;
								case "gestureColse":
									this$1.UIPhotoViewer = null;
									this$1.closePage();
									break;
								case "longPress":
									var nowImg = data.imgs[ret.index];
									api.actionSheet(
										{
											buttons: ["保存到相册"],
											cancelTitle: "取消"
										},
										function(ret) {
											switch (ret.buttonIndex) {
												case 1:
													api.saveMediaToAlbum(
														{
															path: nowImg
														},
														function(ret) {
															toast(ret && ret.status ? "已保存到手机相册" : "保存失败");
														}
													);
													break;
											}
										}
									);
									break;
							}
						}
					);
					break;
				case "mp":
					wx.previewImage({
						urls: data.imgs,
						current: data.imgs[data.index],
						complete: function() {
							this$1.closePage();
						}
					});

					break;
				case "web":
					window.addEventListener("message", function(event) {
						this$1.closePage();
					});
					document
						.getElementById(this.data.frameName)
						.addEventListener("load", function() {
							this$1.isLoading = true;
							this$1.sendMessage();
						});
					break;
			}
		};
		PreviewerImg.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		PreviewerImg.prototype.sendMessage = function() {
			if (this.isLoading && this.props.dataMore.show) {
				if (platform() == "web") {
					if (!document.getElementById(this.data.frameName)) {
						return;
					}
					var targetWindow = document.getElementById(this.data.frameName)
						.contentWindow;
					targetWindow.postMessage(this.nowParam, "*");
				} else if (platform() == "app") {
					sendEvent(this.data.frameName + "_open", this.props.dataMore);
				}
			}
		};
		PreviewerImg.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0);"
				},
				this.props.dataMore.show &&
					(platform() == "web" || G.watermark) &&
					apivm.h("frame", {
						id: this.data.frameName,
						class: "xy_100",
						name: this.data.frameName,
						url: shareAddress() + "html/previewerImg.html",
						pageParam: {name: this.data.frameName},
						useWKWebView: true,
						scaleEnabled: true,
						allowEdit: true
					})
			);
		};

		return PreviewerImg;
	})(Component);
	apivm.define("previewer-img", PreviewerImg);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				dotClose: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.dotClose = true;
							setTimeout(function() {
								this$1.data.dotClose = false;
							}, 300);
						} else {
							G.actionSheetPop.pageClose();
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.closePage = function() {
			if (this.data.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			if (this.data.dotClose || _item.disabled) {
				return;
			}
			_item.buttonIndex = _index + 1;
			G.actionSheetPop.callback(_item);
			this.closePage();
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h("view", {
						onClick: function() {
							return this$1.closePage();
						},
						class: "actionSheet_cancel"
					}),
					apivm.h(
						"view",
						{
							class: this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "flex-shrink: 0;"
						},
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					apivm.h(
						"scroll-view",
						{
							class: !this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "height: auto;background: #FFF;flex-shrink: 1;",
							"scroll-y": true
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						(this.props.dataMore.data || []).map(function(item, index) {
							return (
								(isParameters(item.show) ? item.show : true) && [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.itemClick(item, index);
											},
											class: "actionSheet_item",
											style:
												"justify-content:" +
												(item.justify || "center") +
												";opacity: " +
												(item.disabled ? "0.3" : "1") +
												";"
										},
										apivm.h(
											"view",
											null,
											item.icon &&
												apivm.h("a-iconfont", {
													style: "margin-right:10px;",
													name: item.icon,
													color: (isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333",
													size:
														G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
												})
										),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-1) +
													";color:" +
													((isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333")
											},
											item.name
										)
									),
									index != this$1.props.dataMore.data.length - 1 &&
										!this$1.props.dataMore.cancel &&
										apivm.h(
											"view",
											{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
											apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
										)
								]
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-shrink: 0;"},
						this.props.dataMore.cancel &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "actionSheet_item",
									style:
										"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + ";color:#333"},
									this.props.dataMore.cancel || "取消"
								)
							)
					),
					apivm.h("view", {
						style:
							"background:#fff;flex-shrink: 0;padding-bottom:" +
							safeArea().bottom +
							"px;"
					})
				]
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_cancel": {flex: "1", minHeight: "20%", flexShrink: "0"},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "5px 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var ItemPicture = /*@__PURE__*/ (function(Component) {
		function ItemPicture(props) {
			Component.call(this, props);
		}

		if (Component) ItemPicture.__proto__ = Component;
		ItemPicture.prototype = Object.create(Component && Component.prototype);
		ItemPicture.prototype.constructor = ItemPicture;
		ItemPicture.prototype.addPic = function() {
			var this$1 = this;

			var keyItem = this.props.item;
			var max = -1;
			if (isNumber(keyItem.max) && keyItem.max > 0) {
				max = keyItem.max - keyItem.value.length;
			}
			if (max <= 0) {
				toast("最多选择" + keyItem.max + "张图片");
				return;
			}
			actionSheet(
				{
					title: "请选择图片来源",
					buttons: ["相机", "相册"]
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					getPicture(
						{
							sourceType: _index == 1 ? "camera" : "photos",
							destinationType: "url",
							max: max
						},
						function(ret, err) {
							var dataUrl = ret ? ret.data || ret.base64Data : "";
							if (dataUrl) {
								var _item = {showToast: true, url: dataUrl};
								uploadFile(_item, function(ret) {
									if (ret.otherInfo) {
										ret.otherInfo.url = appUrl() + "image/" + ret.otherInfo.newFileName;
										keyItem.value.push(ret.otherInfo);
										this$1.fire("input", this$1.props.item);
									} else {
										toast(ret.error);
									}
								});
							}
						}
					);
				}
			);
		};
		ItemPicture.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			openWin_imgPreviewer({
				index: _index,
				imgs: this.props.item.value.map(function(obj) {
					return obj.url;
				})
			});
		};
		ItemPicture.prototype.delFile = function(e, _item, _index) {
			stopBubble(e);
			delItemForKey(_item, this.props.item.value, "id");
			this.fire("input", this.props.item);
			if (!_item.dotSystemDel) {
				ajax(
					{u: appUrl() + "file/clear"},
					"clear" + _item.id,
					function(ret, err) {},
					"删除附件",
					"post",
					{
						body: JSON.stringify({fileIds: [_item.id]})
					}
				);
			}
		};
		ItemPicture.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding: 13px 0 13px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:13px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						this.props.item.title &&
							apivm.h(
								"text",
								{style: loadConfiguration() + "color:#333;"},
								this.props.item.title,
								this.props.item.max
									? "(" + this.props.item.value.length + "/" + this.props.item.max + ")"
									: ""
							)
					),
					apivm.h(
						"view",
						{style: "flex-direction:row;flex-wrap: wrap;"},
						(Array.isArray(this.props.item.value)
							? this.props.item.value
							: Object.values(this.props.item.value)
						).map(function(nItem, nIndex) {
							return apivm.h(
								"view",
								{
									class: "aItem_pic",
									onClick: function(e) {
										return this$1.openImages(e, nItem, nIndex);
									}
								},
								apivm.h("image", {
									style:
										"width:100%;height:" +
										((G.pageWidth - 16 - (this$1.props.w || 0)) * 0.25 - 16) +
										"px;border:0px;",
									src: showImg(nItem),
									mode: "aspectFill"
								}),
								apivm.h(
									"view",
									{
										style:
											"position:absolute;top: 6px; right: 6px;z-index: 1;width:auto;height:auto;border:0px;",
										onClick: function(e) {
											return this$1.delFile(e, nItem, nIndex);
										}
									},
									apivm.h("a-iconfont", {
										name: "qingkong",
										color: "rgba(0,0,0,0.65)",
										size: G.appFontSize + 3
									})
								)
							);
						}),
						(!this.props.item.max ||
							this.props.item.max > this.props.item.value.length) &&
							apivm.h(
								"view",
								{class: "aItem_pic"},
								apivm.h(
									"view",
									{
										onClick: this.addPic,
										class: "xy_center",
										style:
											"position:relative;top:0;right:0;z-index:1;width:100%;height:" +
											((G.pageWidth - 16 - (this.props.w || 0)) * 0.25 - 16) +
											"px;border:1px solid #ddd;"
									},
									apivm.h("a-iconfont", {
										name: "xinzeng",
										color: "#ddd",
										size: G.appFontSize + 8
									})
								)
							)
					)
				)
			);
		};

		return ItemPicture;
	})(Component);
	ItemPicture.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		},
		".aItem_pic": {
			padding: "16px 16px 0 0",
			width: "25%",
			height: "auto",
			boxSizing: "border-box"
		}
	};
	apivm.define("item-picture", ItemPicture);

	var ZTextarea = /*@__PURE__*/ (function(Component) {
		function ZTextarea(props) {
			Component.call(this, props);
			this.data = {
				textareaId: this.props.id || "z_textarea" + getNum()
			};
		}

		if (Component) ZTextarea.__proto__ = Component;
		ZTextarea.prototype = Object.create(Component && Component.prototype);
		ZTextarea.prototype.constructor = ZTextarea;
		ZTextarea.prototype.installed = function() {
			var this$1 = this;

			this.props.dataMore.textareaId = this.data.textareaId;
			if (this.props.dataMore.autoFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.textareaId).focus();
				}, 500);
			}
		};
		ZTextarea.prototype.input = function(e) {
			var value = e.detail.value;
			if (this.props.dataMore.expression) {
				value = value.replace(new RegExp(this.props.dataMore.expression, "g"), "");
			}
			this.props.dataMore.value = value;
			this.fire("input", e.detail);
		};
		ZTextarea.prototype.blur = function(e) {
			this.fire("blur", e.detail);
		};
		ZTextarea.prototype.focus = function(e) {
			document.getElementById(this.data.textareaId).focus();
			this.fire("focus", e.detail);
		};
		ZTextarea.prototype.render = function() {
			return apivm.h("textarea", {
				id: this.data.textareaId,
				style:
					"" +
					loadConfiguration() +
					(api.systemType == "android" ? "min-" : "") +
					"height: " +
					(this.props.dataMore.height || "250") +
					"px;" +
					(this.props.style || ""),
				class: "z_textarea " + (this.props.class || ""),
				placeholder:
					this.props.dataMore.replyPlaceholder ||
					this.props.dataMore.placeholder ||
					this.props.dataMore.hint ||
					"请输入" + (this.props.dataMore.title || ""),
				"placeholder-style": "color:#ccc;",
				"auto-height": api.systemType == "android",
				value: this.props.dataMore.value,
				onInput: this.input,
				onBlur: this.blur,
				onFocus: this.focus,
				maxlength: this.props.dataMore.maxlength || this.props.dataMore.max
			});
		};

		return ZTextarea;
	})(Component);
	ZTextarea.css = {
		".z_textarea": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			padding: "0",
			width: "100%",
			fontFamily: "none"
		},
		".z_textarea::placeholder": {color: "#ccc"}
	};
	apivm.define("z-textarea", ZTextarea);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var MoConferenceService = /*@__PURE__*/ (function(Component) {
		function MoConferenceService(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				dTitle: "考勤",
				MG: !this.props.dataMore ? G : null,
				emptyBox: {
					type: "load",
					text: ""
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				listData: [],

				checkType: "2", //1报名 2请假 3.撤销请假
				detail: {},
				textarea: {
					placeholder: "请输入请假理由",
					value: ""
				},

				pictureBox: {
					type: "picture",
					show: true,
					title: "上传凭证",
					hint: "请上传凭证",
					key: "pictures",
					value: [],
					defaultValue: [],
					max: 5,
					valueKey: "newFileName",
					valueType: ",",
					noRequired: true
				}
			};
		}

		if (Component) MoConferenceService.__proto__ = Component;
		MoConferenceService.prototype = Object.create(
			Component && Component.prototype
		);
		MoConferenceService.prototype.constructor = MoConferenceService;
		MoConferenceService.prototype.onShow = function() {
			G.onShowNum++;
		};
		MoConferenceService.prototype.baseInit = function(ref) {
			var detail = ref.detail;

			this.id = this.data.pageParam.id || "";
			this.data.checkType = this.data.pageParam.checkType || this.data.checkType;
			this.data.dTitle =
				this.data.checkType == "1"
					? "会议报名"
					: this.data.checkType == "2"
					? "会议请假"
					: "撤销会议请假";
			this.getData(0);
			this.pageRefresh();
		};
		MoConferenceService.prototype.pageRefresh = function() {};
		MoConferenceService.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		MoConferenceService.prototype.getData = function(_type) {
			var this$1 = this;

			this.data.listData = [];
			var postParam = {
				detailId: this.id || "1807952106947108866",
				myMeetUserId: G.uId,
				meetHandleType:
					this.data.checkType == "1" ? 3 : this.data.checkType == "2" ? 2 : 1 //1：撤销请假 2：查询当前用户已报名 3：报名
			};
			if (this.data.pageParam.meetLeaveId) {
				postParam.meetLeaveId = this.data.pageParam.meetLeaveId;
			}
			ajaxProcess(
				{
					url: appUrl() + "zyMeetInfo/findChildren",
					param: setNewJSON(postParam),
					tag: "zyMeetInfo/findChildren",
					name: "列表"
				},
				function(ret, err) {
					var data = ret ? ret.data || {} : {};
					if (data) {
						this$1.data.emptyBox.type = "";
						// this.listData = data.children
						data.children.forEach(function(item) {
							item.check = false;
							this$1.data.listData.push(item);
						});
						this$1.data.detail = data;
						this$1.data.listData.unshift({
							id: this$1.data.detail.id,
							check: false,
							placeAddress: this$1.data.detail.placeAddress,
							beginDate: this$1.data.detail.beginDate,
							endDate: this$1.data.detail.endDate,
							title: this$1.data.detail.title,
							meetStatus: this$1.data.detail.meetStatus,
							createDate: this$1.data.detail.createDate,
							content: this$1.data.detail.content
						});
					}
				}
			);
		};
		MoConferenceService.prototype.changeCheck = function(index) {
			var itemCheck = !this.data.listData[index].check;
			if (!index) {
				//主会
				this.data.listData.forEach(function(v) {
					v.check = itemCheck;
				});
			} else {
				//副会
				if (itemCheck) {
					this.data.listData[index].check = itemCheck;
					this.data.listData[0].check = itemCheck;
				} else {
					this.data.listData[index].check = itemCheck;
					var dataArr = JSON.parse(JSON.stringify(this.data.listData));
					dataArr = dataArr.length ? dataArr.splice(0, 1) : [];
					var arrList = dataArr.map(function(v) {
						return v.check;
					});
					if (
						arrList.find(function(v) {
							return v.check == true;
						})
					);
					else {
						this.data.listData[0].check = false;
					}
				}
			}
		};
		MoConferenceService.prototype.submit = function() {
			var this$1 = this;

			var urlPost = "";
			var postParam = {};
			var arrList = this.data.listData
				.filter(function(v) {
					return v.check;
				})
				.map(function(v) {
					return v.id;
				});
			// let index = arrList.indexOf(this.detail.id)
			// if (index !== -1) {arrList.splice(index, 1)}
			// if(!arrList.length){toast('请至少选择一个日程');return}
			// return
			switch (this.data.checkType) {
				case "1": //报名
					urlPost = "zyMeetUser/signUp";
					postParam = {
						meetIds: arrList,
						meetParentId: this.data.detail.id
					};

					break;
				case "2": //请假
					if (!this.data.textarea.value) {
						toast(this.data.textarea.placeholder);
						return;
					}
					if (!this.data.pictureBox.value.length) {
						toast("请上传请假附件！");
						return;
					}
					urlPost = "zyMeetLeave/add";
					postParam = {
						meetIds: arrList,
						form: {
							meetId: this.data.detail.id,
							meetUserId: G.uId,
							leaveReason: this.data.textarea.value,
							attachmentIds: this.data.pictureBox.value
								.map(function(v) {
									return v.id;
								})
								.join(",")
						}
					};

					break;
				case "3": //撤销请假
					urlPost = "zyMeetLeave/clear";
					postParam = {
						meetIds: arrList,
						detailId: this.data.pageParam.meetLeaveId
					};

					break;
			}

			// console.log(urlPost,'urlPost')
			// console.log(postParam,'postParam')
			// return
			alert(
				{
					title: "提示",
					msg: "您确认提交吗？",
					buttons: ["确定", "取消"]
				},
				function(ret) {
					if (ret.buttonIndex == 1) {
						ajaxProcess(
							{
								url: "" + (appUrl() + urlPost),
								param: setNewJSON(postParam),
								tag: urlPost,
								name: "考勤" + urlPost
							},
							function(ret, err) {
								var code = ret.code || "";
								if (code == 200) {
									var msg =
										this$1.data.checkType == "1"
											? "报名成功！"
											: this$1.data.checkType == "2"
											? "您的请假申请已提交，请等待管理员审核。"
											: "撤销请假成功！";
									sendEvent({name: "submitOk", extra: {msg: msg}});
									this$1.close();
								} else {
									toast(ret.message || "遇到未知错误！");
								}
							}
						);
					}
				}
			);
		};
		MoConferenceService.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		MoConferenceService.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{_this: this, dataMore: this.props.dataMore},
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h(
					"y-scroll-view",
					{_this: this, refresh: true, style: "background:#F8F8F8FF;"},
					apivm.h(
						"view",
						null,
						!this.data.emptyBox.type &&
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									null,
									apivm.h("image", {
										style: "height:145px",
										mode: "aspectFill",
										src: showImg(
											this.data.pageParam.coverImg ||
												this.data.detail.coverImg ||
												"d9fab2de-2b7d-4fce-9b79-4f97ca4b855f.png"
										)
									})
								),
								apivm.h("view", {style: "margin-top:-20px"}),

								apivm.h(
									"view",
									{class: "infomationBox"},

									(Array.isArray(this.data.listData)
										? this.data.listData
										: Object.values(this.data.listData)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											{class: "infomarion_item"},
											apivm.h(
												"view",
												{
													class: "flex-row align-items",
													onClick: function() {
														return this$1.changeCheck(index$1);
													}
												},
												apivm.h("a-iconfont", {
													name: item$1.check ? "yuanxingxuanzhong" : "yuanxingweixuanzhong",
													color: G.appTheme,
													size: G.appFontSize
												}),
												apivm.h(
													"text",
													{
														class: "text_one" + (platform() == "app" ? "" : "2"),
														style:
															loadConfiguration(1) +
															"margin:5px;" +
															(!index$1 ? "font-weight:600;color:" + G.appTheme : "")
													},
													item$1.title
												)
											),
											apivm.h(
												"view",
												{class: "flex-row"},
												apivm.h(
													"text",
													{class: "mar-5", style: loadConfiguration(-2) + "color:#999999"},
													dayjs(item$1.beginDate).format("YYYY-MM-DD HH:mm")
												)
											)
										);
									}),
									this.data.checkType == "2" &&
										apivm.h(
											"view",
											{style: "padding:15px;"},
											apivm.h(
												"text",
												{
													style: loadConfiguration(4) + "font-weight:600;margin-bottom:10px;"
												},
												"请假理由"
											),
											apivm.h("z-textarea", {dataMore: this.data.textarea}),
											apivm.h("item-picture", {
												dataMore: this.props.dataMore,
												item: this.data.pictureBox
											}),
											apivm.h("z-divider", {style: "height:10px;"})
										),
									apivm.h(
										"view",
										{
											style:
												"padding:" +
												(this.data.checkType == "1" ? "50px" : "0") +
												" 16px 0;"
										},
										apivm.h("z-button", {
											color: G.appTheme,
											size: 4,
											round: true,
											onClick: this.submit,
											text:
												this.data.checkType == "1"
													? "立即报名"
													: this.data.checkType == "2"
													? "立即请假"
													: "撤销请假"
										})
									)
								)
							)
					),
					apivm.h(
						"view",
						null,
						this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
					),
					apivm.h("z-empty", {
						_this: this,
						dataMore: this.data.emptyBox,
						onRefresh: this.pageRefresh
					}),
					apivm.h("view", {
						style:
							"padding-bottom:" +
							(!this.data.pageParam.footerH ? safeArea().bottom : 0) +
							"px;"
					})
				)
			);
		};

		return MoConferenceService;
	})(Component);
	MoConferenceService.css = {
		".flex-row": {flexDirection: "row"},
		".flex-warp": {flexWrap: "wrap"},
		".align-items": {alignItems: "center"},
		".space-between": {justifyContent: "space-between"},
		".space-around": {justifyContent: "space-around"},
		".justify-center": {justifyContent: "center"},
		".pad-16": {padding: "0 16px"},
		".pad-5": {padding: "5px 0"},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".infomationBox": {
			background: "#FFFFFF",
			padding: "15px 0",
			borderRadius: "15px 15px 0 0"
		},
		".infomarion_item": {
			margin: "10px 16px",
			padding: "10px",
			background: "#FFFFFF",
			boxShadow: "0 2px 10px 1px #14184021"
		}
	};
	apivm.define("mo-conference-service", MoConferenceService);
	apivm.render(apivm.h("mo-conference-service", null), "body");
})();
