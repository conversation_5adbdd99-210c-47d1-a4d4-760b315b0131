(function() {
	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	var myjs = {};

	//修改时 需同步修改_zy/myjs.js
	myjs.iszx = false;

	myjs.proxy = false;

	myjs.appName = (myjs.iszx ? "政协" : "人大") + "平台版";

	myjs.appUrl = function() {
		// if(T.platform() == "web"){//适配正式测试 不用重复切换
		// 	switch(location.hostname){
		// 		case "*************":
		// 			return "http://*************:810/lzt/";
		// 		case "***************":
		// 			return "http://***************:54386/lzt/";
		// 	}
		// }
		return (
			T.getPrefs("sys_appUrl") ||
			(myjs.iszx
				? "https://productpc.cszysoft.com:20170/lzt/"
				: "https://productpc.cszysoft.com:20169/lzt/")
		);
	};

	myjs.chatHeader = function() {
		return (
			T.getPrefs("sys_chatHeader") || "platform5" + (myjs.iszx ? "zx" : "rd")
		);
	};

	myjs.chatEnvironment = function() {
		return T.getPrefs("sys_chatEnvironment") || "1";
	};

	myjs.tomcatAddress = function() {
		return (
			T.getPrefs("sys_tomcatAddress") ||
			(T.platform() == "web"
				? window.location.protocol == "https:"
					? "https://cszysoft.com:9091/"
					: "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		); //http://**********:8080/ http://cszysoft.com:9090/ https://cszysoft.com:9091/
	};

	myjs.shareAddress = function(_type) {
		if (_type == 1 && T.platform() != "mp") {
			return "../../";
		}
		return (
			T.getPrefs("sys_shareAddress") ||
			(T.platform() == "web"
				? window.location.protocol.indexOf("http") == 0
					? window.location.protocol
					: "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(myjs.iszx ? "platform5zx/" : "platform5rd/")
		);
	};

	//默认当前页面 area下的地区 无则传参地区
	(myjs.areaId = function(_this) {
		return (
			(_this && _this.data && _this.data.area ? _this.data.area.key || "" : "") ||
			T.pageParam(_this).areaId ||
			T.getPrefs("sys_aresId") ||
			T.getPrefs("sys_platform") ||
			""
		);
	}),
		//系统类型：（平台版：platform）（标准版：standard）
		(myjs.systemType = function(_this) {
			return (
				T.pageParam(_this).platform || T.getPrefs("sys_systemType") || "platform"
			);
		});

	myjs.clientId = "zyrdV5TestAccount";

	myjs.clientSecret = "zyrdV5TestPassword";

	myjs.hw_project_id = "0611d8333100251b2fc1c01937b8e6d9";

	myjs.hw_bucket = "zy-soft";

	myjs.hw_header = "ZY";

	var G = {
		pageWidth:
			api.platform == "web"
				? api.winWidth > api.winHeight
					? 600
					: api.winWidth
				: api.winWidth,
		refreshPageSize: 0, //返回当前页刷新列表的条数
		dotRefsresh: false, // 返回当前页是否不刷新
		showSkeleton: true, //是否展示骨架屏
		seachText: "", //搜索词
		seachPlaceholder: "请输入搜索内容", //搜索提示
		firstAjax: false, //首次网络请求是否成功
		dotCloseListener: false, //当前页面不要划动返回
		hasCloseListener: false, //不管其它页面 直接添加关闭监听

		appName: "",
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		careMode: false, //是否启用了关怀模式
		htmlStyle: "", //html级别设置style 置灰等操作
		htmlClass: "", //html级别设置class
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		viewappearFrist: true, //是否首次进入页面

		touchmoveTask: null, //划动元素时 禁用页面划动返回事件
		nTouchmove: false,

		isAppReview: false, //app是否上架期间 隐藏和显示部分功能

		touchmove: function touchmove() {
			G.nTouchmove = true;
			G.touchmoveTask && clearTimeout(G.touchmoveTask);
			G.touchmoveTask = setTimeout(function() {
				G.nTouchmove = false;
			}, 1000);
		},
		//通用组件 start=====================================================

		imagePreviewer: {
			//全局图片预览组件
			show: false,
			imgs: [],
			activeIndex: 0,
			type: 1
		},

		openImgPreviewer: function openImgPreviewer(_param) {
			if (_param === void 0) {
				_param = {};
			}
			if ((_param.imgs || []).length <= 0) {
				return;
			}
			G.imagePreviewer.activeIndex = _param.index || 0;
			G.imagePreviewer.imgs = _param.imgs;
			G.imagePreviewer.show = true;
			T.sendEvent("updatePage");
		},

		areasBox: {
			//地区切换弹窗组件
			show: false,
			type: "half", //full全屏打开  half半屏打开
			pageParam: null
		},

		openAreas: function openAreas(_param, _callback) {
			if (_param === void 0) {
				_param = {};
			}
			G.areasBox.pageParam = _param;
			G.areasBox.show = true;
			T.addEventListener("base_areas_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_areas_callback");
			});
			T.sendEvent("updatePage");
		},

		alertBox: {
			// 确认提示框
			show: false,
			title: "",
			content: "",
			richText: false,
			input: false,
			textarea: false,
			placeholder: "",
			cancel: {show: false, text: "取消", color: "#333333"},
			sure: {show: true, text: "确定", color: "appTheme"}
		},

		alert: function alert(_param, _callback) {
			var o = {title: "", msg: "", buttons: ["确定"]};
			if (T.isObject(_param)) {
				o = T.setNewJSON(o, _param);
			} else {
				o.msg = T.isParameters(_param) ? _param : "";
			}
			G.alertBox.title = o.title;
			G.alertBox.content = (o.msg || o.content || "").toString();
			G.alertBox.input = o.input;
			G.alertBox.textarea = o.textarea;
			G.alertBox.placeholder = o.placeholder;
			G.alertBox.richText = o.richText;
			G.alertBox.cancel.show = o.buttons.length > 1;
			G.alertBox.cancel.text = o.buttons[1];
			G.alertBox.sure.text = o.buttons[0];
			G.alertBox.show = true;
			T.addEventListener("base_alert_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_alert_callback");
			});
			T.sendEvent("updatePage");
		},

		actionSheetBox: {
			show: false,
			cancel: false,
			title: "",
			active: null,
			data: []
		},

		actionSheet: function actionSheet(_param, _callback) {
			var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
			o = T.setNewJSON(o, _param);
			G.actionSheetBox.title = o.title;
			G.actionSheetBox.cancel = o.cancelTitle;
			var oldButton = o.buttons || [],
				newButton = [];
			oldButton.forEach(function(item) {
				newButton.push(T.isObject(item) ? item : {value: item});
			});
			G.actionSheetBox.data = newButton;
			G.actionSheetBox.active = _param.active;
			G.actionSheetBox.dotClose = _param.dotClose;
			G.actionSheetBox.show = true;
			T.addEventListener("base_actionSheet_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_actionSheet_callback");
			});
			T.sendEvent("updatePage");
		},
		//通用组件 end=====================================================

		installed: function installed(_this) {
			var _this2 = this;
			if (_this.props && _this.props.dataMore);
			else {
				G.fitWidth();
				G.changeConfiguration(_this);
				G.appGrayscale();
				G.initOther();
				T.addEventListener("index_login_ok", function(ret, err) {
					G.initOther();
				});
				//字体刷新
				T.addEventListener("changeConfiguration", function(ret, err) {
					G.changeConfiguration(_this);
				});
				//地区刷新监听
				T.addEventListener("areaChange", function(ret, err) {
					var notifas = ["module", "news", "my", "negotiable", "area"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "areaChange_" + _eItem, extra: ret.value});
					});
				});
				//红点刷新
				T.addEventListener("unreadChange", function(ret, err) {
					var notifas = ["module", "my"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "unreadChange_" + _eItem, extra: ret.value});
					});
				});
				if (
					T.isFunction(_this.close) &&
					!T.isParameters(_this.props.pageParam) &&
					!T.isParameters(_this.props.dataMore)
				) {
					T.addEventListener("keyback", function(ret, err) {
						if (G.imagePreviewer.show) {
							G.imagePreviewer.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show) {
							if (G.alertBox.cancel.show) {
								G.alertBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						if (G.actionSheetBox.show) {
							if (G.actionSheetBox.cancel) {
								G.actionSheetBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						_this2.close(_this);
					});
					T.addEventListener("swiperight", function(ret, err) {
						if (G.nTouchmove) {
							return;
						}
						if (G.imagePreviewer.show) {
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show && G.alertBox.cancel.show) {
							G.alertBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.actionSheetBox.show) {
							G.actionSheetBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						_this2.close(_this);
					});
				}
				setTimeout(function() {
					_this2.setHeader(_this);
				}, 10);
			}
			try {
				_this.init();
			} catch (e) {
				console.log(e);
			}
		},
		close: function close(_this) {
			_this.close();
		},
		setHeader: function setHeader(_this) {
			var title = _this.data.title;
			// console.log("=================="+title);
			// console.log(_this.props);
			// console.log(_this.data);
			if (!title) {
				return;
			}
			if (T.platform() == "web") {
				if (window.parent) {
					window.parent.document.title = title;
				} else {
					document.title = title;
				}
			} else if (T.platform() == "mp") {
				wx.setNavigationBarTitle({
					title: title
				});
			}
		},
		//多端页面显示回调 app、h5、小程序
		onShow: function onShow(_this) {
			var _this3 = this;
			if (_this.props.dataMore) {
				return;
			}
			if (G.viewappearFrist) {
				G.viewappearFrist = false;
				return;
			}
			console.log("返回了当前页面：");
			T.sendEvent({name: "changeConfiguration"});
			if (G.areaId != T.getPrefs("sys_aresId")) {
				G.areaId = T.getPrefs("sys_aresId") || "";
				T.sendEvent({name: "areaChange", extra: {key: G.areaId}});
			}
			setTimeout(function() {
				_this3.setHeader(_this);
			}, 10);
			if (_this.getData) {
				//返回刷新一下
				_this.getData(false, {refresh: 1});
			}
		},
		//初始化后其它配置
		initOther: function initOther() {
			G.areaId = T.getPrefs("sys_aresId") || "";
			G.systemtTypeIsPlatform = T.getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.appName = T.getPrefs("sys_systemName") || "";
			G.uId = T.getPrefs("sys_Id") || "";
			G.userId = T.getPrefs("sys_UserID") || "";
			G.userName = T.getPrefs("sys_UserName") || "";
			G.userImg = T.getPrefs("sys_AppPhoto") || "";
			G.specialRoleKeys = JSON.parse(T.getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				G.getItemForKey("dc_admin", G.specialRoleKeys) ||
				G.getItemForKey("admin", G.specialRoleKeys);
			G.v = T.getPrefs("sys_appVersion") || "";
			if (T.platform() == "app") {
				G.isAppReview =
					JSON.parse(T.getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
		},
		//全局配置
		changeConfiguration: function changeConfiguration(_this) {
			G.appFont =
				T.getPrefs("appFont") && T.getPrefs("appFont") != "0"
					? T.getPrefs("appFont")
					: "heitiSimplified";
			G.appFontSize = Number(
				T.getPrefs("appFontSize") && T.getPrefs("appFontSize") != "0"
					? T.getPrefs("appFontSize")
					: "16"
			);
			G.appTheme =
				T.pageParam(_this).appTheme ||
				T.getPrefs("appTheme" + (myjs.iszx ? "zx" : "rd")) ||
				(myjs.iszx ? "#3088FE" : "#C61414");
			var headTheme =
				_this.data.headTheme ||
				T.pageParam(_this).headTheme ||
				T.getPrefs("headTheme") ||
				"#FFF";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			if (T.platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
			}
			_this.update();
			T.sendEvent("updatePage");
		},
		//是否全局置灰
		appGrayscale: function appGrayscale() {
			var appGrayscale = T.getPrefs("appGrayscale") || "0";
			if (T.platform() == "app");
			else {
				// G.htmlStyle = "filter:"+(appGrayscale == 1?'grayscale(1)':'none')+";";//小程序不知道为啥style没用
				G.htmlClass = appGrayscale == 1 ? "filterGray" : "filterNone";
			}
		},
		//展示图片
		showImg: function showImg(_item) {
			var baseUrl = T.isObject(_item) ? _item.url || "" : _item || "";
			baseUrl = G.showAllSystemImg(baseUrl); //先显示系统图片
			if (
				baseUrl.indexOf("http") == 0 &&
				baseUrl.indexOf("http://127.0.0.1") != 0 &&
				baseUrl.indexOf(myjs.tomcatAddress()) != 0
			) {
				//是链接 不是小程序本地链接 不是处理过的链接
				if (myjs.proxy && T.platform() != "app" && baseUrl.indexOf("https") != 0) {
					baseUrl = myjs.tomcatAddress() + "utils/proxyPic?" + baseUrl;
				}
			}
			return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
		},
		showAllSystemImg: function showAllSystemImg(_url) {
			return !_url ||
				_url.indexOf("http") == 0 ||
				_url.indexOf("/") == 0 ||
				_url.indexOf("../") == 0
				? _url
				: myjs.appUrl() + "image/" + _url;
		},
		//图片处理
		cacheImg: function cacheImg(_item, _thumbnail, _url, _priority) {
			if (!T.isObject(_item) || !T.isParameters(_item.url)) return; //没有传对象 或者没有url的时候不处理
			var baseUrl = _item.webImg || _url || _item.url || ""; //存储当前缓存地址
		},
		//字体配置
		loadConfiguration: function loadConfiguration(_changeSize) {
			return (
				"font-size:" +
				((G.appFontSize || 0) + (_changeSize || 0)) +
				"px;font-family:" +
				G.appFont +
				";"
			);
		},
		//宽度配置
		loadConfigurationSize: function loadConfigurationSize(_changeSize, _who) {
			var changeSize = _changeSize || 0,
				returnCss = "",
				cssWidth,
				cssHeight;
			if (T.isArray(_changeSize)) {
				cssWidth = "width:" + (G.appFontSize + (_changeSize[0] || 0)) + "px;";
				cssHeight = "height:" + (G.appFontSize + (_changeSize[1] || 0)) + "px;";
			} else {
				cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
				cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
			}
			if (!_who) {
				returnCss = cssWidth + cssHeight;
			} else {
				returnCss = _who == "w" ? cssWidth : cssHeight;
			}
			return returnCss;
		},
		//获取item	只有一层级的时候 会返回 当前index	_i
		getItemForKey: function getItemForKey(_value, _list, _key, _child) {
			var hasChild = false;
			if (!T.isParameters(_list)) return;
			var listLength = _list.length;
			for (var i = 0; i < listLength; i++) {
				var listItem = _list[i];
				if (T.isArray(listItem)) {
					hasChild = true;
					var result = G.getItemForKey(_value, listItem, _key, true);
					if (result) return result;
				} else {
					if (!T.isObject(listItem)) {
						if (listItem === _value) {
							return listItem;
						}
					} else {
						if (T.isArray(listItem[_key || "key"])) {
							hasChild = true;
							var result = G.getItemForKey(
								_value,
								listItem[_key || "key"],
								_key,
								true
							);
							if (result) {
								listItem["_i"] = i;
								return listItem;
							}
						} else if (listItem[_key || "key"] === _value) {
							listItem["_i"] = i;
							return listItem;
						}
					}
					if (
						T.isObject(listItem) &&
						listItem.children &&
						T.isArray(listItem.children)
					) {
						hasChild = true;
						var result = G.getItemForKey(_value, listItem.children, _key, true);
						if (result) return result;
					}
				}
			}
			if (!_child && !hasChild) return false;
		},
		//在集合中删除第一个参数obj和index都可以或对比字符串	第二个传入集合	第三个为对比key
		delItemForKey: function delItemForKey(_obj, _list, _key) {
			if (T.isTargetType(_obj, "number") && _obj < _list.length) {
				_list.splice(_obj, 1);
			} else {
				var contrastObj = !T.isObject(_obj) ? _obj : _obj[_key || "key"];
				for (var i = 0; i < _list.length; i++) {
					if (
						(!T.isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) ==
						contrastObj
					) {
						_list.splice(i, 1);
						G.delItemForKey(_obj, _list, _key);
						break;
					}
				}
			}
		},
		//是否显示顶部
		showHeader: function showHeader(_this) {
			return T.platform() == "app" || T.pageParam(_this).showHeader;
		},
		//适配多端状态栏
		headerTop: function headerTop() {
			return T.platform() == "app" ? T.safeArea().top : 0;
		},
		//底部可视区域
		footerBottom: function footerBottom(_bottom) {
			return _bottom ? T.safeArea().bottom : 0;
		},
		//适配pc页打开宽度
		fitWidth: function fitWidth() {
			if (
				T.platform() == "web" &&
				document.documentElement.clientWidth > document.documentElement.clientHeight
			) {
				$("body").style.width = "100%";
				$("body").style.maxWidth = "600px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		},
		//获取当前主题色相对应前景色
		getHeadThemeRelatively: function getHeadThemeRelatively(_this) {
			var theme =
				(_this && _this.data && _this.data.headTheme
					? _this.data.headTheme || ""
					: "") || G.headTheme;
			return theme && T.isColorDarkOrLight(theme) == "dark" ? "#FFF" : "#333";
		},
		//转换成html格式
		convertRichText: function convertRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			var textList = value.split("\n");
			var str = "";
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
				if (addText) {
					str += "<p>" + addText + "</p>";
				}
			}
			return str;
		},
		//清空html格式
		clearRichText: function clearRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			//坑爹的后台管理了 & 符号
			value = value.replace(/&amp;/g, "&");
			// 空格处理
			value = value.replace(/(&nbsp;)/g, " ");
			// 换行处理
			value = value.replace(/<br\/?[^>]*>/g, "\n");

			value = value.replace(/<\/[p|div|h1|h2|h3|h4|h5|h6]>/g, "\n");
			value = value.replace(/<\/?[^>]*>/g, "");
			return value;
		},
		//阻止冒泡事件
		stopBubble: function stopBubble(e) {
			if (!e) return;
			if (T.platform() == "web") {
				e.preventDefault();
				e.stopPropagation();
			} else if (T.platform() == "mp") {
				e.$_canBubble = false;
			}
		},
		getTagColor: function getTagColor(_key) {
			if (_key === void 0) {
				_key = "";
			}
			var tagColors = [
				{key: "未处理", value: "#F6631C"},
				{key: "未开始", value: "#F6631C"},

				{key: "签到中", value: "#F6931C"},
				{key: "报名中", value: "#F6931C"},
				{key: "进行中", value: "#F6931C"},

				{key: "请假通过", value: "#50C614"},
				{key: "请假待审批", value: "#F6931C"},
				{key: "请假中", value: "#F6931C"},
				{key: "已参与", value: G.appTheme},
				{key: "待审核", value: "#F6931C"},
				{key: "已通过", value: "#50C614"},
				{key: "有效", value: G.appTheme},
				{key: "待审查", value: "#F6931C"},
				{key: "人大交办中", value: "#F6931C"},
				{key: "政协交办中", value: "#F6931C"},
				{key: "政府交办中", value: "#F6931C"},
				{key: "党委交办中", value: "#F6931C"},
				{key: "两院交办中", value: "#F6931C"},
				{key: "法院交办中", value: "#F6931C"},
				{key: "检察院交办中", value: "#F6931C"},
				{key: "转参阅件", value: "#C61414"},
				{key: "办理中", value: "#F6931C"},
				{key: "重新办理", value: "#F6931C"},
				{key: "已答复", value: "#50C614"},
				{key: "已办结", value: "#559FFF"},
				{key: "A类", value: "#F6931C"},
				{key: "B类", value: "#1A74DA"},
				{key: "待受理", value: "#F6931C"},
				{key: "已受理", value: "#50C614"},
				{key: "已回复", value: "#50C614"},
				{key: "待交付审议", value: "#F6931C"},
				{key: "专委会审议中", value: "#F6931C"},
				{key: "已上传相关资料", value: "#50C614"},
				{key: "留存", value: "#F6931C"},
				{key: "采用", value: "#50C614"}
			];

			var tagColor = G.getItemForKey(_key, tagColors);
			return tagColor ? tagColor.value : "#666666";
		},
		//获取文件类型 并返回数据
		getFileInfo: function getFileInfo(_name) {
			if (_name === void 0) {
				_name = "";
			}
			var name = _name.toLocaleLowerCase(),
				fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
			try {
				if (name.indexOf(".") != -1)
					name = name.split(".")[name.split(".").length - 1];
				switch (name) {
					case "xlsx":
					case "xlsm":
					case "xlsb":
					case "xltx":
					case "xltm":
					case "xls":
					case "xlt":
					case "et":
					case "csv":
					case "uos": //excel格式
						fileInfo.name = "file-excel-fill";
						fileInfo.color = "#00bd76";
						fileInfo.type = "excel";
						fileInfo.convertType = "0";
						break;
					case "doc":
					case "docx":
					case "docm":
					case "dotx":
					case "dotm":
					case "dot":
					case "xps":
					case "rtf":
					case "wps":
					case "wpt":
					case "uot": //word格式
						fileInfo.name = "file-word-fill";
						fileInfo.color = "#387efa";
						fileInfo.type = "word";
						fileInfo.convertType = "0";
						break;
					case "pdf": //pdf格式
						fileInfo.name = "file-pdf-fill";
						fileInfo.color = "#e9494a";
						fileInfo.type = "pdf";
						fileInfo.convertType = "20";
						break;
					case "ppt":
					case "pptx":
					case "pps":
					case "pot":
					case "pptm":
					case "potx":
					case "potm":
					case "ppsx":
					case "ppsm":
					case "ppa":
					case "ppam":
					case "dps":
					case "dpt":
					case "uop": //ppt
						fileInfo.name = "file-ppt-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "ppt";
						fileInfo.convertType = "0";
						break;
					case "bmp":
					case "gif":
					case "jpg":
					case "pic":
					case "png":
					case "tif":
					case "jpeg":
					case "jpe":
					case "icon":
					case "jfif":
					case "dib": //图片格式 case 'webp':
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "image";
						fileInfo.convertType = "440";
						break;
					case "txt": //文本
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#2696ff";
						fileInfo.type = "txt";
						fileInfo.convertType = "0";
						break;
					case "rar":
					case "zip":
					case "7z":
					case "tar":
					case "gz":
					case "jar":
					case "ios": //压缩格式
						fileInfo.name = "file-zip-fill";
						fileInfo.color = "#a5b0c0";
						fileInfo.type = "compression";
						fileInfo.convertType = "19";
						break;
					case "mp4":
					case "avi":
					case "flv":
					case "f4v":
					case "webm":
					case "m4v":
					case "mov":
					case "3gp":
					case "rm":
					case "rmvb":
					case "mkv":
					case "mpeg":
					case "wmv": //视频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#e14a4a";
						fileInfo.type = "video";
						fileInfo.convertType = "450";
						break;
					case "mp3":
					case "m4a":
					case "amr":
					case "pcm":
					case "wav":
					case "aiff":
					case "aac":
					case "ogg":
					case "wma":
					case "flac":
					case "alac":
					case "wma":
					case "cda": //音频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#8043ff";
						fileInfo.type = "voice";
						fileInfo.convertType = "660";
						break;
					case "folder": //文件夹
						fileInfo.name = "folder-2-fill";
						fileInfo.color = "#ffd977";
						fileInfo.type = "folder";
						break;
				}
			} catch (e) {
				console.log(e.message);
			}
			return fileInfo;
		},
		//获取文件大小
		getFileSize: function getFileSize(_fileSize) {
			if (!_fileSize && _fileSize != 0) return "";
			try {
				var size1 = parseFloat((_fileSize / 1024 / 1024).toFixed(1));
				var size2 = parseFloat((_fileSize / 1024).toFixed(1));
				if (size1 >= 1) {
					return size1 + "MB";
				} else if (size2 >= 1) {
					return size2 + "KB";
				} else {
					return parseInt(_fileSize) + "B";
				}
			} catch (e) {
				return _fileSize;
			}
		},
		//选择文件并上传
		chooseFile: function chooseFile(_this, _item, callback) {
			var max = T.isNumber(_item.max) ? _item.max : 0;
			if (T.platform() == "app") {
				if (T.systemType() == "ios") {
					if (!T.confirmPer("storage", "chooseFile")) {
						//存储权限
						T.addEventListener("storage" + "Per_" + "chooseFile", function(ret, err) {
							T.removeEventListener("storage" + "Per_" + "chooseFile");
							if (ret.value.granted) {
								G.chooseFile(_this, _item, callback);
							}
						});
						return;
					}
				} else {
					if (!api.require("zyRongCloud").hasAllFilesPermission()) {
						T.alert(
							{
								title: "提示",
								msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
								buttons: ["确定", "取消"]
							},
							function(ret) {
								if (ret.buttonIndex == "1") {
									api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
										if (ret.status) {
											G.chooseFile(_this, _item, callback);
										}
									});
								}
							}
						);
						return;
					}
				}
				var fileBrowser = api.require("fileBrowser");
				fileBrowser.open({}, function(ret, err) {
					fileBrowser.close();
					setTimeout(function() {
						_item.url = ret.url;
						G.uploadFile(_this, _item, function(ret) {
							callback && callback(ret);
						});
					}, 500);
				});
			} else if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.type = "file";
				h5Input.accept = "";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.click();
				h5Input.onchange = function() {
					var listLength =
						max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = h5Input.files[j];
							G.uploadFile(_this, nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				};
			} else if (T.platform() == "mp") {
				wx.chooseMessageFile({
					count: max != 0 ? max : 9,
					type: "file",
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							(function(j) {
								var nItem = JSON.parse(JSON.stringify(_item));
								nItem.url = res.tempFiles[j];
								G.uploadFile(_this, nItem, function(ret) {
									callback && callback(ret);
								});
							})(i);
						}
					}
				});
			}
		},
		//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
		uploadFile: function uploadFile(_this, _item, callback) {
			if (_item._fileAjax || _item.module == "-noUpload")
				//有传过 或者明确不传
				return;
			_item._fileAjax = true; //是否请求过	有就不再请求
			_item.state = 1;
			if (_item.showToast) {
				T.showProgress("上传中");
			}
			var nCallack = function nCallack(ret, err) {
				T.hideProgress();
				var code = ret ? ret.code : "";
				if (code == 200) {
					var data = ret.data || {};
					_item.state = 2;
					_item.uploadId = data.id;
					_item.otherInfo = data;
				} else {
					_item.state = 3;
					_item.error = ret ? ret.message || ret.data : err.data || "";
				}
				callback && callback(_item);
			};
			if (T.platform() == "mp") {
				wx.uploadFile({
					url: myjs.tomcatAddress() + "utils/proxy",
					filePath: _item.url.path,
					name: "file",
					header: {
						"Content-Type": "multipart/form-data",
						"u-login-areaId": myjs.areaId(_this),
						Authorization: T.getPrefs("sys_token") || ""
					},

					formData: {
						BASE_URL: myjs.appUrl() + "file/upload",
						BASE_TYPE: "file",
						fileName:
							_item.url.name ||
							_item.url.path.substring(_item.url.path.lastIndexOf("/") + 1)
					},

					success: function success(res) {
						nCallack(JSON.parse(res.data), null);
					},
					fail: function fail(err) {
						nCallack(null, JSON.parse(err.data));
					}
				});
			} else {
				T.ajax(
					{u: myjs.appUrl() + "file/upload", _this: _this, web: _item.web},
					"file/upload" + _item.url,
					nCallack,
					"上传附件",
					"post",
					{
						files: {file: _item.url},
						values: {a: 1}
					},
					{
						"content-type": "file" //安卓附件不能传 得用默认的
					}
				);
			}
		},
		getAreaForKey: function getAreaForKey(_key, _dotAll) {
			var rItem = null;
			var areas = JSON.parse(T.getPrefs("sys_areas") || "[]");
			if (!_dotAll || !areas.length) {
				areas = JSON.parse(T.getPrefs("sys_allAreas") || "[]");
			}
			if (areas.length) {
				rItem = G.getItemForKey(_key, areas, "id");
				if (rItem) {
					rItem.name =
						rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
				}
			}
			return rItem || {};
		},
		showTextSize: function showTextSize(_text, _size, _middle) {
			if (_size && _text) {
				_text =
					_text.length > _size
						? _middle
							? _text.substring(0, _size / 2) +
							  "..." +
							  _text.substring(_text.length - _size / 2)
							: _text.substring(0, _size) + "..."
						: _text;
			}
			return _text;
		},
		ajaxAlert: function ajaxAlert(_param, _this, _callback) {
			var _this4 = this;
			var param = {
				title: "提示",
				msg: _param.msg || "",
				buttons: _param.buttons || ["确定", "取消"]
			};

			if (_param.alertParam) {
				param = T.setNewJSON(param, _param.alertParam);
			}
			T.alert(param, function(ret) {
				if (ret.buttonIndex == "1") {
					_this4.ajaxProcess(_param, _this, _callback);
				}
			});
		},
		ajaxProcess: function ajaxProcess(_param, _this, _callback) {
			if (!_param.dotProgress) T.showProgress(_param.toast);
			T.ajax(
				{u: _param.url, _this: _this},
				"ajaxProcess",
				function(ret) {
					if (!_param.dotProgress) T.hideProgress();
					if (!_param.dotToast) T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					if (ret && ret.code == "200") {
						_callback && _callback(ret);
					}
				},
				"\u64CD\u4F5C",
				"post",
				{
					body: JSON.stringify(_param.param)
				}
			);
		}
	};

	var NET_ERR = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//重启
	function rebootApp() {
		if (platform() == "web") {
			window.location.reload();
		} else if (platform() == "mp") {
			wx.reLaunch({url: "/pages/index/index"});
		} else {
			api.rebootApp();
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G$1.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G$1.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G$1.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G$1.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G$1.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G$1.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G$1.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (
			G$1.headTheme != getPrefs("headTheme") &&
			G$1.headTheme != "transparent"
		) {
			o.pageParam.headTheme = G$1.headTheme;
		}
		if (
			G$1.appTheme != getPrefs("appTheme" + G$1.sysSign) &&
			G$1.appTheme != (G$1.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G$1.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出actionSheet
	function actionSheet(_param, _callback) {
		var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
		o = setNewJSON(o, _param);
		var oldButton = o.buttons || [],
			newButton = [];
		oldButton.forEach(function(item) {
			newButton.push(isObject(item) ? item : {name: item});
		});
		var actionSheetBox = {
			title: o.title,
			cancel: o.cancelTitle,
			data: newButton,
			active: o.active,
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			},
			pageClose: function pageClose() {
				_param.pageClose && _param.pageClose();
			}
		};

		G$1.actionSheetPop = actionSheetBox;
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G$1.alertPop = alertBox;
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE$1 = 60;
	var SECONDS_A_HOUR$1 = SECONDS_A_MINUTE$1 * 60;
	var SECONDS_A_DAY$1 = SECONDS_A_HOUR$1 * 24;
	var SECONDS_A_WEEK$1 = SECONDS_A_DAY$1 * 7;
	var MILLISECONDS_A_SECOND$1 = 1e3;
	var MILLISECONDS_A_MINUTE$1 = SECONDS_A_MINUTE$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_HOUR$1 = SECONDS_A_HOUR$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_DAY$1 = SECONDS_A_DAY$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_WEEK$1 = SECONDS_A_WEEK$1 * MILLISECONDS_A_SECOND$1; // English locales

	var MS$1 = "millisecond";
	var S$1 = "second";
	var MIN$1 = "minute";
	var H$1 = "hour";
	var D$1 = "day";
	var W$1 = "week";
	var M$1 = "month";
	var Q$1 = "quarter";
	var Y$1 = "year";
	var DATE$1 = "date";
	var FORMAT_DEFAULT$1 = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING$1 = "Invalid Date"; // regex

	var REGEX_PARSE$1 = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT$1 = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en$1 = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart$1 = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr$1 = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart$1(hourOffset, 2, "0") +
			":" +
			padStart$1(minuteOffset, 2, "0")
		);
	};
	var monthDiff$1 = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M$1);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M$1);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor$1 = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit$1 = function prettyUnit(u) {
		var special = {
			M: M$1,
			y: Y$1,
			w: W$1,
			d: D$1,
			D: DATE$1,
			h: H$1,
			m: MIN$1,
			s: S$1,
			ms: MS$1,
			Q: Q$1
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined$1 = function isUndefined(s) {
		return s === undefined;
	};
	var U$1 = {
		s: padStart$1,
		z: padZoneStr$1,
		m: monthDiff$1,
		a: absFloor$1,
		p: prettyUnit$1,
		u: isUndefined$1
	};

	var L$1 = "en";
	var Ls$1 = {};
	Ls$1[L$1] = en$1;
	var isDayjs$1 = function isDayjs(d) {
		return d instanceof Dayjs$1;
	};
	var parseLocale$1 = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L$1;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls$1[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls$1[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls$1[name] = preset;
			l = name;
		}
		if (!isLocal && l) L$1 = l;
		return l || (!isLocal && L$1);
	};
	var dayjs$1 = function dayjs(date, c) {
		if (isDayjs$1(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs$1(cfg);
	};
	var wrapper$1 = function wrapper(date, instance) {
		return dayjs$1(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils$1 = U$1;
	Utils$1.l = parseLocale$1;
	Utils$1.i = isDayjs$1;
	Utils$1.w = wrapper$1;
	var parseDate$1 = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils$1.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE$1);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs$1 = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale$1(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate$1(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils$1;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING$1);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs$1(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs$1(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs$1(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils$1.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils$1.u(_startOf) ? _startOf : true;
			var unit = Utils$1.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils$1.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D$1);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils$1.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y$1:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M$1:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W$1: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D$1:
				case DATE$1:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H$1:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN$1:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S$1:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils$1.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M$1] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y$1] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H$1] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN$1] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S$1] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS$1] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D$1 ? this.$D + (_int - this.$W) : _int;
			if (unit === M$1 || unit === Y$1) {
				var date = this.clone().set(DATE$1, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE$1, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils$1.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils$1.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs$1(_this2);
				return Utils$1.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M$1) {
				return this.set(M$1, this.$M + number);
			}
			if (unit === Y$1) {
				return this.set(Y$1, this.$y + number);
			}
			if (unit === D$1) {
				return instanceFactorySet(1);
			}
			if (unit === W$1) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN$1] = MILLISECONDS_A_MINUTE$1),
				(_C$MIN$C$H$C$S$unit[H$1] = MILLISECONDS_A_HOUR$1),
				(_C$MIN$C$H$C$S$unit[S$1] = MILLISECONDS_A_SECOND$1),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils$1.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING$1;
			var str = formatStr || FORMAT_DEFAULT$1;
			var zoneStr = Utils$1.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils$1.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils$1.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils$1.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils$1.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils$1.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils$1.s(this.$s, 2, "0"),
				SSS: Utils$1.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT$1, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils$1.p(units);
			var that = dayjs$1(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE$1;
			var diff = this - that;
			var result = Utils$1.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y$1] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M$1] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q$1] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W$1] = (diff - zoneDelta) / MILLISECONDS_A_WEEK$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[D$1] = (diff - zoneDelta) / MILLISECONDS_A_DAY$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[H$1] = diff / MILLISECONDS_A_HOUR$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN$1] = diff / MILLISECONDS_A_MINUTE$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[S$1] = diff / MILLISECONDS_A_SECOND$1),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils$1.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M$1).$D;
		};
		_proto.$locale = function $locale() {
			return Ls$1[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale$1(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils$1.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto$1 = Dayjs$1.prototype;
	dayjs$1.prototype = proto$1;
	[
		["$ms", MS$1],
		["$s", S$1],
		["$m", MIN$1],
		["$H", H$1],
		["$W", D$1],
		["$M", M$1],
		["$y", Y$1],
		["$D", DATE$1]
	].forEach(function(g) {
		proto$1[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs$1.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs$1, dayjs$1);
			plugin.$i = true;
		}
		return dayjs$1;
	};
	dayjs$1.locale = parseLocale$1;
	dayjs$1.isDayjs = isDayjs$1;
	dayjs$1.unix = function(timestamp) {
		return dayjs$1(timestamp * 1e3);
	};
	dayjs$1.en = Ls$1[L$1];
	dayjs$1.Ls = Ls$1;
	dayjs$1.p = {};

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//打开修改密码
	function openWin_password(_param) {
		openWin("mo_password", "../mo_password/mo_password.stml", _param);
	}

	//打开app文本
	function openWin_apptext(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		_item.code = _item.code || "apptext";
		openWin(
			openPage + (_item.id || _item.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开附件预览
	function openWin_filePreviewer(_param) {
		openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
	}

	//获取配置信息 公开的
	function getAppInfos(_param, _callback) {
		var postParam = {
			codes: [
				"systemName",
				"appOnlyHeader",
				"appTomcatAddress",
				"appDownloadUrl",
				"systemNameAreaPrefix",
				"platformAreaId",
				"systemType",
				"infomationTop",
				"appShareAddress",
				"appVersion",
				"appReviewVersion",
				"reddotSwitch",
				"systemGrayscale",
				"systemWatermark",
				"systemLoginContact",
				"appMapWebKey",
				"appInitUserGroup"
			]
		};

		ajax(
			{u: appUrl() + "config/openRead"},
			"config/openRead",
			function(ret, err) {
				var data = ret ? ret.data || "" : "";
				if (data) {
					G$1.headTitle = data.systemName || "";
					setPrefs("sys_systemName", G$1.headTitle);
					setPrefs(
						"sys_chatHeader",
						data.appOnlyHeader ||
							appUrl()
								.split("://")[1]
								.replace(/[^a-zA-Z0-9]/g, "")
					);
					setPrefs("sys_appVersion", data.appVersion || "");
					setPrefs("sys_shareAddress", data.appShareAddress); //app分享地址
					setPrefs("sys_tomcatAddress", data.appTomcatAddress); //保存一下系统服务地址
					setPrefs("sys_appDownloadUrl", data.appDownloadUrl || ""); //app下载地址
					setPrefs("sys_platform", data.platformAreaId || ""); //平台系统最高地区id
					setPrefs("sys_systemType", data.systemType || ""); //系统类型：（平台版：platform）（标准版：standard）
					setPrefs("infomationTop", data.infomationTop || "5"); //资讯头条数量
					setPrefs("sys_appReviewVersion", data.appReviewVersion || "{}"); //上架期间版本号{"ios":"","android":""}
					setPrefs("sys_reddotSwitch", data.reddotSwitch || "0"); //开启红点提示 1是0否
					setPrefs("sys_appInitUserGroup", data.appInitUserGroup || "0"); //是否获取全局通讯录 1是0否
					setPrefs("sys_grayscale", data.systemGrayscale || ""); //置灰
					setPrefs("sys_watermark", data.systemWatermark || ""); //水印
					setPrefs("sys_systemLoginContact", data.systemLoginContact || ""); //登录页面运维人员信息
					setPrefs("sys_systemNameAreaPrefix", data.systemNameAreaPrefix || ""); //是否系统名称加地区前缀
					setPrefs(
						"sys_appMapWebKey",
						data.appMapWebKey || "5e2e9231ea0d9fedc723a86b8f8a5ed2"
					);
				}
				sendEvent({name: "sys_refresh"});
				_callback && _callback();
			},
			"取app配置",
			"post",
			{
				body: JSON.stringify(postParam)
			},
			{Authorization: ""}
		);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G$1.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G$1.chatInfos));
		return G$1.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G$1.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G$1.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G$1.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		[].forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	// 发短信验证码
	function sendCode(_param, _callback) {
		var reg = /^1[3-9]\d{9}$/;
		if (!reg.test(_param.phone)) {
			toast("请输入正确的手机号");
			return;
		}
		showProgress("发送中");
		ajax(
			{u: appUrl() + "open_api/verifyCode/send", web: true},
			"sendcode",
			function(ret, err) {
				hideProgress();
				var code = ret ? ret.code : "";
				if (code == 200) {
					toast("发送成功");
					_callback(ret, err);
				} else {
					toast((ret ? ret.message || ret.data : "") || "发送失败");
				}
			},
			"\u53D1\u9001\u77ED\u4FE1",
			"post",
			{
				body: JSON.stringify({
					sendType: "no_login",
					mobile: _param.phone
				})
			},

			{
				Authorization: ""
			}
		);
	}

	//全局页面引用变量
	var G$1 = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//底部可视区域
	function footerBottom() {
		return safeArea().bottom;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G$1.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G$1.appFont != "heitiSimplified" ? "font-family:" + G$1.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G$1.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G$1.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G$1.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G$1.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G$1.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	// import dayjs from "./dayjs";
	// import { MD5 } from './crypto-ts.js';
	/**
	 * 封装和适配 api相关所有接口 和一些别的
	 */
	var T = {};
	T.NET_ERR = "网络不小心断开了";
	T.NET_OK = "操作成功";
	T.NET_NO = "操作失败，请重试";
	T.JK_ERR = "接口异常，请联系技术";
	T.LOAD_ING = "加载中，请稍候...";
	T.LOAD_MORE = "点击加载更多";
	T.LOAD_ALL = "已加载完";
	T.LOAD_NO = "暂无数据";
	T.LOAD_NOT = "页面尚未加载完成，请刷新重试";

	T.isParameters = function(obj) {
		return obj != null && obj != undefined;
	};
	T.isTargetType = function(obj, typeString) {
		return typeof obj === typeString;
	};
	T.isNumber = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "number");
	};
	T.isObject = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "object");
	};
	T.isArray = function(obj) {
		return T.isParameters(obj) && toString.apply(obj) === "[object Array]";
	};
	T.isFunction = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "function");
	};

	T.setNewJSON = function(obj, newobj, _ifReplace) {
		obj = obj || {};
		newobj = newobj || {};
		var returnObj = {};
		for (var key in obj) {
			returnObj[key] = obj[key];
		}
		for (var key in newobj) {
			if (_ifReplace && returnObj.hasOwnProperty(key)) {
				//是否不替换前者 默认替换
				continue;
			}
			returnObj[key] = newobj[key];
		}
		return returnObj;
	};

	T.getNum = function() {
		return Math.round(Math.random() * 1000000000);
	};

	T.trim = function(str) {
		if (String.prototype.trim) {
			return str == null ? "" : String.prototype.trim.call(str);
		} else {
			return str.replace(/(^\s*)|(\s*$)/g, "");
		}
	};
	T.trimAll = function(str) {
		return str.replace(/\s*/g, "");
	};
	T.removeTag = function(str) {
		if (!str) return str;
		return T.decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	};
	T.decodeCharacter = function(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	};

	T.platform = function() {
		try {
			return api.platform || "";
		} catch (e) {
			return "";
		}
	};

	T.rebootApp = function() {
		if (T.platform() == "web") {
			window.location.reload();
		} else if (T.platform() == "mp") {
			wx.reLaunch({url: "/pages/index/index"});
		} else {
			api.rebootApp();
		}
	};

	T.appName = function() {
		try {
			return myjs.appName || "";
		} catch (e) {
			return "";
		}
	};

	T.systemType = function() {
		try {
			return api.systemType;
		} catch (e) {
			return "";
		}
	};

	T.pageParam = function(_this) {
		try {
			var pageParam =
				(_this && _this.props ? _this.props.pageParam : null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(T.getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (T.platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	};

	T.safeArea = function() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	};

	T.setPrefs = function(key, value) {
		if (!T.isParameters(value)) {
			T.removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {}
	};

	T.getPrefs = function(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			return "";
		}
	};

	T.removePrefs = function(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {}
	};

	T.addEventListener = function(name, callback) {
		var keyback = function keyback(ret, err) {
			T.isFunction(callback) && callback(ret, err);
		};
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			if (!window.baseEventList) {
				window.baseEventList = [];
			}
			if (G.getItemForKey(name, window.baseEventList)) {
				G.delItemForKey(name, window.baseEventList);
			}
			window.baseEventList.push({
				key: name,
				value: keyback
			});
		} else {
			api.addEventListener({name: name}, keyback);
		}
	};

	T.removeEventListener = function(name) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			G.delItemForKey(name, window.baseEventList);
		} else {
			api.removeEventListener({name: name});
		}
	};

	T.sendEvent = function(name, extra) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			var pageframes = window.parent.document.getElementsByTagName("iframe");
			for (var i = 0; i < pageframes.length; i++) {
				if (T.isArray(pageframes[i].contentWindow.baseEventList)) {
					var sendItem = G.getItemForKey(
						T.isObject(name) ? name.name : name,
						pageframes[i].contentWindow.baseEventList
					);
					if (sendItem) {
						sendItem.value({value: T.isObject(name) ? name.extra : extra});
					}
				}
			}
		} else {
			try {
				api.sendEvent(T.isObject(name) ? name : {name: name, extra: extra});
			} catch (e) {}
		}
	};

	T.removeLaunchView = function() {
		try {
			api.removeLaunchView();
		} catch (e) {}
	};

	T.setScreenOrientation = function(orientation) {
		try {
			api.setScreenOrientation({orientation: orientation});
		} catch (e) {}
	};

	T.cancelAjax = function(name) {
		try {
			api.cancelAjax({tag: name});
		} catch (e) {}
	};

	T.ajax = function(url, tag, callback, logText, method, data, header) {
		if (header === void 0) {
			header = {};
		}
		T.cancelAjax(tag);
		var getUrl = url; //请求链接
		var frequency = 0; //网络异常 重复请求次数
		var dataType = "json"; //返回数据类型
		var cacheType = ""; //请求类型
		var paramData = {};
		var areaId = "";
		var timeout = 0;
		var isWeb = "";
		if (T.isObject(url)) {
			getUrl = url.u; //请求链接
			dataType = url.dt || "json"; //
			cacheType = url.t || ""; //
			frequency = url.frequency || 0;
			paramData = url.paramData || {};
			areaId = url.areaId || myjs.areaId(url._this);
			timeout = url.timeout || 0;
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method ? method : "get",
			cache: false,
			timeout: 50,
			dataType: dataType,
			data: T.isObject(data) ? data : {},
			headers: T.setNewJSON(
				{
					"u-login-areaId": areaId,
					Authorization: T.getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header
			)
		};

		o = T.setNewJSON(o, paramData);
		if (T.platform() == "web") {
			delete o.tag;
		}
		if (o.url.indexOf("push/rongCloud") != -1) {
			//融云接口
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					myjs.chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = myjs.chatEnvironment();
			}
		}
		if (
			isWeb &&
			JSON.stringify(o.data) != "{}" &&
			(o.data.body || JSON.stringify(o.data.values) != "{}")
		) {
			//公众端通用 过期时间不传token且置空public_token
			var webToken = "";
			if (
				T.getPrefs("tokenEndTime") &&
				new Date().getTime() < T.getPrefs("tokenEndTime")
			) {
				webToken = T.getPrefs("public_token") || "";
			} else {
				T.removePrefs("public_token");
			}
			(o.headers.Authorization = header.Authorization || webToken || ""),
				(o.headers["u-terminal"] = "PUBLIC");
			// var isBody = o.data.body?true:false;
			// var postParam = isBody?JSON.parse(o.data.body):o.data.values;
			// var signParam = {};
			// function getParam(_obj){
			// 	// console.log(JSON.stringify(_obj));
			// 	for (var key in _obj) {
			// 		var kValue = _obj[key];
			// 		if(T.isObject(kValue) && !T.isArray(kValue)){
			// 			getParam(kValue);
			// 		}else{
			// 			kValue = T.isArray(kValue)?kValue.join(","):kValue;
			// 			if (signParam.hasOwnProperty(key)) {
			// 				signParam[key] += (signParam[key]?',':'') + kValue;
			// 			}else{
			// 				signParam[key] = kValue;
			// 			}
			// 		}
			// 	}
			// }
			// getParam(postParam);
			// var signStr = T.sort_ascii(signParam,"#");
			// postParam.clientId = M.clientId;
			// postParam.token = isWeb;
			// postParam.timestamp = dayjs().valueOf();
			// postParam.nonce = "zyrd";
			// postParam.sign = MD5(signStr + "#" + M.clientId + isWeb + postParam.timestamp + postParam.nonce).toString().toUpperCase()
			// if(isBody){
			// 	o.data.body = JSON.stringify(postParam);
			// }
		}
		var oldContentType = o.headers["content-type"];
		if (myjs.proxy && T.platform() != "app" && o.url.indexOf("https") != 0) {
			//小程序 使用代理 T.platform() == "mp" &&
			var oldUrl = o.url;
			var proxyUrl = myjs.tomcatAddress() + "utils/proxy";
			if (oldUrl.indexOf("?") != -1) {
				o.url = proxyUrl + oldUrl.substring(oldUrl.indexOf("?"));
				oldUrl = oldUrl.substring(0, oldUrl.indexOf("?"));
			} else {
				o.url = proxyUrl;
			}
			o.url +=
				(o.url.indexOf("?") != -1
					? o.url.substring(o.url.indexOf("?")) == "?"
						? ""
						: "&"
					: "?") +
				"BASE_URL=" +
				oldUrl;
			o.url += "&BASE_TYPE=" + oldContentType;
			o.headers["content-type"] = "application/x-www-form-urlencoded";
		}
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (T.platform() == "app" && logText) {
			if (o.method == "post") {
				console.log(logText + "post【" + frequency + "】：" + JSON.stringify(o));
			} else {
				console.log(logText + "get【" + frequency + "】：" + JSON.stringify(o));
			}
		}
		try {
			var cbFun = function cbFun(ret, err) {
				// if(T.isObject(url) && T.isParameters(url._this.ajax)){
				// 	url._this.ajax = true;
				// }
				if (T.isFunction(callback)) {
					if (err) {
						try {
							ret = JSON.parse(err.msg);
							err = null;
						} catch (e) {
							ret = JSON.parse(JSON.stringify(err));
							err = null;
						}
					}
					if (err) {
						// if (frequency > 0) {
						// 	var frequencyUrl = url;
						// 	frequencyUrl.frequency--;
						// 	T.ajax(frequencyUrl, tag, callback, logText, method, data, header);
						// 	return;
						// }
					}
					if (T.platform() == "app" && logText) {
						if (ret)
							console.log("得到" + logText + "返回结果ret：" + JSON.stringify(ret));
						if (err)
							console.log("得到" + logText + "返回结果err：" + JSON.stringify(err));
					}
					if (ret) {
						ret.message = ret.message || ret.msg || "";
						var errcode = ret.code || "";
						if ((errcode == 302 || errcode == 2) && cacheType != "login") {
							//令牌失效
							T.hideProgress();
							T.sendEvent({
								name: "index",
								extra: {type: "verificationToken", errmsg: ret.message}
							});
							// return;
						}
					}
					callback(ret, err, true);
				}
			};
			setTimeout(function() {
				if (T.platform() == "web") {
					var xhr = new XMLHttpRequest();
					xhr.open(o.method, o.url);
					for (var header in o.headers) {
						xhr.setRequestHeader(header, o.headers[header]);
					}
					var sendValue = "";
					if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
						var dValue = o.data.values || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue +=
								(!sendValue ? "" : "&") +
								vItem +
								"=" +
								encodeURIComponent(dValue[vItem]);
						}
					} else if (oldContentType.indexOf("file") != -1) {
						sendValue = new FormData();
						var dValue = o.data.values || {};
						var fileValue = o.data.files || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
						}
						for (var vItem in fileValue) {
							sendValue.append(vItem, fileValue[vItem]);
						}
					} else {
						sendValue = o.data.body || JSON.stringify(o.data.values); //encodeURIComponent web加了之后 不能传递json了
					}
					xhr.send(sendValue);
					xhr.onreadystatechange = function() {
						if (xhr.readyState === XMLHttpRequest.DONE) {
							if (xhr.responseText) {
								var response = this.responseText;
								if (o.dataType == "json") {
									var isJSON = false;
									try {
										response = JSON.parse(response);
										isJSON = true;
									} catch (e) {
										isJSON = false;
									}
									if (isJSON) {
										cbFun(response, null);
									} else {
										cbFun(null, response);
									}
								} else {
									cbFun(response, null);
								}
							} else {
								cbFun(null, {});
							}
						}
					};
				} else {
					api.ajax(o, cbFun);
				}
			}, timeout);
		} catch (e) {
			console.log(e);
		}
	};

	T.openWin = function(name, url, pageParam, _this, allowEdit, _more) {
		var delay = 0;
		url = T.handleSYSLink(url, _this); //先处理跳转链接
		var o = {
			name: name,
			url: T.platform() == "web" ? url.substring(url.lastIndexOf("/") + 1) : url,
			pageParam: pageParam ? pageParam : {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: delay,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (T.isObject(_more)) {
			o = T.setNewJSON(o, _more);
		}
		o.pageParam.headTheme =
			(_this && _this.data && _this.data.headTheme
				? _this.data.headTheme || ""
				: "") ||
			G.headTheme ||
			"";
		o.pageParam.appTheme = G.appTheme || "";
		o.pageParam.areaId = o.pageParam.areaId || myjs.areaId(_this);
		o.pageParam.v = G.v;
		if (o.pageParam.paramSaveKey) {
			T.setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		api.openWin(o);
	};

	T.closeWin = function(_param) {
		var o = {};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				T.removePrefs(api.pageParam.paramSaveKey);
			}
			api.closeWin(o);
		} catch (e) {}
	};

	T.clearCache = function(callback) {
		var o = {};
		try {
			api.clearCache(o, function(ret, err) {
				T.isFunction(callback) && callback(ret, err);
			});
		} catch (e) {}
	};

	T.toast = function(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: "middle",
			global: false
		};

		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.msg = T.isParameters(_param) ? _param : "";
			o.location = location || "middle";
			o.global = global;
		}
		o.msg = o.msg.toString();
		try {
			api.toast(o);
		} catch (e) {}
	};

	T.showProgress = function(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.title = T.isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		try {
			api.showProgress(o);
		} catch (e) {}
	};

	T.hideProgress = function() {
		try {
			api.hideProgress();
		} catch (e) {}
	};

	T.alert = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ["确定"]
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = T.isParameters(_param)?_param:"";
		// }
		// o.msg = o.msg.toString();
		// try{
		// 	api.alert(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.confirm = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ['确定', '取消']
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = _param;
		// }
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.confirm(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.actionSheet = function(_param, _callback) {
		G.actionSheet(_param, _callback);
		// var o = {
		// 	title: '请选择',
		// 	cancelTitle: '取消',
		// 	destructiveTitle: "",
		// };
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.actionSheet(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.getPicture = function(_param, callback) {
		if (!callback) {
			T.toast("请先设置callback");
			return;
		}
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = T.setNewJSON(o, _param);
		try {
			if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				//ios拍照需要加到真实dom能才进onchange
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (T.platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (T.platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!T.confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					//相机相册权限
					T.addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						T.removeEventListener(preName + "Per_" + "getPicture");
						if (ret.value.granted) {
							T.getPicture(_param, callback);
						}
					});
					return;
				}
				if (o.sourceType == "camera" || o.userOne) {
					api.getPicture(o, function(ret, err) {
						T.isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	};

	T.hasPermission = function(one_per) {
		if (T.platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				//判断一堆时 就自己看	一般是一个一个判断
				T.alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	};

	T.requestPermission = function(one_per, callback, _fName) {
		if (T.platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				//把结果 发监听过去
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					T.sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				T.isFunction(callback) && callback(ret, err);
			});
		}
	};

	T.confirmPer = function(perm, _fName, _reason) {
		if (T.platform() == "app") {
			var has = T.hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				T.confirm(
					{
						title: "无法使用" + hintWord[perm],
						msg: T.systemType() == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret, err) {
						if (1 == ret.buttonIndex) {
							T.requestPermission(perm, null, _fName);
						} else {
							T.sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	};

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	T.handleSYSLink = function(_link, _this, myParam) {
		if (_link === void 0) {
			_link = "";
		}
		// if(_link.indexOf("http") != 0){
		// 	return _link;
		// }
		myParam = myParam || {};
		//index.html?token={{token}}&userId={{userId}}
		_link = _link.replace("{{tomcatAddress}}", myjs.tomcatAddress());
		_link = _link.replace("{{shareAddress}}", myjs.shareAddress());
		_link = _link.replace(
			"{{token}}",
			encodeURIComponent(T.getPrefs("sys_token"))
		); //当前app登录用户的token，例如：bearer eyJhbGciOiJ...
		_link = _link.replace("{{sysUrl}}", myjs.appUrl()); //当前app请求系统地址，例如：http://**************:54386/lzt/
		_link = _link.replace("{{areaId}}", myParam.areaId || myjs.areaId(_this)); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", myjs.iszx); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			//是app内页面 带上特有参数如果没有
			if (_link.indexOf("sysUrl-zy-") == -1) {
				//没有带地址
				_link += "-zyz-sysUrl-zy-" + myjs.appUrl();
			}
			if (_link.indexOf("sysAreaId-zy-") == -1) {
				//没有带地区
				_link += "-zyz-sysAreaId-zy-" + (myParam.areaId || myjs.areaId(_this));
			}
			if (_link.indexOf("iszx-zy-") == -1) {
				//没有带人大政协判断
				_link += "-zyz-iszx-zy-" + myjs.iszx;
			}
			if (_link.indexOf("appTheme-zy-") == -1) {
				//没有带主题
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			}
			if (_link.indexOf("careMode-zy-") == -1) {
				//没有唯一标识
				_link += "-zyz-careMode-zy-" + G.careMode;
			}
		}
		return _link;
	};

	//按照ascii 排序
	T.sort_ascii = function(obj, _default) {
		var arr = [];
		var num = 0;
		for (var i in obj) {
			arr[num] = i;
			num++;
		}
		var sortArr = arr.sort();
		var str = "";
		for (var _i = 0; _i < sortArr.length; _i++) {
			var sValue = obj[sortArr[_i]];
			str +=
				sortArr[_i] +
				"=" +
				(T.isTargetType(sValue, "number")
					? sValue
					: T.isObject(sValue)
					? JSON.stringify(sValue)
					: sValue || _default) +
				"&";
		}
		var char = "&";
		str = str.replace(new RegExp("^\\" + char + "+|\\" + char + "+$", "g"), "");
		return str;
	};

	/** 判断颜色属于深色还是浅色*/
	T.isColorDarkOrLight = function(hexcolor) {
		try {
			var colorrgb = T.colorRgb(hexcolor);
			var colors = colorrgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
			var red = colors[1];
			var green = colors[2];
			var blue = colors[3];
			var brightness;
			brightness = red * 299 + green * 587 + blue * 114;
			brightness = brightness / 255000;
			if (brightness >= 0.5) {
				return "light";
			} else {
				return "dark";
			}
		} catch (e) {
			return "";
		}
	};

	//16进制颜色转化为RGB颜色
	T.colorRgb = function(str) {
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var sColor = str.toLowerCase();
		if (sColor && reg.test(sColor)) {
			if (sColor.length === 4) {
				var sColorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
				}
				sColor = sColorNew;
			}
			var sColorChange = [];
			for (var i = 1; i < 7; i += 2) {
				sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
			}
			return "rgb(" + sColorChange.join(",") + ")";
		} else {
			return sColor;
		}
	};

	/** 16进制颜色 转换成rgba颜色	可设置透明 */
	T.colorRgba = function(_color, _alpha) {
		if (!_color) return;
		// 16进制颜色值的正则
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		// 把颜色值变成小写
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(T.isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	};

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var ZSearch = /*@__PURE__*/ (function(Component) {
		function ZSearch(props) {
			Component.call(this, props);
			this.data = {
				propValue: ""
			};
			this.compute = {
				id: function() {
					return this.props.id || "input";
				}
			};
		}

		if (Component) ZSearch.__proto__ = Component;
		ZSearch.prototype = Object.create(Component && Component.prototype);
		ZSearch.prototype.constructor = ZSearch;
		ZSearch.prototype.installed = function() {
			var this$1 = this;
			if (
				T.isParameters(this.props.dataMore) ? this.props.dataMore.autofocus : false
			) {
				//是否自动获取焦点
				setTimeout(function() {
					$("#" + this$1.id).focus();
				}, 150);
			}
		};
		ZSearch.prototype.inputConfirm = function() {
			var this$1 = this;

			$("#" + this.id).blur();
			setTimeout(function() {
				this$1.fire("confirm", {});
			}, 0);
		};
		ZSearch.prototype.inputIng = function() {
			var this$1 = this;

			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.input = this.props.dataMore.input.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			setTimeout(function() {
				this$1.fire("input", {});
			}, 0);
		};
		ZSearch.prototype.inputBlur = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("blur", {});
			}, 0);
		};
		ZSearch.prototype.inputFocus = function() {
			var this$1 = this;

			$("#" + this.id).focus();
			setTimeout(function() {
				this$1.fire("focus", {});
			}, 0);
		};
		ZSearch.prototype.clean = function() {
			var this$1 = this;

			if (!this.i) {
				this.props.dataMore.input = "";
				this.i = 1;
			} else {
				this.props.dataMore.input = " ".repeat(this.i++ % 2);
				this.props.dataMore.input = " ".repeat(this.i++ % 2);
			}
			this.inputIng();
			setTimeout(function() {
				this$1.fire("clean", {});
			}, 0);
		};
		ZSearch.prototype.openLeftFilters = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("leftFilters", {});
			}, 0);
		};
		ZSearch.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "z_search_box " + (this.props.class || ""),
					style:
						"\n\tborder-top-left-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-top-right-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-bottom-left-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-bottom-right-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tbackground: " +
						(this.props.bg || "#F4F5F7") +
						";\n\tjustify-content: " +
						(this.props.justify ||
							(this.props.type == "1" ? "center" : "flex-start")) +
						";\n\t" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					{style: "width:auto;height:auto;"},
					T.isParameters(this.props.dataMore) &&
						this.props.dataMore.leftFilters &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.openLeftFilters();
								},
								style:
									"margin-left:-5px;margin-right:15px;flex-direction:row;align-items: center;"
							},
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(1) +
										"font-weight: 400;color: #333;margin-right:4px;"
								},
								this.props.leftFiltersText
							),
							apivm.h("a-iconfont", {
								name: "xiangxia",
								color: "#333",
								size: G.appFontSize - 4
							}),
							apivm.h("view", {
								style: "width:1px;height:24px;background:#E5E7E8;margin-left:8px;"
							})
						)
				),
				apivm.h(
					"view",
					{style: "width:auto;height:auto;"},
					(!T.isParameters(this.props.dataMore) ||
						(!this.props.dataMore.dotIcon && !this.props.dataMore.leftFilters)) &&
						apivm.h(
							"view",
							{class: "z_search_input_icon", style: "margin-right:10px;"},
							apivm.h("a-iconfont", {
								class: "z_search_icon",
								name: "sousuo",
								color: "#999",
								size: G.appFontSize - 2
							})
						)
				),
				this.props.type == "1"
					? apivm.h(
							"text",
							{
								class: "z_search_text",
								style:
									"color:#999;line-height: " +
									(G.appFontSize + 2) +
									"px;" +
									G.loadConfiguration(-2)
							},
							this.props.placeholder
					  )
					: [
							apivm.h("input", {
								id: this.id,
								style:
									"" + G.loadConfiguration() + (this.props.dataMore.inputStyle || ""),
								"placeholder-style": "color:#999;",
								class: "z_search_input",
								type: this.props.inputType || "text",
								placeholder: this.props.placeholder,
								onInput: function(e) {
									if (typeof this$1 != "undefined") {
										this$1.props.dataMore.input = e.target.value;
									} else {
										this$1.data.this.props.dataMore.input = e.target.value;
									}
									this$1.inputIng(e);
								},
								maxlength: this.props.dataMore.maxlength,
								"confirm-type": this.props.confirmType || "search",
								"keyboard-type": this.props.keyboardType || "default",
								onConfirm: this.inputConfirm,
								onBlur: this.inputBlur,
								onFocus: this.inputFocus,
								value:
									typeof this == "undefined"
										? this.data.this.props.dataMore.input
										: this.props.dataMore.input
							}),
							this.props.dataMore.input &&
								!this.props.dataMore.dotCleanIcon &&
								apivm.h(
									"view",
									{
										onClick: this.clean,
										class: "z_search_input_icon",
										style: "padding: 5px;padding-left:0;margin-right: -10px;"
									},
									apivm.h("a-iconfont", {
										name: "qingkong",
										color: "#666",
										size: G.appFontSize
									})
								)
					  ]
			);
		};

		return ZSearch;
	})(Component);
	ZSearch.css = {
		".z_search_box": {
			width: "100%",
			height: "100%",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "2px 15px"
		},
		".z_search_text": {marginLeft: "4px"},
		".z_search_input": {
			paddingLeft: "0px",
			paddingRight: "10px",
			width: "1px",
			flex: "1",
			background: "transparent",
			borderColor: "transparent",
			color: "#333"
		},
		".z_search_input::placeholder": {color: "#999"},
		".z_search_input_icon": {
			alignItems: "center",
			justifyContent: "center",
			width: "auto",
			height: "auto"
		}
	};
	apivm.define("z-search", ZSearch);

	var ADivider = /*@__PURE__*/ (function(Component) {
		function ADivider(props) {
			Component.call(this, props);
			this.compute = {
				boxClass: function() {
					return "a-divider " + (this.props.class || "");
				},
				lineStyle: function() {
					return this.props["line-color"]
						? "border-top-color:" + this.props["line-color"] + ";"
						: "";
				},
				textStyle: function() {
					return (
						(this.props.color ? "color:" + this.props.color + ";" : "") +
						"" +
						(this.props.style || "")
					);
				}
			};
		}

		if (Component) ADivider.__proto__ = Component;
		ADivider.prototype = Object.create(Component && Component.prototype);
		ADivider.prototype.constructor = ADivider;
		ADivider.prototype.lineClass = function(position) {
			var width =
				this.props["content-position"] == position
					? "a-divider_line-width"
					: "a-divider_line-flex";
			var style = this.props.dashed
				? "a-divider_line-dashed"
				: "a-divider_line-solid";
			return "a-divider_line " + width + " " + style;
		};
		ADivider.prototype.render = function() {
			return apivm.h(
				"view",
				{class: this.boxClass, style: this.props.style || ""},
				this.props.content
					? [
							apivm.h("view", {class: this.lineClass("left"), style: this.lineStyle}),
							apivm.h(
								"text",
								{class: "a-divider_text", style: this.textStyle},
								this.props.content
							),
							apivm.h("view", {class: this.lineClass("right"), style: this.lineStyle})
					  ]
					: apivm.h("view", {class: this.lineClass("center"), style: this.lineStyle})
			);
		};

		return ADivider;
	})(Component);
	ADivider.css = {
		".a-divider": {flexDirection: "row", alignItems: "center"},
		".a-divider_line": {borderTopWidth: "1px", borderTopColor: "#F6F6F6"},
		".a-divider_line-solid": {borderTopStyle: "solid"},
		".a-divider_line-dashed": {borderTopStyle: "dashed"},
		".a-divider_line-width": {width: "10%"},
		".a-divider_line-flex": {flex: "1"},
		".a-divider_text": {
			fontSize: "14px",
			color: "#969799",
			padding: "0 16px",
			textAlign: "center",
			maxWidth: "75%"
		}
	};
	apivm.define("a-divider", ADivider);

	var ZActionsheetInput = /*@__PURE__*/ (function(Component) {
		function ZActionsheetInput(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				search: {
					input: "",
					autofocus: false,
					dotIcon: true,
					inputStyle: "text-align: center;"
				}
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.autofocus && $("#" + this.props.dataMore.key)) {
								setTimeout(function() {
									$("#" + this$1.props.dataMore.key).focus();
								}, 150);
							}
							this.data.search.input = this.props.dataMore.input;
						} else {
							//重置 不让input重复绑定 导致不返回数据
							this.data.search.input = "";
							this.data.search = JSON.parse(JSON.stringify(this.data.search));
						}
					}
				}
			};
		}

		if (Component) ZActionsheetInput.__proto__ = Component;
		ZActionsheetInput.prototype = Object.create(Component && Component.prototype);
		ZActionsheetInput.prototype.constructor = ZActionsheetInput;
		ZActionsheetInput.prototype.installed = function() {};
		ZActionsheetInput.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		ZActionsheetInput.prototype.penetrate = function() {};
		ZActionsheetInput.prototype.itemClick = function() {
			var this$1 = this;

			this.props.dataMore.input = this.data.search.input;
			setTimeout(function() {
				this$1.fire("click", this$1.props.dataMore);
				this$1.closePage();
			}, 0);
		};
		ZActionsheetInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "actionSheetInput_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "flex:1;min-height:30%;flex-shrink: 0;"
				}),
				apivm.h(
					"view",
					{
						class: "actionSheetInput_warp",
						onClick: function() {
							return this$1.penetrate();
						}
					},
					apivm.h(
						"view",
						{class: "actionSheetInput_header_warp"},
						apivm.h(
							"view",
							{class: "actionSheetInput_header_main"},
							apivm.h(
								"text",
								{
									style: G.loadConfiguration(1) + "color:#333",
									class: "actionSheetInput_header_main_text"
								},
								this.props.dataMore.value
							)
						),
						apivm.h(
							"view",
							{class: "actionSheetInput_header_left_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "actionSheetInput_header_btn"
									},
									apivm.h(
										"text",
										{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"取消"
									)
								)
							)
						),
						apivm.h(
							"view",
							{class: "actionSheetInput_header_right_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick();
										},
										class: "actionSheetInput_header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						)
					),
					apivm.h(
						"view",
						{style: "width:100%;height:1px;padding:0 16px;"},
						apivm.h("a-divider", null)
					),
					apivm.h(
						"view",
						{style: "align-items: center;min-height:300px;padding-top:70px;"},
						apivm.h(
							"view",
							null,
							this.props.dataMore.fileInfo.type == "text"
								? apivm.h(
										"text",
										{style: G.loadConfiguration(1) + "color:#333;margin:20px 4px;"},
										this.props.dataMore.fileInfo.text
								  )
								: this.props.dataMore.fileInfo.type == "img"
								? apivm.h("img", {src: "", alt: ""})
								: apivm.h("a-iconfont", {
										style: "font-weight:" + (this.props.dataMore.weight || "400") + ";",
										name: this.props.dataMore.fileInfo.name,
										color:
											this.props.dataMore.fileInfo.color == "appTheme"
												? G.appTheme
												: this.props.dataMore.fileInfo.color || "#fff",
										size:
											G.appFontSize +
											(T.isParameters(this.props.dataMore.size)
												? Number(this.props.dataMore.size)
												: 4)
								  })
						),
						apivm.h(
							"view",
							{style: "padding:20px 16px;width: 100%;"},
							this.data.show
								? apivm.h(
										"view",
										{style: "height:36px;width:100%;"},
										apivm.h("z-search", {
											id: this.props.dataMore.key,
											dataMore: this.data.search,
											onConfirm: function() {
												return this$1.itemClick();
											},
											placeholder: this.props.dataMore.placeholder
										})
								  )
								: null
						)
					)
				)
			);
		};

		return ZActionsheetInput;
	})(Component);
	ZActionsheetInput.css = {
		".actionSheetInput_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".actionSheetInput_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			height: "auto"
		},
		".actionSheetInput_header_warp": {
			height: "49px",
			flexDirection: "row",
			width: "100%",
			alignItems: "center"
		},
		".actionSheetInput_header_btn": {
			height: "49px",
			width: "auto",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".actionSheetInput_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".actionSheetInput_header_main": {
			flex: "1",
			width: "1px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "0 49px"
		},
		".actionSheetInput_header_main_text": {fontWeight: "600", maxWidth: "200px"},
		".actionSheetInput_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row"
		},
		".actionSheetInput_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row-reverse"
		}
	};
	apivm.define("z-actionSheet-input", ZActionsheetInput);

	var ZTabs = /*@__PURE__*/ (function(Component) {
		function ZTabs(props) {
			Component.call(this, props);
			this.data = {
				oldData: [],

				scrollIView: "",
				scrollIViewAnimation: true,
				itemDotBounces: this.props.dotBounces || false
			};
			this.compute = {
				itemData: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						return [];
					}
					var hasChange = false;
					if (this.data.oldData.length != this.props.dataMore.data.length) {
						hasChange = true;
					} else {
						for (var i = 0; i < this.props.dataMore.data.length; i++) {
							if (
								this.props.dataMore.data[i].key != this.data.oldData[i].key ||
								this.props.dataMore.data[i].value != this.data.oldData[i].value
							) {
								hasChange = true;
								break;
							}
						}
					}
					if (hasChange) {
						this.data.oldData = this.props.dataMore.data.concat([]);
						if (this.props.type == "4") {
							setTimeout(function() {
								this$1.tabClick(this$1.data.oldData[this$1.data.oldData.length - 1]);
							}, 30);
						}
					}
					return this.data.oldData;
				}
			};
		}

		if (Component) ZTabs.__proto__ = Component;
		ZTabs.prototype = Object.create(Component && Component.prototype);
		ZTabs.prototype.constructor = ZTabs;
		ZTabs.prototype.installed = function() {
			if (!this.props.dataMore) {
				return;
			}
			if (
				!this.props.dotDefault &&
				!G.getItemForKey(this.props.dataMore.key, this.itemData, "key")
			) {
				this.tabClick(
					G.getItemForKey(this.props.dataMore.defaultKey, this.itemData, "key") ||
						this.itemData[0]
				);
			}
		};
		ZTabs.prototype.tabClick = function(_item) {
			var this$1 = this;

			if (!_item) {
				return;
			}
			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			//不需要默认的时候 可以取消tab
			if (this.props.dotDefault && this.props.dataMore.key == _item.key) {
				this.props.dataMore.key = "";
				setTimeout(function() {
					this$1.fire("change", {key: this$1.props.dataMore.key});
				}, 0);
				return;
			}
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;
			setTimeout(function() {
				this$1.fire("change", {key: this$1.props.dataMore.key});
			}, 0);
			var nowView = this.props.id + "_tabs_" + _item.key;
			try {
				if (T.platform() == "app") {
					document.getElementById(this.props.id).scrollTo({view: nowView});
				} else if (T.platform() == "mp") {
					this.data.scrollIView = nowView;
				} else {
					document.getElementById(this.props.id).scrollTo({
						left: document.getElementById(nowView).offsetLeft,
						behavior: "smooth" //带动画划动
					});
				}
			} catch (e) {}
		};
		ZTabs.prototype.nTouchmove = function() {
			G.touchmove();
		};
		ZTabs.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style: "width:100%;height:40px;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				!this.props.type || this.props.type == 1
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab_box",
										style:
											(T.platform() != "app" ? "display: inline-block;" : "") +
											"width:" +
											(this$1.props.average
												? 100 / this$1.itemData.length + "%"
												: "auto") +
											";"
									},
									apivm.h(
										"view",
										{
											class: "zy-tab",
											style:
												"margin-left:" +
												(!index$1 ? "8" : "0") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "8" : "0") +
												"px;"
										},
										apivm.h(
											"view",
											{style: "align-items: center;"},
											apivm.h(
												"text",
												{
													style:
														G.loadConfiguration(this$1.props.size) +
														";color:#" +
														(this$1.props.dataMore.key == item$1.key ? "000" : "666") +
														";font-weight: " +
														(this$1.props.dataMore.key == item$1.key ? "8" : "4") +
														"00;",
													class: "zy-tab__text"
												},
												item$1.value
											),
											apivm.h("view", {
												class: "zy-tab__line",
												style:
													"background:" +
													(this$1.props.dataMore.key != item$1.key
														? "transparent"
														: this$1.props.bg) +
													";"
											})
										)
									)
								);
							})
					  )
					: this.props.type == 2
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type2_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type2",
											style:
												"background:" +
												(this$1.props.dataMore.key == item$1.key
													? this$1.props.bg
													: "#F8F8F8") +
												";margin-left:" +
												(!index$1 ? "16" : "5") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "5") +
												"px;"
										},
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";line-height:" +
													((this$1.props.size || 0) + 16) * 1.5 +
													"px;color:" +
													(this$1.props.dataMore.key == item$1.key ? "#FFF" : "#666"),
												class: "zy-tab-type2__text"
											},
											item$1.value
										)
									)
								);
							})
					  )
					: this.props.type == 3
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type3_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type3",
											style:
												"margin-left:" +
												(!index$1 ? "16" : "6") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "6") +
												"px;"
										},
										apivm.h(
											"view",
											{class: "zy-tab-hot_box"},
											T.isParameters(item$1.badge) &&
												item$1.badge > 0 &&
												apivm.h(
													"view",
													{class: "zy-tab-hot_warp"},
													apivm.h(
														"text",
														{
															style:
																"font-size:" + ((this$1.props.size || 0) + 16 - 6) + "px;",
															class: "zy-tab-hot_text"
														},
														item$1.badge
													)
												)
										),
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";line-height:" +
													((this$1.props.size || 0) + 16) * 1.4 +
													"px;color:" +
													(this$1.props.dataMore.key == item$1.key
														? this$1.props.bg
														: "#666"),
												class: "zy-tab-type3__text"
											},
											item$1.value
										),
										apivm.h("view", {
											class: "zy-tab-type3__line",
											style:
												"background:" +
												(this$1.props.dataMore.key != item$1.key
													? "transparent"
													: this$1.props.bg) +
												";"
										})
									)
								);
							})
					  )
					: this.props.type == 4
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type4_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type4",
											style:
												"margin-left:" +
												(!index$1 ? "16" : "0") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "0") +
												"px;"
										},
										apivm.h(
											"view",
											{style: "display:" + (index$1 ? "flex;" : "none") + ";"},
											apivm.h("a-iconfont", {
												style: "margin:0 10px;transform: rotate(180deg) scale(0.7,0.7);",
												name: "fanhui1",
												color: index$1 ? "#999" : "rgba(0,0,0,0)",
												size: G.appFontSize - 4
											})
										),
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";line-height:" +
													((this$1.props.size || 0) + 16) * 1.4 +
													"px;color:" +
													(index$1 == this$1.itemData.length - 1 && index$1 != 0
														? "#333"
														: "#999"),
												class: "zy-tab-type4__text"
											},
											item$1.value
										)
									)
								);
							})
					  )
					: this.props.type == 5
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type5_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type5",
											style:
												"border-radius:" +
												(!index$1
													? "2px 0 0 2px"
													: index$1 == this$1.itemData.length - 1
													? "0 2px 2px 0"
													: "0") +
												";z-index:" +
												(this$1.props.dataMore.key == item$1.key ? 1 : 0) +
												";background:" +
												(this$1.props.dataMore.key == item$1.key
													? this$1.props.bg
													: "#FFF") +
												";margin-left:" +
												(!index$1 ? "16" : "0") +
												"px;margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "-1") +
												"px;border: 1px solid " +
												(this$1.props.dataMore.key == item$1.key
													? this$1.props.bg
													: "#333") +
												";"
										},
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";color:" +
													(this$1.props.dataMore.key == item$1.key ? "#FFF" : "#333") +
													";",
												class: "zy-tab-type5__text"
											},
											item$1.value
										)
									)
								);
							})
					  )
					: this.props.type == 6
					? apivm.h(
							"scroll-view",
							{
								id: this.props.id,
								class: "zy-tab-box",
								"scroll-into-view": this.data.scrollIView,
								"scroll-with-animation": this.data.scrollIViewAnimation,
								style:
									"" +
									(T.platform() != "app" ? "display: block;white-space: nowrap;" : ""),
								bounces: !this.data.itemDotBounces,
								"scroll-x": true,
								"scroll-y": false,
								"show-scrollbar": false
							},
							(Array.isArray(this.itemData)
								? this.itemData
								: Object.values(this.itemData)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										id: this$1.props.id + "_tabs_" + item$1.key,
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchStart: this$1.nTouchmove,
										onClick: function() {
											return this$1.tabClick(item$1);
										},
										class: "zy-tab-type2_box",
										style: "" + (T.platform() != "app" ? "display: inline-block;" : "")
									},
									apivm.h(
										"view",
										{
											class: "zy-tab-type2",
											style:
												"background:" +
												(this$1.props.dataMore.key == item$1.key
													? this$1.props.bg
													: "#F8F8F8") +
												";margin-right:" +
												(index$1 == this$1.itemData.length - 1 ? "16" : "5") +
												"px;border-radius:" +
												(this$1.props.borderRadius
													? this$1.props.borderRadius + "px"
													: "0") +
												";padding: 2px 15px; "
										},
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(this$1.props.size) +
													";line-height:" +
													((this$1.props.size || 0) + 16) * 1.5 +
													"px;color:" +
													(this$1.props.dataMore.key == item$1.key ? "#FFF" : "#666"),
												class: "zy-tab-type2__text"
											},
											item$1.value
										)
									)
								);
							})
					  )
					: []
			);
		};

		return ZTabs;
	})(Component);
	ZTabs.css = {
		".zy-tab-box": {
			flex: "1",
			height: "100%",
			flexDirection: "row",
			width: "auto",
			overflowY: "hidden !important"
		},
		".zy-tab": {
			padding: "6px 8px 4px 8px",
			flexShrink: "0",
			width: "auto",
			fontWeight: "400"
		},
		".zy-tab__text": {color: "#333", flexShrink: "0", padding: "1px 0"},
		".zy-tab__line": {
			position: "absolute",
			zIndex: "999",
			bottom: "-3px",
			width: "34px",
			maxWidth: "80%",
			height: "2px",
			borderRadius: "999px",
			background: "#333"
		},
		".zy-tab-type2_box": {flexShrink: "0", width: "auto"},
		".zy-tab-type2": {
			flexShrink: "0",
			width: "auto",
			fontWeight: "400",
			padding: "0px 8px",
			borderRadius: "2px",
			margin: "6px 5px"
		},
		".zy-tab-type2__text": {flexShrink: "0"},
		".zy-tab-type3_box": {flexShrink: "0", width: "auto"},
		".zy-tab-type3": {
			flexShrink: "0",
			width: "auto",
			padding: "9px 8px",
			margin: "17px 6px 5px",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			background: "#FFF",
			alignItems: "center"
		},
		".zy-tab-type3__text": {flexShrink: "0"},
		".zy-tab-type3__line": {
			position: "absolute",
			zIndex: "999",
			bottom: "-5px",
			width: "61%",
			height: "1px",
			borderRadius: "999px",
			background: "#333"
		},
		".zy-tab-type4_box": {flexShrink: "0", width: "auto"},
		".zy-tab-type4": {
			flexShrink: "0",
			width: "auto",
			fontWeight: "400",
			flexDirection: "row",
			alignItems: "center"
		},
		".zy-tab-type4__text": {flexShrink: "0"},
		".zy-tab-hot_box": {
			position: "absolute",
			zIndex: "999",
			top: "-7px",
			right: "-7px"
		},
		".zy-tab-hot_warp": {
			minWidth: "18px",
			minHeight: "18px",
			background: "#EF0000",
			borderRadius: "50px",
			alignItems: "center",
			justifyContent: "center",
			padding: "0 2px"
		},
		".zy-tab-hot_text": {color: "#FFF"},
		".zy-tab-type5_box": {flexShrink: "0", width: "auto"},
		".zy-tab-type5": {
			flexShrink: "0",
			width: "auto",
			fontWeight: "400",
			padding: "2px 12px",
			margin: "6px 5px"
		},
		".zy-tab-type5__text": {flexShrink: "0", fontWeight: "bold"}
	};
	apivm.define("z-tabs", ZTabs);

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				style:
					(this.props.style || "") +
					";font-size: " +
					(this.props.size || 16) +
					"px;flex-shrink:0;",
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: "yuanxingxuanzhongfill"
					: "danxuan_weixuanzhong",
				color: this.props.color
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var YCloudDisk = /*@__PURE__*/ (function(Component) {
		function YCloudDisk(props) {
			Component.call(this, props);
			this.data = {
				isOpenListMore: false
			};
		}

		if (Component) YCloudDisk.__proto__ = Component;
		YCloudDisk.prototype = Object.create(Component && Component.prototype);
		YCloudDisk.prototype.constructor = YCloudDisk;
		YCloudDisk.prototype.installed = function() {};
		YCloudDisk.prototype.isSelectValue = function(_item) {
			return G.getItemForKey(_item.id, this.props.dataSelect, "id");
		};
		YCloudDisk.prototype.itemTap = function(_item) {
			var this$1 = this;

			if (this.data.isOpenListMore) {
				return;
			}
			if (
				this.props.dataMore.isSelect &&
				(this.props.dataMore.selectType == "file"
					? _item.fileInfo.type != "folder"
					: true)
			) {
				var nItem = G.getItemForKey(_item.id, this.props.dataSelect, "id");
				if (nItem) {
					if (!nItem.notDel) {
						G.delItemForKey(nItem, this.props.dataSelect, "id");
					}
				} else {
					if (_item.dotSelect) {
						T.toast(_item.dotSelectToast);
						return;
					}
					setTimeout(function() {
						this$1.props.dataSelect.push(_item);
					}, 0);
				}
				setTimeout(function() {
					this$1.fire("changeDataSelect", {});
				}, 0);
			} else {
				setTimeout(function() {
					this$1.fire("openDetails", _item);
				}, 0);
			}
		};
		YCloudDisk.prototype.itemPress = function(e, _item) {
			this.openListMore(e, _item);
		};
		YCloudDisk.prototype.openListMore = function(e, _item) {
			var this$1 = this;

			G.stopBubble(e);
			if (this.props.dataMore.isSelect) {
				return;
			}
			if (_item.dotSelect) {
				T.toast(_item.dotSelectToast);
				return;
			}
			this.data.isOpenListMore = true;
			setTimeout(function() {
				this$1.data.isOpenListMore = false;
			}, 200);
			this.props.dataSelect.push(_item);
			this.props.dataMore.isSelect = true;
			setTimeout(function() {
				this$1.fire("changeSelect", {});
				this$1.fire("changeDataSelect", {});
			}, 0);
		};
		YCloudDisk.prototype.showDot = function(_item) {
			return (
				_item.isRedDot == 1 ||
				G.getItemForKey(_item.id, this.props.dataMore.listUnread || [])
			);
		};
		YCloudDisk.prototype.openleftMore = function(e, _item) {
			var this$1 = this;

			G.stopBubble(e);
			setTimeout(function() {
				this$1.fire("leftMore", _item);
			}, 0);
		};
		YCloudDisk.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: this.props.class || "", style: this.props.style || ""},
				this.props.dataMore.mode == "list"
					? apivm.h(
							"view",
							{class: "cloudDisk_list_box"},
							(Array.isArray(this.props.data)
								? this.props.data
								: Object.values(this.props.data)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										class: "cloudDisk_list_item",
										onClick: function() {
											return this$1.itemTap(item$1);
										},
										onLongpress: function(e) {
											return this$1.itemPress(e, item$1);
										}
									},
									apivm.h(
										"view",
										null,
										this$1.props.dataMore.hasMore &&
											apivm.h(
												"view",
												{
													style: "padding:10px;",
													onClick: function(e) {
														return this$1.openleftMore(e, item$1);
													}
												},
												apivm.h("a-iconfont", {
													name: "gengduo3",
													color: "#999",
													size: G.appFontSize + 10
												})
											)
									),
									apivm.h(
										"view",
										{
											style:
												"width:auto;height:auto;padding:4px;margin-right:15px;position: relative;"
										},
										apivm.h("a-iconfont", {
											name: item$1.fileInfo.name,
											color: item$1.fileInfo.color,
											size: G.appFontSize + 20
										}),
										this$1.showDot(item$1)
											? apivm.h(
													"view",
													{
														class: "cloudDisk_list_redDot",
														style:
															"" +
															G.loadConfigurationSize(-6) +
															(G.careMode ? "top:1px;right: 1px;" : "")
													},
													" "
											  )
											: null
									),
									apivm.h(
										"view",
										{style: "flex:1;"},
										apivm.h(
											"view",
											{style: "flex-direction:row;align-items: center;min-height:65px;"},
											apivm.h(
												"view",
												{style: "flex:1;"},
												apivm.h(
													"text",
													{
														style:
															G.loadConfiguration(1) + "color: #333;word-break:break-all;",
														class: "text_one" + (T.platform() == "app" ? "" : "2")
													},
													item$1.name
												),
												this$1.props.dataMore.type != "details"
													? apivm.h(
															"view",
															{
																style: "margin-top:4px;flex-direction:row; align-items: center;"
															},
															apivm.h(
																"view",
																null,
																item$1.firstMsg
																	? apivm.h(
																			"view",
																			{style: "flex-direction:row; align-items: center;"},
																			apivm.h(
																				"text",
																				{
																					style:
																						G.loadConfiguration(-4) +
																						"color: " +
																						(item$1.fristColor || "#999") +
																						";"
																				},
																				item$1.firstMsg
																			),
																			apivm.h("view", {
																				style:
																					"width:1px;height:" +
																					(G.appFontSize - 1) +
																					"px;background:#eee;margin:0 10px;"
																			})
																	  )
																	: null
															),
															apivm.h(
																"view",
																null,
																item$1.firstIcon &&
																	item$1.firstIcon.name &&
																	apivm.h("a-iconfont", {
																		style: "margin-right:6px;",
																		name: item$1.firstIcon.name,
																		color: item$1.firstIcon.color || "#999",
																		size: G.appFontSize
																	})
															),
															apivm.h(
																"text",
																{
																	style: G.loadConfiguration(-4) + "color: #999;flex-shrink: 0;"
																},
																dayjs(item$1.time).format("YYYY-MM-DD HH:mm")
															),
															apivm.h(
																"view",
																null,
																T.isArray(item$1.addMsg) && item$1.addMsg.length > 0
																	? apivm.h(
																			"view",
																			{style: "flex-direction:row; align-items: center;"},
																			(Array.isArray(item$1.addMsg)
																				? item$1.addMsg
																				: Object.values(item$1.addMsg)
																			).map(function(nItem, nIndex) {
																				return apivm.h(
																					"view",
																					{style: "flex-direction:row; align-items: center;"},
																					apivm.h("view", {
																						style:
																							"width:1px;height:" +
																							(G.appFontSize - 1) +
																							"px;background:#eee;margin:0 10px;"
																					}),
																					apivm.h(
																						"text",
																						{
																							style:
																								G.loadConfiguration(-4) +
																								"color: " +
																								(nItem.color || "#999") +
																								";"
																						},
																						nItem.text
																					)
																				);
																			})
																	  )
																	: item$1.addMsg
																	? apivm.h(
																			"view",
																			{style: "flex-direction:row; align-items: center;"},
																			apivm.h("view", {
																				style:
																					"width:1px;height:" +
																					(G.appFontSize - 1) +
																					"px;background:#eee;margin:0 10px;"
																			}),
																			apivm.h(
																				"text",
																				{
																					style:
																						G.loadConfiguration(-4) +
																						"color: " +
																						(item$1.addColor || "#999") +
																						";"
																				},
																				item$1.addMsg
																			)
																	  )
																	: null
															)
													  )
													: null
											),
											apivm.h(
												"view",
												{style: "width:auto;height:auto;"},
												this$1.props.dataMore.type == "details" ||
													(this$1.props.dataMore.isSelect &&
														this$1.props.dataMore.selectType == "file" &&
														item$1.fileInfo.type == "folder")
													? apivm.h(
															"view",
															{
																style:
																	"width:auto;height:auto;padding:8px;transform: rotate(-90deg);"
															},
															apivm.h("a-iconfont", {
																name: "xiangxia1",
																color: "#999999",
																size: G.appFontSize
															})
													  )
													: this$1.props.dataMore.isSelect
													? apivm.h("z-radio", {
															style: "padding:6px;",
															checked: this$1.isSelectValue(item$1),
															size: G.appFontSize + 4,
															color: this$1.isSelectValue(item$1) ? G.appTheme : "#999"
													  })
													: apivm.h(
															"view",
															{
																style: "width:auto;height:auto;padding:8px;margin: -8px 0;",
																onClick: function(e) {
																	return this$1.openListMore(e, item$1);
																}
															},
															apivm.h("a-iconfont", {
																name: "gengduo",
																color: "#999999",
																size: G.appFontSize
															})
													  )
											)
										),
										apivm.h(
											"view",
											{style: "width:100%;height:1px;"},
											apivm.h("a-divider", null)
										)
									)
								);
							})
					  )
					: this.props.dataMore.mode == "grid"
					? apivm.h(
							"view",
							{class: "cloudDisk_list_grid_box"},
							(Array.isArray(this.props.data)
								? this.props.data
								: Object.values(this.props.data)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									{
										class: "cloudDisk_list_grid_item",
										onClick: function() {
											return this$1.itemTap(item$1);
										},
										onLongpress: function(e) {
											return this$1.itemPress(e, item$1);
										}
									},
									apivm.h(
										"view",
										{class: "cloudDisk_list_grid_warp"},
										apivm.h(
											"view",
											{
												style:
													"width:auto;height:auto;align-items: center;margin-bottom:10px;"
											},
											apivm.h(
												"view",
												{style: "position: relative;"},
												apivm.h("a-iconfont", {
													name: item$1.fileInfo.name,
													color: item$1.fileInfo.color,
													size: G.appFontSize + 30
												}),
												this$1.showDot(item$1)
													? apivm.h(
															"view",
															{
																class: "cloudDisk_list_redDot",
																style:
																	"" +
																	G.loadConfigurationSize(-6) +
																	(G.careMode ? "top:1px;right: 1px;" : "")
															},
															" "
													  )
													: null
											)
										),
										apivm.h(
											"text",
											{
												class: "cloudDisk_list_grid_name",
												style:
													G.loadConfiguration(-2) +
													"line-height:" +
													(G.appFontSize - 2) * 1.4 +
													"px;height:" +
													(G.appFontSize - 2) * 1.4 * 2 +
													"px;"
											},
											item$1.name
										),
										apivm.h(
											"view",
											{
												style: "width:auto;height:auto;align-items: center;margin-top:8px;"
											},
											this$1.props.dataMore.isSelect
												? apivm.h("z-radio", {
														style:
															"margin:-2px 0 -" + (T.platform() == "app" ? 2 : 3) + "px 0;",
														checked: this$1.isSelectValue(item$1),
														size: G.appFontSize + 4,
														color: this$1.isSelectValue(item$1) ? G.appTheme : "#999"
												  })
												: apivm.h(
														"view",
														{
															style: "width:auto;height:auto;padding:8px;margin: -8px 0;",
															onClick: function(e) {
																return this$1.openListMore(e, item$1);
															}
														},
														apivm.h("a-iconfont", {
															name: "gengduo",
															color: "#999999",
															size: G.appFontSize
														})
												  )
										)
									)
								);
							})
					  )
					: []
			);
		};

		return YCloudDisk;
	})(Component);
	YCloudDisk.css = {
		".cloudDisk_list_item": {
			flexDirection: "row",
			alignItems: "center",
			padding: "0px 15px",
			minHeight: "65px"
		},
		".cloudDisk_list_grid_box": {
			flexDirection: "row",
			flexWrap: "wrap",
			padding: "0 2px 16px 16px"
		},
		".cloudDisk_list_grid_item": {
			width: "33.33%",
			height: "auto",
			padding: "15px 14px 0 0"
		},
		".cloudDisk_list_grid_warp": {
			padding: "9px",
			borderRadius: "4px",
			border: "1px solid #EBEBEB"
		},
		".cloudDisk_list_grid_name": {
			color: "#333333",
			WebkitLineClamp: "2",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden",
			textOverflow: "ellipsis",
			whiteSpace: "normal !important",
			textAlign: "center"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".cloudDisk_list_redDot": {
			position: "absolute",
			zIndex: "999",
			top: "2px",
			right: "2px",
			background: "#f92323",
			borderRadius: "50%",
			whiteSpace: "nowrap",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("y-cloud-disk", YCloudDisk);

	var YCloudDiskAround = /*@__PURE__*/ (function(Component) {
		function YCloudDiskAround(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				listSelect: null,
				hasAddFolder: true,
				orderBys: {
					key: "updatedate",
					direction: 1 //0 顺序 1倒序
				},
				level: {key: "", defaultKey: "", data: [{key: "0", value: "我的文件"}]},

				listData: [
					// { id:"1", name:"XX文件夹", fileInfo:{"name":"folder-2-fill",color:"#F6C21C",type:"folder"}, time:"2020-02-06 12:00", },
				],
				dataMore: {
					mode: "list", //list列表 grid宫格
					isSelect: false, //是否选择中
					selectType: "file",
					type: "details",
					hasMore: false
				},

				addInput: {
					show: false,
					autofocus: true,
					key: "around_add_folder_callback",
					value: "新建文件夹",
					size: 24,
					weight: "bold",
					fileInfo: G.getFileInfo("folder"),
					input: "",
					placeholder: "新建文件夹"
				}
			};
			this.compute = {
				isShow: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.listData = [];
							this.data.listSelect = this.props.dataMore.listSelect || [];
							this.data.level.data[0].value =
								this.props.dataMore.key == "long_shared"
									? "共享文件"
									: this.props.dataMore.key == "chat_file"
									? "文件"
									: "我的文件";
							this.data.dataMore.hasMore = this.props.dataMore.key == "chat_file";
							if (
								this.props.dataMore.key == "long_choose" ||
								this.props.dataMore.key == "chat_file"
							) {
								this.data.dataMore.type = "select";
								this.data.dataMore.isSelect = true;
								// this.orderBys.key = "filemenu";
								// this.orderBys.direction = "0";
								this.data.hasAddFolder = false;
							}
							this.getData();
						} else {
							this.data.level.data = [this.data.level.data[0]];
						}
					}
				},
				titleName: function() {
					var name = "";
					switch (this.props.dataMore.key) {
						case "long_copy":
							name =
								"复制到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_move":
							name =
								"移动到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_shared":
							name =
								"共享到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_resave":
							name =
								"转存到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
						case "long_choose":
							name = "选择云盘文件";
							break;
						case "chat_file":
							name = "聊天文件";
							break;
					}

					return name;
				}
			};
		}

		if (Component) YCloudDiskAround.__proto__ = Component;
		YCloudDiskAround.prototype = Object.create(Component && Component.prototype);
		YCloudDiskAround.prototype.constructor = YCloudDiskAround;
		YCloudDiskAround.prototype.installed = function() {};
		YCloudDiskAround.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		YCloudDiskAround.prototype.penetrate = function() {};
		YCloudDiskAround.prototype.itemClick = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.props.dataMore.toId = this$1.data.level.key;
				this$1.props.dataMore.toItem =
					this$1.data.level.data[this$1.data.level.data.length - 1];
				this$1.props.dataMore.listSelect = this$1.data.listSelect;
				this$1.fire("click", this$1.props.dataMore);
				this$1.closePage();
			}, 0);
		};
		YCloudDiskAround.prototype.openDetails = function(ref) {
			var detail = ref.detail;

			// console.log(JSON.stringify(detail));
			if (detail.fileInfo.type == "folder") {
				this.data.level.data.push({key: detail.id, value: detail.name});
			}
		};
		YCloudDiskAround.prototype.openLeftMore = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			var buttons = ["打开"];
			if (detail.createBy == G.uId || detail.createBy == G.userId) {
				buttons.push("删除");
			}
			T.actionSheet(
				{
					title: "提示",
					buttons: buttons,
					dotClose: true
				},
				function(ret, err) {
					if (ret.value == "打开") {
						openWin_filePreviewer({
							id: detail.fileInfoId,
							suffix: detail.fileInfo.type,
							fileSource: "5"
						});
					} else if (ret.value == "删除") {
						G.ajaxAlert(
							{
								msg: "确定删除吗?",
								url: myjs.appUrl() + "chatGroupFile/dels",
								param: {ids: [detail.groupFileId]}
							},
							this$1,
							function(ret) {
								G.delItemForKey(
									detail.groupFileId,
									this$1.data.listSelect,
									"groupFileId"
								);
								this$1.getData();
							}
						);
					}
				}
			);
		};
		YCloudDiskAround.prototype.getData = function(_type) {
			var this$1 = this;

			if (!this.data.show) {
				return;
			}
			var url =
				myjs.appUrl() +
				(this.props.dataMore.key == "long_shared" ? "pubsharefile" : "panfile") +
				"/list";
			var tableId =
				this.props.dataMore.key == "long_shared"
					? "id_pan_pubshare_file"
					: "id_pan_file_list";
			var postParam = {
				pageNo: 1,
				pageSize: 9999,
				keyword: "",
				tableId: tableId,
				query: {fileMenu: "1", fileStatus: "0"},
				orderBys: [
					{
						columnId: tableId + "_" + this.data.orderBys.key,
						isDesc: this.data.orderBys.direction
					}
				],
				wheres: [
					{
						columnId: tableId + "_parentid",
						queryType: "EQ",
						value: this.data.level.key || "0"
					}
				]
			};

			if (this.props.dataMore.key == "long_choose") {
				delete postParam.query;
			}
			if (this.props.dataMore.key == "chat_file") {
				url = myjs.appUrl() + "chatGroupFile/list";
				postParam = {
					pageNo: 1,
					pageSize: 9999,
					keyword: "",
					query: {chatGroupId: this.props.dataMore.id}
				};
			}
			T.ajax(
				{u: url, _this: this},
				"panfile" + "/menutree",
				function(ret, err) {
					T.hideProgress();
					var code = ret ? ret.code : "";
					var data = ret ? ret.data || [] : [];
					if (T.isArray(data) && data.length) {
						var nowList = [];
						data.forEach(function(_eItem, _eIndex, _eArr) {
							//item index 原数组对象
							var item = {};
							if (this$1.props.dataMore.key == "chat_file") {
								item.groupFileId = _eItem.id;
								_eItem = _eItem.fileInfo || {};
							}
							item.id = _eItem.id || ""; //id
							item.name = _eItem.fileName || _eItem.originalFileName || ""; //标题
							item.time = _eItem.updateDate || _eItem.createDate;
							item.fileInfoId = _eItem.fileInfoId || _eItem.id;
							item.createBy = _eItem.createBy;

							item.fileInfo = G.getFileInfo(_eItem.extName || "folder");
							if (
								this$1.props.dataMore.key == "long_choose" ||
								this$1.props.dataMore.key == "chat_file"
							) {
								nowList.push(item);
							} else {
								if (!G.getItemForKey(item.id, this$1.data.listSelect, "id")) {
									nowList.push(item);
								}
							}
						});
						if (!_type) {
							this$1.data.listData = nowList;
						} else {
							this$1.data.listData = this$1.data.listData.concat(nowList);
						}
					} else {
						this$1.data.listData = [];
					}
				},
				"列表",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		YCloudDiskAround.prototype.levelChange = function(ref) {
			var detail = ref.detail;

			var nLevel = [],
				lastItem = null;
			for (var i = 0; i < this.data.level.data.length; i++) {
				lastItem = this.data.level.data[i];
				nLevel.push(lastItem);
				if (lastItem.key == detail.key) {
					break;
				}
			}
			this.data.level.data = nLevel;
			this.getData(0);
		};
		YCloudDiskAround.prototype.addFolder = function() {
			var this$1 = this;

			this.data.addInput.input = "";
			this.data.addInput.show = true;
			setTimeout(function() {
				$("#" + this$1.data.addInput.key).focus();
			}, 150);
		};
		YCloudDiskAround.prototype.keyCallback = function(ref) {
			var detail = ref.detail;

			console.log(JSON.stringify(detail));
			switch (detail.key) {
				case "around_add_folder_callback":
					this.addFolderCallback();
					break;
			}
		};
		YCloudDiskAround.prototype.addFolderCallback = function() {
			var this$1 = this;

			var postParam = {
				parentId: this.data.level.key || "0",
				fileName: this.data.addInput.input || this.data.addInput.placeholder,
				pubshareName: this.data.addInput.input || this.data.addInput.placeholder
			};

			T.showProgress("新建中");
			T.ajax(
				{
					u:
						myjs.appUrl() +
						(this.props.dataMore.key == "long_shared" ? "pubsharefile" : "panfile") +
						"/addmenu",
					_this: this
				},
				"panfile" + "/addmenu",
				function(ret, err) {
					T.hideProgress();
					T.toast(ret ? ret.message : T.NET_ERR);
					if (ret && ret.code == 200) {
						this$1.getData(0);
					}
				},
				"新建文件夹",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		YCloudDiskAround.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "cloudDiskAround_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				apivm.h(
					"view",
					{
						class: "cloudDiskAround_warp",
						onClick: function() {
							return this$1.penetrate();
						}
					},
					apivm.h(
						"view",
						{class: "cloudDiskAround_header_warp"},
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_main"},
							apivm.h(
								"text",
								{
									style: G.loadConfiguration(1) + "color:#333",
									class: "cloudDiskAround_header_main_text"
								},
								this.titleName
							)
						),
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_left_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "cloudDiskAround_header_btn"
									},
									apivm.h(
										"text",
										{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"取消"
									)
								)
							)
						),
						apivm.h(
							"view",
							{class: "cloudDiskAround_header_right_box"},
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick();
										},
										class: "cloudDiskAround_header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						)
					),
					apivm.h(
						"view",
						{style: "width:100%;height:1px;padding:0 16px;"},
						apivm.h("a-divider", null)
					),
					apivm.h(
						"view",
						{
							style:
								"flex-direction:row;align-items: center;padding:10px 16px 10px 10px;margin-top:10px;"
						},
						apivm.h(
							"view",
							{style: "flex:1;width:1px;"},
							apivm.h("z-tabs", {
								id: "tab_cloud_disk_around",
								style: "height:" + (G.appFontSize + 5) + "px;",
								size: -2,
								dataMore: this.data.level,
								onChange: this.levelChange,
								type: 4,
								bg: "" + G.appTheme
							})
						),
						apivm.h(
							"view",
							null,
							this.data.hasAddFolder
								? apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.addFolder();
											},
											style: "margin-left:10px;flex-direction:row;align-items: center;"
										},
										apivm.h("a-iconfont", {
											style: "margin-right:6px;",
											name: "folder-add-fill",
											color: "#F6931C",
											size: G.appFontSize + 4
										}),
										apivm.h(
											"text",
											{style: G.loadConfiguration(1) + "color:#333;"},
											"新建"
										)
								  )
								: null
						)
					),
					apivm.h(
						"view",
						{style: "width:100%;height:1px;padding:0 16px;"},
						apivm.h("a-divider", null)
					),
					apivm.h(
						"scroll-view",
						{style: "flex:1;height:1px;", "scroll-y": true},
						apivm.h("y-cloud-disk", {
							data: this.data.listData,
							dataMore: this.data.dataMore,
							dataSelect: this.data.listSelect,
							onOpenDetails: this.openDetails,
							onLeftMore: this.openLeftMore
						})
					)
				),

				apivm.h("z-actionSheet-input", {
					dataMore: this.data.addInput,
					onClick: this.keyCallback
				})
			);
		};

		return YCloudDiskAround;
	})(Component);
	YCloudDiskAround.css = {
		".cloudDiskAround_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".cloudDiskAround_warp": {
			flex: "1",
			height: "1px",
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".cloudDiskAround_header_warp": {
			height: "49px",
			flexDirection: "row",
			width: "100%",
			alignItems: "center"
		},
		".cloudDiskAround_header_btn": {
			height: "49px",
			width: "auto",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".cloudDiskAround_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".cloudDiskAround_header_main": {
			flex: "1",
			width: "1px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "0 49px"
		},
		".cloudDiskAround_header_main_text": {
			fontWeight: "600",
			maxWidth: "200px",
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".cloudDiskAround_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row"
		},
		".cloudDiskAround_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "49px",
			flexDirection: "row-reverse"
		}
	};
	apivm.define("y-cloud-disk-around", YCloudDiskAround);

	function _inheritsLoose(subClass, superClass) {
		subClass.prototype = Object.create(superClass.prototype);
		subClass.prototype.constructor = subClass;

		_setPrototypeOf(subClass, superClass);
	}

	function _setPrototypeOf(o, p) {
		_setPrototypeOf = Object.setPrototypeOf
			? Object.setPrototypeOf.bind()
			: function _setPrototypeOf(o, p) {
					o.__proto__ = p;
					return o;
			  };
		return _setPrototypeOf(o, p);
	}

	var Hex = /*#__PURE__*/ (function() {
		function Hex() {}
		Hex.stringify = function stringify(wordArray) {
			var hexChars = [];
			for (var i = 0; i < wordArray.sigBytes; i++) {
				var bite = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				hexChars.push((bite >>> 4).toString(16));
				hexChars.push((bite & 0x0f).toString(16));
			}
			return hexChars.join("");
		};
		Hex.parse = function parse(hexStr) {
			var hexStrLength = hexStr.length;
			var words = [];
			for (var i = 0; i < hexStrLength; i += 2) {
				words[i >>> 3] |= parseInt(hexStr.substr(i, 2), 16) << (24 - (i % 8) * 4);
			}
			return new WordArray(words, hexStrLength / 2);
		};
		return Hex;
	})();
	var WordArray = /*#__PURE__*/ (function() {
		WordArray.random = function random(nBytes) {
			var words = [];
			var r = function r(m_w) {
				var m_z = 0x3ade68b1;
				var mask = 0xffffffff;
				return function() {
					m_z = (0x9069 * (m_z & 0xffff) + (m_z >> 0x10)) & mask;
					m_w = (0x4650 * (m_w & 0xffff) + (m_w >> 0x10)) & mask;
					var result = ((m_z << 0x10) + m_w) & mask;
					result /= 0x100000000;
					result += 0.5;
					return result * (Math.random() > 0.5 ? 1 : -1);
				};
			};
			for (var i = 0, rcache; i < nBytes; i += 4) {
				var _r = r((rcache || Math.random()) * 0x100000000);
				rcache = _r() * 0x3ade67b7;
				words.push((_r() * 0x100000000) | 0);
			}
			return new WordArray(words, nBytes);
		};
		function WordArray(words, sigBytes) {
			this.words = words || [];
			if (sigBytes !== undefined) {
				this.sigBytes = sigBytes;
			} else {
				this.sigBytes = this.words.length * 4;
			}
		}
		var _proto = WordArray.prototype;
		_proto.toString = function toString(encoder) {
			return (encoder || Hex).stringify(this);
		};
		_proto.concat = function concat(wordArray) {
			this.clamp();
			if (this.sigBytes % 4) {
				for (var i = 0; i < wordArray.sigBytes; i++) {
					var thatByte = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
					this.words[(this.sigBytes + i) >>> 2] |=
						thatByte << (24 - ((this.sigBytes + i) % 4) * 8);
				}
			} else {
				for (var _i = 0; _i < wordArray.sigBytes; _i += 4) {
					this.words[(this.sigBytes + _i) >>> 2] = wordArray.words[_i >>> 2];
				}
			}
			this.sigBytes += wordArray.sigBytes;
			return this;
		};
		_proto.clamp = function clamp() {
			this.words[this.sigBytes >>> 2] &=
				0xffffffff << (32 - (this.sigBytes % 4) * 8);
			this.words.length = Math.ceil(this.sigBytes / 4);
		};
		_proto.clone = function clone() {
			return new WordArray(this.words.slice(0), this.sigBytes);
		};
		return WordArray;
	})();
	var Latin1 = /*#__PURE__*/ (function() {
		function Latin1() {}
		Latin1.stringify = function stringify(wordArray) {
			var latin1Chars = [];
			for (var i = 0; i < wordArray.sigBytes; i++) {
				var bite = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				latin1Chars.push(String.fromCharCode(bite));
			}
			return latin1Chars.join("");
		};
		Latin1.parse = function parse(latin1Str) {
			var latin1StrLength = latin1Str.length;
			var words = [];
			for (var i = 0; i < latin1StrLength; i++) {
				words[i >>> 2] |= (latin1Str.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8);
			}
			return new WordArray(words, latin1StrLength);
		};
		return Latin1;
	})();
	var Utf8 = /*#__PURE__*/ (function() {
		function Utf8() {}
		Utf8.stringify = function stringify(wordArray) {
			try {
				return decodeURIComponent(escape(Latin1.stringify(wordArray)));
			} catch (e) {
				throw new Error("Malformed UTF-8 data");
			}
		};
		Utf8.parse = function parse(utf8Str) {
			return Latin1.parse(unescape(encodeURIComponent(utf8Str)));
		};
		return Utf8;
	})();
	var BufferedBlockAlgorithm = /*#__PURE__*/ (function() {
		function BufferedBlockAlgorithm(cfg) {
			this._minBufferSize = 0;
			this.cfg = Object.assign({blockSize: 1}, cfg);
			this._data = new WordArray();
			this._nDataBytes = 0;
		}
		var _proto2 = BufferedBlockAlgorithm.prototype;
		_proto2.reset = function reset() {
			this._data = new WordArray();
			this._nDataBytes = 0;
		};
		_proto2._append = function _append(data) {
			if (typeof data === "string") {
				data = Utf8.parse(data);
			}
			this._data.concat(data);
			this._nDataBytes += data.sigBytes;
		};
		_proto2._process = function _process(doFlush) {
			if (!this.cfg.blockSize) {
				throw new Error("missing blockSize in config");
			}
			var blockSizeBytes = this.cfg.blockSize * 4;
			var nBlocksReady = this._data.sigBytes / blockSizeBytes;
			if (doFlush) {
				nBlocksReady = Math.ceil(nBlocksReady);
			} else {
				nBlocksReady = Math.max((nBlocksReady | 0) - this._minBufferSize, 0);
			}
			var nWordsReady = nBlocksReady * this.cfg.blockSize;
			var nBytesReady = Math.min(nWordsReady * 4, this._data.sigBytes);
			var processedWords;
			if (nWordsReady) {
				for (var offset = 0; offset < nWordsReady; offset += this.cfg.blockSize) {
					this._doProcessBlock(this._data.words, offset);
				}
				processedWords = this._data.words.splice(0, nWordsReady);
				this._data.sigBytes -= nBytesReady;
			}
			return new WordArray(processedWords, nBytesReady);
		};
		_proto2.clone = function clone() {
			var clone = this.constructor();
			for (var attr in this) {
				if (this.hasOwnProperty(attr)) {
					clone[attr] = this[attr];
				}
			}
			clone._data = this._data.clone();
			return clone;
		};
		return BufferedBlockAlgorithm;
	})();
	var Base = function Base() {};
	var CipherParams = /*#__PURE__*/ (function(_Base) {
		_inheritsLoose(CipherParams, _Base);
		function CipherParams(cipherParams) {
			var _this;
			_this = _Base.call(this) || this;
			_this.ciphertext = cipherParams.ciphertext;
			_this.key = cipherParams.key;
			_this.iv = cipherParams.iv;
			_this.salt = cipherParams.salt;
			_this.algorithm = cipherParams.algorithm;
			_this.mode = cipherParams.mode;
			_this.padding = cipherParams.padding;
			_this.blockSize = cipherParams.blockSize;
			_this.formatter = cipherParams.formatter;
			return _this;
		}
		var _proto3 = CipherParams.prototype;
		_proto3.extend = function extend(additionalParams) {
			if (additionalParams.ciphertext !== undefined) {
				this.ciphertext = additionalParams.ciphertext;
			}
			if (additionalParams.key !== undefined) {
				this.key = additionalParams.key;
			}
			if (additionalParams.iv !== undefined) {
				this.iv = additionalParams.iv;
			}
			if (additionalParams.salt !== undefined) {
				this.salt = additionalParams.salt;
			}
			if (additionalParams.algorithm !== undefined) {
				this.algorithm = additionalParams.algorithm;
			}
			if (additionalParams.mode !== undefined) {
				this.mode = additionalParams.mode;
			}
			if (additionalParams.padding !== undefined) {
				this.padding = additionalParams.padding;
			}
			if (additionalParams.blockSize !== undefined) {
				this.blockSize = additionalParams.blockSize;
			}
			if (additionalParams.formatter !== undefined) {
				this.formatter = additionalParams.formatter;
			}
			return this;
		};
		_proto3.toString = function toString(formatter) {
			if (formatter) {
				return formatter.stringify(this);
			} else if (this.formatter) {
				return this.formatter.stringify(this);
			} else {
				throw new Error(
					"cipher needs a formatter to be able to convert the result into a string"
				);
			}
		};
		return CipherParams;
	})(Base);
	var Base64 = /*#__PURE__*/ (function() {
		function Base64() {}
		Base64.stringify = function stringify(wordArray) {
			wordArray.clamp();
			var base64Chars = [];
			for (var i = 0; i < wordArray.sigBytes; i += 3) {
				var byte1 = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				var byte2 =
					(wordArray.words[(i + 1) >>> 2] >>> (24 - ((i + 1) % 4) * 8)) & 0xff;
				var byte3 =
					(wordArray.words[(i + 2) >>> 2] >>> (24 - ((i + 2) % 4) * 8)) & 0xff;
				var triplet = (byte1 << 16) | (byte2 << 8) | byte3;
				for (var j = 0; j < 4 && i + j * 0.75 < wordArray.sigBytes; j++) {
					base64Chars.push(this._map.charAt((triplet >>> (6 * (3 - j))) & 0x3f));
				}
			}
			var paddingChar = this._map.charAt(64);
			if (paddingChar) {
				while (base64Chars.length % 4) {
					base64Chars.push(paddingChar);
				}
			}
			return base64Chars.join("");
		};
		Base64.parse = function parse(base64Str) {
			var base64StrLength = base64Str.length;
			if (this._reverseMap === undefined) {
				this._reverseMap = [];
				for (var j = 0; j < this._map.length; j++) {
					this._reverseMap[this._map.charCodeAt(j)] = j;
				}
			}
			var paddingChar = this._map.charAt(64);
			if (paddingChar) {
				var paddingIndex = base64Str.indexOf(paddingChar);
				if (paddingIndex !== -1) {
					base64StrLength = paddingIndex;
				}
			}
			return this.parseLoop(base64Str, base64StrLength, this._reverseMap);
		};
		Base64.parseLoop = function parseLoop(
			base64Str,
			base64StrLength,
			reverseMap
		) {
			var words = [];
			var nBytes = 0;
			for (var i = 0; i < base64StrLength; i++) {
				if (i % 4) {
					var bits1 = reverseMap[base64Str.charCodeAt(i - 1)] << ((i % 4) * 2);
					var bits2 = reverseMap[base64Str.charCodeAt(i)] >>> (6 - (i % 4) * 2);
					words[nBytes >>> 2] |= (bits1 | bits2) << (24 - (nBytes % 4) * 8);
					nBytes++;
				}
			}
			return new WordArray(words, nBytes);
		};
		return Base64;
	})();
	Base64._map =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
	Base64._reverseMap = undefined;
	var OpenSSL = /*#__PURE__*/ (function() {
		function OpenSSL() {}
		OpenSSL.stringify = function stringify(cipherParams) {
			if (!cipherParams.ciphertext) {
				throw new Error("missing ciphertext in params");
			}
			var ciphertext = cipherParams.ciphertext;
			var salt = cipherParams.salt;
			var wordArray;
			if (salt) {
				if (typeof salt === "string") {
					throw new Error("salt is expected to be a WordArray");
				}
				wordArray = new WordArray([0x53616c74, 0x65645f5f])
					.concat(salt)
					.concat(ciphertext);
			} else {
				wordArray = ciphertext;
			}
			return wordArray.toString(Base64);
		};
		OpenSSL.parse = function parse(openSSLStr) {
			var ciphertext = Base64.parse(openSSLStr);
			var salt;
			if (
				ciphertext.words[0] === 0x53616c74 &&
				ciphertext.words[1] === 0x65645f5f
			) {
				salt = new WordArray(ciphertext.words.slice(2, 4));
				ciphertext.words.splice(0, 4);
				ciphertext.sigBytes -= 16;
			}
			return new CipherParams({ciphertext: ciphertext, salt: salt});
		};
		return OpenSSL;
	})();
	var SerializableCipher = /*#__PURE__*/ (function() {
		function SerializableCipher() {}
		SerializableCipher.encrypt = function encrypt(cipher, message, key, cfg) {
			var config = Object.assign({}, this.cfg, cfg);
			var encryptor = cipher.createEncryptor(key, config);
			var ciphertext = encryptor.finalize(message);
			return new CipherParams({
				ciphertext: ciphertext,
				key: key,
				iv: encryptor.cfg.iv,
				algorithm: cipher,
				mode: encryptor.cfg.mode,
				padding: encryptor.cfg.padding,
				blockSize: encryptor.cfg.blockSize,
				formatter: config.format
			});
		};
		SerializableCipher.decrypt = function decrypt(
			cipher,
			ciphertext,
			key,
			optionalCfg
		) {
			var cfg = Object.assign({}, this.cfg, optionalCfg);
			if (!cfg.format) {
				throw new Error("could not determine format");
			}
			ciphertext = this._parse(ciphertext, cfg.format);
			if (!ciphertext.ciphertext) {
				throw new Error("could not determine ciphertext");
			}
			var plaintext = cipher
				.createDecryptor(key, cfg)
				.finalize(ciphertext.ciphertext);
			return plaintext;
		};
		SerializableCipher._parse = function _parse(ciphertext, format) {
			if (typeof ciphertext === "string") {
				return format.parse(ciphertext);
			} else {
				return ciphertext;
			}
		};
		return SerializableCipher;
	})();
	SerializableCipher.cfg = {
		blockSize: 4,
		iv: new WordArray([]),
		format: OpenSSL
	};
	var Hasher = /*#__PURE__*/ (function(_BufferedBlockAlgorit) {
		_inheritsLoose(Hasher, _BufferedBlockAlgorit);
		Hasher._createHelper = function _createHelper(hasher) {
			function helper(message, cfg) {
				var hasherClass = hasher;
				var hasherInstance = new hasherClass(cfg);
				return hasherInstance.finalize(message);
			}
			return helper;
		};
		function Hasher(cfg) {
			var _this2;
			_this2 =
				_BufferedBlockAlgorit.call(
					this,
					Object.assign({blockSize: 512 / 32}, cfg)
				) || this;
			_this2.reset();
			return _this2;
		}
		var _proto4 = Hasher.prototype;
		_proto4.update = function update(messageUpdate) {
			this._append(messageUpdate);
			this._process();
			return this;
		};
		_proto4.finalize = function finalize(messageUpdate) {
			if (messageUpdate) {
				this._append(messageUpdate);
			}
			var hash = this._doFinalize();
			return hash;
		};
		return Hasher;
	})(BufferedBlockAlgorithm);
	var T$1 = [];
	for (var i = 0; i < 64; i++) {
		T$1[i] = (Math.abs(Math.sin(i + 1)) * 0x100000000) | 0;
	}
	var MD5 = /*#__PURE__*/ (function(_Hasher) {
		_inheritsLoose(MD5, _Hasher);
		function MD5() {
			return _Hasher.apply(this, arguments) || this;
		}
		MD5.FF = function FF(a, b, c, d, x, s, t) {
			var n = a + ((b & c) | (~b & d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.GG = function GG(a, b, c, d, x, s, t) {
			var n = a + ((b & d) | (c & ~d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.HH = function HH(a, b, c, d, x, s, t) {
			var n = a + (b ^ c ^ d) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.II = function II(a, b, c, d, x, s, t) {
			var n = a + (c ^ (b | ~d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		var _proto5 = MD5.prototype;
		_proto5.reset = function reset() {
			_Hasher.prototype.reset.call(this);
			this._hash = new WordArray([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476]);
		};
		_proto5._doProcessBlock = function _doProcessBlock(M, offset) {
			for (var _i2 = 0; _i2 < 16; _i2++) {
				var offset_i = offset + _i2;
				var M_offset_i = M[offset_i];
				M[offset_i] =
					(((M_offset_i << 8) | (M_offset_i >>> 24)) & 0x00ff00ff) |
					(((M_offset_i << 24) | (M_offset_i >>> 8)) & 0xff00ff00);
			}
			var H = this._hash.words;
			var M_offset_0 = M[offset + 0];
			var M_offset_1 = M[offset + 1];
			var M_offset_2 = M[offset + 2];
			var M_offset_3 = M[offset + 3];
			var M_offset_4 = M[offset + 4];
			var M_offset_5 = M[offset + 5];
			var M_offset_6 = M[offset + 6];
			var M_offset_7 = M[offset + 7];
			var M_offset_8 = M[offset + 8];
			var M_offset_9 = M[offset + 9];
			var M_offset_10 = M[offset + 10];
			var M_offset_11 = M[offset + 11];
			var M_offset_12 = M[offset + 12];
			var M_offset_13 = M[offset + 13];
			var M_offset_14 = M[offset + 14];
			var M_offset_15 = M[offset + 15];
			var a = H[0];
			var b = H[1];
			var c = H[2];
			var d = H[3];
			a = MD5.FF(a, b, c, d, M_offset_0, 7, T$1[0]);
			d = MD5.FF(d, a, b, c, M_offset_1, 12, T$1[1]);
			c = MD5.FF(c, d, a, b, M_offset_2, 17, T$1[2]);
			b = MD5.FF(b, c, d, a, M_offset_3, 22, T$1[3]);
			a = MD5.FF(a, b, c, d, M_offset_4, 7, T$1[4]);
			d = MD5.FF(d, a, b, c, M_offset_5, 12, T$1[5]);
			c = MD5.FF(c, d, a, b, M_offset_6, 17, T$1[6]);
			b = MD5.FF(b, c, d, a, M_offset_7, 22, T$1[7]);
			a = MD5.FF(a, b, c, d, M_offset_8, 7, T$1[8]);
			d = MD5.FF(d, a, b, c, M_offset_9, 12, T$1[9]);
			c = MD5.FF(c, d, a, b, M_offset_10, 17, T$1[10]);
			b = MD5.FF(b, c, d, a, M_offset_11, 22, T$1[11]);
			a = MD5.FF(a, b, c, d, M_offset_12, 7, T$1[12]);
			d = MD5.FF(d, a, b, c, M_offset_13, 12, T$1[13]);
			c = MD5.FF(c, d, a, b, M_offset_14, 17, T$1[14]);
			b = MD5.FF(b, c, d, a, M_offset_15, 22, T$1[15]);
			a = MD5.GG(a, b, c, d, M_offset_1, 5, T$1[16]);
			d = MD5.GG(d, a, b, c, M_offset_6, 9, T$1[17]);
			c = MD5.GG(c, d, a, b, M_offset_11, 14, T$1[18]);
			b = MD5.GG(b, c, d, a, M_offset_0, 20, T$1[19]);
			a = MD5.GG(a, b, c, d, M_offset_5, 5, T$1[20]);
			d = MD5.GG(d, a, b, c, M_offset_10, 9, T$1[21]);
			c = MD5.GG(c, d, a, b, M_offset_15, 14, T$1[22]);
			b = MD5.GG(b, c, d, a, M_offset_4, 20, T$1[23]);
			a = MD5.GG(a, b, c, d, M_offset_9, 5, T$1[24]);
			d = MD5.GG(d, a, b, c, M_offset_14, 9, T$1[25]);
			c = MD5.GG(c, d, a, b, M_offset_3, 14, T$1[26]);
			b = MD5.GG(b, c, d, a, M_offset_8, 20, T$1[27]);
			a = MD5.GG(a, b, c, d, M_offset_13, 5, T$1[28]);
			d = MD5.GG(d, a, b, c, M_offset_2, 9, T$1[29]);
			c = MD5.GG(c, d, a, b, M_offset_7, 14, T$1[30]);
			b = MD5.GG(b, c, d, a, M_offset_12, 20, T$1[31]);
			a = MD5.HH(a, b, c, d, M_offset_5, 4, T$1[32]);
			d = MD5.HH(d, a, b, c, M_offset_8, 11, T$1[33]);
			c = MD5.HH(c, d, a, b, M_offset_11, 16, T$1[34]);
			b = MD5.HH(b, c, d, a, M_offset_14, 23, T$1[35]);
			a = MD5.HH(a, b, c, d, M_offset_1, 4, T$1[36]);
			d = MD5.HH(d, a, b, c, M_offset_4, 11, T$1[37]);
			c = MD5.HH(c, d, a, b, M_offset_7, 16, T$1[38]);
			b = MD5.HH(b, c, d, a, M_offset_10, 23, T$1[39]);
			a = MD5.HH(a, b, c, d, M_offset_13, 4, T$1[40]);
			d = MD5.HH(d, a, b, c, M_offset_0, 11, T$1[41]);
			c = MD5.HH(c, d, a, b, M_offset_3, 16, T$1[42]);
			b = MD5.HH(b, c, d, a, M_offset_6, 23, T$1[43]);
			a = MD5.HH(a, b, c, d, M_offset_9, 4, T$1[44]);
			d = MD5.HH(d, a, b, c, M_offset_12, 11, T$1[45]);
			c = MD5.HH(c, d, a, b, M_offset_15, 16, T$1[46]);
			b = MD5.HH(b, c, d, a, M_offset_2, 23, T$1[47]);
			a = MD5.II(a, b, c, d, M_offset_0, 6, T$1[48]);
			d = MD5.II(d, a, b, c, M_offset_7, 10, T$1[49]);
			c = MD5.II(c, d, a, b, M_offset_14, 15, T$1[50]);
			b = MD5.II(b, c, d, a, M_offset_5, 21, T$1[51]);
			a = MD5.II(a, b, c, d, M_offset_12, 6, T$1[52]);
			d = MD5.II(d, a, b, c, M_offset_3, 10, T$1[53]);
			c = MD5.II(c, d, a, b, M_offset_10, 15, T$1[54]);
			b = MD5.II(b, c, d, a, M_offset_1, 21, T$1[55]);
			a = MD5.II(a, b, c, d, M_offset_8, 6, T$1[56]);
			d = MD5.II(d, a, b, c, M_offset_15, 10, T$1[57]);
			c = MD5.II(c, d, a, b, M_offset_6, 15, T$1[58]);
			b = MD5.II(b, c, d, a, M_offset_13, 21, T$1[59]);
			a = MD5.II(a, b, c, d, M_offset_4, 6, T$1[60]);
			d = MD5.II(d, a, b, c, M_offset_11, 10, T$1[61]);
			c = MD5.II(c, d, a, b, M_offset_2, 15, T$1[62]);
			b = MD5.II(b, c, d, a, M_offset_9, 21, T$1[63]);
			H[0] = (H[0] + a) | 0;
			H[1] = (H[1] + b) | 0;
			H[2] = (H[2] + c) | 0;
			H[3] = (H[3] + d) | 0;
		};
		_proto5._doFinalize = function _doFinalize() {
			var data = this._data;
			var dataWords = data.words;
			var nBitsTotal = this._nDataBytes * 8;
			var nBitsLeft = data.sigBytes * 8;
			dataWords[nBitsLeft >>> 5] |= 0x80 << (24 - (nBitsLeft % 32));
			var nBitsTotalH = Math.floor(nBitsTotal / 0x100000000);
			var nBitsTotalL = nBitsTotal;
			dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 15] =
				(((nBitsTotalH << 8) | (nBitsTotalH >>> 24)) & 0x00ff00ff) |
				(((nBitsTotalH << 24) | (nBitsTotalH >>> 8)) & 0xff00ff00);
			dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 14] =
				(((nBitsTotalL << 8) | (nBitsTotalL >>> 24)) & 0x00ff00ff) |
				(((nBitsTotalL << 24) | (nBitsTotalL >>> 8)) & 0xff00ff00);
			data.sigBytes = (dataWords.length + 1) * 4;
			this._process();
			var hash = this._hash;
			var H = hash.words;
			for (var _i3 = 0; _i3 < 4; _i3++) {
				var H_i = H[_i3];
				H[_i3] =
					(((H_i << 8) | (H_i >>> 24)) & 0x00ff00ff) |
					(((H_i << 24) | (H_i >>> 8)) & 0xff00ff00);
			}
			return hash;
		};
		return MD5;
	})(Hasher);
	var EvpKDF = /*#__PURE__*/ (function() {
		function EvpKDF(cfg) {
			this.cfg = Object.assign(
				{keySize: 128 / 32, hasher: MD5, iterations: 1},
				cfg
			);
		}
		var _proto6 = EvpKDF.prototype;
		_proto6.compute = function compute(password, salt) {
			var hasher = new this.cfg.hasher();
			var derivedKey = new WordArray();
			var block;
			while (derivedKey.words.length < this.cfg.keySize) {
				if (block) {
					hasher.update(block);
				}
				block = hasher.update(password).finalize(salt);
				hasher.reset();
				for (var _i4 = 1; _i4 < this.cfg.iterations; _i4++) {
					block = hasher.finalize(block);
					hasher.reset();
				}
				derivedKey.concat(block);
			}
			derivedKey.sigBytes = this.cfg.keySize * 4;
			return derivedKey;
		};
		return EvpKDF;
	})();
	var OpenSSLKdf = /*#__PURE__*/ (function() {
		function OpenSSLKdf() {}
		OpenSSLKdf.execute = function execute(password, keySize, ivSize, salt) {
			if (!salt) {
				salt = WordArray.random(64 / 8);
			}
			var key = new EvpKDF({keySize: keySize + ivSize}).compute(password, salt);
			var iv = new WordArray(key.words.slice(keySize), ivSize * 4);
			key.sigBytes = keySize * 4;
			return new CipherParams({key: key, iv: iv, salt: salt});
		};
		return OpenSSLKdf;
	})();
	var PasswordBasedCipher = /*#__PURE__*/ (function() {
		function PasswordBasedCipher() {}
		PasswordBasedCipher.encrypt = function encrypt(
			cipher,
			message,
			password,
			cfg
		) {
			var config = Object.assign({}, this.cfg, cfg);
			if (config.kdf === undefined) {
				throw new Error("missing kdf in config");
			}
			var derivedParams = config.kdf.execute(
				password,
				cipher.keySize,
				cipher.ivSize
			);
			if (derivedParams.iv !== undefined) {
				config.iv = derivedParams.iv;
			}
			var ciphertext = SerializableCipher.encrypt.call(
				this,
				cipher,
				message,
				derivedParams.key,
				config
			);
			return ciphertext.extend(derivedParams);
		};
		PasswordBasedCipher.decrypt = function decrypt(
			cipher,
			ciphertext,
			password,
			cfg
		) {
			var config = Object.assign({}, this.cfg, cfg);
			if (config.format === undefined) {
				throw new Error("missing format in config");
			}
			ciphertext = this._parse(ciphertext, config.format);
			if (config.kdf === undefined) {
				throw new Error("the key derivation function must be set");
			}
			var derivedParams = config.kdf.execute(
				password,
				cipher.keySize,
				cipher.ivSize,
				ciphertext.salt
			);
			if (derivedParams.iv !== undefined) {
				config.iv = derivedParams.iv;
			}
			var plaintext = SerializableCipher.decrypt.call(
				this,
				cipher,
				ciphertext,
				derivedParams.key,
				config
			);
			return plaintext;
		};
		PasswordBasedCipher._parse = function _parse(ciphertext, format) {
			if (typeof ciphertext === "string") {
				return format.parse(ciphertext);
			} else {
				return ciphertext;
			}
		};
		return PasswordBasedCipher;
	})();
	PasswordBasedCipher.cfg = {
		blockSize: 4,
		iv: new WordArray([]),
		format: OpenSSL,
		kdf: OpenSSLKdf
	};
	var Cipher = /*#__PURE__*/ (function(_BufferedBlockAlgorit2) {
		_inheritsLoose(Cipher, _BufferedBlockAlgorit2);
		function Cipher(xformMode, key, cfg) {
			var _this3;
			_this3 =
				_BufferedBlockAlgorit2.call(this, Object.assign({blockSize: 1}, cfg)) ||
				this;
			_this3._xformMode = xformMode;
			_this3._key = key;
			_this3.reset();
			return _this3;
		}
		Cipher.createEncryptor = function createEncryptor(key, cfg) {
			var thisClass = this;
			return new thisClass(this._ENC_XFORM_MODE, key, cfg);
		};
		Cipher.createDecryptor = function createDecryptor(key, cfg) {
			var thisClass = this;
			return new thisClass(this._DEC_XFORM_MODE, key, cfg);
		};
		Cipher._createHelper = function _createHelper(cipher) {
			function encrypt(message, key, cfg) {
				if (typeof key === "string") {
					return PasswordBasedCipher.encrypt(cipher, message, key, cfg);
				} else {
					return SerializableCipher.encrypt(cipher, message, key, cfg);
				}
			}
			function decrypt(ciphertext, key, cfg) {
				if (typeof key === "string") {
					return PasswordBasedCipher.decrypt(cipher, ciphertext, key, cfg);
				} else {
					return SerializableCipher.decrypt(cipher, ciphertext, key, cfg);
				}
			}
			return {encrypt: encrypt, decrypt: decrypt};
		};
		var _proto7 = Cipher.prototype;
		_proto7.process = function process(dataUpdate) {
			this._append(dataUpdate);
			return this._process();
		};
		_proto7.finalize = function finalize(dataUpdate) {
			if (dataUpdate) {
				this._append(dataUpdate);
			}
			var finalProcessedData = this._doFinalize();
			return finalProcessedData;
		};
		return Cipher;
	})(BufferedBlockAlgorithm);
	Cipher._ENC_XFORM_MODE = 1;
	Cipher._DEC_XFORM_MODE = 2;
	Cipher.keySize = 4;
	Cipher.ivSize = 4;
	var BlockCipherModeAlgorithm = /*#__PURE__*/ (function() {
		function BlockCipherModeAlgorithm(cipher, iv) {
			this.init(cipher, iv);
		}
		var _proto8 = BlockCipherModeAlgorithm.prototype;
		_proto8.init = function init(cipher, iv) {
			this._cipher = cipher;
			this._iv = iv;
		};
		return BlockCipherModeAlgorithm;
	})();
	var BlockCipherMode = /*#__PURE__*/ (function() {
		function BlockCipherMode() {}
		BlockCipherMode.createEncryptor = function createEncryptor(cipher, iv) {
			var encryptorClass = this.Encryptor;
			return new encryptorClass(cipher, iv);
		};
		BlockCipherMode.createDecryptor = function createDecryptor(cipher, iv) {
			var decryptorClass = this.Decryptor;
			return new decryptorClass(cipher, iv);
		};
		return BlockCipherMode;
	})();
	BlockCipherMode.Encryptor = BlockCipherModeAlgorithm;
	BlockCipherMode.Decryptor = BlockCipherModeAlgorithm;
	var CBCEncryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor) {
		_inheritsLoose(CBCEncryptor, _BlockCipherModeAlgor);
		function CBCEncryptor() {
			return _BlockCipherModeAlgor.apply(this, arguments) || this;
		}
		var _proto9 = CBCEncryptor.prototype;
		_proto9.processBlock = function processBlock(words, offset) {
			if (this._cipher.cfg.blockSize === undefined) {
				throw new Error("missing blockSize in cipher config");
			}
			this.xorBlock(words, offset, this._cipher.cfg.blockSize);
			this._cipher.encryptBlock(words, offset);
			this._prevBlock = words.slice(offset, offset + this._cipher.cfg.blockSize);
		};
		_proto9.xorBlock = function xorBlock(words, offset, blockSize) {
			var block;
			if (this._iv) {
				block = this._iv;
				this._iv = undefined;
			} else {
				block = this._prevBlock;
			}
			if (block !== undefined) {
				for (var _i5 = 0; _i5 < blockSize; _i5++) {
					words[offset + _i5] ^= block[_i5];
				}
			}
		};
		return CBCEncryptor;
	})(BlockCipherModeAlgorithm);
	var CBCDecryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor2) {
		_inheritsLoose(CBCDecryptor, _BlockCipherModeAlgor2);
		function CBCDecryptor() {
			return _BlockCipherModeAlgor2.apply(this, arguments) || this;
		}
		var _proto10 = CBCDecryptor.prototype;
		_proto10.processBlock = function processBlock(words, offset) {
			if (this._cipher.cfg.blockSize === undefined) {
				throw new Error("missing blockSize in cipher config");
			}
			var thisBlock = words.slice(offset, offset + this._cipher.cfg.blockSize);
			this._cipher.decryptBlock(words, offset);
			this.xorBlock(words, offset, this._cipher.cfg.blockSize);
			this._prevBlock = thisBlock;
		};
		_proto10.xorBlock = function xorBlock(words, offset, blockSize) {
			var block;
			if (this._iv) {
				block = this._iv;
				this._iv = undefined;
			} else {
				block = this._prevBlock;
			}
			if (block !== undefined) {
				for (var _i6 = 0; _i6 < blockSize; _i6++) {
					words[offset + _i6] ^= block[_i6];
				}
			}
		};
		return CBCDecryptor;
	})(BlockCipherModeAlgorithm);
	var CBC = /*#__PURE__*/ (function(_BlockCipherMode) {
		_inheritsLoose(CBC, _BlockCipherMode);
		function CBC() {
			return _BlockCipherMode.apply(this, arguments) || this;
		}
		return CBC;
	})(BlockCipherMode);
	CBC.Encryptor = CBCEncryptor;
	CBC.Decryptor = CBCDecryptor;
	var PKCS7 = /*#__PURE__*/ (function() {
		function PKCS7() {}
		PKCS7.pad = function pad(data, blockSize) {
			var blockSizeBytes = blockSize * 4;
			var nPaddingBytes = blockSizeBytes - (data.sigBytes % blockSizeBytes);
			var paddingWord =
				(nPaddingBytes << 24) |
				(nPaddingBytes << 16) |
				(nPaddingBytes << 8) |
				nPaddingBytes;
			var paddingWords = [];
			for (var _i7 = 0; _i7 < nPaddingBytes; _i7 += 4) {
				paddingWords.push(paddingWord);
			}
			var padding = new WordArray(paddingWords, nPaddingBytes);
			data.concat(padding);
		};
		PKCS7.unpad = function unpad(data) {
			var nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;
			data.sigBytes -= nPaddingBytes;
		};
		return PKCS7;
	})();
	var BlockCipher = /*#__PURE__*/ (function(_Cipher) {
		_inheritsLoose(BlockCipher, _Cipher);
		function BlockCipher(xformMode, key, cfg) {
			return (
				_Cipher.call(
					this,
					xformMode,
					key,
					Object.assign({blockSize: 4, mode: CBC, padding: PKCS7}, cfg)
				) || this
			);
		}
		var _proto11 = BlockCipher.prototype;
		_proto11.reset = function reset() {
			_Cipher.prototype.reset.call(this);
			if (this.cfg.mode === undefined) {
				throw new Error("missing mode in config");
			}
			var modeCreator;
			if (this._xformMode === this.constructor._ENC_XFORM_MODE) {
				modeCreator = this.cfg.mode.createEncryptor;
			} else {
				modeCreator = this.cfg.mode.createDecryptor;
				this._minBufferSize = 1;
			}
			if (this._mode && this._mode.__creator === modeCreator) {
				this._mode.init(this, this.cfg.iv && this.cfg.iv.words);
			} else {
				this._mode = modeCreator.call(
					this.cfg.mode,
					this,
					this.cfg.iv && this.cfg.iv.words
				);
				this._mode.__creator = modeCreator;
			}
		};
		_proto11._doProcessBlock = function _doProcessBlock(words, offset) {
			this._mode.processBlock(words, offset);
		};
		_proto11._doFinalize = function _doFinalize() {
			if (this.cfg.padding === undefined) {
				throw new Error("missing padding in config");
			}
			var finalProcessedBlocks;
			if (this._xformMode === this.constructor._ENC_XFORM_MODE) {
				if (this.cfg.blockSize === undefined) {
					throw new Error("missing blockSize in config");
				}
				this.cfg.padding.pad(this._data, this.cfg.blockSize);
				finalProcessedBlocks = this._process(!!"flush");
			} else {
				finalProcessedBlocks = this._process(!!"flush");
				this.cfg.padding.unpad(finalProcessedBlocks);
			}
			return finalProcessedBlocks;
		};
		return BlockCipher;
	})(Cipher);
	var SBOX = [];
	var INV_SBOX = [];
	var SUB_MIX_0 = [];
	var SUB_MIX_1 = [];
	var SUB_MIX_2 = [];
	var SUB_MIX_3 = [];
	var INV_SUB_MIX_0 = [];
	var INV_SUB_MIX_1 = [];
	var INV_SUB_MIX_2 = [];
	var INV_SUB_MIX_3 = [];
	(function() {
		var d = [];
		for (var _i8 = 0; _i8 < 256; _i8++) {
			if (_i8 < 128) {
				d[_i8] = _i8 << 1;
			} else {
				d[_i8] = (_i8 << 1) ^ 0x11b;
			}
		}
		var x = 0;
		var xi = 0;
		for (var _i9 = 0; _i9 < 256; _i9++) {
			var sx = xi ^ (xi << 1) ^ (xi << 2) ^ (xi << 3) ^ (xi << 4);
			sx = (sx >>> 8) ^ (sx & 0xff) ^ 0x63;
			SBOX[x] = sx;
			INV_SBOX[sx] = x;
			var x2 = d[x];
			var x4 = d[x2];
			var x8 = d[x4];
			var t = (d[sx] * 0x101) ^ (sx * 0x1010100);
			SUB_MIX_0[x] = (t << 24) | (t >>> 8);
			SUB_MIX_1[x] = (t << 16) | (t >>> 16);
			SUB_MIX_2[x] = (t << 8) | (t >>> 24);
			SUB_MIX_3[x] = t;
			t = (x8 * 0x1010101) ^ (x4 * 0x10001) ^ (x2 * 0x101) ^ (x * 0x1010100);
			INV_SUB_MIX_0[sx] = (t << 24) | (t >>> 8);
			INV_SUB_MIX_1[sx] = (t << 16) | (t >>> 16);
			INV_SUB_MIX_2[sx] = (t << 8) | (t >>> 24);
			INV_SUB_MIX_3[sx] = t;
			if (!x) {
				x = xi = 1;
			} else {
				x = x2 ^ d[d[d[x8 ^ x2]]];
				xi ^= d[d[xi]];
			}
		}
	})();
	var RCON = [0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36];
	var AES = /*#__PURE__*/ (function(_BlockCipher) {
		_inheritsLoose(AES, _BlockCipher);
		function AES(xformMode, key, cfg) {
			return _BlockCipher.call(this, xformMode, key, cfg) || this;
		}
		var _proto12 = AES.prototype;
		_proto12.reset = function reset() {
			_BlockCipher.prototype.reset.call(this);
			if (this._nRounds && this._keyPriorReset === this._key) {
				return;
			}
			var key = (this._keyPriorReset = this._key);
			var keyWords = key.words;
			var keySize = key.sigBytes / 4;
			var nRounds = (this._nRounds = keySize + 6);
			var ksRows = (nRounds + 1) * 4;
			var keySchedule = (this._keySchedule = []);
			for (var ksRow = 0; ksRow < ksRows; ksRow++) {
				if (ksRow < keySize) {
					keySchedule[ksRow] = keyWords[ksRow];
				} else {
					var t = keySchedule[ksRow - 1];
					if (!(ksRow % keySize)) {
						t = (t << 8) | (t >>> 24);
						t =
							(SBOX[t >>> 24] << 24) |
							(SBOX[(t >>> 16) & 0xff] << 16) |
							(SBOX[(t >>> 8) & 0xff] << 8) |
							SBOX[t & 0xff];
						t ^= RCON[(ksRow / keySize) | 0] << 24;
					} else if (keySize > 6 && ksRow % keySize === 4) {
						t =
							(SBOX[t >>> 24] << 24) |
							(SBOX[(t >>> 16) & 0xff] << 16) |
							(SBOX[(t >>> 8) & 0xff] << 8) |
							SBOX[t & 0xff];
					}
					keySchedule[ksRow] = keySchedule[ksRow - keySize] ^ t;
				}
			}
			var invKeySchedule = (this._invKeySchedule = []);
			for (var invKsRow = 0; invKsRow < ksRows; invKsRow++) {
				var _ksRow = ksRows - invKsRow;
				var _t = void 0;
				if (invKsRow % 4) {
					_t = keySchedule[_ksRow];
				} else {
					_t = keySchedule[_ksRow - 4];
				}
				if (invKsRow < 4 || _ksRow <= 4) {
					invKeySchedule[invKsRow] = _t;
				} else {
					invKeySchedule[invKsRow] =
						INV_SUB_MIX_0[SBOX[_t >>> 24]] ^
						INV_SUB_MIX_1[SBOX[(_t >>> 16) & 0xff]] ^
						INV_SUB_MIX_2[SBOX[(_t >>> 8) & 0xff]] ^
						INV_SUB_MIX_3[SBOX[_t & 0xff]];
				}
			}
		};
		_proto12.encryptBlock = function encryptBlock(M, offset) {
			this._doCryptBlock(
				M,
				offset,
				this._keySchedule,
				SUB_MIX_0,
				SUB_MIX_1,
				SUB_MIX_2,
				SUB_MIX_3,
				SBOX
			);
		};
		_proto12.decryptBlock = function decryptBlock(M, offset) {
			var t = M[offset + 1];
			M[offset + 1] = M[offset + 3];
			M[offset + 3] = t;
			this._doCryptBlock(
				M,
				offset,
				this._invKeySchedule,
				INV_SUB_MIX_0,
				INV_SUB_MIX_1,
				INV_SUB_MIX_2,
				INV_SUB_MIX_3,
				INV_SBOX
			);
			t = M[offset + 1];
			M[offset + 1] = M[offset + 3];
			M[offset + 3] = t;
		};
		_proto12._doCryptBlock = function _doCryptBlock(
			M,
			offset,
			keySchedule,
			sub_mix_0,
			sub_mix_1,
			sub_mix_2,
			sub_mix_3,
			sbox
		) {
			var s0 = M[offset] ^ keySchedule[0];
			var s1 = M[offset + 1] ^ keySchedule[1];
			var s2 = M[offset + 2] ^ keySchedule[2];
			var s3 = M[offset + 3] ^ keySchedule[3];
			var ksRow = 4;
			for (var round = 1; round < this._nRounds; round++) {
				var t0 =
					sub_mix_0[s0 >>> 24] ^
					sub_mix_1[(s1 >>> 16) & 0xff] ^
					sub_mix_2[(s2 >>> 8) & 0xff] ^
					sub_mix_3[s3 & 0xff] ^
					keySchedule[ksRow++];
				var t1 =
					sub_mix_0[s1 >>> 24] ^
					sub_mix_1[(s2 >>> 16) & 0xff] ^
					sub_mix_2[(s3 >>> 8) & 0xff] ^
					sub_mix_3[s0 & 0xff] ^
					keySchedule[ksRow++];
				var t2 =
					sub_mix_0[s2 >>> 24] ^
					sub_mix_1[(s3 >>> 16) & 0xff] ^
					sub_mix_2[(s0 >>> 8) & 0xff] ^
					sub_mix_3[s1 & 0xff] ^
					keySchedule[ksRow++];
				var t3 =
					sub_mix_0[s3 >>> 24] ^
					sub_mix_1[(s0 >>> 16) & 0xff] ^
					sub_mix_2[(s1 >>> 8) & 0xff] ^
					sub_mix_3[s2 & 0xff] ^
					keySchedule[ksRow++];
				s0 = t0;
				s1 = t1;
				s2 = t2;
				s3 = t3;
			}
			var t0g =
				((sbox[s0 >>> 24] << 24) |
					(sbox[(s1 >>> 16) & 0xff] << 16) |
					(sbox[(s2 >>> 8) & 0xff] << 8) |
					sbox[s3 & 0xff]) ^
				keySchedule[ksRow++];
			var t1g =
				((sbox[s1 >>> 24] << 24) |
					(sbox[(s2 >>> 16) & 0xff] << 16) |
					(sbox[(s3 >>> 8) & 0xff] << 8) |
					sbox[s0 & 0xff]) ^
				keySchedule[ksRow++];
			var t2g =
				((sbox[s2 >>> 24] << 24) |
					(sbox[(s3 >>> 16) & 0xff] << 16) |
					(sbox[(s0 >>> 8) & 0xff] << 8) |
					sbox[s1 & 0xff]) ^
				keySchedule[ksRow++];
			var t3g =
				((sbox[s3 >>> 24] << 24) |
					(sbox[(s0 >>> 16) & 0xff] << 16) |
					(sbox[(s1 >>> 8) & 0xff] << 8) |
					sbox[s2 & 0xff]) ^
				keySchedule[ksRow++];
			M[offset] = t0g;
			M[offset + 1] = t1g;
			M[offset + 2] = t2g;
			M[offset + 3] = t3g;
		};
		return AES;
	})(BlockCipher);
	AES.keySize = 8;
	var H$2 = [];
	var K = [];
	var W$2 = [];
	var SHA256 = /*#__PURE__*/ (function(_Hasher2) {
		_inheritsLoose(SHA256, _Hasher2);
		function SHA256() {
			return _Hasher2.apply(this, arguments) || this;
		}
		var _proto13 = SHA256.prototype;
		_proto13.reset = function reset() {
			_Hasher2.prototype.reset.call(this);
			this._hash = new WordArray(H$2.slice(0));
		};
		_proto13._doProcessBlock = function _doProcessBlock(M, offset) {
			var Hl = this._hash.words;
			var a = Hl[0];
			var b = Hl[1];
			var c = Hl[2];
			var d = Hl[3];
			var e = Hl[4];
			var f = Hl[5];
			var g = Hl[6];
			var h = Hl[7];
			for (var _i10 = 0; _i10 < 64; _i10++) {
				if (_i10 < 16) {
					W$2[_i10] = M[offset + _i10] | 0;
				} else {
					var gamma0x = W$2[_i10 - 15];
					var gamma0 =
						((gamma0x << 25) | (gamma0x >>> 7)) ^
						((gamma0x << 14) | (gamma0x >>> 18)) ^
						(gamma0x >>> 3);
					var gamma1x = W$2[_i10 - 2];
					var gamma1 =
						((gamma1x << 15) | (gamma1x >>> 17)) ^
						((gamma1x << 13) | (gamma1x >>> 19)) ^
						(gamma1x >>> 10);
					W$2[_i10] = gamma0 + W$2[_i10 - 7] + gamma1 + W$2[_i10 - 16];
				}
				var ch = (e & f) ^ (~e & g);
				var maj = (a & b) ^ (a & c) ^ (b & c);
				var sigma0 =
					((a << 30) | (a >>> 2)) ^
					((a << 19) | (a >>> 13)) ^
					((a << 10) | (a >>> 22));
				var sigma1 =
					((e << 26) | (e >>> 6)) ^
					((e << 21) | (e >>> 11)) ^
					((e << 7) | (e >>> 25));
				var t1 = h + sigma1 + ch + K[_i10] + W$2[_i10];
				var t2 = sigma0 + maj;
				h = g;
				g = f;
				f = e;
				e = (d + t1) | 0;
				d = c;
				c = b;
				b = a;
				a = (t1 + t2) | 0;
			}
			Hl[0] = (Hl[0] + a) | 0;
			Hl[1] = (Hl[1] + b) | 0;
			Hl[2] = (Hl[2] + c) | 0;
			Hl[3] = (Hl[3] + d) | 0;
			Hl[4] = (Hl[4] + e) | 0;
			Hl[5] = (Hl[5] + f) | 0;
			Hl[6] = (Hl[6] + g) | 0;
			Hl[7] = (Hl[7] + h) | 0;
		};
		_proto13._doFinalize = function _doFinalize() {
			var nBitsTotal = this._nDataBytes * 8;
			var nBitsLeft = this._data.sigBytes * 8;
			this._data.words[nBitsLeft >>> 5] |= 0x80 << (24 - (nBitsLeft % 32));
			this._data.words[(((nBitsLeft + 64) >>> 9) << 4) + 14] = Math.floor(
				nBitsTotal / 0x100000000
			);
			this._data.words[(((nBitsLeft + 64) >>> 9) << 4) + 15] = nBitsTotal;
			this._data.sigBytes = this._data.words.length * 4;
			this._process();
			return this._hash;
		};
		return SHA256;
	})(Hasher);
	var NoPadding = /*#__PURE__*/ (function() {
		function NoPadding() {}
		NoPadding.pad = function pad(data, blockSize) {};
		NoPadding.unpad = function unpad(data) {};
		return NoPadding;
	})();
	var ECBEncryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor3) {
		_inheritsLoose(ECBEncryptor, _BlockCipherModeAlgor3);
		function ECBEncryptor() {
			return _BlockCipherModeAlgor3.apply(this, arguments) || this;
		}
		var _proto14 = ECBEncryptor.prototype;
		_proto14.processBlock = function processBlock(words, offset) {
			this._cipher.encryptBlock(words, offset);
		};
		return ECBEncryptor;
	})(BlockCipherModeAlgorithm);
	var ECBDecryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor4) {
		_inheritsLoose(ECBDecryptor, _BlockCipherModeAlgor4);
		function ECBDecryptor() {
			return _BlockCipherModeAlgor4.apply(this, arguments) || this;
		}
		var _proto15 = ECBDecryptor.prototype;
		_proto15.processBlock = function processBlock(words, offset) {
			this._cipher.decryptBlock(words, offset);
		};
		return ECBDecryptor;
	})(BlockCipherModeAlgorithm);
	var ECB = /*#__PURE__*/ (function(_BlockCipherMode2) {
		_inheritsLoose(ECB, _BlockCipherMode2);
		function ECB() {
			return _BlockCipherMode2.apply(this, arguments) || this;
		}
		return ECB;
	})(BlockCipherMode);
	ECB.Encryptor = ECBEncryptor;
	ECB.Decryptor = ECBDecryptor;
	var lib = {
		BlockCipher: BlockCipher,
		WordArray: WordArray,
		CipherParams: CipherParams,
		Hasher: Hasher,
		SerializableCipher: SerializableCipher,
		PasswordBasedCipher: PasswordBasedCipher
	};
	var algo = {AES: AES, SHA256: SHA256, MD5: MD5};
	var enc = {Utf8: Utf8, Hex: Hex};
	var pad = {NoPadding: NoPadding, PKCS7: PKCS7};
	var mode = {CBC: CBC, ECB: ECB};
	var AES$1 = lib.BlockCipher._createHelper(algo.AES);
	var SHA256$1 = lib.Hasher._createHelper(algo.SHA256);
	var MD5$1 = lib.Hasher._createHelper(algo.MD5);

	// 加密
	function encrypt(word, keyStr) {
		var key = enc.Utf8.parse(keyStr);
		var srcs = enc.Utf8.parse(word);
		var encrypted = AES$1.encrypt(srcs, key, {
			mode: mode.ECB,
			padding: pad.PKCS7
		});
		return encrypted.toString();
	}

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var ZCheckbox = /*@__PURE__*/ (function(Component) {
		function ZCheckbox(props) {
			Component.call(this, props);
		}

		if (Component) ZCheckbox.__proto__ = Component;
		ZCheckbox.prototype = Object.create(Component && Component.prototype);
		ZCheckbox.prototype.constructor = ZCheckbox;
		ZCheckbox.prototype.render = function() {
			return apivm.h("a-iconfont", {
				size: G$1.appFontSize + (this.props.size || 0),
				name: this.props.checked ? "fangxingxuanzhongfill" : "fangxingweixuanzhong",
				color: this.props.checked ? this.props.color || G$1.appTheme : "#999"
			});
		};

		return ZCheckbox;
	})(Component);
	apivm.define("z-checkbox", ZCheckbox);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G$1.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G$1.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h("view", {class: "watermark_box"}, G$1.watermark),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G$1.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G$1.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G$1.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G$1.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G$1.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				dotClose: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.dotClose = true;
							setTimeout(function() {
								this$1.data.dotClose = false;
							}, 300);
						} else {
							G$1.actionSheetPop.pageClose();
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.closePage = function() {
			if (this.data.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			if (this.data.dotClose || _item.disabled) {
				return;
			}
			_item.buttonIndex = _index + 1;
			G$1.actionSheetPop.callback(_item);
			this.closePage();
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h("view", {
						onClick: function() {
							return this$1.closePage();
						},
						class: "actionSheet_cancel"
					}),
					apivm.h(
						"view",
						{
							class: this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "flex-shrink: 0;"
						},
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					apivm.h(
						"scroll-view",
						{
							class: !this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "height: auto;background: #FFF;flex-shrink: 1;",
							"scroll-y": true
						},
						apivm.h("view", {class: "watermark_box"}, G$1.watermark),
						(this.props.dataMore.data || []).map(function(item, index) {
							return (
								(isParameters(item.show) ? item.show : true) && [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.itemClick(item, index);
											},
											class: "actionSheet_item",
											style:
												"justify-content:" +
												(item.justify || "center") +
												";opacity: " +
												(item.disabled ? "0.3" : "1") +
												";"
										},
										apivm.h(
											"view",
											null,
											item.icon &&
												apivm.h("a-iconfont", {
													style: "margin-right:10px;",
													name: item.icon,
													color: (isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G$1.appTheme
														: item.color || "#333",
													size:
														G$1.appFontSize +
														(isParameters(item.size) ? Number(item.size) : 4)
												})
										),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-1) +
													";color:" +
													((isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G$1.appTheme
														: item.color || "#333")
											},
											item.name
										)
									),
									index != this$1.props.dataMore.data.length - 1 &&
										!this$1.props.dataMore.cancel &&
										apivm.h(
											"view",
											{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
											apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
										)
								]
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-shrink: 0;"},
						this.props.dataMore.cancel &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "actionSheet_item",
									style:
										"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + ";color:#333"},
									this.props.dataMore.cancel || "取消"
								)
							)
					),
					apivm.h("view", {
						style:
							"background:#fff;flex-shrink: 0;padding-bottom:" +
							safeArea().bottom +
							"px;"
					})
				]
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_cancel": {flex: "1", minHeight: "20%", flexShrink: "0"},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "5px 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G$1.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G$1.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G$1.appTheme : "#FFF",
						size: G$1.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G$1.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.inputBlur = function(e) {
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color: "#999",
								size: G$1.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G$1.appFontSize + 14) +
									"px;color:#999;" +
									loadConfiguration()
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration() +
								"height:" +
								(G$1.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style": "color:#ccc;",
							class: "z_input_input flex_w",
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								isParameters(this.props.readonly)
									? this.props.readonly
									: false,
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G$1.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G$1.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"}
	};
	apivm.define("z-input", ZInput);

	var YLogin = /*@__PURE__*/ (function(Component) {
		function YLogin(props) {
			Component.call(this, props);
			this.data = {
				accountInfo: {
					show: true,
					icon: "shoujihaoma",
					confirmType: "next",
					cleanFocus: true,
					iconSize: 4,
					maxlength: 32,
					value: "",
					placeholder: "账号"
				}, //账号信息
				passwordInfo: {
					show: true,
					icon: "mima1",
					confirmType: "go",
					cleanFocus: true,
					iconSize: 4,
					maxlength: 32,
					value: "",
					placeholder: "密码",
					inputType: "password",
					isLook: false
				}, //密码信息

				captchaInfo: {
					show: false,
					cleanFocus: true,
					confirmType: "go",
					icon: "yanzhengma1",
					iconSize: 4,
					maxlength: 6,
					value: "",
					placeholder: "验证码",
					hint: "获取验证码",
					timeAll: 60,
					nowTimeout: 0,
					expression: "\\D"
				}, //验证码
				loginErrorInfo: {text: "", type: -1}, //登录失败请重试 登录失败相关	0账号处不对	1密码处不对
				agreement: false, //是否同意用户协议
				agreementText: "我已阅读并同意《服务协议》和《隐私政策》",
				showPage: false
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.show) {
						this.show = this.props.dataMore.show;
						if (this.show) {
							this.baseInit();
						} else {
							this.baseclose();
						}
					}
				}
			};
		}

		if (Component) YLogin.__proto__ = Component;
		YLogin.prototype = Object.create(Component && Component.prototype);
		YLogin.prototype.constructor = YLogin;
		YLogin.prototype.installed = function() {
			G$1.sysSign = sysSign();
			G$1.appName = getPrefs("sys_systemName") || "";
			G$1.appFont = getPrefs("appFont") || "heitiSimplified";
			G$1.appTheme =
				getPrefs("appTheme" + G$1.sysSign) ||
				(G$1.sysSign == "rd" ? "#C61414" : "#3088FE");
			G$1.appFontSize = Number(getPrefs("appFontSize") || "16");
			this.data.showPage = true;
		};
		YLogin.prototype.uninstall = function() {
			this.baseclose();
		};
		YLogin.prototype.baseInit = function() {
			var this$1 = this;

			this.netizen = this.props.dataMore.netizen || "";
			G$1.showHeadTheme = G$1.appTheme;
			setTimeout(function() {
				this$1.update();
			}, 0);
			this.data.accountInfo.value = getPrefs("loginUserName") || "";
			this.data.passwordInfo.value = getPrefs("loginPassword") || "";
			if (this.netizen == 1) {
				this.data.accountInfo.placeholder = "手机号";
				this.data.passwordInfo.show = false;
				this.data.captchaInfo.show = true;
			} else {
				ajax(
					{u: appUrl() + "open_api/verifyCode/enableStatus"},
					"enableStatus",
					function(ret, err) {
						this$1.data.captchaInfo.show = ret && ret.data;
					},
					"开启短信验证",
					"post",
					{
						body: JSON.stringify({})
					},
					{Authorization: ""}
				);
			}
			if (!G$1.appName) {
				getAppInfos();
			}
		};
		YLogin.prototype.baseclose = function() {
			G$1.showHeadTheme = "";
		};
		YLogin.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		YLogin.prototype.inputFocus = function(_who) {
			this.data.loginErrorInfo.text = "";
		};
		YLogin.prototype.inputConfirm = function(_who) {
			switch (_who) {
				case "accountInfo":
					if (this.data.passwordInfo.show) {
						$("#passwordInput").focus();
					} else if (this.data.captchaInfo.show) {
						$("#captchaInput").focus();
					}
					break;
				case "passwordInfo":
					if (this.data.captchaInfo.show) {
						$("#captchaInput").focus();
					} else {
						this.login();
					}
					break;
				case "captchaInfo":
					this.login();
					break;
			}
		};
		YLogin.prototype.login = function() {
			var this$1 = this;

			if (!this.data.agreement) {
				toast({msg: "请" + this.data.agreementText.substring(2)});
				return;
			}
			this.data.loginErrorInfo.text = "";
			if (this.data.accountInfo.show && !this.data.accountInfo.value) {
				this.data.loginErrorInfo.text = "账号不能为空！";
				this.data.loginErrorInfo.type = 0;
				return;
			}
			if (this.data.passwordInfo.show && !this.data.passwordInfo.value) {
				this.data.loginErrorInfo.text = "密码不能为空！";
				this.data.loginErrorInfo.type = 1;
				return;
			}
			if (this.data.captchaInfo.show && !this.data.captchaInfo.value) {
				this.data.loginErrorInfo.text = "验证码不能为空！";
				this.data.loginErrorInfo.type = 2;
				return;
			}
			removePrefs("sys_aresId"); //登录的时候删除地区
			removePrefs("sys_token");
			showProgress("登录中");
			var param = {};

			if (this.netizen == 1) {
				param.grant_type = "anonymous";
				param.mobile = this.data.accountInfo.value;
				param.userName = "公众" + param.mobile.substr(-4);
			} else {
				param.grant_type = "password";
				param.username = this.data.accountInfo.value;
				param.password = encrypt(this.data.passwordInfo.value, "zysofthnzx202002");
			}
			if (this.data.captchaInfo.show) {
				param.verifyCodeId = this.data.captchaInfo.id;
				param.verifyCode = this.data.captchaInfo.value;
			}
			ajax(
				{u: appUrl() + "oauth/token?", t: "login", web: this.netizen == 1},
				"login",
				function(ret, err) {
					var code = ret ? ret.code : "";
					if (code == 200) {
						var token = ret.data.token || "";
						setPrefs("sys_token", token);
						setPrefs("loginUserName", this$1.data.accountInfo.value);
						setPrefs("terminal", this$1.netizen == 1 ? "PUBLIC" : "APP");
						if (this$1.netizen == 1) {
							hideProgress();
							setPrefs("public_token", token); //公众
							setPrefs(
								"tokenEndTime",
								"" + new Date(new Date() * 1 + 1800 * 1000).getTime()
							); //公众30min过期
							setPrefs("isAutoLogin", "true");
							G$1.loginResult = {
								accountId: null,
								mobile: param.mobile,
								userName: param.userName
							};

							this$1.verifyScuress();
						} else {
							setPrefs("loginPassword", this$1.data.passwordInfo.value);
							this$1.loginAllBack();
						}
					} else {
						hideProgress();
						this$1.data.loginErrorInfo.text = ret
							? ret.message || ret.body
							: err.msg || err.body || NET_ERR;
						this$1.data.loginErrorInfo.type = -1;
					}
				},
				"登录token",
				"post",
				{
					values: param
				},
				{
					"u-login-areaId": "",
					Authorization: "basic enlzb2Z0Onp5c29mdCo2MDc5",
					"content-type": "application/x-www-form-urlencoded"
				}
			);
		};
		YLogin.prototype.loginAllBack = function(_progress) {
			var this$1 = this;

			if (_progress) {
				showProgress("登录中", true);
			}
			getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {
				hideProgress();
				if (ret) {
					var code = ret.code || "";
					if (code == 200) {
						setPrefs("loginPassword", this$1.data.passwordInfo.value);
						setPrefs("isAutoLogin", "true");
						G$1.loginResult = ret.data;
						this$1.verifyScuress();
					} else {
						this$1.data.loginErrorInfo.text = ret.message;
						this$1.data.loginErrorInfo.type = -1;
					}
				} else {
					this$1.data.loginErrorInfo.text = ret
						? ret.message || ret.body
						: err.msg || err.body || NET_ERR;
					this$1.data.loginErrorInfo.type = -1;
				}
			});
		};
		YLogin.prototype.verifyScuress = function() {
			var this$1 = this;

			saveLogin(G$1.loginResult);
			setTimeout(function() {
				this$1.fire("result");
			}, 0);
			if (!G$1.appName) {
				getAppInfos();
			}
		};
		YLogin.prototype.getVerification = function() {
			var this$1 = this;

			if (this.data.captchaInfo.nowTimeout > 0) {
				return;
			}
			sendCode({phone: this.data.accountInfo.value}, function(ret) {
				this$1.data.captchaInfo.id = ret.data;
				this$1.data.captchaInfo.isSend = this$1.data.accountInfo.value;
				this$1.data.captchaInfo.nowTimeout = this$1.data.captchaInfo.timeAll;
				this$1.countdown();
			});
		};
		YLogin.prototype.countdown = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.captchaInfo.nowTimeout--;
				if (this$1.data.captchaInfo.nowTimeout > 0) {
					this$1.countdown();
				}
			}, 1000);
		};
		YLogin.prototype.chageAgreement = function(_index) {
			if (isParameters(_index)) {
				if (_index > 5 && _index < 12) {
					openWin_apptext({title: "服务协议", pt: "fwxy", id: "fwxy"});
					return;
				} else if (_index > 12 && _index < 19) {
					openWin_apptext({title: "隐私政策", pt: "yszc", id: "yszc"});
					return;
				}
			}
			this.data.agreement = !this.data.agreement;
		};
		YLogin.prototype.openOther = function(_who) {
			switch (_who) {
				case "password":
					openWin_password({title: "重置密码", type: 1});
					break;
				case "helpLogin":
					openWin_apptext({title: "其他帮助", pt: "qtbz"});
					break;
				case "register":
					openWin_password({title: "立即注册", type: 3});
					break;
			}
		};
		YLogin.prototype.hiddenEvent = function(_type) {
			if (!G$1.loginDoubleClick) {
				G$1.loginDoubleClick = true;
				setTimeout(function() {
					G$1.loginDoubleClick = false;
				}, 500);
				return;
			}
			var buttons = [];
			if (_type == "left") {
				buttons = [
					"还原为初始地址",
					"自定义平台(rd人大，zx政协)",
					"自定义系统地址",
					"一键改为标准环境",
					"是否提示被挤下线(0是1否)",
					"修改融云chatHeader",
					"伪装成~(不懂不要点)",
					"修改融云正测环境(1正2测)"
				];
			} else {
				buttons = ["app版本"];
			}
			function reboot() {
				setTimeout(function() {
					alert("已修改，立即重启生效", function(ret) {
						cleanAllMsg();
						rebootApp();
					});
				}, 50);
			}
			actionSheet(
				{
					title: "隐藏操作Public beta-V 0.0.4",
					buttons: buttons
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					if (_index <= buttons.length) {
						var nowType = 1; // 正常提示文本  2输入框文本 3确定文本
						var msgText = buttons[_index - 1];
						var alertParam = {
							title: "",
							msg: msgText,
							buttons: ["我知道了"]
						};

						switch (msgText) {
							case "app版本":
								alertParam.title = msgText;
								alertParam.msg = api.appVersion;
								break;
							case "自定义平台(rd人大，zx政协)":
								nowType = 2;
								alertParam.msg = G$1.sysSign;
								break;
							case "自定义系统地址":
								nowType = 2;
								alertParam.msg = appUrl();
								break;
							case "是否提示被挤下线(0是1否)":
								nowType = 2;
								alertParam.msg = getPrefs("downLine") || "0";
								break;
							case "修改融云chatHeader":
								nowType = 2;
								alertParam.msg = chatHeader();
								break;
							case "修改融云正测环境(1正2测)":
								nowType = 2;
								alertParam.msg = chatEnvironment();
								break;
							case "伪装成~(不懂不要点)":
								nowType = 2;
								alertParam.msg = getPrefs("camouflageId");
								break;
							case "还原为初始地址":
							case "一键改为标准环境":
								nowType = 3;
								break;
						}

						if (nowType == 2) {
							alertParam.title = msgText;
							alertParam.type = "input";
							alertParam.buttons = ["确定", "取消"];
						} else if (nowType == 3) {
							alertParam.buttons = ["确定", "取消"];
						}
						alert(alertParam, function(ret) {
							if ((nowType == 2 || nowType == 3) && ret.buttonIndex == 1) {
								switch (msgText) {
									case "还原为初始地址":
										removePrefs("sys_appUrl");
										removePrefs("sys_sign");
										removePrefs("sys_chatEnvironment");
										removePrefs("sys_chatHeader");
										removePrefs("camouflageId");
										reboot();
										break;
									case "一键改为标准环境":
										setPrefs(
											"sys_appUrl",
											"https://bz.cszysoft.com:" +
												(G$1.sysSign == "rd" ? "8088" : "8089") +
												"/lzt/"
										);
										reboot();
										break;
									case "自定义平台(rd人大，zx政协)":
										if (ret.content != alertParam.msg) {
											setPrefs("sys_sign", ret.content);
											reboot();
										}
										break;
									case "自定义系统地址":
										if (ret.content != alertParam.msg) {
											setPrefs("sys_appUrl", ret.content);
											reboot();
										}
										break;
									case "修改融云正测环境(1正2测)":
										if (ret.content != alertParam.msg) {
											setPrefs("sys_chatEnvironment", ret.content);
											reboot();
										}
										break;
									case "是否提示被挤下线(0是1否)":
										if (ret.content != alertParam.msg) {
											setPrefs("downLine", ret.content);
											reboot();
										}
										break;
									case "修改融云chatHeader":
										if (ret.content != alertParam.msg) {
											setPrefs("sys_chatHeader", ret.content);
											reboot();
										}
										break;
									case "伪装成~(不懂不要点)":
										if (ret.content != alertParam.msg) {
											setPrefs("camouflageId", ret.content);
											reboot();
										}
										break;
								}
							}
						});
					}
				}
			);
		};
		YLogin.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:" +
						(this.props.dataMore.bg ? "#FFF" : "transparent") +
						";"
				},
				this.data.showPage && [
					apivm.h(
						"y-scroll-view",
						{class: "flex_h"},
						apivm.h(
							"view",
							{
								style:
									"background:" +
									G$1.appTheme +
									";border-bottom-right-radius:50px;padding-top:" +
									headerTop() +
									"px;height: " +
									(166 + headerTop()) +
									"px;"
							},
							apivm.h(
								"view",
								{class: "login_bg_default"},
								apivm.h("image", {
									style: loadConfigurationSize(39) + "border-radius:15px;",
									src: showImg(appUrl() + "pageImg/open/logo"),
									mode: "aspectFit"
								}),
								apivm.h(
									"text",
									{class: "login_bg_default_text", style: loadConfiguration(14)},
									G$1.appName
								)
							)
						),
						apivm.h(
							"view",
							{style: "width:50px;height:50px;background:" + G$1.appTheme + ";"},
							apivm.h("view", {
								style:
									"width:100%;height:100%;background:#FFF;border-top-left-radius:50px;"
							})
						),
						apivm.h(
							"view",
							{style: "padding:0 30px;"},
							apivm.h(
								"view",
								{
									class: "login_input_warp",
									style:
										"border:1px solid " +
										(this.data.loginErrorInfo.text && this.data.loginErrorInfo.type == 0
											? "#ec1f2e"
											: "transparent") +
										";"
								},
								apivm.h("z-input", {
									id: "accountInput",
									class: "flex_w",
									dataMore: this.data.accountInfo,
									bg: "transparent",
									onConfirm: function() {
										return this$1.inputConfirm("accountInfo");
									},
									onFocus: function() {
										return this$1.inputFocus("accountInfo");
									}
								})
							),
							apivm.h(
								"view",
								null,
								this.data.passwordInfo.show &&
									apivm.h(
										"view",
										{
											class: "login_input_warp",
											style:
												"margin-top:20px;border:1px solid " +
												(this.data.loginErrorInfo.text && this.data.loginErrorInfo.type == 1
													? "#ec1f2e"
													: "transparent") +
												";"
										},
										apivm.h("z-input", {
											id: "passwordInput",
											class: "flex_w",
											dataMore: this.data.passwordInfo,
											bg: "transparent",
											onConfirm: function() {
												return this$1.inputConfirm("passwordInfo");
											},
											onFocus: function() {
												return this$1.inputFocus("passwordInfo");
											}
										})
									)
							),
							apivm.h(
								"view",
								null,
								this.data.captchaInfo.show &&
									apivm.h(
										"view",
										{
											class: "login_input_warp",
											style:
												"margin-top:20px;border:1px solid " +
												(this.data.loginErrorInfo.text && this.data.loginErrorInfo.type == 2
													? "#ec1f2e"
													: "transparent") +
												";"
										},
										apivm.h("z-input", {
											id: "captchaInput",
											class: "flex_w",
											dataMore: this.data.captchaInfo,
											bg: "transparent",
											onConfirm: function() {
												return this$1.inputConfirm("captchaInfo");
											},
											onFocus: function() {
												return this$1.inputFocus("captchaInfo");
											}
										}),
										apivm.h("z-button", {
											onClick: function() {
												return this$1.getVerification();
											},
											plain: true,
											text:
												this.data.captchaInfo.nowTimeout > 0
													? "重发" + this.data.captchaInfo.nowTimeout + "s"
													: this.data.captchaInfo.hint,
											size: 14,
											style: "padding:4px 10px;margin-right:10px;",
											color: this.data.captchaInfo.nowTimeout > 0 ? "#999" : G$1.appTheme
										})
									)
							),
							apivm.h(
								"view",
								null,
								this.data.loginErrorInfo.text &&
									apivm.h(
										"text",
										{style: loadConfiguration(), class: "login_msg_error"},
										this.data.loginErrorInfo.text
									)
							)
						),
						apivm.h(
							"view",
							{style: "margin-top: 15px;padding: 0 20px 0 30px;"},
							apivm.h(
								"view",
								{style: "flex-direction:row;"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.chageAgreement();
										},
										style: "margin-right: 5px;"
									},
									apivm.h("z-checkbox", {
										checked: this.data.agreement,
										size: 2,
										color: this.data.agreement ? G$1.appTheme : "#999"
									})
								),
								apivm.h(
									"view",
									{style: "flex:1;flex-direction:row;flex-wrap: wrap;"},
									(Array.isArray(this.data.agreementText)
										? this.data.agreementText
										: Object.values(this.data.agreementText)
									).map(function(item$1, index$1) {
										return apivm.h(
											"text",
											{
												onClick: function() {
													return this$1.chageAgreement(index$1);
												},
												style:
													loadConfiguration(-2) +
													"color:" +
													((index$1 > 6 && index$1 < 13) || (index$1 > 13 && index$1 < 20)
														? G$1.appTheme
														: "#333")
											},
											item$1
										);
									})
								)
							)
						),
						apivm.h(
							"view",
							{style: "margin-top: 47px;padding: 0 30px;"},
							apivm.h("z-button", {
								onClick: function() {
									return this$1.login();
								},
								disabled:
									(this.data.accountInfo.show && !this.data.accountInfo.value) ||
									(this.data.passwordInfo.show && !this.data.passwordInfo.value) ||
									(this.data.captchaInfo.show && !this.data.captchaInfo.value),
								text: "登录",
								size: 18,
								style: "box-shadow: 0px 2px 10px 1px rgba(198,20,20,0.12);height:48px;",
								color: G$1.appTheme
							})
						),
						apivm.h(
							"view",
							{
								style:
									"flex-direction:row;align-items : center;justify-content : center;margin-top:60px;"
							},
							apivm.h(
								"text",
								{
									style: loadConfiguration(-2),
									onClick: function() {
										return this$1.openOther("password");
									}
								},
								"忘记密码"
							),
							apivm.h("view", {
								style:
									"width:1px;height:" +
									G$1.appFontSize +
									"px;background:#666;margin:0 20px;"
							}),
							apivm.h(
								"text",
								{
									style: loadConfiguration(-2),
									onClick: function() {
										return this$1.openOther("helpLogin");
									}
								},
								"其他帮助"
							),
							[]
						),
						apivm.h("view", {class: "xy_center"}, G$1.loginInfo),
						apivm.h("view", {style: "height:50px;"})
					),
					apivm.h("text", {
						onClick: function() {
							return this$1.hiddenEvent("left");
						},
						style: "height:" + (footerBottom() + 50) + "px;left: 0;",
						class: "login_alertInfo"
					}),
					apivm.h("text", {
						onClick: function() {
							return this$1.hiddenEvent("right");
						},
						style: "height:" + (footerBottom() + 50) + "px;right: 0;",
						class: "login_alertInfo"
					})
				]
			);
		};

		return YLogin;
	})(Component);
	YLogin.css = {
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".flex_h": {flex: "1", height: "1px"},
		".flex_w": {flex: "1", width: "1px"},
		".login_alertInfo": {
			position: "absolute",
			zIndex: "999",
			bottom: "0",
			width: "50px",
			height: "50px",
			background: "transparent"
		},
		".login_bg_default": {
			position: "absolute",
			zIndex: "999",
			alignItems: "center",
			bottom: "30px",
			left: "30px",
			right: "20px",
			flexDirection: "row",
			wordWrap: "break-word",
			wordBreak: "break-all"
		},
		".login_bg_default_text": {
			color: "#fff",
			marginLeft: "10px",
			fontWeight: "600"
		},
		".login_input_warp": {
			flexDirection: "row",
			alignItems: "center",
			width: "100%",
			height: "48px",
			marginTop: "20px",
			boxShadow: "0px 1px 6px 1px rgba(24,64,118,0.12)",
			background: "#FFF",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomRightRadius: "4px",
			borderBottomLeftRadius: "4px"
		},
		".login_msg_error": {
			color: "red",
			textAlign: "center",
			padding: "5px 10px",
			marginTop: "15px"
		}
	};
	apivm.define("y-login", YLogin);

	var ZButton$1 = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.zButtonClick = function(e, _props) {
			var this$1 = this;

			if (!this.props.disabled) {
				setTimeout(function() {
					this$1.fire("click", {});
				}, 0);
			}
			if (!this.props.bubble) {
				G.stopBubble(e);
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "z-button",
					style:
						"\n\t\tborder-top-left-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-top-right-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-bottom-right-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-bottom-left-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-color:" +
						this.props.color +
						";\n\t\tbackground:" +
						(this.props.plain ? "#FFF" : this.props.color) +
						";\n\t\topacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";\n\t\t" +
						this.props.style,
					onClick: function(e) {
						return this$1.zButtonClick(e, this$1.props);
					}
				},
				apivm.h(
					"text",
					{
						style:
							"\n\t\tcolor:" +
							(this.props.plain ? this.props.color : "#FFF") +
							";\n\t\t" +
							G.loadConfiguration((this.props.size || 16) - 16) +
							"\n\t"
					},
					this.props.text
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton$1.css = {
		".z-button": {
			padding: "8.3px 12px",
			textAlign: "center",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			display: "flex",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton$1);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.installed = function() {};
		ZAvatar.prototype.imgError = function(e) {};
		ZAvatar.prototype.imgLoad = function(e) {};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{class: "class||''", style: this.props.style || ""},
				apivm.h("z-image", {
					proportionMin: "0.9",
					proportionMax: "1.1",
					class: "z_avatar_img",
					thumbnail: true,
					mode: this.props.mode || "aspectFill",
					style: "border-radius: " + (this.props.radius || "50%") + ";",
					src: G.showImg(
						(T.isObject(this.props.data)
						? this.props.data.url ||
						  this.props.data.headImg ||
						  this.props.data.senderHeadImg
						: this.props.data)
							? this.props.data
							: myjs.appUrl() + "img/default_user_head.jpg"
					),
					onError: this.imgError,
					onLoad: this.imgLoad
				})
			);
		};

		return ZAvatar;
	})(Component);
	ZAvatar.css = {".z_avatar_img": {width: "100%", height: "100%"}};
	apivm.define("z-avatar", ZAvatar);

	var ImagePreviewer = /*@__PURE__*/ (function(Component) {
		function ImagePreviewer(props) {
			Component.call(this, props);
			this.data = {
				index: 1,
				activeIndex: 0,
				indicator: false,
				statusBarStyle: ""
			};
		}

		if (Component) ImagePreviewer.__proto__ = Component;
		ImagePreviewer.prototype = Object.create(Component && Component.prototype);
		ImagePreviewer.prototype.constructor = ImagePreviewer;
		ImagePreviewer.prototype.current = function() {
			return T.platform() == "web"
				? this.props.activeIndex
				: this.data.activeIndex;
		};
		ImagePreviewer.prototype.closePre = function() {
			this.close();
		};
		ImagePreviewer.prototype.close = function() {
			G.imagePreviewer.show = false;
			T.sendEvent("updatePage");
		};
		ImagePreviewer.prototype.installed = function() {
			// 修复顶部状态栏样式
			if (this.props.type == 1) {
				// api.setStatusBarStyle({ style: "dark" });
				this.data.indicator = true;
			} else if (this.props.type == 2) {
				// api.setStatusBarStyle({ style: "white" });
				this.data.indicator = false;
			}
			this.data.activeIndex = this.props.activeIndex;
			// 滑动到指定图片显示
			if (this.props.activeIndex) {
				this.data.index = parseInt(this.props.activeIndex) + 1;
			}
		};
		ImagePreviewer.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "image-previewer-page"},
				apivm.h(
					"view",
					{style: "height:1px;flex:1;justify-content: center;"},
					apivm.h(
						"view",
						{style: "height:1px;flex:1;justify-content: center;"},
						apivm.h(
							"swiper",
							{
								onClick: function() {
									return this$1.closePre();
								},
								class: "image-previewer-swiper",
								circular: true,
								current: this.current(),
								"indicator-dots": "true",
								"indicator-color": "#737373",
								"indicator-active-color": "#ffffff"
							},
							(Array.isArray(this.props.imgs)
								? this.props.imgs
								: Object.values(this.props.imgs)
							).map(function(item$1, index$1) {
								return apivm.h(
									"swiper-item",
									{
										onClick: function() {
											return this$1.closePre();
										},
										style: "height:100%;"
									},
									apivm.h("image", {
										class: "image-previewer-img",
										src: G.showImg(item$1),
										thumbnail: "false",
										mode: "aspectFit"
									})
								);
							})
						)
					)
				)
			);
		};

		return ImagePreviewer;
	})(Component);
	ImagePreviewer.css = {
		".image-previewer-page": {height: "100%", background: "#000"},
		".image-previewer-swiper": {height: "100%"},
		".image-previewer-img": {height: "100%", width: "100%"},
		".image-previewer-header": {
			display: "flex",
			flexDirection: "row",
			alignItems: "center",
			flexWrap: "nowrap",
			padding: "0 5px"
		},
		".image-previewer-back": {
			width: "30px",
			height: "45px",
			backgroundRepeat: "no-repeat",
			backgroundPosition: "center",
			backgroundSize: "20px"
		},
		".image-previewer-close": {width: "30px", height: "45px"},
		".image-previewer-title": {
			flex: "1",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexWrap: "nowrap",
			fontSize: "18px",
			textAlign: "center",
			textOverflow: "ellipsis",
			overflow: "hidden",
			whiteSpace: "nowrap",
			color: "#fff",
			height: "45px",
			lineHeight: "45px"
		},
		".image-previewer-placeholder": {
			width: "30px",
			height: "45px",
			marginRight: "5px"
		},
		".image-previewer-right": {width: "30px", height: "45px"}
	};
	apivm.define("image-previewer", ImagePreviewer);

	var ZActionsheet$1 = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.dotClose) {
								setTimeout(function() {
									this$1.props.dataMore.dotClose = false;
								}, 500);
							}
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.installed = function() {};
		ZActionsheet.prototype.closePage = function() {
			if (this.props.dataMore.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
			T.sendEvent("updatePage");
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			var this$1 = this;

			if (this.props.dataMore.dotClose) {
				return;
			}
			if (_item.disabled) {
				return;
			}
			if (T.isParameters(this.props.active)) {
				this.props.active.key = _item.key;
			}
			_item.buttonIndex = _index + 1;
			setTimeout(function() {
				this$1.fire("click", _item);
				this$1.closePage();
			}, 0);
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "actionSheet_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "flex:1;min-height:30%;flex-shrink: 0;"
				}),
				apivm.h(
					"scroll-view",
					{class: "actionSheet_warp", style: "flex-shrink: 1;", "scroll-y": true},
					apivm.h(
						"view",
						null,
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(-2 + 1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					this.props.data.map(function(item, index) {
						return (
							(T.isParameters(item.show) ? item.show : true) && [
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick(item, index);
										},
										class: "actionSheet_item",
										style:
											"justify-content:" +
											(item.type ? "flex-start" : "center") +
											";opacity: " +
											(item.disabled ? "0.3" : "1") +
											";"
									},
									apivm.h(
										"view",
										null,
										T.isParameters(this$1.props.active) &&
											T.isParameters(this$1.props.active.direction) &&
											this$1.props.active.direction >= 0 &&
											item.key == this$1.props.active.key &&
											apivm.h("a-iconfont", {
												style:
													"margin-left:-25px;margin-right:10px;transform: rotate(" +
													(this$1.props.active.direction == 1 ? "0" : "180") +
													"deg);",
												name: "zhixiangxia",
												color: G.appTheme,
												size: G.appFontSize - 1
											})
									),
									apivm.h(
										"view",
										null,
										item.type == "img"
											? apivm.h("img", {src: "", alt: ""})
											: item.type == "icon"
											? apivm.h("a-iconfont", {
													style:
														"font-weight:" + (item.weight || "400") + ";margin-right:10px;",
													name: item.src,
													color: item.color || "#333",
													size:
														G.appFontSize +
														(T.isParameters(item.size) ? Number(item.size) : 4)
											  })
											: ""
									),
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(-2 + 1) +
												";color:" +
												(item.key ==
												(T.isParameters(this$1.props.active) && this$1.props.active.key)
													? G.appTheme
													: "#333")
										},
										item.value
									)
								),
								index != this$1.props.data.length - 1 &&
									!this$1.props.cancel &&
									!this$1.props.dataMore.cancel &&
									apivm.h(
										"view",
										{style: "width:100%;height:1px;padding:0 16px;"},
										apivm.h("a-divider", null)
									)
							]
						);
					})
				),
				apivm.h(
					"view",
					null,
					(this.props.cancel || this.props.dataMore.cancel) &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.closePage();
								},
								class: "actionSheet_item",
								style:
									"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration(-2) + ";color:#333"},
								this.props.dataMore.cancel || "取消"
							)
						)
				),
				apivm.h("view", {
					style: "background:#fff;padding-bottom:" + T.safeArea().bottom + "px;"
				})
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet$1.css = {
		".actionSheet_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			height: "auto"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "0 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet$1);

	// 加密
	function getFileAdress(_param, _callback) {
		if (_param === void 0) {
			_param = {};
		}
		//文档地址 https://www.yozodcs.com/page/help.html#help9
		T.ajax(
			{u: "https://www.yozodcs.com/fcscloud/file/http?"},
			"onlinefile",
			function(ret, err) {
				if (ret) {
					var data = (ret.data || {}).data;
					if (data) {
						T.ajax(
							{u: "https://www.yozodcs.com/fcscloud/composite/convert?"},
							"onlinefile",
							function(ret, err) {
								if (ret) {
									var viewUrl = (ret.data || {}).viewUrl;
									if (viewUrl) {
										_callback(viewUrl, ret.data);
									} else {
										_callback(null, ret.message || "打开失败，请重试");
									}
								} else {
									_callback(null, "打开失败");
								}
							},
							"生成链接",
							"post",
							{
								values: {
									srcRelativePath: data,
									convertType:
										G.getFileInfo(data.substring(data.lastIndexOf("."))).convertType ||
										"0",
									isDccAsync: 1,
									isCopy: 0,
									noCache: 0,
									fileUrl: _param.url,
									showFooter: 0, //是否显示页脚
									isHeaderBar: 0,
									htmlTitle: "详情"
								}
							},

							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);
					} else {
						_callback(null, ret.message || "打开失败，请重试");
					}
				} else {
					_callback(null, "打开失败");
				}
			},
			"在线转换",
			"post",
			{
				values: {
					fileUrl: _param.url
				}
			},

			{
				"content-type": "application/x-www-form-urlencoded"
			}
		);
	}

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				withSrc: "",
				showSrc: "",
				showPoster: "",
				showError: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.src != this.data.withSrc) {
						this.data.withSrc = this.props.src;
						this.dealWith();
					}
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.installed = function() {};
		ZVideo.prototype.dealWith = function() {
			var this$1 = this;

			console.log(
				"withSrc:" +
					this.data.withSrc +
					"-" +
					this.data.showSrc +
					"-" +
					dayjs().unix()
			);
			if ((this.data.withSrc + "").indexOf("http") != 0) {
				//不是http开头 说明系统内附件
				var cachePath = T.getPrefs("attach_" + this.data.withSrc);
				console.log(cachePath);
				if (
					cachePath &&
					dayjs().unix() - Number(cachePath.split("-attachPath-")[0]) < 86400
				) {
					this.data.showSrc = cachePath.split("-attachPath-")[1];
					this.data.showPoster =
						myjs.tomcatAddress() + "utils2/proxyVideo?" + this.data.showSrc;
					return;
				}
				var src = myjs.appUrl() + "file/preview/" + this.data.withSrc;
				getFileAdress({url: src}, function(ret, err) {
					if (ret) {
						T.ajax(
							{u: ret, dt: "text", _this: this$1},
							"onlinefile",
							function(ret, err) {
								var matchResult = ret ? ret.match(/videoFile = "([^"]+)"/) : "";
								if (matchResult) {
									this$1.data.showError = "";
									function unicodeToChinese(str) {
										return str.replace(/\\u(\w{4})/g, function(match, code) {
											return String.fromCharCode(parseInt(code, 16));
										});
									}
									var path = unicodeToChinese(matchResult[1]).replace(/\\/g, "");
									this$1.data.showSrc = path;
									this$1.data.showPoster =
										myjs.tomcatAddress() + "utils2/proxyVideo?" + this$1.data.showSrc;
									T.setPrefs(
										"attach_" + this$1.data.withSrc,
										dayjs().unix() + "-attachPath-" + path
									);
								} else {
									this$1.data.showError = err;
								}
							},
							"附件详情",
							"get"
						);
					} else {
						this$1.data.showError = err;
					}
				});
			} else {
				this.data.showSrc = this.data.withSrc;
				this.data.showPoster =
					myjs.tomcatAddress() + "utils2/proxyVideo?" + this.data.showSrc;
			}
		};
		ZVideo.prototype.again = function(e) {
			if (this.data.showError) {
				this.data.showError = "";
				this.dealWith();
			}
			G.stopBubble(e);
		};
		ZVideo.prototype.render = function() {
			return apivm.h(
				"view",
				{
					a: this.monitor,
					style:
						"width:100%;height: " +
						(T.platform() == "web"
							? api.winWidth > 600
								? 600
								: api.winWidth
							: api.winWidth) *
							0.52 +
						"px;" +
						this.props.style,
					class: this.props.class
				},
				this.data.showSrc
					? apivm.h("video", {
							id: this.props.id,
							controls: true,
							style: "width:100%;height:100%;object-fit: cover;",
							src: this.data.showSrc,
							poster: this.props.poster || this.data.showPoster,
							mode: "aspectFill"
					  })
					: apivm.h(
							"view",
							{
								onClick: this.again,
								style:
									"width:100%;height:100%;align-items: center;justify-content: center;"
							},
							this.data.showError != ""
								? apivm.h(
										"view",
										{
											style:
												"flex-direction:row;align-items: center;justify-content: center;"
										},
										apivm.h("a-iconfont", {
											name: "huanyuan",
											color: "#666",
											style: "font-weight: 600;margin-right:10px;",
											size: G.appFontSize
										}),
										apivm.h(
											"text",
											{style: "color:#666;" + G.loadConfiguration()},
											"视频加载失败"
										)
								  )
								: apivm.h("image", {
										style: "width:50px;height:50px;",
										src: myjs.shareAddress(1) + "img/loading.gif",
										mode: "aspectFill",
										thumbnail: "false"
								  })
					  )
			);
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false //是否展开了
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.installed = function() {};
		ZRichText.prototype.expandShow = function() {
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(T.isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (T.isObject(expText) || T.isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			var notTagText = T.removeTag(expText);
			this.data.hasExpand =
				T.isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = T.decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log(expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = T.removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nowItem = {
					label: nlabel,
					index: index,
					text: item,
					style: styleMatch ? styleMatch[1] : ""
				};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: styleMatch ? styleMatch[1] : ""
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: styleMatch ? styleMatch[1] : ""
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var tdWidth = this$1.getStyle(item.style, "width") || "150px";
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					showTexts.push(addItem);
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			G.stopBubble(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			console.log(imgs, "imgs");
			G.openImgPreviewer({
				index: G.getItemForKey(_item.index + "", imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			G.stopBubble(e);
			if (T.platform() == "web") {
				window.open(_item.href);
				return;
			}
			T.openWin(
				"mo_details_url",
				"../mo_details_url/mo_details_url.stml",
				{url: _item.href},
				this
			);
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp(_item + ":s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			G.touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{a: this.monitor, class: this.props.class},
				apivm.h(
					"view",
					null,
					(Array.isArray(this.data.listData)
						? this.data.listData
						: Object.values(this.data.listData)
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							null,
							item$1.label == "text"
								? apivm.h(
										"view",
										null,
										apivm.h(
											"text",
											{
												style:
													this$1.props.style +
													"line-height: " +
													G.appFontSize * 1.8 +
													"px;" +
													(this$1.props.detail && T.platform() != "app"
														? "text-indent: 2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item$1.text
										)
								  )
								: null,
							item$1.label == "text_a"
								? apivm.h(
										"view",
										{
											onClick: function(e) {
												return this$1.openHrefs(e, item$1, index$1);
											}
										},
										apivm.h(
											"text",
											{style: this$1.props.style + "color: blue;", class: "richText"},
											item$1.text
										)
								  )
								: item$1.label == "br"
								? apivm.h(
										"view",
										null,
										apivm.h("view", {style: "height:" + G.appFontSize + "px;"})
								  )
								: item$1.label == "img"
								? apivm.h(
										"view",
										{
											onClick: function(e) {
												return this$1.openImages(e, item$1, index$1);
											}
										},
										apivm.h(
											"view",
											{class: "richImgBox"},
											apivm.h("image", {
												class: "richImg",
												mode: "aspectFill",
												thumbnail: "false",
												src: G.showImg(item$1.src)
											})
										)
								  )
								: item$1.label == "video" || item$1.label == "source"
								? apivm.h(
										"view",
										{style: "margin:5px 0;"},
										item$1.src && apivm.h("z-video", {src: item$1.src})
								  )
								: item$1.label == "table"
								? apivm.h(
										"view",
										null,
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchStart: this$1.nTouchmove
												},
												(item$1.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item$1.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{style: "" + G.loadConfiguration(), class: "richText"},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								  )
								: apivm.h("view", null)
						);
					})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: function() {
									return this$1.expandShow();
								}
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration(-2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {alignItems: "center", justifyContent: "center"},
		".richImg": {width: "100%", maxWidth: "100%", maxHeight: "100%"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	var ZAlert$1 = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				search: {dotIcon: true, show: true, input: "", value: ""}
			};
			this.compute = {
				isShow: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.input) {
								this.data.search.input = this.props.dataMore.content;
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.installed = function() {};
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				this.fire("cancel");
			}
			T.sendEvent("updatePage");
		};
		ZAlert.prototype.closeStop = function(e) {
			G.stopBubble(e);
		};
		ZAlert.prototype.inputIng = function(e) {
			if (T.isParameters(e)) {
				this.data.search.input = e.detail.value;
			}
		};
		ZAlert.prototype.itemClick = function() {
			var this$1 = this;

			setTimeout(function() {
				if (this$1.props.dataMore.input || this$1.props.dataMore.textarea) {
					this$1.props.dataMore.content = this$1.data.search.input;
				}
				this$1.data.search.input = "";
				this$1.fire("click", this$1.props.dataMore);
				this$1.fire("sure");
				this$1.closePage(1);
			}, 0);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "alert_box",
					onClick: this.closeStop,
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				this.data.show
					? apivm.h(
							"view",
							{class: "alert_warp", onClick: this.closeStop},
							apivm.h(
								"view",
								{class: "alert_content_title"},
								this.props.dataMore.title &&
									apivm.h(
										"text",
										{class: "alert_title", style: "" + G.loadConfiguration(4)},
										this.props.dataMore.title
									)
							),
							apivm.h(
								"scroll-view",
								{class: "alert_content_box", "scroll-x": false, "scroll-y": true},
								this.props.dataMore.richText
									? apivm.h("z-rich-text", {
											style: G.loadConfiguration(1) + "color:#333;",
											nodes: this.props.dataMore.content
									  })
									: this.props.dataMore.input
									? apivm.h(
											"view",
											{style: "height:36px;width:100%;"},
											apivm.h("z-search", {
												id: "alert_input",
												dataMore: this.data.search,
												placeholder: this.props.dataMore.placeholder
											})
									  )
									: this.props.dataMore.textarea
									? apivm.h("textarea", {
											id: "alert_input",
											style:
												G.loadConfiguration(1) +
												"height: " +
												(this.props.dataMore.height || "150") +
												"px;",
											class: "alert_textarea",
											placeholder: this.props.dataMore.placeholder,
											"placeholder-style": "color:#999;",
											value: this.data.search.input,
											"confirm-type": "return",
											onInput: this.inputIng
									  })
									: apivm.h(
											"text",
											{class: "alert_content", style: "" + G.loadConfiguration(1)},
											this.props.dataMore.content
									  )
							),
							apivm.h(
								"view",
								{style: "width:100%;height:1px;padding:0 15px;"},
								apivm.h("a-divider", null)
							),
							apivm.h(
								"view",
								{class: "alert_btn_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "alert_btn_item",
										style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) +
												"color:" +
												(this.props.dataMore.cancel.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.cancel.color) +
												";"
										},
										this.props.dataMore.cancel.text
									)
								),
								apivm.h(
									"view",
									{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
									apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
								),
								apivm.h(
									"view",
									{
										onClick: this.itemClick,
										class: "alert_btn_item",
										style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) +
												"color:" +
												(this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color) +
												";"
										},
										this.props.dataMore.sure.text
									)
								)
							)
					  )
					: null
			);
		};

		return ZAlert;
	})(Component);
	ZAlert$1.css = {
		".alert_box": {
			position: "absolute",
			zIndex: "1001",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {margin: "30px 15px", maxHeight: "350px"},
		".alert_title": {
			color: "#333333",
			fontWeight: "bold",
			padding: "20px 20px 0",
			textAlign: "center"
		},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {flexDirection: "row", alignItems: "center"},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#eee",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		},
		".alert_textarea::placeholder": {color: "#999"}
	};
	apivm.define("z-alert", ZAlert$1);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				showPage: false,
				initFrist: true, //首次初始化
				viewappearFrist: true, //是否首次进入页面
				initFrequency: false,
				pageParam: {},
				pageType: ""
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore) {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							if (this.data.show) {
								this.baseInit();
								setTimeout(function() {
									this$1.data.showPage = true;
								}, 80);
								console.log("base-page-param：" + JSON.stringify(this.data.pageParam));
							} else {
								this.data.showPage = false;
								if (this.data.pageParam.paramSaveKey) {
									T.removePrefs(this.data.pageParam.paramSaveKey);
								}
								this.fire("baseclose", null);
							}
						}
						//监听刷新事件
						if (this.props.dataMore.pageRefresh == 1) {
							this.props.dataMore.pageRefresh = 0;
							this.pageRefresh();
						}
					}
				}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			if (!this.props.dataMore) {
				this.baseInit();
				console.log("base-page-param：" + JSON.stringify(this.data.pageParam)); //没有dataMore 才是新页面
				// T.addEventListener('viewappear', (ret, err)=> {
				// 	// console.log("我收到了返回事件"+JSON.stringify(T.pageParam(this)));
				// 	if(this.viewappearFrist){
				// 		this.viewappearFrist = false;
				// 		return;
				// 	}
				// 	this.pageRefresh();
				// });
				T.addEventListener("updatePage", function(ret, err) {
					this$1.update();
				});
			}
		};
		YBasePage.prototype.pageRefresh = function() {
			var this$1 = this;
			setTimeout(function() {
				this$1.fire("pageRefresh");
			}, 0);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			//组件内 刚开始会调用多次 这里判断一下 300ms只返回一次
			if (this.data.initFrequency) {
				return;
			}
			this.data.initFrequency = true;
			setTimeout(function() {
				this$1.data.initFrequency = false;
			}, 300);
			this.data.pageParam = T.pageParam(this.props._this);
			if (this.data.pageParam.token) {
				T.setPrefs("sys_token", decodeURIComponent(this.data.pageParam.token));
				if (!T.getPrefs("sys_Mobile")) {
					getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
				}
			}
			if (this.data.pageParam.areaId) {
				T.setPrefs("sys_aresId", this.data.pageParam.areaId);
			}
			this.init();
			setTimeout(
				function() {
					this$1.fire("init", {first: this$1.data.initFrist});
					this$1.data.initFrist = false;
				},
				T.systemType() == "android" && !this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.init = function() {
			this.data.pageType = this.data.pageParam.pageType || "page";
		};
		YBasePage.prototype.close = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("close", null);
			}, 0);
		};
		YBasePage.prototype.penetrate = function() {};
		YBasePage.prototype.alertCallback = function(_type) {
			if (T.isNumber(_type)) {
				T.sendEvent("base_alert_callback", {buttonIndex: _type});
			} else {
				_type.detail.buttonIndex = 1;
				T.sendEvent("base_alert_callback", _type.detail);
			}
		};
		YBasePage.prototype.areaCallback = function(e) {
			T.sendEvent("base_areas_callback", e.detail);
		};
		YBasePage.prototype.actionSheetCallback = function(e) {
			T.sendEvent("base_actionSheet_callback", e.detail);
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: G.htmlClass + " base_page_warp",
					style:
						G.htmlStyle +
						";display:" +
						(this.props.dataMore ? (this.data.showPage ? "flex" : "none") : "flex") +
						";background:" +
						(this.props.dataMore && this.props.dataMore.type == "half"
							? "rgba(0,0,0,0.4)"
							: "#FFF") +
						";"
				},
				G.appTheme && [
					this.props.dataMore &&
						this.props.dataMore.type == "half" &&
						apivm.h("view", {
							onClick: function() {
								return this$1.close();
							},
							style:
								"height:" +
								(this.props.dataMore.boxAuto
									? "1px;flex:1"
									: (this.props.shadowH || "14%") + ";flex-shrink: 0;")
						}),
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.penetrate();
							},
							style:
								"background:" +
								(this.props.bg || "#FFF") +
								";height:" +
								(this.props.dataMore && this.props.dataMore.boxAuto
									? "auto;max-height:90%;"
									: "1px;flex:1") +
								";border-radius: " +
								(this.props.dataMore && this.props.dataMore.type == "half"
									? "10px 10px"
									: "0 0") +
								" 0 0;"
						},
						apivm.h(
							"view",
							{style: "flex-shrink: 0;"},
							this.props.dataMore &&
								this.props.dataMore.type == "half" &&
								!this.props.closeH && [
									apivm.h(
										"view",
										{class: "base_page_header_warp", style: "height: 49px;"},
										apivm.h(
											"view",
											{
												class: "base_page_header_main",
												style: this.props.titleStyle || "padding: 0 44px;"
											},
											this.props.titleBox
												? [this.props.children.length >= 3 ? this.props.children[2] : null]
												: [
														apivm.h(
															"text",
															{
																style: G.loadConfiguration(1) + "color:#333",
																class: "base_page_header_main_text"
															},
															G.showTextSize(this.props.title, 8, 1)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "base_page_header_left_box", style: "height:49px;"},
											this.props.back
												? [this.props.children.length >= 1 ? this.props.children[0] : null]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "base_page_header_btn"
															},
															apivm.h(
																"text",
																{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
																"取消"
															)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "base_page_header_right_box", style: "height:49px;"},
											this.props.more
												? [this.props.children.length >= 2 ? this.props.children[1] : null]
												: []
										)
									),
									apivm.h(
										"view",
										{style: "width:100%;height:1px;padding:0 16px;"},
										apivm.h("a-divider", null)
									)
								]
						),
						apivm.h(
							"view",
							{
								style:
									"width:100%;height:" +
									(this.props.dataMore && this.props.dataMore.boxAuto
										? "auto"
										: "1px;flex:1") +
									";" +
									(T.platform() != "app" ? "overflow-y: scroll;" : "")
							},

							apivm.h(
								"view",
								null,
								((this.props.dataMore &&
									(this.props.dataMore.type == "full" ||
										this.data.pageParam.pageType == "home")) ||
									!this.props.dataMore) &&
									(this.props.titleBox || this.props.back || this.props.more
										? true
										: G.showHeader(this.props._this) && !this.props.closeH) &&
									apivm.h(
										"view",
										{
											style:
												"width:100%;height:auto;padding-top:" +
												G.headerTop() +
												"px;background:" +
												((this.props._this &&
												this.props._this.data &&
												this.props._this.data.headTheme
													? this.props._this.data.headTheme || ""
													: "") || G.headTheme)
										},
										apivm.h(
											"view",
											{class: "base_page_header_warp", style: "height: 44px;"},
											apivm.h(
												"view",
												{
													class: "base_page_header_main",
													style: this.props.titleStyle || "padding: 0 44px;"
												},
												this.props.titleBox
													? [this.props.children.length >= 3 ? this.props.children[2] : null]
													: [
															apivm.h(
																"text",
																{
																	style:
																		G.loadConfiguration(4) +
																		"color:" +
																		G.getHeadThemeRelatively(this.props._this),
																	class: "base_page_header_main_text"
																},
																G.showTextSize(this.props.title, 8, 1)
															)
													  ]
											),
											apivm.h(
												"view",
												{class: "base_page_header_left_box", style: "height:44px;"},
												apivm.h(
													"view",
													{style: "height: 44px;"},
													this.props.back
														? [
																this.props.children.length >= 1 ? this.props.children[0] : null
														  ]
														: [
																apivm.h(
																	"view",
																	{
																		onClick: function() {
																			return this$1.close();
																		},
																		class: "base_page_header_btn",
																		style: {
																			display: (this.props.dataMore &&
																			this.props.dataMore.type == "full"
																			? true
																			: G.showHeader(this.props._this) &&
																			  this.data.pageType == "page")
																				? "flex"
																				: "none"
																		}
																	},
																	apivm.h("a-iconfont", {
																		name: "fanhui1",
																		color: G.getHeadThemeRelatively(this.props._this),
																		size: G.appFontSize + 1
																	})
																)
														  ]
												)
											),
											apivm.h(
												"view",
												{class: "base_page_header_right_box", style: "height:44px;"},
												this.props.more
													? [this.props.children.length >= 2 ? this.props.children[1] : null]
													: []
											)
										)
									)
							),
							apivm.h(
								"view",
								{
									style:
										"width:100%;height:" +
										(this.props.dataMore && this.props.dataMore.boxAuto
											? "auto"
											: "1px;flex:1") +
										";"
								},
								this.props.children.length >= 4 ? this.props.children[3] : null
							)
						)
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h(
							"view",
							{
								class: "suspend_box",
								style: "display:" + (G.imagePreviewer.show ? "flex" : "none") + ";"
							},
							G.imagePreviewer.show &&
								apivm.h("image-previewer", {
									imgs: G.imagePreviewer.imgs,
									activeIndex: G.imagePreviewer.activeIndex,
									type: G.imagePreviewer.type
								})
						),
						apivm.h("mo-areas", {
							dataMore: G.areasBox,
							pageParam: G.areasBox.pageParam,
							onChange: this.areaCallback
						}),
						apivm.h("z-actionSheet", {
							dataMore: G.actionSheetBox,
							data: G.actionSheetBox.data,
							active: G.actionSheetBox.active,
							onClick: this.actionSheetCallback
						}),
						apivm.h("z-alert", {
							dataMore: G.alertBox,
							onClick: this.alertCallback,
							onCancel: function() {
								return this$1.alertCallback(2);
							}
						})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		div: {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".base_page_warp": {
			width: "100%",
			height: "100%",
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0",
			background: "rgba(0,0,0,0.4)"
		},
		".base_page_header_warp": {
			flexDirection: "row",
			width: "100%",
			alignItems: "center",
			flexShrink: "0"
		},
		".base_page_header_main": {
			width: "1px",
			height: "100%",
			flex: "1",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_main_text": {fontWeight: "600", flexShrink: "0"},
		".base_page_header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".base_page_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px",
			flexDirection: "row-reverse",
			flexShrink: "0"
		},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".avm-toast": {zIndex: "999"},
		".avm-confirm-mask": {zIndex: "999"},
		".suspend_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.6)"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var ASkeleton = /*@__PURE__*/ (function(Component) {
		function ASkeleton(props) {
			Component.call(this, props);
			this.data = {};
			this.compute = {
				rows: function() {
					var row = this.props.row || 0;
					return Array.from({length: row}).fill("");
				},
				length: function() {},
				avatarClass: function() {
					return (
						"a-skeleton_avatar " +
						(this.props["avatar-shape"] == "square" ? "" : "a-skeleton_round")
					);
				},
				avatarStyle: function() {
					var size = this.props["avatar-size"];
					return size ? "width:" + size + ";height:" + size + ";" : "";
				},
				titleStyle: function() {
					var titleWidth = this.props["title-width"];
					return titleWidth ? "width:" + titleWidth + ";" : "";
				}
			};
		}

		if (Component) ASkeleton.__proto__ = Component;
		ASkeleton.prototype = Object.create(Component && Component.prototype);
		ASkeleton.prototype.constructor = ASkeleton;
		ASkeleton.prototype.beforeRender = function() {
			if (!("loading" in this.props)) {
				this.props.loading = true;
			}
		};
		ASkeleton.prototype.getChildNode = function() {
			return this.props.children.length > 0 ? this.props.children[0] : null;
		};
		ASkeleton.prototype.getRowStyle = function(index) {
			return "width:" + this.getRowWidth(index) + ";";
		};
		ASkeleton.prototype.getRowWidth = function(index) {
			var rowWidth = this.props["row-width"] || "100%";

			if (rowWidth === "100%" && index === this.props.row - 1) {
				return "60%";
			}

			if (Array.isArray(rowWidth)) {
				return rowWidth[index];
			}

			return rowWidth;
		};
		ASkeleton.prototype.render = function() {
			var this$1 = this;
			return (
				(this.props.loading &&
					apivm.h(
						"view",
						{class: "a-skeleton"},
						this.props.avatar &&
							apivm.h("view", {class: this.avatarClass, style: this.avatarStyle}),
						apivm.h(
							"view",
							{style: "flex:1"},
							this.props.title &&
								apivm.h(
									"text",
									{class: "a-skeleton_title", style: this.titleStyle},
									this.props.title
								),
							this.props.row &&
								apivm.h(
									"view",
									null,
									(Array.isArray(this.rows) ? this.rows : Object.values(this.rows)).map(
										function(item$1, index$1) {
											return apivm.h("view", {
												class: "a-skeleton_row",
												style: this$1.getRowStyle(index$1)
											});
										}
									)
								)
						)
					)) ||
				this.getChildNode()
			);
		};

		return ASkeleton;
	})(Component);
	ASkeleton.css = {
		".a-skeleton": {width: "100%", flexDirection: "row", padding: "10px 16px"},
		".a-skeleton_avatar": {
			flexShrink: "0",
			width: "32px",
			height: "32px",
			marginRight: "16px",
			backgroundColor: "#f2f3f5"
		},
		".a-skeleton_round": {borderRadius: "999px"},
		".a-skeleton_title": {
			width: "40%",
			height: "16px",
			backgroundColor: "#f2f3f5"
		},
		".a-skeleton_row": {
			marginTop: "12px",
			width: "40%",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("a-skeleton", ASkeleton);

	var YScrollView$1 = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				refreshTriggered: false, //设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发
				upperThreshold: this.props.upperThreshold || 50, //距顶部/左边多远时，触发 scrolltoupper 事件
				lowerThreshold: this.props.lowerThreshold || 50, //距底部/右边多远时，触发 scrolltolower 事件

				scrollVID: "" //滚动位置
			};
			this.compute = {
				monitor: function() {
					if (this.data.scrollVID != (this.props["scroll-into-view"] || "")) {
						this.data.scrollVID = this.props["scroll-into-view"] || "";
						if (this.data.scrollVID) {
							this.scrollTo(this.data.scrollVID);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.install = function() {};
		YScrollView.prototype.installed = function() {};
		YScrollView.prototype.onscrolltoupper = function(e) {};
		YScrollView.prototype.onscrolltolower = function(e) {
			this.fire("up", {});
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			this.fire("scroll", detail);
		};
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			this.fire("lower", {});
			this.data.refreshTriggered = true;
			setTimeout(function() {
				this$1.data.refreshTriggered = false;
			}, 150);
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			try {
				if (T.platform() != "mp") {
					var _animated = T.isParameters(this.props["scroll-with-animation"])
						? this.props["scroll-with-animation"]
						: true;
					document
						.getElementById(this.props.id)
						.scrollTo(
							T.platform() == "app"
								? {view: nowView, animated: _animated}
								: {
										top: this.getOffestValue(
											document.getElementById(nowView),
											this.props.id
										).top,
										behavior: _animated ? "smooth" : "instant"
								  }
						);
				}
			} catch (e) {}
		};
		YScrollView.prototype.getOffestValue = function(elem, parentId) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == parentId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"view",
				{a: this.monitor, style: "flex:1;height:1px;" + (this.props.style || "")},
				apivm.h(
					"scroll-view",
					{
						id: "" + this.props.id,
						style: (this.props.style || "") + "background-color:transparent;",
						class: "" + (this.props.class || ""),
						"scroll-x": T.isParameters(this.props["scroll-x"])
							? this.props["scroll-x"]
							: false,
						"scroll-y": T.isParameters(this.props["scroll-y"])
							? this.props["scroll-y"]
							: true,
						bounces: T.isParameters(this.props["bounces"])
							? this.props["bounces"]
							: true,
						"scroll-into-view":
							"" + (T.platform() == "mp" ? this.props["scroll-into-view"] || "" : ""),
						"scroll-with-animation":
							T.platform() == "mp"
								? T.isParameters(this.props["scroll-with-animation"])
									? this.props["scroll-with-animation"]
									: true
								: false,
						"refresher-enabled":
							T.platform() != "web" &&
							(T.isParameters(this.props["refresher-enabled"])
								? this.props["refresher-enabled"]
								: false),
						"refresher-threshold": T.isParameters(this.props["refresher-threshold"])
							? this.props["refresher-threshold"]
							: 65,
						"refresher-background": T.isParameters(this.props["refresher-background"])
							? this.props["refresher-background"]
							: "#FFF",
						"refresher-triggered": this.data.refreshTriggered,
						"upper-threshold": this.upperThreshold,
						"lower-threshold": this.lowerThreshold,
						onScrolltoupper: this.onscrolltoupper,
						onScrolltolower: this.onscrolltolower,
						onRefresherrefresh: this.onrefresherrefresh,
						onScroll: this.onscroll
					},
					apivm.h(
						"view",
						{style: this.props.firsts || ""},
						this.props.children || null
					),
					apivm.h(
						"view",
						null,
						T.isParameters(this.props.data)
							? [
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											style: {display: this.props.data.skeleton ? "flex" : "none"}
										},
										(Array.isArray([1, 2, 3]) ? [1, 2, 3] : Object.values([1, 2, 3])).map(
											function(item$1, index$1) {
												return apivm.h("a-skeleton", {title: true, row: "3"});
											}
										)
									),
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											style: {
												display:
													!this.props.data.skeleton &&
													!this.props.data.notList &&
													this.props.data.listLength == 0
														? "flex"
														: "none"
											}
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(-4) + "color: #CCCCCC;padding:16px;"},
											this.props.data.text || "暂无数据"
										)
									),
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											onClick: this.onscrolltolower,
											style: {
												display:
													!this.props.data.skeleton &&
													!this.props.data.notList &&
													this.props.data.listLength != 0
														? "flex"
														: "none"
											}
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(-4) + "color: #CCCCCC;padding:15px;"},
											this.props.data.text
										)
									),
									apivm.h(
										"view",
										{style: {display: !this.props.data.dotFooter ? "flex" : "none"}},
										apivm.h("view", {
											style: "padding-bottom:" + T.safeArea().bottom + "px;"
										})
									)
							  ]
							: null
					)
				)
			);
		};

		return YScrollView;
	})(Component);
	YScrollView$1.css = {
		".y_scroll_box": {
			alignItems: "center",
			justifyContent: "center",
			width: "100%"
		}
	};
	apivm.define("y-scroll-view", YScrollView$1);

	var MoCloudDiskS = /*@__PURE__*/ (function(Component) {
		function MoCloudDiskS(props) {
			Component.call(this, props);
			this.data = {
				G: G,
				pageParam: {},
				pageType: "",
				title: "",

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				pageNot: {
					skeleton: true,
					listLength: 0,
					type: "0",
					text: "",
					dotFooter: true
				},
				listData: [],

				listSelect: [],
				dataMore: {
					mode: "list", //list列表 grid宫格
					isSelect: false, //是否选择中
					type: "select"
				},

				panShareId: "", //分享id
				secretCode: "", //随机校验码

				user: {name: "", url: ""},
				shareCode: {
					show: false,
					input: "",
					dotIcon: true,
					maxlength: 4
				},

				expireTime: null,

				level: {
					key: "",
					defaultKey: "",
					data: [
						//面包屑导航
						{key: "0", value: "全部文件"}
					]
				},

				loginInfo: {
					show: false,
					components: true,
					callback: true,
					openType: "1", //1转存云盘   2转发好友
					bg: true
				},

				aroundPage: {
					//转存弹窗
					show: false,
					key: "long_resave", //long_move long_copy
					type: "half", //full全屏打开  half半屏打开
					toId: "",
					listSelect: [],
					footerBtns: {
						key: "panfile",
						value: "我的文件",
						data: [
							{
								key: "panfile",
								value: "我的文件",
								font: "suoyouwenjian",
								tableId: "id_pan_file_list",
								pointNumber: 0,
								pointType: "big"
							},
							{
								key: "pubsharefile",
								value: "共享文件",
								font: "wj-gxwj",
								tableId: "id_pan_pubshare_file",
								pointNumber: 0,
								pointType: "big"
							}
						]
					}
				},

				appName: ""
			};
		}

		if (Component) MoCloudDiskS.__proto__ = Component;
		MoCloudDiskS.prototype = Object.create(Component && Component.prototype);
		MoCloudDiskS.prototype.constructor = MoCloudDiskS;
		MoCloudDiskS.prototype.onShow = function() {
			G.onShow(this);
		};
		MoCloudDiskS.prototype.installed = function() {};
		MoCloudDiskS.prototype.baseInit = function() {
			this.data.pageParam = T.pageParam(this);
			G.installed(this);
		};
		MoCloudDiskS.prototype.init = function() {
			this.data.pageType = this.data.pageParam.pageType || "page";
			this.data.title = this.data.pageParam.title || "云盘分享";
			this.data.panShareId = this.data.pageParam.id || "735735634341269504";
			this.data.shareCode.input = T.getPrefs("s_" + this.data.panShareId) || "";
			this.getInitData();
		};
		MoCloudDiskS.prototype.close = function() {
			if (this.data.loginInfo.show) {
				this.data.loginInfo.show = false;
				return;
			}
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				T.closeWin();
			}
		};
		MoCloudDiskS.prototype.getInitData = function() {
			var this$1 = this;

			T.ajax(
				{
					u: myjs.appUrl() + "config/openRead",
					_this: this,
					areaId: myjs.areaId(this)
				},
				"config/openRead",
				function(ret, err) {
					var data = ret ? ret.data || "" : "";
					if (data) {
						this$1.data.appName = data.systemName || "";
					}
				},
				"取app配置",
				"post",
				{
					body: JSON.stringify({codes: ["systemName"]})
				},
				{Authorization: ""}
			);
			var postParam = {
				shareCode: this.data.shareCode.input
			};

			T.setPrefs("s_" + this.data.panShareId, this.data.shareCode.input);
			T.ajax(
				{u: myjs.appUrl() + "s/" + this.data.panShareId, _this: this},
				"disk_s",
				function(ret, err) {
					T.hideProgress();
					var code = ret ? ret.code : "";
					var data = ret ? ret.data || {} : {};
					if (ret && ret.code == "200") {
						this$1.data.user.name = data.userAccount.userName;
						this$1.data.user.url = data.userAccount.headImg || data.userAccount.photo;
						this$1.data.expireTime = data.expireTime || "";
						this$1.data.secretCode = data.secretCode || "";
						if (
							this$1.data.expireTime != "永久有效" &&
							this$1.data.expireTime != "已过期"
						) {
							this$1.data.expireTime += "失效";
						}
						var shareType = data.shareType;
						if (this$1.data.shareCode.input || !shareType) {
							this$1.data.shareCode.show = false;
							this$1.getData(0);
						} else {
							this$1.data.shareCode.show = true;
						}
					} else {
						T.toast(ret ? ret.message || ret.data || T.NET_NO : T.NET_ERR);
						this$1.data.shareCode.show = true;
					}
				},
				"云盘共享信息",
				"post",
				{
					body: JSON.stringify(postParam)
				},
				{Authorization: ""}
			);
		};
		MoCloudDiskS.prototype.getData = function(_type) {
			var this$1 = this;

			if (!this.data.secretCode) {
				return;
			}
			if (!_type) {
				this.data.pageNo = 1;
			}
			var postParam = {
				pageNo: this.data.pageNo,
				pageSize: !_type
					? this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize
					: this.data.pageSize,
				keyword: this.searchInput,
				panShareId: this.data.panShareId,
				secretCode: this.data.secretCode,
				id: this.data.level.key || "0"
			};

			T.ajax(
				{u: myjs.appUrl() + "s/list", _this: this},
				"s/list",
				function(ret, err) {
					T.hideProgress();
					this$1.data.pageNot.skeleton = false;
					var code = ret ? ret.code : "";
					var data = ret ? ret.data || [] : [];
					this$1.data.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1; //类型 列表中只有有网和无网的情况
					this$1.data.pageNot.text =
						ret && code != 200 ? ret.message || ret.data : ""; //只有接口报的异常才改文字
					if (!_type) {
						this$1.data.pageNo = 1;
					}
					if (T.isArray(data) && data.length) {
						var nowList = [];
						data.forEach(function(_eItem, _eIndex, _eArr) {
							//item index 原数组对象
							var item = {};
							item.id = _eItem.id || ""; //id
							item.name = _eItem.fileName || ""; //标题
							item.time = _eItem.updateDate;
							item.fileInfoId = _eItem.fileInfoId;
							item.fileInfo = G.getFileInfo(_eItem.extName || "folder");
							item.addMsg = _eItem.fileStatus != 0 ? _eItem.fileStatusName || "" : "";
							item.dotSelect = _eItem.fileStatus != 0;
							item.dotSelectToast =
								"该文件" + (item.fileInfo.type == "folder" ? "夹" : "") + item.addMsg;
							nowList.push(item);
						});
						if (!_type) {
							this$1.data.listData = nowList;
						} else {
							this$1.data.listData = this$1.data.listData.concat(nowList);
						}
						this$1.data.pageNo++;
						this$1.data.pageNot.text = T.LOAD_ALL; //当前返回的数量 等于 请求的数量 说明可能还有	少于说明没有了
						(this$1.data.refreshPageSize =
							Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
							this$1.data.pageSize),
							(this$1.data.pageNo =
								Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1);
					} else if (_type == 1) {
						//加载更多的时候 底部显示文字
						this$1.data.pageNot.text = ret
							? code == 200
								? T.LOAD_ALL
								: ret.message
							: T.NET_ERR;
					} else {
						this$1.data.listData = [];
					}
					this$1.data.pageNot.listLength = this$1.data.listData.length;
					if (this$1.data.level.data.length == 1) {
						this$1.data.level.data[0].value =
							"全部文件(" + this$1.data.listData.length + ")";
					}
				},
				"分享列表",
				"post",
				{
					body: JSON.stringify(postParam)
				},
				{Authorization: ""}
			);
		};
		MoCloudDiskS.prototype.loadMore = function() {};
		MoCloudDiskS.prototype.levelChange = function(ref) {
			var detail = ref.detail;

			var nLevel = [],
				lastItem = null;
			for (var i = 0; i < this.data.level.data.length; i++) {
				lastItem = this.data.level.data[i];
				nLevel.push(lastItem);
				if (lastItem.key == detail.key) {
					break;
				}
			}
			this.data.level.data = nLevel;
			this.data.listSelect = [];
			this.data.dataMore.isSelect = false;
			this.getData(0);
		};
		MoCloudDiskS.prototype.changeSelect = function() {};
		MoCloudDiskS.prototype.changeDataSelect = function(ref) {
			var detail = ref.detail;

			console.log(JSON.stringify(this.data.listSelect));
		};
		MoCloudDiskS.prototype.openDetails = function(ref) {
			var detail = ref.detail;

			console.log(JSON.stringify(detail));
			if (detail.addMsg) {
				T.toast("该文件" + detail.addMsg);
				return;
			}
			if (detail.fileInfo.type == "folder") {
				this.data.level.data.push({key: detail.id, value: detail.name});
			} else {
				T.openWin(
					"mo_preview_file",
					"../mo_preview_file/mo_preview_file.stml",
					{id: detail.fileInfoId},
					this
				);
			}
		};
		MoCloudDiskS.prototype.checkAllHasCancel = function() {
			var this$1 = this;

			var hasCancel = true;
			this.data.listData.forEach(function(_item, index) {
				var nItem = G.getItemForKey(_item.id, this$1.data.listSelect, "id");
				if (!nItem) {
					hasCancel = false;
				}
			});
			return hasCancel;
		};
		MoCloudDiskS.prototype.checkAll = function() {
			var this$1 = this;

			if (this.checkAllHasCancel()) {
				this.data.listSelect = [];
			} else {
				this.data.listData.forEach(function(_item, index) {
					var nItem = G.getItemForKey(_item.id, this$1.data.listSelect, "id");
					if (!nItem && !_item.dotSelect) {
						this$1.data.listSelect.push(_item);
					}
				});
			}
		};
		MoCloudDiskS.prototype.forwardFriend = function() {
			T.toast("加急开发中");
		};
		MoCloudDiskS.prototype.saveTo = function() {
			var this$1 = this;

			// if(!this.listSelect.length){
			// 	T.toast("请选择需要转存的文件");
			// 	return;
			// }
			if (!this.data.listSelect.length) {
				this.data.dataMore.isSelect = true;
				this.checkAll();
			}
			if (!this.data.listSelect.length) {
				T.toast("请选择转存文件");
				return;
			}
			this.checkLogin(function(ret) {
				this$1.data.loginInfo.openType = "1";
				if (ret.code == 302) {
					//未登录系统
					this$1.data.loginInfo.show = true;
				} else if (ret.code == 200) {
					//已登录
					this$1.loginOK();
				} else {
					T.toast(ret.message || ret.data || T.NET_NO);
				}
			});
		};
		MoCloudDiskS.prototype.loginOK = function() {
			this.data.loginInfo.show = false;
			if (this.data.loginInfo.openType == "1") {
				this.data.aroundPage.show = true;
			}
		};
		MoCloudDiskS.prototype.checkLogin = function(callback) {
			T.ajax(
				{u: myjs.appUrl() + "login/user?", t: "login", _this: this},
				"userLogin",
				function(ret, err) {
					if (ret) {
						callback(ret);
					} else {
						T.toast(T.NET_ERR);
					}
				},
				"校验登录",
				"post",
				{
					values: {}
				}
			);
		};
		MoCloudDiskS.prototype.aroundCallback = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			var postParam = {
				ids: this.data.listSelect.map(function(obj) {
					return obj.id;
				}),
				toId: this.data.aroundPage.toId
			};

			T.showProgress("操作中");
			T.ajax(
				{u: myjs.appUrl() + "pansharefile/resave", _this: this},
				"pansharefile/resave",
				function(ret, err) {
					T.hideProgress();
					T.toast(ret ? ret.message : T.NET_ERR);
					if (ret && ret.code == 200) {
						this$1.getData(0);
						this$1.data.dataMore.isSelect = false;
						this$1.changeSelect();
						this$1.data.listSelect = [];
					}
				},
				"转存",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		MoCloudDiskS.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{
					_this: this,
					title: this.data.title,
					dataMore: this.props.dataMore,
					pageParam: this.props.pageParam,
					onInit: this.baseInit,
					onClose: this.close,
					onPageRefresh: function() {
						return this$1.getData(false);
					}
				},
				apivm.h("view", {style: "width:auto;height:100%;"}),
				apivm.h("view", {style: "width:auto;height:100%;"}),
				apivm.h("view", null),
				apivm.h(
					"view",
					{
						style:
							"width:100%;height:" +
							(this.props.dataMore && this.props.dataMore.boxAuto
								? "auto"
								: "1px;flex:1") +
							";"
					},
					apivm.h(
						"view",
						{
							style:
								"width:100%;height:" +
								(this.props.dataMore && this.props.dataMore.boxAuto
									? "auto"
									: "1px;flex:1") +
								";display:" +
								(this.data.user.name && !this.data.shareCode.show ? "flex" : "none") +
								";"
						},
						apivm.h(
							"view",
							null,
							T.platform() == "web" &&
								apivm.h(
									"view",
									{
										style:
											"width:100%;padding:10px 16px;border-bottom:10px solid #EDEDED;flex-direction:row; align-items: center;"
									},
									apivm.h("image", {
										style: this.data.G.loadConfigurationSize(14) + "border-radius:15px;",
										src: "" + this.data.G.showImg(myjs.appUrl() + "pageImg/open/logo"),
										mode: "aspectFill"
									}),
									apivm.h(
										"text",
										{
											style:
												this.data.G.loadConfiguration(1) +
												"margin-left: 10px;font-weight: 600;color:#333;"
										},
										this.data.appName
									)
								)
						),
						apivm.h(
							"view",
							{
								style:
									"flex-direction:row; align-items: center;padding:20px 16px 10px 16px;"
							},
							apivm.h("z-avatar", {
								style: this.data.G.loadConfigurationSize(34),
								key: this.data.user.refresh,
								data: this.data.user
							}),
							apivm.h(
								"view",
								{style: "width:1px;flex:1;margin-left:20px;"},
								apivm.h(
									"text",
									{
										style:
											this.data.G.loadConfiguration(2) + "font-weight: 600;color: #333;"
									},
									this.data.user.name
								),
								apivm.h(
									"view",
									{style: "margin-top:5px; flex-direction:row; align-items: center;"},
									apivm.h(
										"text",
										{
											style:
												this.data.G.loadConfiguration(-2) +
												"font-weight: 400;color: #666;flex:1;"
										},
										"云盘文件分享人"
									),
									apivm.h(
										"text",
										{
											style:
												this.data.G.loadConfiguration(-2) + "font-weight: 400;color: #999;"
										},
										this.data.expireTime
									)
								)
							)
						),
						apivm.h(
							"view",
							{style: "flex-direction:row; align-items: center;"},
							apivm.h(
								"view",
								{style: "flex:1;width:1px;padding:10px 0px;"},
								apivm.h("z-tabs", {
									id: "tab_cloud_disk_s",
									style: "height:" + (this.data.G.appFontSize + 5) + "px;",
									size: -2,
									dataMore: this.data.level,
									onChange: this.levelChange,
									type: 4,
									bg: "" + this.data.G.appTheme
								})
							),
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.checkAll();
									},
									class: "header_btn",
									style: {display: this.data.dataMore.isSelect ? "flex" : "none"}
								},
								apivm.h(
									"text",
									{
										style:
											this.data.G.loadConfiguration(-2) +
											"color:" +
											this.data.G.appTheme +
											";margin:0 4px;"
									},
									this.checkAllHasCancel() ? "取消" : "",
									"全选"
								)
							)
						),
						apivm.h(
							"view",
							{style: "width:100%;height:1px;padding:0 16px;"},
							apivm.h("a-divider", null)
						),
						apivm.h(
							"y-scroll-view",
							{style: "width:100%;height:1px;flex:1;"},
							apivm.h("y-cloud-disk", {
								data: this.data.listData,
								dataMore: this.data.dataMore,
								dataSelect: this.data.listSelect,
								onChangeSelect: this.changeSelect,
								onChangeDataSelect: this.changeDataSelect,
								onOpenDetails: this.openDetails
							})
						),
						apivm.h(
							"view",
							{style: "padding-bottom:" + T.safeArea().bottom + "px;"},
							apivm.h(
								"view",
								{style: "padding:20px 40px;flex-direction:row; align-items: center;"},
								apivm.h("z-button", {
									onClick: function() {
										return this$1.forwardFriend();
									},
									round: true,
									plain: true,
									text: "转发好友",
									disabled: false,
									size: this.data.G.appFontSize,
									fontstyle: "" + this.data.G.loadConfiguration(),
									style: "flex:1;",
									color: this.data.G.appTheme
								}),
								apivm.h("z-button", {
									onClick: function() {
										return this$1.saveTo();
									},
									round: true,
									text: "转存云盘",
									disabled: false,
									size: this.data.G.appFontSize,
									fontstyle: "" + this.data.G.loadConfiguration(),
									style: "flex:1;margin-left:31px;",
									color: this.data.G.appTheme
								})
							)
						)
					),
					apivm.h(
						"view",
						{
							class: "input_code_box",
							style:
								"background: url('" +
								myjs.shareAddress(1) +
								"img/bg_cloud_disk_share.png') no-repeat; background-size: cover;display:" +
								(this.data.shareCode.show ? "flex" : "none") +
								";"
						},
						apivm.h(
							"view",
							{class: "logo_bg_default"},
							apivm.h("image", {
								style: this.data.G.loadConfigurationSize(24) + "border-radius:4px;",
								src: "" + this.data.G.showImg(myjs.appUrl() + "pageImg/open/logo"),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									class: "logo_bg_default_text",
									style: this.data.G.loadConfiguration(4)
								},
								this.data.appName,
								"云盘分享"
							)
						),
						apivm.h(
							"view",
							{class: "input_code_warp"},
							apivm.h(
								"view",
								{style: "flex-direction:row; align-items: center;"},
								apivm.h("z-avatar", {
									style: this.data.G.loadConfigurationSize(34),
									key: this.data.user.refresh,
									data: this.data.user
								}),
								apivm.h(
									"view",
									{style: "width:1px;flex:1;margin-left:20px;"},
									apivm.h(
										"text",
										{
											style:
												this.data.G.loadConfiguration(2) + "font-weight: 600;color: #333;"
										},
										this.data.user.name
									),
									apivm.h(
										"text",
										{
											style:
												this.data.G.loadConfiguration(-2) +
												"font-weight: 400;color: #666;margin-top:5px;"
										},
										"云盘文件分享人"
									)
								)
							),
							apivm.h(
								"view",
								{style: "margin:40px 0 10px 0;"},
								apivm.h(
									"text",
									{
										style:
											this.data.G.loadConfiguration(-2) + "font-weight: 600;color: #333;"
									},
									"提取码"
								)
							),
							apivm.h(
								"view",
								{style: "height:36px;width:100%;"},
								apivm.h("z-search", {
									id: "extra_code_s",
									dataMore: this.data.shareCode,
									placeholder: "请输入提取码"
								})
							)
						),
						apivm.h(
							"view",
							{style: "padding:10px 36px;"},
							apivm.h("z-button", {
								onClick: function() {
									return this$1.getInitData();
								},
								text: "提取文件",
								disabled: false,
								size: this.data.G.appFontSize,
								fontstyle: "" + this.data.G.loadConfiguration(),
								style: "",
								color: this.data.G.appTheme
							})
						),
						apivm.h(
							"text",
							{
								style:
									this.data.G.loadConfiguration(-2) +
									"font-weight: 400;color: #999;text-align: center;"
							},
							this.data.expireTime
						)
					)
				),
				apivm.h(
					"view",
					{
						class: "base_page",
						style: "display:" + (this.data.loginInfo.show ? "flex" : "none") + ";"
					},
					apivm.h(
						"view",
						{style: "width:100%;height:1px;flex:1;"},
						apivm.h("y-login", {
							dataMore: this.data.loginInfo,
							onResult: this.loginOK
						})
					),
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.close();
							},
							style: "margin-top:15px;"
						},
						apivm.h("a-iconfont", {
							class: "dialog_close",
							name: "gengduo1",
							color: "#FFF",
							size: this.data.G.appFontSize + 16
						})
					)
				),

				apivm.h("y-cloud-disk-around", {
					dataMore: this.data.aroundPage,
					onClick: this.aroundCallback
				})
			);
		};

		return MoCloudDiskS;
	})(Component);
	MoCloudDiskS.css = {
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".input_code_box": {
			width: "100%",
			height: "100%",
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".logo_bg_default": {
			flexDirection: "row",
			alignItems: "center",
			margin: "60px 16px 0 16px"
		},
		".logo_bg_default_text": {
			marginLeft: "10px",
			fontWeight: "600",
			color: "#333333"
		},
		".input_code_warp": {
			background: "#FFF",
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderRadius: "4px 4px 4px 4px",
			padding: "20px",
			margin: "20px 16px"
		},
		".base_page": {
			width: "100%",
			height: "100%",
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0",
			background: "rgba(0,0,0,0.3)",
			padding: "60px 10px",
			alignItems: "center"
		},
		".dialog_close": {transform: "rotate(45deg)"}
	};
	apivm.define("mo-cloud-disk-s", MoCloudDiskS);
	apivm.render(apivm.h("mo-cloud-disk-s", null), "body");
})();
