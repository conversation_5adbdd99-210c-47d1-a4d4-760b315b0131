(function() {
	var module5 = {
		name: "资讯",
		code: "5",
		businessCode: "informationContent",
		behaviorCode: "information_content"
	};
	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};
	var module17 = {name: "个人收藏", code: "17", listFlat: true};
	var module30 = {name: "投票", code: "30", businessCode: "vote"};
	var module50 = {name: "工作站", code: "50"};

	//打开链接
	function openWin_url(_param) {
		_param.url = handleSYSLink(_param.url);
		if (
			(_param.wopen || _param.url.indexOf("wopen") != -1) &&
			platform() == "web"
		) {
			window.open(_param.url);
		} else {
			openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
		}
	}

	//打开图片预览
	function openWin_imgPreviewer(_param) {
		G.imgPreviewerPop = {
			show: true,
			index: _param.index,
			imgs: _param.imgs
		};

		console.log(JSON.stringify(G.imgPreviewerPop));
	}

	//打开附件预览
	function openWin_filePreviewer(_param) {
		openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
	}

	//打开聊天转发
	function openWin_chat_share(_param, _callback) {
		_param.title = _param.title || "发送给";
		_param.callback = _param.callback || "chat_share_callback";
		addEventListener(_param.callback, function(ret) {
			_callback && _callback(ret);
		});
		openWin("mo_chat_share", "../mo_chat_share/mo_chat_share.stml", _param);
	}

	//打开聊天用户相关
	function openWin_chat_user(_param, _callback) {
		_param.callback = _param.callback || "chat_user_callback";
		addEventListener(_param.callback, function(ret) {
			_callback && _callback(ret);
		});
		openWin("mo_chat_user", "../mo_chat_user/mo_chat_user.stml", _param);
	}

	//打开详情------------------------------------------------------------------------------------

	//打开资讯
	function openWin_news(_item) {
		var openPage =
			_item.code == "7" ? "mo_news_topic" : _item.id ? "mo_details_n" : "mo_news";
		var param = {};
		param.id = _item.id;
		param.code = module5.code;
		if (_item.link) {
			openWin_url({url: _item.link});
		} else {
			openWin(
				openPage + (_item.id || _item.code),
				"../" + openPage + "/" + openPage + ".stml",
				param
			);
		}
		addBehaviorRecord({id: param.id, behaviorCode: module5.behaviorCode});
	}

	//打开代表信息
	function openWin_npcinfo(_item) {
		var openPage = _item.id ? "mo_npcinfo_details" : "mo_npcinfo_list";
		var param = {};
		param.code = module9.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module9.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开收藏
	function openWin_collect(_item) {
		var openPage = "mo_favorite";
		var param = {};
		param.code = module17.code;
		openWin(
			openPage + (_item.id || module17.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开投票
	function openWin_vote(_item) {
		var openPage = _item.id ? "mo_vote_details" : "mo_business_list";
		openWin(
			openPage + (_item.id || module30.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开代表工作站代表墙
	function openWin_workstation_members(_item) {
		var openPage = "mo_workstation_members";
		var param = {};
		param = setNewJSON(param, _item);
		openWin(
			openPage + _item.id,
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	//获取群成员列表
	function getGroupListUser(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "chatGroupMember/list",
				param: setNewJSON(
					{pageNo: 1, pageSize: 9999, query: {chatGroupId: ""}},
					_param.param
				),
				tag: "chatGroupMember",
				name: "群成员"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.id = _eItem.accountId || "";
						item.targetId = _eItem.accountId || "";
						item.name = _eItem.userName || "";
						item.url = _eItem.headImg || _eItem.photo || "";
						item.isOwner = _eItem.isOwner;
						nowList.push(item);
					});
					setAllChatInfo(nowList);
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	// 增加阅读量
	function addBehaviorRecord(_param) {
		ajax(
			{u: appUrl() + ("behavior/record/" + _param.behaviorCode + "/" + _param.id)},
			"behavior/record",
			function(ret, err) {},
			"阅读",
			"post",
			{
				body: JSON.stringify({})
			}
		);
	}

	//弹窗提示
	function ajaxAlert(_param, _callback) {
		var param = {
			title: "提示",
			msg: _param.msg || "",
			buttons: ["确定", "取消"]
		};

		if (_param.alertParam) {
			param = setNewJSON(param, _param.alertParam);
		}
		console.log(_param.url + "\n" + JSON.stringify(_param.param));
		alert(param, function(ret) {
			if (ret.buttonIndex == "1") {
				ajaxProcess(_param, _callback);
			}
		});
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR$1);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取融云群或用户信息
	function getBaseChatInfos(_item, callback) {
		var _id = _item.targetId;
		var _type = _item.conversationType;
		var _force = _item.force;
		if (_id.indexOf(chatHeader()) == 0) {
			_id = _id.split(chatHeader())[1];
		}
		var nTask = getItemForKey("task_" + _id, G.chatInfoTask);
		if (nTask) {
			nTask.callback.push(callback);
			return;
		}
		if (!_item.index) {
			getAllChatInfo();
		}
		var nowItem = getItemForKey(_id, G.chatInfos, "id");
		if (!nowItem || _force) {
			nTask = {key: "task_" + _id, callback: []};
			G.chatInfoTask.push(nTask);
			nTask.callback.push(callback);
			ajax(
				{
					u:
						appUrl() + (_type == "PRIVATE" ? "user/infoByAccount" : "chatGroup/info")
				},
				"infoByChat" + _id + "_" + _id,
				function(ret, err) {
					var item = null;
					if (ret && ret.code == "200") {
						if (_type == "PRIVATE") {
							var data = ret.data || [];
							var headImg = "",
								photo = "";
							data.forEach(function(_nItem) {
								if (_nItem.headImg && !headImg) {
									headImg = _nItem.headImg;
								}
								if (_nItem.photo && !photo) {
									photo = _nItem.photo;
								}
							});
							data = data[0];
							item = {
								id: data.accountId,
								name: data.userName,
								url: headImg || photo || ""
							};
						} else {
							var data = ret.data || {};
							item = {
								id: data.id,
								businessCode: data.businessCode,
								name: data.groupName,
								url: data.groupImg || "",
								memberUserIds: data.memberUserIds || []
							};
						}
						setAllChatInfo(item);
					}
					nTask = getItemForKey("task_" + _id, G.chatInfoTask);
					for (var i = 0; i < nTask.callback.length; i++) {
						var call = nTask.callback[i];
						call && call(item, ret, err);
					}
					delItemForKey("task_" + _id, G.chatInfoTask);
				},
				"\u83B7\u53D6\u4FE1\u606F" + _id,
				"post",
				{
					body: JSON.stringify({accountId: _id, detailId: _id, isSelectList: 1})
				}
			);
		} else {
			callback && callback(nowItem);
		}
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	// 收藏/取消收藏
	function optionCollect(_param, _callback) {
		var param = {
			businessCode: _param.code,
			businessId: _param.id,
			theme: _param.theme,
			ids: _param.ids,
			folderId: _param.folderId || "0"
		};

		if (_param.collect) {
			param = {form: param};
		}
		ajax(
			{u: appUrl() + ("favorite/" + (_param.collect ? "add" : "dels"))},
			"favorite/add",
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			(_param.collect ? "" : "取消") + "\u6536\u85CF",
			"post",
			{
				body: JSON.stringify(param)
			}
		);
	}

	function dealVideoId(_id) {
		return _id.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "");
	}

	//当前页面播放视频集合
	function videoPlayPush(_id) {
		if (!G.playVideos) {
			G.playVideos = [];
		}
		if (getItemForKey(_id, G.playVideos)) {
			return;
		}
		videoPlayRemoves();
		G.playVideos.push(_id);
		console.log("当前播放：" + JSON.stringify(G.playVideos));
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	//处理融云群组成员差异
	function dealGroupUser(_item, callback) {
		var _id = _item.id;
		var callbackData = {};
		if (_id.indexOf(chatHeader()) == 0) {
			_id = _id.split(chatHeader())[1];
		}
		getGroupListUser(
			{
				param: {
					query: {
						chatGroupId: _id
					}
				}
			},

			function(ret) {
				var data = ret ? ret.dealWith || [] : [];
				if (isArray(data) && data.length != 0) {
					var systemUsers = data.map(function(obj) {
						return chatHeader() + obj.id;
					});
					var userIds = "";
					data.forEach(function(_nItem) {
						if (_nItem.id != G.userId) {
							userIds += (userIds ? "," : "") + (chatHeader() + _nItem.id);
						} else {
							callbackData.isJoin = true;
							callbackData.isOwner = _nItem.isOwner;
						}
					});
					callback && callback(callbackData);
					ajax(
						tomcatAddress() + "push/rongCloud?",
						"createGroup",
						function(ret, err) {
							if (_item.joinType == "groupQr") {
								//是通过群二维码进入
								var cName =
									G.userName +
									" \u901A\u8FC7\u7FA4\u4E8C\u7EF4\u7801\u52A0\u5165\u7FA4\u804A";
								var cData =
									G.userName +
									"|OUI|" +
									G.userId +
									"|| \u901A\u8FC7\u7FA4\u4E8C\u7EF4\u7801\u52A0\u5165\u7FA4\u804A";
								sendEvent({
									name: "rongCloud",
									extra: {
										type: "sendCommandNotificationMessage",
										param: {
											conversationType: "GROUP",
											targetId: chatHeader() + _item.id,
											name: cName,
											data: cData
										}
									}
								});
							}
							var data = ret.members || [];
							var rongUsers = data.map(function(obj) {
								return obj.id;
							});
							var manyData = "",
								lessData = "";
							systemUsers.forEach(function(_nItem) {
								if (!getItemForKey(_nItem, rongUsers)) {
									lessData += (lessData ? "," : "") + _nItem;
								}
							});
							rongUsers.forEach(function(_nItem) {
								if (!getItemForKey(_nItem, systemUsers)) {
									manyData += (manyData ? "," : "") + _nItem;
								}
							});
							console.log("多的数据：" + manyData);
							console.log("少的数据：" + lessData);
							if (manyData) {
								ajax(
									tomcatAddress() + "push/rongCloud?",
									"dealGroupUserquitGroup",
									function(ret, err) {},
									"多的人离开",
									"post",
									{
										values: {
											type: "quitGroup",
											userIds: manyData,
											groupId: chatHeader() + _id
										}
									},
									{"content-type": "application/x-www-form-urlencoded"}
								);
							}
							if (lessData) {
								ajax(
									tomcatAddress() + "push/rongCloud?",
									"dealGroupUserjoinGroup",
									function(ret, err) {},
									"少的人加上",
									"post",
									{
										values: {
											type: "joinGroup",
											userIds: lessData,
											groupId: chatHeader() + _id,
											groupName: _item.name
										}
									},
									{"content-type": "application/x-www-form-urlencoded"}
								);
							}
						},
						"同步融云群组",
						"post",
						{
							values: {
								type: "createGroup",
								userIds: systemUsers.join(","),
								groupId: chatHeader() + _item.id,
								groupName: _item.name
							}
						},
						{"content-type": "application/x-www-form-urlencoded"}
					);
				}
			}
		);
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//底部可视区域
	function footerBottom() {
		return safeArea().bottom;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//移动事件 不左滑关闭屏幕
	function touchmove() {
		G.nTouchmove = true;
		clearTimeout(G.touchmoveTask);
		G.touchmoveTask = setTimeout(function() {
			G.nTouchmove = false;
		}, 1000);
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//列表无数据处理
	function dealData(_type, _this, ret) {
		if (!_type) {
			_this.data.listData = [];
			_this.data.emptyBox.type = ret ? "1" : "2";
			_this.data.emptyBox.text =
				ret && ret.code != 200 ? ret.message || ret.data : "";
		} else {
			_this.data.emptyBox.text = ret
				? ret.code == 200
					? LOAD_ALL
					: ret.message || ret.data
				: NET_ERR$1;
		}
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
	function uploadFile(_item, callback) {
		if (_item._fileAjax || _item.module == "-noUpload") return;
		_item._fileAjax = true;
		_item.state = 1;
		if (_item.showToast) {
			showProgress("上传中");
		}
		var nCallack = function nCallack(ret, err) {
			hideProgress();
			var code = ret ? ret.code : "";
			if (code == 200) {
				var data = ret.data || {};
				_item.state = 2;
				_item.uploadId = data.id;
				_item.otherInfo = data;
			} else {
				_item.state = 3;
				_item.error = ret ? ret.message || ret.data : err.data || err.body || "";
			}
			callback && callback(_item);
		};
		if (platform() == "mp") {
			wx.uploadFile({
				url: appUrl() + "file/upload",
				filePath: _item.url.path,
				name: "file",
				header: {
					"Content-Type": "multipart/form-data",
					"u-login-areaId": areaId(),
					Authorization: getPrefs("sys_token") || ""
				},

				success: function success(res) {
					nCallack(JSON.parse(res.data), null);
				},
				fail: function fail(err) {
					nCallack(null, JSON.parse(err.data));
				}
			});
		} else {
			ajax(
				{u: appUrl() + "file/upload", web: _item.web},
				"file/upload" + _item.url,
				nCallack,
				"上传附件",
				"post",
				{
					files: {file: _item.url}
				},
				{"content-type": "file"}
			);
		}
	}

	//计算得出文件显示大小
	function getFileSize(_fileSize) {
		try {
			var size1 = parseFloat((_fileSize / 1024 / 1024).toFixed(1));
			var size2 = parseFloat((_fileSize / 1024).toFixed(1));
			if (size1 >= 1) {
				return size1 + "MB";
			} else if (size2 >= 1) {
				return size2 + "KB";
			} else {
				return parseInt(_fileSize) + "B";
			}
		} catch (e) {
			return _fileSize;
		}
	}

	//获取文件类型 并返回数据
	function getFileInfo(_name) {
		var name = (_name || "").toLocaleLowerCase(),
			fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
		try {
			if (name.indexOf(".") != -1)
				name = name.split(".")[name.split(".").length - 1];
			switch (name) {
				case "xlsx":
				case "xlsm":
				case "xlsb":
				case "xltx":
				case "xltm":
				case "xls":
				case "xlt":
				case "et":
				case "csv":
				case "uos": //excel格式
					fileInfo.name = "file-excel-fill";
					fileInfo.color = "#00bd76";
					fileInfo.type = "excel";
					fileInfo.convertType = "0";
					break;
				case "doc":
				case "docx":
				case "docm":
				case "dotx":
				case "dotm":
				case "dot":
				case "xps":
				case "rtf":
				case "wps":
				case "wpt":
				case "uot": //word格式
					fileInfo.name = "file-word-fill";
					fileInfo.color = "#387efa";
					fileInfo.type = "word";
					fileInfo.convertType = "0";
					break;
				case "pdf": //pdf格式
					fileInfo.name = "file-pdf-fill";
					fileInfo.color = "#e9494a";
					fileInfo.type = "pdf";
					fileInfo.convertType = "20";
					break;
				case "ppt":
				case "pptx":
				case "pps":
				case "pot":
				case "pptm":
				case "potx":
				case "potm":
				case "ppsx":
				case "ppsm":
				case "ppa":
				case "ppam":
				case "dps":
				case "dpt":
				case "uop": //ppt
					fileInfo.name = "file-ppt-fill";
					fileInfo.color = "#ff7440";
					fileInfo.type = "ppt";
					fileInfo.convertType = "0";
					break;
				case "bmp":
				case "gif":
				case "jpg":
				case "pic":
				case "png":
				case "tif":
				case "jpeg":
				case "jpe":
				case "icon":
				case "jfif":
				case "dib": //图片格式 case 'webp':
					fileInfo.name = "file-text-fill";
					fileInfo.color = "#ff7440";
					fileInfo.type = "image";
					fileInfo.convertType = "440";
					break;
				case "txt": //文本
					fileInfo.name = "file-text-fill";
					fileInfo.color = "#2696ff";
					fileInfo.type = "txt";
					fileInfo.convertType = "0";
					break;
				case "rar":
				case "zip":
				case "7z":
				case "tar":
				case "gz":
				case "jar":
				case "ios": //压缩格式
					fileInfo.name = "file-zip-fill";
					fileInfo.color = "#a5b0c0";
					fileInfo.type = "compression";
					fileInfo.convertType = "19";
					break;
				case "mp4":
				case "avi":
				case "flv":
				case "f4v":
				case "webm":
				case "m4v":
				case "mov":
				case "3gp":
				case "rm":
				case "rmvb":
				case "mkv":
				case "mpeg":
				case "wmv": //视频格式
					fileInfo.name = "file-music-fill";
					fileInfo.color = "#e14a4a";
					fileInfo.type = "video";
					fileInfo.convertType = "450";
					break;
				case "mp3":
				case "m4a":
				case "amr":
				case "pcm":
				case "wav":
				case "aiff":
				case "aac":
				case "ogg":
				case "wma":
				case "flac":
				case "alac":
				case "wma":
				case "cda": //音频格式
					fileInfo.name = "file-music-fill";
					fileInfo.color = "#8043ff";
					fileInfo.type = "voice";
					fileInfo.convertType = "660";
					break;
				case "folder": //文件夹
					fileInfo.name = "folder-2-fill";
					fileInfo.color = "#ffd977";
					fileInfo.type = "folder";
					break;
			}
		} catch (e) {
			console.log(e.message);
		}
		return fileInfo;
	}

	//展示省略文字
	function showTextSize(_text, _size, _middle) {
		if (_size && _text) {
			if (_text.length > _size) {
				if (_middle) {
					var mSize = _size / 2;
					var nLast = getSizeText(_text, mSize);
					var nNext = getSizeText(_text, mSize, 1);
					if (nLast.length + nNext.length < _text.length) {
						_text = nLast + "..." + nNext;
					}
				} else {
					var nText = getSizeText(_text, _size);
					_text = nText + (nText.length < _text.length ? "..." : "");
				}
			}
		}
		return _text;
	}

	function getSizeText(_text, _size, _next) {
		var texts = _text.split("");
		var nowSize = 0,
			nowLength = 0;
		if (_next) {
			for (var i = texts.length - 1; i >= 0; i--) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(texts.length - nowLength);
		} else {
			for (var i = 0; i < texts.length; i++) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(0, nowLength);
		}
	}

	//选择文件并上传
	function chooseFile(_item, callback) {
		var max = isNumber(_item.max) ? _item.max : 0;
		if (platform() == "app") {
			if (api.systemType == "ios") {
				if (!confirmPer("storage", "chooseFile")) {
					//存储权限
					addEventListener("storagePer_chooseFile", function(ret, err) {
						removeEventListener("storagePer_chooseFile");
						if (ret.value.granted) {
							chooseFile(_item, callback);
						}
					});
					return;
				}
			} else {
				if (!api.require("zyRongCloud").hasAllFilesPermission()) {
					alert(
						{
							title: "提示",
							msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
							buttons: ["确定", "取消"]
						},
						function(ret) {
							if (ret.buttonIndex == "1") {
								api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
									if (ret.status) {
										chooseFile(_item, callback);
									}
								});
							}
						}
					);
					return;
				}
			}
			var fileBrowser = api.require("fileBrowser");
			fileBrowser.open({}, function(ret, err) {
				fileBrowser.close();
				setTimeout(function() {
					_item.url = ret.url;
					uploadFile(_item, function(ret) {
						callback && callback(ret);
					});
				}, 500);
			});
		} else if (platform() == "web") {
			var h5Input = document.createElement("input");
			h5Input.type = "file";
			h5Input.accept = "";
			var ua = navigator.userAgent.toLowerCase();
			var version = "";
			if (ua.indexOf("android") > 0) {
				var reg = /android [\d._]+/gi;
				var v_info = ua.match(reg);
				version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
				version = parseInt(version.split(".")[0]);
			}
			if (!version || Number(version) <= 13) {
				h5Input.multiple = "multiple";
			}
			h5Input.click();
			h5Input.onchange = function() {
				var listLength =
					max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
				for (var i = 0; i < listLength; i++) {
					(function(j) {
						var nItem = JSON.parse(JSON.stringify(_item));
						nItem.url = h5Input.files[j];
						uploadFile(nItem, function(ret) {
							callback && callback(ret);
						});
					})(i);
				}
			};
		} else if (platform() == "mp") {
			wx.chooseMessageFile({
				count: max != 0 ? max : 9,
				type: "file",
				success: function success(res) {
					for (var i = 0; i < res.tempFiles.length; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = res.tempFiles[j];
							uploadFile(nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				}
			});
		}
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var LOAD_ALL = "已加载完";
	var NET_ERR$1 = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否数字
	function isNumber(_obj) {
		return isParameters(_obj) && typeof _obj === "number";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//移除字符串所有标签
	function removeTag(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//转义字符串
	function decodeCharacter(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;|&emsp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//移除监听
	function removeEventListener(name) {
		if (
			platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			delItemForKey(name, window.baseEventList);
		} else {
			try {
				api.removeEventListener({name: name});
			} catch (e) {}
		}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (G.headTheme != getPrefs("headTheme") && G.headTheme != "transparent") {
			o.pageParam.headTheme = G.headTheme;
		}
		if (
			G.appTheme != getPrefs("appTheme" + G.sysSign) &&
			G.appTheme != (G.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出actionSheet
	function actionSheet(_param, _callback) {
		var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
		o = setNewJSON(o, _param);
		var oldButton = o.buttons || [],
			newButton = [];
		oldButton.forEach(function(item) {
			newButton.push(isObject(item) ? item : {name: item});
		});
		var actionSheetBox = {
			title: o.title,
			cancel: o.cancelTitle,
			data: newButton,
			active: o.active,
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			},
			pageClose: function pageClose() {
				_param.pageClose && _param.pageClose();
			}
		};

		G.actionSheetPop = actionSheetBox;
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	function getPicture(_param, callback) {
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = setNewJSON(o, _param);
		try {
			if (platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						if (ret.value.granted) {
							getPicture(_param, callback);
						}
						removeEventListener(preName + "Per_" + "getPicture");
					});
					return;
				}
				if (o.sourceType == "camera") {
					api.getPicture(o, function(ret, err) {
						isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	}

	function hasPermission(one_per) {
		if (platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	}

	function requestPermission(one_per, callback, _fName) {
		if (platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				isFunction(callback) && callback(ret, err);
			});
		}
	}

	function confirmPer(perm, _fName, _reason) {
		if (platform() == "app") {
			var has = hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				alert(
					{
						title: "无法使用" + hintWord[perm],
						msg: api.systemType == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret) {
						if (1 == ret.buttonIndex) {
							requestPermission(perm, null, _fName);
						} else {
							sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	}

	//获取文件详情
	function getDetailsFile(_param, _callback) {
		ajax(
			{u: appUrl() + "file/info/" + _param.param.id},
			_param.tag || "file" + _param.param.id,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					data.fileInfo = getFileInfo(data.extName);
					data.dealFileSize = getFileSize(data.fileSize);
					ret.dealWith = data;
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取资讯详情
	function getDetails5(_param, _callback) {
		ajax(
			{u: appUrl() + "newsContent/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var unshiftContent = "",
						pushContent = "";
					var fileLink = data.linkDetailVos || [];
					var infoPic = data.infoPic || "";
					var infoContent = data.infoContent || "";
					infoPic = infoPic
						? infoPic.indexOf(",") != -1
							? infoPic.split(",")[0]
							: infoPic
						: "";
					var infoVideo = data.infoVideo || "";
					if (isParameters(fileLink) && fileLink.length) {
						var useType1 = [],
							useType2 = [];
						fileLink.forEach(function(_mItem) {
							if (
								_mItem.fileId ||
								(isArray(_mItem.attachments) && _mItem.attachments.length)
							) {
								if (_mItem.useType == "1" && useType1.length < 3) {
									useType1.push({url: _mItem.attachments[0].newFileName});
								} else if (_mItem.useType == "2") {
									useType2.push(
										_mItem.fileId ||
											(_mItem.attachments.length ? _mItem.attachments[0].id : "")
									);
								}
							}
						});
						if (!infoPic && useType1.length) {
							infoPic = useType1[0].url;
						}
						if (useType2.length && useType2[0]) {
							infoVideo = useType2[0];
						}
					}
					var match = infoContent.match(/<img[^>]+src="([^">]+)"/);
					var cImg = match ? match[1] : "";
					if (!infoPic && cImg) {
						infoPic = cImg;
					}
					if (infoVideo) {
						unshiftContent += '<video src="' + infoVideo + '"></video></br>';
					}
					ret.dealWith = {
						code: module5.code,
						name: {"1": "资讯", "2": "资料", "7": "履职技巧"}[data.moduleId || "1"],
						id: data.id,
						title: data.infoTitle || "",
						subTitle: data.infoSubtitle || "",
						content: unshiftContent + infoContent + pushContent,
						url: infoPic,
						attachments: data.attachments || [],
						time: dayjs(data.pubTime).format("YYYY-MM-DD"),
						source: data.infoSource,
						leftAdd: [
							{text: dayjs(data.pubTime).format("YYYY-MM-DD")},
							{text: data.infoSource}
						],
						rightAdd: [],
						share: data.isShare == "1",
						comment: data.isCompraise || "1",
						commentList: true,
						screen: data.isScreen || "1",
						module: data.moduleId || "1"
					};
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取投票详情
	function getDetails30(_param, _callback) {
		ajax(
			{u: appUrl() + "voteTopic/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					ret.dealWith = {
						code: module30.code,
						name: module30.name,
						id: data.id,
						title: data.topic || "",
						src: data.topicImg || ""
					};
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站详情
	function getDetails50(_param, _callback) {
		ajax(
			{u: appUrl() + "workerStation/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var item = {
						code: module50.code,
						name: module50.name,
						id: data.id,
						title: data.name || "",
						content: data.introduction || "暂无",
						url: data.showImgs || appUrl() + "pageImg/open/workstation_default"
					};

					item.userName = data.contactUserName || "";
					item.userPhone = data.contactTelephone || "";
					item.openTime = data.openTime || "";
					item.address = data.address || "";
					item.hasFocus = data.hasFocus || 0;
					item.excellentStationType = data.excellentStationType || {};
					item.talkGroupId = data.talkGroupId;
					item.workers = (data.workers || []).map(function(obj) {
						return {id: obj.userId, targetId: obj.accountId, name: obj.userName};
					});
					item.lawPointId = data.lawPointId || "";
					item.cityName = data.cityName || "";
					item.countryName = data.countryName || "";
					ret.dealWith = item;
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	// 复制
	function copyText(_text, _callback) {
		console.log("copy:" + _text);
		if (platform() == "app") {
			api.require("clipBoard").set(
				{
					value: _text
				},
				function(ret, err) {
					_callback && _callback(ret, err);
				}
			);
		} else if (platform() == "web") {
			htmlCopyText(_text, function(ret) {
				_callback && _callback(ret, null);
			});
		} else if (platform() == "mp") {
			wx.setClipboardData({
				data: _text,
				success: function success(ret) {
					_callback && _callback(ret, null);
				},
				fail: function fail(err) {
					_callback && _callback(null, err);
				}
			});
		}
	}

	// web复制
	function htmlCopyText(_text, callback) {
		var success = true;
		var textarea = document.createElement("textarea");
		textarea.value = _text;
		document.body.appendChild(textarea);
		textarea.select();
		textarea.setSelectionRange(0, textarea.value.length); // 兼容 iOS 设备
		try {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(_text).then(
					function() {
						success = true;
					},
					function(err) {
						success = false;
					}
				);
			} else {
				var input = document.createElement("input");
				input.setAttribute("value", _text);
				document.body.appendChild(input);
				input.select();
				input.setSelectionRange(0, input.value.length); // 兼容 iOS 设备
				success = document.execCommand("copy");
				document.body.removeChild(input);
			}
		} catch (err) {
			success = false;
		} finally {
			document.body.removeChild(textarea);
		}
		callback && callback(success);
	}

	//返回qq一样的时间样式	返回格式	默认qq列表	 1为详情
	function getConversionTime(_begin, _end, _interface) {
		var returnTime, reDetailsTime;
		_end.setHours(0);
		_end.setMinutes(0);
		_end.setSeconds(0);
		_end.setMilliseconds(0);
		var beginTime = Math.round(_begin.getTime() / 1000);
		var endTime = Math.round(_end.getTime() / 1000);
		if (endTime - beginTime <= 0) {
			returnTime = "" + getConversionHour(_begin) + getConversionMinute(_begin);
			reDetailsTime = "" + getConversionHour(_begin) + getConversionMinute(_begin);
		} else if (endTime - beginTime <= 86400) {
			returnTime =
				"昨天" + getConversionHour(_begin) + getConversionMinute(_begin);
			reDetailsTime =
				"昨天" + getConversionHour(_begin) + getConversionMinute(_begin);
		} else if (endTime - beginTime < 86400 * 6) {
			returnTime = getConversionDay(_begin);
			reDetailsTime =
				getConversionDay(_begin) +
				" " +
				getConversionHour(_begin) +
				getConversionMinute(_begin);
		} else {
			returnTime =
				(_begin.getFullYear() != _end.getFullYear()
					? getConversionYear(_begin)
					: "") +
				getConversionMonth(_begin) +
				getConversionDate(_begin);
			reDetailsTime =
				(_begin.getFullYear() != _end.getFullYear()
					? getConversionYear(_begin)
					: "") +
				getConversionMonth(_begin) +
				getConversionDate(_begin) +
				" " +
				getConversionHour(_begin) +
				getConversionMinute(_begin);
		}
		if (_interface == 1) {
			return reDetailsTime;
		}
		return returnTime;
	}
	function getConversionYear(_data) {
		return _data.getFullYear() + "-";
	}
	function getConversionMonth(_data) {
		var str = _data.getMonth() + 1;
		if (str < 10) {
			str = "0" + str;
		}
		return str + "-";
	}
	function getConversionDate(_data) {
		var str = _data.getDate();
		if (str < 10) {
			str = "0" + str;
		}
		return str + "";
	}
	function getConversionHour(_data) {
		var str = _data.getHours();
		if (str < 10) {
			str = "0" + str;
		}
		return str + ":";
	}
	function getConversionMinute(_data) {
		var str = _data.getMinutes();
		if (str < 10) {
			str = "0" + str;
		}
		return str;
	}
	function getConversionDay(_data) {
		return ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][
			_data.getDay()
		];
	}

	var emotion = [
		{name: "Expression_1", text: "[微笑]"},
		{name: "Expression_2", text: "[撇嘴]"},
		{name: "Expression_3", text: "[色]"},
		{name: "Expression_4", text: "[发呆]"},
		{name: "Expression_5", text: "[得意]"},
		{name: "Expression_6", text: "[流泪]"},
		{name: "Expression_7", text: "[害羞]"},
		{name: "Expression_8", text: "[闭嘴]"},
		{name: "Expression_9", text: "[睡]"},
		{name: "Expression_10", text: "[大哭]"},
		{name: "Expression_11", text: "[尴尬]"},
		{name: "Expression_12", text: "[发怒]"},
		{name: "Expression_13", text: "[调皮]"},
		{name: "Expression_14", text: "[呲牙]"},
		{name: "Expression_15", text: "[惊讶]"},
		{name: "Expression_16", text: "[难过]"},
		{name: "Expression_17", text: "[酷]"},
		{name: "Expression_18", text: "[冷汗]"},
		{name: "Expression_19", text: "[抓狂]"},
		{name: "Expression_20", text: "[吐]"},
		{name: "Expression_21", text: "[偷笑]"},
		{name: "Expression_22", text: "[愉快]"},
		{name: "Expression_23", text: "[白眼]"},
		{name: "Expression_24", text: "[傲慢]"},
		{name: "Expression_25", text: "[饥饿]"},
		{name: "Expression_26", text: "[困]"},
		{name: "Expression_27", text: "[恐惧]"},
		{name: "Expression_28", text: "[流汗]"},
		{name: "Expression_29", text: "[憨笑]"},
		{name: "Expression_30", text: "[悠闲]"},
		{name: "Expression_31", text: "[奋斗]"},
		{name: "Expression_32", text: "[咒骂]"},
		{name: "Expression_33", text: "[疑问]"},
		{name: "Expression_34", text: "[嘘]"},
		{name: "Expression_35", text: "[晕]"},
		{name: "Expression_36", text: "[疯了]"},
		{name: "Expression_37", text: "[衰]"},
		{name: "Expression_38", text: "[骷髅]"},
		{name: "Expression_39", text: "[敲打]"},
		{name: "Expression_40", text: "[再见]"},
		{name: "Expression_41", text: "[擦汗]"},
		{name: "Expression_42", text: "[抠鼻]"},
		{name: "Expression_43", text: "[鼓掌]"},
		{name: "Expression_44", text: "[糗大了]"},
		{name: "Expression_45", text: "[坏笑]"},
		{name: "Expression_46", text: "[左哼哼]"},
		{name: "Expression_47", text: "[右哼哼]"},
		{name: "Expression_48", text: "[哈欠]"},
		{name: "Expression_49", text: "[鄙视]"},
		{name: "Expression_50", text: "[委屈]"},
		{name: "Expression_51", text: "[快哭了]"},
		{name: "Expression_52", text: "[阴险]"},
		{name: "Expression_53", text: "[亲亲]"},
		{name: "Expression_54", text: "[吓]"},
		{name: "Expression_55", text: "[可怜]"},
		{name: "Expression_56", text: "[菜刀]"},
		{name: "Expression_57", text: "[西瓜]"},
		{name: "Expression_58", text: "[啤酒]"},
		{name: "Expression_59", text: "[篮球]"},
		{name: "Expression_60", text: "[乒乓]"},
		{name: "Expression_61", text: "[咖啡]"},
		{name: "Expression_62", text: "[饭]"},
		{name: "Expression_63", text: "[猪头]"},
		{name: "Expression_64", text: "[玫瑰]"},
		{name: "Expression_65", text: "[凋谢]"},
		{name: "Expression_66", text: "[嘴唇]"},
		{name: "Expression_67", text: "[爱心]"},
		{name: "Expression_68", text: "[心碎]"},
		{name: "Expression_69", text: "[蛋糕]"},
		{name: "Expression_70", text: "[闪电]"},
		{name: "Expression_71", text: "[炸弹]"},
		{name: "Expression_72", text: "[刀]"},
		{name: "Expression_73", text: "[足球]"},
		{name: "Expression_74", text: "[瓢虫]"},
		{name: "Expression_75", text: "[便便]"},
		{name: "Expression_76", text: "[月亮]"},
		{name: "Expression_77", text: "[太阳]"},
		{name: "Expression_78", text: "[礼物]"},
		{name: "Expression_79", text: "[拥抱]"},
		{name: "Expression_80", text: "[强]"},
		{name: "Expression_81", text: "[弱]"},
		{name: "Expression_82", text: "[握手]"},
		{name: "Expression_83", text: "[胜利]"},
		{name: "Expression_84", text: "[抱拳]"},
		{name: "Expression_85", text: "[勾引]"},
		{name: "Expression_86", text: "[拳头]"},
		{name: "Expression_87", text: "[差劲]"},
		{name: "Expression_88", text: "[爱你]"},
		{name: "Expression_89", text: "[NO]"},
		{name: "Expression_90", text: "[OK]"},
		{name: "Expression_91", text: "[爱情]"},
		{name: "Expression_92", text: "[飞吻]"},
		{name: "Expression_93", text: "[跳跳]"},
		{name: "Expression_94", text: "[发抖]"},
		{name: "Expression_95", text: "[怄火]"},
		{name: "Expression_96", text: "[转圈]"},
		{name: "Expression_97", text: "[磕头]"},
		{name: "Expression_98", text: "[回头]"},
		{name: "Expression_99", text: "[跳绳]"},
		{name: "Expression_100", text: "[投降]"},
		{name: "Expression_101", text: "[激动]"},
		{name: "Expression_102", text: "[街舞]"},
		{name: "Expression_103", text: "[献吻]"},
		{name: "Expression_104", text: "[左太极]"},
		{name: "Expression_105", text: "[右太极]"}
	];

	var CollectOk = /*@__PURE__*/ (function(Component) {
		function CollectOk(props) {
			Component.call(this, props);
		}

		if (Component) CollectOk.__proto__ = Component;
		CollectOk.prototype = Object.create(Component && Component.prototype);
		CollectOk.prototype.constructor = CollectOk;
		CollectOk.prototype.openCollect = function() {
			openWin_collect({});
		};
		CollectOk.prototype.render = function() {
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";position:absolute;z-index:999;left:0;right:0;bottom:0;"
				},
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{style: "padding:10px 16px " + (safeArea().bottom + 70) + "px 16px;"},
						apivm.h(
							"view",
							{
								class: "flex_row",
								style:
									"background: #FFFFFF;box-shadow: 0px 2px 10px 1px rgba(0,0,0,0.08);border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px;padding:14px 10px;"
							},
							apivm.h(
								"text",
								{
									style:
										loadConfiguration() +
										"color:#333;font-weight: 600;padding: 0px 10px;flex:1;"
								},
								"已成功添加收藏"
							),
							apivm.h(
								"view",
								{onClick: this.openCollect, class: "flex_row"},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration() + "color:" + G.appTheme + ";margin-right:6px;"
									},
									"前往查看"
								),
								apivm.h("a-iconfont", {
									style: "transform: rotate(180deg) scale(0.7,0.7);",
									name: "fanhui1",
									color: G.appTheme,
									size: G.appFontSize - 4
								})
							)
						)
					)
			);
		};

		return CollectOk;
	})(Component);
	apivm.define("collect-ok", CollectOk);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.inputBlur = function(e) {
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color: "#999",
								size: G.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G.appFontSize + 14) +
									"px;color:#999;" +
									loadConfiguration()
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style": "color:#ccc;",
							class: "z_input_input flex_w",
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								isParameters(this.props.readonly)
									? this.props.readonly
									: false,
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"}
	};
	apivm.define("z-input", ZInput);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var ZBreadcrumb = /*@__PURE__*/ (function(Component) {
		function ZBreadcrumb(props) {
			Component.call(this, props);
			this.data = {
				oldData: "",
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var data = this.props.dataMore.data;
					if (isArray(data) && this.data.oldData != JSON.stringify(data)) {
						if (this.data.oldData) {
							setTimeout(function() {
								this$1.tabClick(data[data.length - 1]);
							}, 30);
						}
						this.data.oldData = JSON.stringify(data);
					}
				}
			};
		}

		if (Component) ZBreadcrumb.__proto__ = Component;
		ZBreadcrumb.prototype = Object.create(Component && Component.prototype);
		ZBreadcrumb.prototype.constructor = ZBreadcrumb;
		ZBreadcrumb.prototype.tabClick = function(_item) {
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;

			var nLevel = [];
			for (var i = 0; i < this.props.dataMore.data.length; i++) {
				var item = this.props.dataMore.data[i];
				nLevel.push(item);
				if (item.key == _item.key) {
					break;
				}
			}
			this.props.dataMore.data = nLevel;

			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view = "breadcrumb_" + _item.key;
		};
		ZBreadcrumb.prototype.nTouchmove = function() {
			touchmove();
		};
		ZBreadcrumb.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1, list) {
						return apivm.h(
							"view",
							{
								id: "breadcrumb_" + item$1.key,
								style:
									"display: inline-block;margin-left:" +
									(!index$1 ? "16" : "0") +
									"px;margin-right:" +
									(index$1 == list.length - 1 ? "16" : "0") +
									"px;",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"view",
								{class: "flex_row"},
								apivm.h(
									"view",
									{style: "display:" + (index$1 ? "flex" : "none") + ";"},
									apivm.h("a-iconfont", {
										style: "margin:0 8px;transform: rotate(180deg) scale(0.7,0.7);",
										name: "fanhui1",
										color: index$1 ? "#999" : "rgba(0,0,0,0)",
										size: G.appFontSize - 4
									})
								),
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(this$1.props.size) +
											";color:" +
											(index$1 == list.length - 1 && index$1 ? "#333" : "#999")
									},
									item$1.value
								)
							)
						);
					})
				)
			);
		};

		return ZBreadcrumb;
	})(Component);
	apivm.define("z-breadcrumb", ZBreadcrumb);

	var CollectAdd = /*@__PURE__*/ (function(Component) {
		function CollectAdd(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				level: {key: "0", data: [{key: "0", value: "全部"}]},

				listData: [],
				favoriteFolder: [],
				emptyBox: {
					type: "load",
					text: ""
				}
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						this.data.level.key = "0";
						this.data.level.data = [this.data.level.data[0]];
						this.data.emptyBox.type = "load";
						if (this.data.show) {
							this.baseInit();
						}
					}
				},
				titleName: function() {
					var name = "";
					switch (this.props.dataMore.key) {
						case "long_add":
							name =
								"收藏到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
					}

					return name;
				}
			};
		}

		if (Component) CollectAdd.__proto__ = Component;
		CollectAdd.prototype = Object.create(Component && Component.prototype);
		CollectAdd.prototype.constructor = CollectAdd;
		CollectAdd.prototype.baseInit = function() {
			this.getData();
		};
		CollectAdd.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		CollectAdd.prototype.penetrate = function() {};
		CollectAdd.prototype.confirmBtn = function() {
			var this$1 = this;

			var data = this.props.dataMore.data;
			optionCollect(
				{
					code: data.code,
					id: data.id,
					collect: true,
					theme: data.title,
					folderId: this.data.level.key
				},
				function(ret) {
					if (ret && ret.code == 200) {
						this$1.closePage();
						this$1.fire("refresh");
					} else {
						toast(ret ? ret.message || ret.data : NET_ERR);
					}
				}
			);
		};
		CollectAdd.prototype.openFolder = function(_item) {
			this.data.level.data.push({key: _item.id, value: _item.name});
		};
		CollectAdd.prototype.getData = function(_type) {
			var this$1 = this;

			ajax(
				{u: appUrl() + "favoriteFolder/list"},
				"favoriteFolder",
				function(ret, err) {
					hideProgress();
					var code = ret ? ret.code : "";
					this$1.data.favoriteFolder =
						code == "200" && isArray(ret.data) ? ret.data : [];
					this$1.showFavoriteFolder(ret);
				},
				"收藏夹列表",
				"post",
				{
					body: JSON.stringify({})
				}
			);
		};
		CollectAdd.prototype.showFavoriteFolder = function(ret) {
			var newList = [];
			if (this.data.level.key == "0") {
				newList = this.data.favoriteFolder;
			} else {
				newList =
					(getItemForKey(this.data.level.key, this.data.favoriteFolder, "id") || {})
						.children || [];
			}
			this.data.listData = newList;
			if (!this.data.listData.length) {
				this.data.emptyBox.type = ret ? "1" : "2";
				this.data.emptyBox.text =
					ret && ret.code != 200 ? ret.message || ret.data : "";
			} else {
				this.data.emptyBox.type = "";
				this.data.emptyBox.text = "";
			}
		};
		CollectAdd.prototype.levelChange = function() {
			this.getData(0);
		};
		CollectAdd.prototype.addFolder = function() {
			var this$1 = this;

			alert(
				{
					title: "请输入文件夹名字",
					type: "input",
					msg: "",
					placeholder: "新建收藏夹",
					buttons: ["确定", "取消"]
				},
				function(ret) {
					if (ret.buttonIndex == 1) {
						ajaxProcess(
							{
								toast: "新建中",
								url: appUrl() + "favoriteFolder/add",
								param: {
									form: {
										parentId: this$1.data.level.key || "0",
										name: ret.content || ret.placeholder
									}
								},

								name: "新建收藏"
							},
							function(ret, err) {
								if (ret && ret.code == 200) {
									this$1.getData(0);
								}
							}
						);
					}
				}
			);
		};
		CollectAdd.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.titleName
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{
								class: "flex_row",
								style: "padding:10px 16px 10px 10px;margin-top:10px;"
							},
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h("z-breadcrumb", {
									size: -2,
									dataMore: this.data.level,
									onChange: this.levelChange
								})
							),
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.addFolder();
									},
									class: "flex_row",
									style: "margin-left:10px;"
								},
								apivm.h("a-iconfont", {
									style: "margin-right:6px;",
									name: "folder-add-fill",
									color: "#F6931C",
									size: G.appFontSize + 4
								}),
								apivm.h("text", {style: loadConfiguration(1) + "color:#333;"}, "新建")
							)
						),
						apivm.h(
							"view",
							{class: "flex_h"},
							apivm.h(
								"view",
								null,
								!this.data.emptyBox.type &&
									this.data.listData.map(function(item, index) {
										return [
											apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.openFolder(item, index);
													}
												},
												apivm.h(
													"view",
													{class: "folder_item flex_row"},
													apivm.h("a-iconfont", {
														name: "folder-2-fill",
														color: "#ffd977",
														size: G.appFontSize + 20
													}),
													apivm.h(
														"view",
														{class: "folder_item_body flex_w"},
														apivm.h(
															"view",
															{class: "folder_item_warp flex_row"},
															apivm.h(
																"text",
																{style: loadConfiguration(1) + "color: #333;flex:1;"},
																item.name
															),
															apivm.h(
																"view",
																{style: "padding:8px;transform: rotate(-90deg);"},
																apivm.h("a-iconfont", {
																	name: "xiangxia1",
																	color: "#999999",
																	size: G.appFontSize
																})
															)
														),
														apivm.h("view", {class: "folder_item_line"})
													)
												)
											)
										];
									})
							),
							apivm.h(
								"view",
								null,
								this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
							),
							apivm.h("z-empty", {
								_this: this,
								style: "margin:50px 0;",
								dataMore: this.data.emptyBox,
								onRefresh: this.getData
							})
						)
					)
				]
			);
		};

		return CollectAdd;
	})(Component);
	CollectAdd.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".folder_item": {padding: "0 16px"},
		".folder_item_body": {marginLeft: "15px"},
		".folder_item_warp": {minHeight: "65px", padding: "5px 0"},
		".folder_item_line": {borderTop: "1px solid #EEEEEE"},
		".folder_item_more": {width: "auto", height: "auto", padding: "6px"}
	};
	apivm.define("collect-add", CollectAdd);

	var PreviewerImg = /*@__PURE__*/ (function(Component) {
		function PreviewerImg(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				frameName: "previewerImg"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.props.dataMore.watermark = G.watermark;
							this.baseInit();
						} else {
							if (platform() == "app" && this.UIPhotoViewer) {
								this.UIPhotoViewer.close();
							}
						}
					}
					if (this.data.show) {
						if (this.nowParam != JSON.stringify(this.props.dataMore)) {
							this.nowParam = JSON.stringify(this.props.dataMore);
							this.sendMessage();
						}
					}
				}
			};
		}

		if (Component) PreviewerImg.__proto__ = Component;
		PreviewerImg.prototype = Object.create(Component && Component.prototype);
		PreviewerImg.prototype.constructor = PreviewerImg;
		PreviewerImg.prototype.baseInit = function() {
			var this$1 = this;

			var data = this.props.dataMore;
			switch (platform()) {
				case "app":
					if (G.watermark) {
						//有水印使用web
						addEventListener(this.data.frameName + "_msg", function() {
							this$1.closePage();
						});
						api.setFrameClient({frameName: this.data.frameName}, function(ret, err) {
							if (ret.state == 2) {
								this$1.isLoading = true;
								setTimeout(function() {
									this$1.sendMessage();
								}, 400);
							}
						});
						return;
					}
					this.UIPhotoViewer = api.require("UIPhotoViewer");
					this.UIPhotoViewer.open(
						{
							images: data.imgs,
							activeIndex: data.index,
							gestureClose: true,
							bgColor: "#000"
						},
						function(ret, err) {
							switch (ret.eventType) {
								case "click":
									this$1.closePage();
									break;
								case "gestureColse":
									this$1.UIPhotoViewer = null;
									this$1.closePage();
									break;
								case "longPress":
									var nowImg = data.imgs[ret.index];
									api.actionSheet(
										{
											buttons: ["保存到相册"],
											cancelTitle: "取消"
										},
										function(ret) {
											switch (ret.buttonIndex) {
												case 1:
													api.saveMediaToAlbum(
														{
															path: nowImg
														},
														function(ret) {
															toast(ret && ret.status ? "已保存到手机相册" : "保存失败");
														}
													);
													break;
											}
										}
									);
									break;
							}
						}
					);
					break;
				case "mp":
					wx.previewImage({
						urls: data.imgs,
						current: data.imgs[data.index],
						complete: function() {
							this$1.closePage();
						}
					});

					break;
				case "web":
					window.addEventListener("message", function(event) {
						this$1.closePage();
					});
					document
						.getElementById(this.data.frameName)
						.addEventListener("load", function() {
							this$1.isLoading = true;
							this$1.sendMessage();
						});
					break;
			}
		};
		PreviewerImg.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		PreviewerImg.prototype.sendMessage = function() {
			if (this.isLoading && this.props.dataMore.show) {
				if (platform() == "web") {
					if (!document.getElementById(this.data.frameName)) {
						return;
					}
					var targetWindow = document.getElementById(this.data.frameName)
						.contentWindow;
					targetWindow.postMessage(this.nowParam, "*");
				} else if (platform() == "app") {
					sendEvent(this.data.frameName + "_open", this.props.dataMore);
				}
			}
		};
		PreviewerImg.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0);"
				},
				this.props.dataMore.show &&
					(platform() == "web" || G.watermark) &&
					apivm.h("frame", {
						id: this.data.frameName,
						class: "xy_100",
						name: this.data.frameName,
						url: shareAddress() + "html/previewerImg.html",
						pageParam: {name: this.data.frameName},
						useWKWebView: true,
						scaleEnabled: true,
						allowEdit: true
					})
			);
		};

		return PreviewerImg;
	})(Component);
	apivm.define("previewer-img", PreviewerImg);

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				controls: true
			};
			this.compute = {
				getSrc: function() {
					var src = this.props.src;
					if (src.indexOf("http") != 0) {
						src = appUrl() + "file/preview/" + src;
					}
					return src;
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.firstPause = function() {
			var this$1 = this;

			if (!this.isPause) {
				this.isPause = true;
				if (platform() == "app" && !this.props.poster) {
					this.data.controls = false;
					setTimeout(function() {
						if (!this$1.props.autoplay) {
							document.getElementById(dealVideoId(this$1.props.src)) &&
								document.getElementById(dealVideoId(this$1.props.src)).pause();
						}
						this$1.data.controls = true;
					}, 50);
				} else {
					this.data.controls = true;
				}
			}
		};
		ZVideo.prototype.loadedmetadata = function(e) {
			if (api.systemType != "ios") {
				this.isPause = false;
				this.firstPause();
			}
		};
		ZVideo.prototype.play = function() {
			videoPlayPush(dealVideoId(this.props.src));
		};
		ZVideo.prototype.pause = function() {
			videoPlayRemove(dealVideoId(this.props.src));
		};
		ZVideo.prototype.render = function() {
			return apivm.h("video", {
				id: dealVideoId(this.props.src),
				style: "width:100%;height:" + G.pageWidth * 0.56 + "px;",
				controls: this.data.controls,
				autoplay:
					(platform() == "app" && !this.props.poster) || this.props.autoplay,
				onLoadedmetadata: this.loadedmetadata,
				onWaiting: this.firstPause,
				onPlay: this.play,
				onPause: this.pause,
				onEnded: this.pause,
				src: this.getSrc,
				poster: showImg(this.props.poster)
			});
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false, //是否展开了
				appDetailsStyle: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.expandShow = function(e) {
			stopBubble(e);
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (isObject(expText) || isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			this.data.appDetailsStyle = getPrefs("appDetailsStyle");
			if (this.data.appDetailsStyle == "edit") {
				this.data.showText = expText;
				return;
			}

			var notTagText = removeTag(expText);
			this.data.hasExpand =
				isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log("解析一："+expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			// console.log("解析二："+expText);
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nStyle = styleMatch ? styleMatch[1] : "";
				if (nlabel == "img") {
					nStyle = nStyle
						.replace("width: auto;", "width:100%;")
						.replace("width:auto;", "width:100%;");
				}
				var nowItem = {label: nlabel, index: index, text: item, style: nStyle};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: nStyle
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: nStyle
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var widthMatch = item.text.match(/width="([^"]*)"/);
						var tdWidth = widthMatch ? widthMatch[1] + "px" : null;
						if (!tdWidth) {
							tdWidth = this$1.getStyle(item.style, "width") || "150px";
						}
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					if (addItem.label == "img" && !addItem.src);
					else {
						showTexts.push(addItem);
					}
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			openWin_imgPreviewer({
				index: getItemForKey(_item.index, imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			stopBubble(e);
			if (!_item.href) {
				return;
			}
			if (platform() == "web") {
				window.open(_item.href);
				return;
			}
			openWin_url({url: _item.href});
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp('[;"]s*' + _item + "s*:s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle != "edit" &&
						this.data.listData.map(function(item, index) {
							return [
								apivm.h(
									"view",
									null,
									item.label == "text" &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(this$1.props.size || 0) +
													"line-height:" +
													(G.appFontSize + (this$1.props.size || 0)) * 1.8 +
													"px;" +
													(this$1.props.detail &&
													((platform() == "mp" && item.text.indexOf("　") != 0) ||
														(platform() == "web" &&
															item.text.indexOf(" ") != 0 &&
															item.text.indexOf("　") != 0))
														? "text-indent:2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail &&
												item.text.indexOf(" ") != 0 &&
												item.text.indexOf("　") != 0
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item.text
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "text_a" &&
										apivm.h(
											"view",
											{
												onClick: function(e) {
													return this$1.openHrefs(e, item, index);
												}
											},
											apivm.h(
												"text",
												{
													style: loadConfiguration(this$1.props.size || 0) + "color: blue;",
													class: "richText"
												},
												item.text
											)
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "br" &&
										apivm.h("view", {
											style:
												"height:" + (G.appFontSize - 6 + (this$1.props.size || 0)) + "px;"
										})
								),
								apivm.h(
									"view",
									null,
									item.label == "img" &&
										apivm.h(
											"view",
											{
												class: "richImgBox",
												onClick: function(e) {
													return this$1.openImages(e, item, index);
												}
											},
											apivm.h("image", {
												class: "richImg",
												style: item.style || "",
												mode: "widthFix",
												thumbnail: "false",
												src: item.src
											})
										)
								),
								apivm.h(
									"view",
									null,
									(item.label == "video" || item.label == "source") &&
										apivm.h(
											"view",
											{class: "richImgBox"},
											item.src && apivm.h("z-video", {src: item.src})
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "table" &&
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchEnd: this$1.nTouchmove
												},
												(item.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(this$1.props.size || 0) +
																			"text-align: center;",
																		class: "richText"
																	},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								)
							];
						})
				),
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle == "edit" &&
						apivm.h("rich-text", {nodes: this.data.showText})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: this.expandShow
							},
							apivm.h(
								"text",
								{style: loadConfiguration((this.props.size || 0) - 2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {
			alignItems: "center",
			justifyContent: "center",
			margin: "10px 0"
		},
		".richImg": {maxWidth: "100% !important"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	function initMap() {
		var map = api.require("aMap");
		try {
			map.updateMapViewPrivacy({
				privacyAgree: "didAgree",
				privacyShow: "didShow",
				containStatus: "didContain"
			});
			map.updateSearchPrivacy({
				privacyAgree: "didAgree",
				privacyShow: "didShow",
				containStatus: "didContain"
			});
		} catch (e) {}
		return map;
	}

	function openNavigation(_param) {
		var installedBaidu = true;
		var installedAutonavi = true;
		var installedTencent = true;
		if (platform() == "app") {
			installedBaidu = api.appInstalled({
				sync: true,
				appBundle: api.systemType == "ios" ? "baidumap" : "com.baidu.BaiduMap"
			});
			installedAutonavi = api.appInstalled({
				sync: true,
				appBundle: api.systemType == "ios" ? "iosamap" : "com.autonavi.minimap"
			});
			installedTencent = api.appInstalled({
				sync: true,
				appBundle: api.systemType == "ios" ? "sosomap" : "com.tencent.map"
			});
		}
		if (_param.map) {
			_param.map.hide();
		}
		actionSheet(
			{
				title: "请选择",
				buttons: [
					{
						name: "百度地图" + (!installedBaidu ? "(未安装)" : ""),
						color: !installedBaidu ? "#333" : G.appTheme,
						disabled: !installedBaidu
					},
					{
						name: "高德地图" + (!installedAutonavi ? "(未安装)" : ""),
						color: !installedAutonavi ? "#333" : G.appTheme,
						disabled: !installedAutonavi
					},
					{
						name: "腾讯地图" + (!installedTencent ? "(未安装)" : ""),
						color: !installedTencent ? "#333" : G.appTheme,
						disabled: !installedTencent
					}
				],

				pageClose: function pageClose() {
					if (_param.map) {
						_param.map.show();
					}
				}
			},
			function(ret, err) {
				var _index = ret.buttonIndex;
				switch (_index) {
					case 1:
						openNavigationBaidu(_param);
						break;
					case 2:
						openNavigationAutonavi(_param);
						break;
					case 3:
						openNavigationTencent(_param);
						break;
				}
			}
		);
	}

	//打开百度导航
	function openNavigationBaidu(_param) {
		var transBMap = qqMapTransBMap(_param.lon, _param.lat);
		var _address = _param.poi;
		var _lon = transBMap.lng;
		var _lat = transBMap.lat;
		var param = {
			iosUrl:
				"baidumap://map/direction?destination=latlng:" +
				_lat +
				"," +
				_lon +
				"|name:" +
				_address +
				"&mode=driving&coord_type=bd09ll&src=ios.zhengyu.app",
			uri:
				"baidumap://map/direction?destination=latlng:" +
				_lat +
				"," +
				_lon +
				"|name:" +
				_address +
				"&mode=driving&coord_type=bd09ll&src=android.zhengyu.app",
			url:
				"https://api.map.baidu.com/marker?location=" +
				_lat +
				"," +
				_lon +
				"&title=" +
				_address +
				"&output=html"
		};

		if (platform() == "app") {
			api.openApp(param);
		} else {
			openNavigationWeb(param);
		}
	}

	//打开高德导航
	function openNavigationAutonavi(_param) {
		var _address = _param.poi;
		var _lon = _param.lon;
		var _lat = _param.lat;
		var param = {
			iosUrl:
				"iosamap://path?sourceApplication=" +
				api.appName +
				"&dname=" +
				_address +
				"&dlat=" +
				_lat +
				"&dlon=" +
				_lon +
				"&dev=0&t=0",
			uri:
				"amapuri://route/plan?sourceApplication=" +
				api.appName +
				"&dname=" +
				_address +
				"&dlat=" +
				_lat +
				"&dlon=" +
				_lon +
				"&dev=0&t=0",
			url:
				"https://uri.amap.com/marker?position=" +
				_lon +
				"," +
				_lat +
				"&name=" +
				_address +
				""
		};

		if (platform() == "app") {
			api.openApp(param);
		} else {
			openNavigationWeb(param);
		}
	}

	//打开腾讯导航
	function openNavigationTencent(_param) {
		var _address = _param.poi;
		var _lon = _param.lon;
		var _lat = _param.lat;
		var param = {
			iosUrl:
				"qqmap://map/routeplan?type=drive&fromcoord=CurrentLocation&to=" +
				_address +
				"&tocoord=" +
				_lat +
				"," +
				_lon +
				"&referer=" +
				api.appName,
			uri:
				"qqmap://map/routeplan?type=drive&fromcoord=CurrentLocation&to=" +
				_address +
				"&tocoord=" +
				_lat +
				"," +
				_lon +
				"&referer=" +
				api.appName,
			url:
				"https://apis.map.qq.com/uri/v1/marker?marker=coord:" +
				_lat +
				"," +
				_lon +
				";addr:" +
				_address +
				""
		};

		if (platform() == "app") {
			api.openApp(param);
		} else {
			openNavigationWeb(param);
		}
	}

	function openNavigationWeb(_param) {
		openWin_url({url: _param.url});
	}

	//将腾讯/高德地图经纬度转换为百度地图经纬度
	function qqMapTransBMap(lng, lat) {
		var x_pi = (3.14159265358979324 * 3000.0) / 180.0;
		var x = lng;
		var y = lat;
		var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
		var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
		var lngs = z * Math.cos(theta) + 0.0065;
		var lats = z * Math.sin(theta) + 0.006;
		return {lng: lngs, lat: lats};
	}

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				dotClose: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.dotClose = true;
							setTimeout(function() {
								this$1.data.dotClose = false;
							}, 300);
						} else {
							G.actionSheetPop.pageClose();
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.closePage = function() {
			if (this.data.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			if (this.data.dotClose || _item.disabled) {
				return;
			}
			_item.buttonIndex = _index + 1;
			G.actionSheetPop.callback(_item);
			this.closePage();
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h("view", {
						onClick: function() {
							return this$1.closePage();
						},
						class: "actionSheet_cancel"
					}),
					apivm.h(
						"view",
						{
							class: this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "flex-shrink: 0;"
						},
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					apivm.h(
						"scroll-view",
						{
							class: !this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "height: auto;background: #FFF;flex-shrink: 1;",
							"scroll-y": true
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						(this.props.dataMore.data || []).map(function(item, index) {
							return (
								(isParameters(item.show) ? item.show : true) && [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.itemClick(item, index);
											},
											class: "actionSheet_item",
											style:
												"justify-content:" +
												(item.justify || "center") +
												";opacity: " +
												(item.disabled ? "0.3" : "1") +
												";"
										},
										apivm.h(
											"view",
											null,
											item.icon &&
												apivm.h("a-iconfont", {
													style: "margin-right:10px;",
													name: item.icon,
													color: (isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333",
													size:
														G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
												})
										),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-1) +
													";color:" +
													((isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333")
											},
											item.name
										)
									),
									index != this$1.props.dataMore.data.length - 1 &&
										!this$1.props.dataMore.cancel &&
										apivm.h(
											"view",
											{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
											apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
										)
								]
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-shrink: 0;"},
						this.props.dataMore.cancel &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "actionSheet_item",
									style:
										"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + ";color:#333"},
									this.props.dataMore.cancel || "取消"
								)
							)
					),
					apivm.h("view", {
						style:
							"background:#fff;flex-shrink: 0;padding-bottom:" +
							safeArea().bottom +
							"px;"
					})
				]
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_cancel": {flex: "1", minHeight: "20%", flexShrink: "0"},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "5px 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var MapChat = /*@__PURE__*/ (function(Component) {
		function MapChat(props) {
			Component.call(this, props);
			this.data = {
				show: false
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							setTimeout(
								function() {
									this$1.baseInit();
								},
								platform() == "app" ? 50 : 0
							);
						} else {
							this.baseclose();
						}
					}
				}
			};
		}

		if (Component) MapChat.__proto__ = Component;
		MapChat.prototype = Object.create(Component && Component.prototype);
		MapChat.prototype.constructor = MapChat;
		MapChat.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			this.sendLon = "";
			getBoundingClientRect("map_box", function(ret) {
				var boxrect = ret;
				if (platform() == "app") {
					var setCenter = function(_lon, _lat) {
						this$1.sendLon = _lon;
						this$1.sendLat = _lat;
						this$1.map.setCenter({coords: {lon: _lon, lat: _lat}});
						this$1.map.getNameFromCoords({lon: _lon, lat: _lat}, function(ret, err) {
							console.log(JSON.stringify(ret));
							if (ret.status) {
								this$1.sendContent = ret.sematicDescription || ret.address || "";
								this$1.map.addAnnotations(
									{
										annotations: [
											{
												id: 1,
												lon: _lon,
												lat: _lat,
												icons: ["widget://image/icon_mark_map.png"],
												w: api.systemType == "ios" ? 45 : 39,
												h: api.systemType == "ios" ? 45 : 39
											}
										]
									},

									function(ret) {}
								);
								this$1.map.setBubble(
									{
										id: 1,
										content: {
											title: ret.city + " " + ret.district,
											subTitle: this$1.sendContent
										},

										styles: {
											titleColor: "#000",
											titleSize: 14,
											subTitleColor: "#666",
											subTitleSize: 12,
											marginT: 10,
											marginB: 10,
											w: 260,
											h: 75
										}
									},

									function(ret) {
										this$1.map.closeBubble({id: 1});
									}
								);
								this$1.map.popupBubble({id: 1});
							}
						});
					};
					this$1.map = initMap();
					if (!this$1.map) {
						toast("地图模块未绑定，请联系管理员");
						return;
					}
					this$1.map.open(
						{
							rect: {
								x: boxrect.left,
								y: boxrect.top,
								w: boxrect.width,
								h: boxrect.height
							},

							zoomLevel: 16,
							showUserLocation: true,
							showsAccuracyRing: false
						},
						function(ret) {
							this$1.map.setCompass({show: true, position: {x: 10, y: 10}});
							if (!dm.item) {
								this$1.map.addEventListener({name: "click"}, function(ret) {
									if (ret.status) {
										this$1.map.removeAnnotations();
										setCenter(ret.lon, ret.lat);
									}
								});
								this$1.map.getLocation(function(ret, err) {
									if (ret.status) {
										this$1.map.showUserLocation({isShow: false}); //不显示用户位置
										setCenter(ret.lon, ret.lat);
									}
								});
							} else {
								setTimeout(
									function() {
										this$1.map.showUserLocation({isShow: false}); //不显示用户位置
										setCenter(dm.item.content.longitude, dm.item.content.latitude);
									},
									api.systemType == "ios" ? 0 : 300
								);
							}
						}
					);
				} else if (platform() == "web") {
					window.onmessage = function(e) {
						this$1.getFrameMsg(e.data);
					};
					document.getElementById("mapframe").addEventListener("load", function() {
						this$1.sendFrameMsg(dm, "init");
					});
				}
			});
		};
		MapChat.prototype.baseclose = function() {
			//页面做为组件被隐藏时监听
			if (platform() == "app") {
				if (this.map) {
					this.map.close();
				}
			} else if (platform() == "web");
		};
		MapChat.prototype.penetrate = function() {};
		MapChat.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		MapChat.prototype.getFrameMsg = function(_obj) {
			switch (_obj.mType) {
				case "setData":
					this.sendLon = _obj.lon;
					this.sendLat = _obj.lat;
					this.sendContent = _obj.poi;
					break;
			}
		};
		MapChat.prototype.sendFrameMsg = function(_obj, _type) {
			var sendMsg = {};
			if (isObject(_obj)) {
				for (var value in _obj) {
					if (!isFunction(_obj[value])) {
						sendMsg[value] = _obj[value];
					}
				}
			}
			sendMsg.mType = _type;
			document.getElementById("mapframe").contentWindow.postMessage(sendMsg, "*");
		};
		MapChat.prototype.confirmBtn = function() {
			var dm = this.props.dataMore;
			if (!dm.item) {
				if (!this.sendLat || !this.sendLon) {
					return;
				}
				this.closePage();
				dm.callback({lat: this.sendLat, lon: this.sendLon, poi: this.sendContent});
				return;
			}
			openNavigation({
				lon: dm.item.content.longitude,
				lat: dm.item.content.latitude,
				poi: dm.item.content.poi,
				map: this.map
			});
		};
		MapChat.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "位置"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										this.props.dataMore.item ? "导航" : "发送"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{id: "map_box", class: "flex_h"},
							platform() == "web"
								? apivm.h("frame", {
										id: "mapframe",
										class: "xy_100",
										name: "mapframe",
										allow: "microphone *;camera *;geolocation *;",
										url: shareAddress() + "html/amap.html"
								  })
								: null
						)
					)
			);
		};

		return MapChat;
	})(Component);
	MapChat.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		}
	};
	apivm.define("map-chat", MapChat);

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				size: G.appFontSize + (this.props.size || 0),
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: this.props.type == 3
						? "fangxingxuanzhongfill"
						: "yuanxingxuanzhongfill"
					: this.props.type == 3
					? "fangxingweixuanzhong"
					: "danxuan_weixuanzhong",
				color: this.props.checked ? this.props.color || G.appTheme : "#999"
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
			this.data = {
				esrc: appUrl() + "img/default_user_head.jpg",
				asrc: null,
				bsrc: ""
			};
			this.compute = {
				monitor: function() {
					var url = isObject(this.props.src) ? this.props.src.url : this.props.src;
					if (this.data.asrc != url || !this.data.asrc) {
						this.data.asrc = url || this.data.esrc;
						this.data.bsrc = "";
					}
				}
			};
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.error = function() {
			this.data.bsrc = this.data.esrc;
		};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{s: this.monitor, class: "xy_100"},
				apivm.h("z-image", {
					round: true,
					mode: "scaleToFill",
					thumbnail: "false",
					src: showImg(this.data.bsrc || this.data.asrc, "150x150-compress-"),
					onError: this.error
				})
			);
		};

		return ZAvatar;
	})(Component);
	apivm.define("z-avatar", ZAvatar);

	var ItemFile = /*@__PURE__*/ (function(Component) {
		function ItemFile(props) {
			Component.call(this, props);
		}

		if (Component) ItemFile.__proto__ = Component;
		ItemFile.prototype = Object.create(Component && Component.prototype);
		ItemFile.prototype.constructor = ItemFile;
		ItemFile.prototype.openDetail = function(e) {
			if (this.props.item.fileInfo.type == "folder") {
				this.fire("folder", this.props.item);
				return;
			}
			if (this.props.select) {
				var nItem = getItemForKey(this.props.item.id, this.props.listSelect, "id");
				if (nItem) {
					if (!nItem.readonly) {
						delItemForKey(nItem, this.props.listSelect, "id");
					}
				} else {
					this.props.listSelect.push(this.props.item);
				}
			}
			stopBubble(e);
		};
		ItemFile.prototype.isSelectValue = function(_item, _type) {
			var selectItem = getItemForKey(_item.id, this.props.listSelect, "id");
			if (_type == "color") {
				return selectItem && !selectItem.readonly;
			}
			return selectItem;
		};
		ItemFile.prototype.showDot = function(_item) {
			return (
				_item.isRedDot == 1 ||
				getItemForKey(_item.id, this.props.dataMore.listUnread || [])
			);
		};
		ItemFile.prototype.openLeftMore = function(e, _item) {
			stopBubble(e);
			this.fire("leftMore", _item);
		};
		ItemFile.prototype.openRightMore = function(e, _item) {
			stopBubble(e);
			this.fire("rightMore", _item);
		};
		ItemFile.prototype.showType = function() {
			var dataMore = this.props.dataMore;
			var select = this.props.select;
			if (
				dataMore.type == "details" ||
				(select && this.props.item.fileInfo.type == "folder")
			) {
				return 1;
			}
			if (select) {
				return 2;
			}
			return 3;
		};
		ItemFile.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{onClick: this.openDetail, class: "item_file_item flex_row"},
				apivm.h(
					"view",
					null,
					this.showType() == 2 &&
						apivm.h(
							"view",
							{
								style: "padding:10px;",
								onClick: function(e) {
									return this$1.openLeftMore(e, this$1.props.item);
								}
							},
							apivm.h("a-iconfont", {
								name: "gengduo3",
								color: "#999",
								size: G.appFontSize + 10
							})
						)
				),
				apivm.h(
					"view",
					{style: "padding:4px;margin-right:10px;"},
					apivm.h("a-iconfont", {
						name: this.props.item.fileInfo.name,
						color: this.props.item.fileInfo.color,
						size: G.appFontSize + 20
					}),
					this.showDot(this.props.item)
						? apivm.h("view", {
								class: "item_file_redDot flex_row",
								style: "" + loadConfigurationSize(-6)
						  })
						: null
				),
				apivm.h(
					"view",
					{class: "flex_w"},
					apivm.h(
						"text",
						{
							class: "text_one",
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 3) +
								"px;font-weight: 600;color:#333;"
						},
						this.props.item.name
					),
					this.props.dataMore.type != "details"
						? apivm.h(
								"view",
								{class: "flex_row", style: "margin-top:4px;"},
								apivm.h(
									"view",
									null,
									this.props.item.firstMsg &&
										apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(-4) +
														"color: " +
														(this.props.item.fristColor || "#999") +
														";"
												},
												this.props.item.firstMsg
											),
											apivm.h("view", {
												style:
													"width:1px;height:" +
													(G.appFontSize - 1) +
													"px;background:#eee;margin:0 10px;"
											})
										)
								),
								apivm.h(
									"view",
									null,
									this.props.item.firstIcon &&
										this.props.item.firstIcon.name &&
										apivm.h("a-iconfont", {
											style: "margin-right:6px;",
											name: this.props.item.firstIcon.name,
											color: this.props.item.firstIcon.color || "#999",
											size: G.appFontSize
										})
								),
								apivm.h(
									"text",
									{style: loadConfiguration(-4) + "color: #999;flex-shrink: 0;"},
									dayjs(this.props.item.time).format("YYYY-MM-DD HH:mm")
								),
								apivm.h(
									"view",
									null,
									isArray(this.props.item.addMsg) &&
										this.props.item.addMsg.length > 0 &&
										apivm.h(
											"view",
											{class: "flex_row"},
											(Array.isArray(this.props.item.addMsg)
												? this.props.item.addMsg
												: Object.values(this.props.item.addMsg)
											).map(function(nItem, nIndex) {
												return apivm.h(
													"view",
													{class: "flex_row"},
													apivm.h("view", {
														style:
															"width:1px;height:" +
															(G.appFontSize - 1) +
															"px;background:#eee;margin:0 10px;"
													}),
													apivm.h(
														"text",
														{
															style:
																loadConfiguration(-4) +
																"color: " +
																(nItem.color || "#999") +
																";"
														},
														nItem.text
													)
												);
											})
										)
								),
								apivm.h(
									"view",
									null,
									!isArray(this.props.item.addMsg) &&
										this.props.item.addMsg &&
										apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h("view", {
												style:
													"width:1px;height:" +
													(G.appFontSize - 1) +
													"px;background:#eee;margin:0 10px;"
											}),
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(-4) +
														"color: " +
														(this.props.item.addColor || "#999") +
														";"
												},
												this.props.item.addMsg
											)
										)
								)
						  )
						: null
				),
				apivm.h(
					"view",
					null,
					this.showType() == 1 &&
						apivm.h(
							"view",
							{style: "padding:8px;transform: rotate(-90deg);"},
							apivm.h("a-iconfont", {
								name: "xiangxia1",
								color: "#999999",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.showType() == 2 &&
						apivm.h(
							"view",
							{style: "margin-left:5px;"},
							apivm.h("z-radio", {
								checked: this.isSelectValue(this.props.item),
								size: 4,
								color: this.isSelectValue(this.props.item, "color")
									? G.appTheme
									: "#999"
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.showType() == 3 &&
						apivm.h(
							"view",
							{
								style: "padding:8px;margin: -8px 0;",
								onClick: function(e) {
									return this$1.openRightMore(e, this$1.props.item);
								}
							},
							apivm.h("a-iconfont", {
								name: "gengduo",
								color: "#999999",
								size: G.appFontSize
							})
						)
				)
			);
		};

		return ItemFile;
	})(Component);
	ItemFile.css = {
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".item_file_item": {minHeight: "70px", padding: "5px 16px"},
		".item_file_redDot": {
			position: "absolute",
			zIndex: "1",
			top: "2px",
			right: "2px",
			background: "#f92323",
			borderRadius: "50%",
			whiteSpace: "nowrap",
			justifyContent: "center"
		}
	};
	apivm.define("item-file", ItemFile);

	var FileList = /*@__PURE__*/ (function(Component) {
		function FileList(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				inputBox: {
					show: true,
					value: "",
					placeholder: "请输入关键词"
				},

				level: {key: "0", data: [{key: "0", value: "全部"}]},

				listData: [],
				listSelect: [],
				itemFileMore: {
					type: "select"
				},

				emptyBox: {
					type: "load",
					text: ""
				},

				orderBys: {
					key: "updatedate",
					direction: 1 //0 顺序 1倒序
				},
				select: false,
				hasAddFolder: false //新建按钮
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						this.data.level.key = "0";
						this.data.level.data = [this.data.level.data[0]];
						this.data.emptyBox.type = "load";
						if (this.data.show) {
							this.baseInit();
						}
					}
				},
				titleName: function() {
					var name = "";
					switch (this.props.dataMore.key) {
						case "cloud_file":
							name = "云盘文件";
							break;
						case "chat_file":
							name = "聊天文件";
							break;
						case "cloud_resave":
							name =
								"转存到“" +
								(this.data.level.data.length == 1
									? "我的云盘"
									: this.data.level.data[this.data.level.data.length - 1].value) +
								"”";
							break;
					}

					return name;
				}
			};
		}

		if (Component) FileList.__proto__ = Component;
		FileList.prototype = Object.create(Component && Component.prototype);
		FileList.prototype.constructor = FileList;
		FileList.prototype.baseInit = function() {
			this.data.select = this.props.dataMore.select;
			this.data.hasAddFolder = this.props.dataMore.addFolder;
			this.data.listSelect = [];
			this.getData(0);
		};
		FileList.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		FileList.prototype.penetrate = function() {};
		FileList.prototype.confirmBtn = function() {
			var dataMore = this.props.dataMore;
			dataMore.toId = this.data.level.key;
			dataMore.toItem = this.data.level.data[this.data.level.data.length - 1];
			dataMore.listSelect = this.data.listSelect;
			if (dataMore.callback) {
				dataMore.callback(dataMore);
			} else {
				this.fire("click", dataMore);
			}
			this.closePage();
		};
		FileList.prototype.openFolder = function(e) {
			this.data.level.data.push({key: e.detail.id, value: e.detail.name});
		};
		FileList.prototype.openLeftMore = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			var buttons = ["打开"];
			if (detail.createBy == G.uId || detail.createBy == G.userId) {
				buttons.push("删除");
			}
			actionSheet(
				{
					title: "提示",
					buttons: buttons
				},
				function(ret, err) {
					if (ret.name == "打开") {
						openWin_filePreviewer({
							id: detail.fileInfoId,
							suffix: detail.fileInfo.type,
							fileSource: "5"
						});
					} else if (ret.name == "删除") {
						var delKey = "groupFileId";
						var url = appUrl() + "chatGroupFile/dels";
						var param = {ids: [detail[delKey]]};
						if (this$1.props.dataMore.key == "cloud_file") {
							url = appUrl() + "panfile/recycleflag";
							delKey = "id";
							param = {ids: [detail[delKey]], isRecycle: 1};
						}
						ajaxAlert(
							{
								msg: "确定删除吗?",
								url: url,
								param: param,
								toast: "删除中"
							},
							function(ret) {
								delItemForKey(detail[delKey], this$1.data.listData, delKey);
								delItemForKey(detail[delKey], this$1.data.listSelect, delKey);
							}
						);
					}
				}
			);
		};
		FileList.prototype.getData = function(_type) {
			var this$1 = this;

			var dataMore = this.props.dataMore;
			var url =
				appUrl() +
				(dataMore.key == "long_shared" ? "pubsharefile" : "panfile") +
				"/list";
			var tableId =
				dataMore.key == "long_shared" ? "id_pan_pubshare_file" : "id_pan_file_list";
			var postParam = {
				pageNo: 1,
				pageSize: 9999,
				keyword: "",
				tableId: tableId,
				query: {fileMenu: "1", fileStatus: "0"},
				orderBys: [
					{
						columnId: tableId + "_" + this.data.orderBys.key,
						isDesc: this.data.orderBys.direction
					}
				],
				wheres: [
					{
						columnId: tableId + "_parentid",
						queryType: "EQ",
						value: this.data.level.key || "0"
					}
				]
			};

			if (dataMore.key == "cloud_file") {
				delete postParam.query;
			}
			if (dataMore.key == "chat_file") {
				url = appUrl() + "chatGroupFile/list";
				postParam = {
					pageNo: 1,
					pageSize: 9999,
					keyword: "",
					query: {chatGroupId: dataMore.id}
				};
			}
			ajax(
				{u: url},
				"file_list",
				function(ret, err) {
					hideProgress();
					var code = ret ? ret.code : "";
					var data = ret ? ret.data || [] : [];
					if (!isArray(data) || !data.length) {
						dealData(_type, this$1, ret);
						return;
					}
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						if (dataMore.key == "chat_file") {
							item.groupFileId = _eItem.id;
							_eItem = _eItem.fileInfo || {};
						}
						item.id = _eItem.id || ""; //id
						item.name = _eItem.fileName || _eItem.originalFileName || ""; //标题
						item.time = _eItem.updateDate || _eItem.createDate;
						item.fileInfoId = _eItem.fileInfoId || _eItem.id;
						item.createBy = _eItem.createBy || _eItem.accountId;

						item.fileInfo = getFileInfo(_eItem.extName || "folder");
						if (dataMore.key == "cloud_file" || dataMore.key == "chat_file") {
							nowList.push(item);
						} else {
							if (!getItemForKey(item.id, this$1.data.listSelect, "id")) {
								nowList.push(item);
							}
						}
					});
					if (!_type) {
						this$1.data.listData = nowList;
					} else {
						this$1.data.listData = this$1.data.listData.concat(nowList);
					}
					this$1.data.emptyBox.type = "";
					this$1.data.emptyBox.text = LOAD_ALL;
				},
				"列表",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		FileList.prototype.levelChange = function() {
			this.getData(0);
		};
		FileList.prototype.addFolder = function() {
			var this$1 = this;

			alert(
				{
					title: "请输入文件夹名字",
					type: "input",
					msg: "",
					placeholder: "新建文件夹",
					buttons: ["确定", "取消"]
				},
				function(ret) {
					if (ret.buttonIndex == 1) {
						ajaxProcess(
							{
								toast: "新建中",
								url:
									appUrl() +
									(this$1.props.dataMore.key == "cloud_shared"
										? "pubsharefile"
										: "panfile") +
									"/addmenu",
								param: {
									parentId: this$1.data.level.key || "0",
									fileName: ret.content || ret.placeholder,
									pubshareName: ret.content || ret.placeholder
								},

								name: "新建文件夹"
							},
							function(ret, err) {
								if (ret && ret.code == 200) {
									this$1.getData(0);
								}
							}
						);
					}
				}
			);
		};
		FileList.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.titleName
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{
								class: "flex_row",
								style: "padding:10px 16px 10px 10px;margin-top:10px;"
							},
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h("z-breadcrumb", {
									size: -2,
									dataMore: this.data.level,
									onChange: this.levelChange
								})
							),
							this.data.hasAddFolder &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.addFolder();
										},
										class: "flex_row",
										style: "margin-left:10px;"
									},
									apivm.h("a-iconfont", {
										style: "margin-right:6px;",
										name: "folder-add-fill",
										color: "#F6931C",
										size: G.appFontSize + 4
									}),
									apivm.h("text", {style: loadConfiguration(1) + "color:#333;"}, "新建")
								)
						),
						apivm.h(
							"y-scroll-view",
							{_this: this},
							apivm.h(
								"view",
								null,
								!this.data.emptyBox.type &&
									this.data.listData.map(function(item, index) {
										return [
											apivm.h("item-file", {
												dataMore: this$1.data.itemFileMore,
												search: this$1.data.inputBox,
												_this: this$1,
												select: this$1.data.select,
												listSelect: this$1.data.listSelect,
												item: item,
												onRefresh: function() {
													return this$1.getData(0);
												},
												onFolder: this$1.openFolder,
												onLeftMore: this$1.openLeftMore
											}),
											apivm.h(
												"view",
												{style: "padding-left:56px;"},
												apivm.h("z-divider", null)
											)
										];
									})
							),
							apivm.h(
								"view",
								null,
								this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
							),
							apivm.h("z-empty", {
								_this: this,
								style: "margin:50px 0;",
								dataMore: this.data.emptyBox,
								onRefresh: this.getData
							})
						)
					)
				]
			);
		};

		return FileList;
	})(Component);
	FileList.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".folder_item": {padding: "0 16px"},
		".folder_item_body": {marginLeft: "15px"},
		".folder_item_warp": {minHeight: "65px", padding: "5px 0"},
		".folder_item_line": {borderTop: "1px solid #EEEEEE"},
		".folder_item_more": {width: "auto", height: "auto", padding: "6px"}
	};
	apivm.define("file-list", FileList);

	var ZTag = /*@__PURE__*/ (function(Component) {
		function ZTag(props) {
			Component.call(this, props);
		}

		if (Component) ZTag.__proto__ = Component;
		ZTag.prototype = Object.create(Component && Component.prototype);
		ZTag.prototype.constructor = ZTag;
		ZTag.prototype.getTagStyle = function() {
			var color = this.props.color || G.appTheme;
			var rc = "",
				rbg = "",
				rbor = "";
			switch (this.props.type) {
				case "2":
					rc = color;
					rbg = "#FFF";
					rbor = colorRgba(color);
					break;
				case "3":
					rc = "#FFF";
					rbg = color;
					rbor = color;
					break;
				default:
					rc = color;
					rbg = colorRgba(color, 0.1);
					rbor = "transparent";
					break;
			}

			return (
				"color:" +
				rc +
				";background:" +
				rbg +
				";border: 1px solid " +
				rbor +
				";border-radius: " +
				(this.props.roundSize || 2) +
				"px;"
			);
		};
		ZTag.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "flex-direction:row;"},
				apivm.h(
					"text",
					{
						id: "" + (this.props.id || ""),
						class: "tag_box " + (this.props.class || ""),
						style:
							"" +
							loadConfiguration((this.props.size || 0) - 2) +
							this.getTagStyle() +
							(this.props.style || "")
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZTag;
	})(Component);
	ZTag.css = {".tag_box": {padding: "1px 10px"}};
	apivm.define("z-tag", ZTag);

	var YChatbox = /*@__PURE__*/ (function(Component) {
		function YChatbox(props) {
			Component.call(this, props);
			this.data = {
				isFocus: false,
				emotions: emotion,
				emotionUsed: [],

				extras: [
					{
						show: true,
						title: "图片",
						normalImg: shareAddress(1) + "image/chat_box/album.png",
						type: "image"
					},

					{
						show: true,
						title: "相机",
						normalImg: shareAddress(1) + "image/chat_box/cam.png",
						type: "camera"
					},

					{
						show: true,
						title: "位置",
						normalImg: shareAddress(1) + "image/chat_box/loc.png",
						type: "location"
					},

					{
						show: true,
						title: "文件",
						normalImg: shareAddress(1) + "image/chat_box/file.png",
						type: "files"
					}
				]
			};
		}

		if (Component) YChatbox.__proto__ = Component;
		YChatbox.prototype = Object.create(Component && Component.prototype);
		YChatbox.prototype.constructor = YChatbox;
		YChatbox.prototype.installed = function() {
			//可以音视频
			if (getPrefs("zyRongCloudRTC_init") == "true") {
				this.data.extras.push({
					show: true,
					title: "语音通话",
					normalImg: shareAddress(1) + "image/chat_box/phone.png",
					type: "phone"
				});
				this.data.extras.push({
					show: true,
					title: "视频通话",
					normalImg: shareAddress(1) + "image/chat_box/video.png",
					type: "video"
				});
			}
			if (this.props.dataMore.conversationType == "GROUP") {
				this.data.extras.push({
					show: true,
					title: "投票",
					normalImg: shareAddress(1) + "image/chat_box/vote.png",
					type: "vote"
				});
			}
		};
		YChatbox.prototype.chatDel = function() {
			if (!this.props.dataMore.value) {
				return;
			}
			var nowEmotion = "";
			for (var i = 0; i < this.data.emotions.length; i++) {
				var nameReg = new RegExp("(.+(?=[" + this.data.emotions[i].text + "]$))");
				if (nameReg.test(this.props.dataMore.value)) {
					nowEmotion = this.data.emotions[i].text;
					break;
				}
			}
			if (nowEmotion) {
				this.props.dataMore.value = this.props.dataMore.value.substring(
					0,
					this.props.dataMore.value.lastIndexOf(nowEmotion)
				);
			} else {
				this.props.dataMore.value = this.props.dataMore.value.substring(
					0,
					this.props.dataMore.value.length - 1
				);
			}
		};
		YChatbox.prototype.chatSend = function() {
			if (!this.props.dataMore.value) {
				return;
			}
			this.fire("confirm");
		};
		YChatbox.prototype.inputIng = function(e) {
			this.data.isFocus = true;
			if (isParameters(e)) {
				this.props.dataMore.value = e.detail.value;
			}
			this.fire("input");
		};
		YChatbox.prototype.inputBlur = function() {
			this.data.isFocus = false;
			this.fire("blur");
		};
		YChatbox.prototype.inputFocus = function() {
			this.data.isFocus = true;
			$("#" + this.props.dataMore.id).focus();
			this.props.dataMore.mode = "";
			this.fire("focus");
		};
		YChatbox.prototype.switchMode = function(_mode) {
			var this$1 = this;

			if (this.props.dataMore.mode == _mode) {
				this.props.dataMore.mode = "";
				setTimeout(function() {
					$("#" + this$1.props.dataMore.id).focus();
				}, 100);
			} else {
				this.props.dataMore.mode = _mode;
				if (this.props.dataMore.mode == "expression") {
					this.data.emotionUsed = JSON.parse(getPrefs("emotionUsed") || "[]");
				}
				setTimeout(function() {
					this$1.fire("focus");
				}, 0);
			}
		};
		YChatbox.prototype.emotionEnter = function(_item) {
			this.props.dataMore.value += _item.text;
			var nowEmotionUsed = JSON.parse(getPrefs("emotionUsed") || "[]");
			if (getItemForKey(_item.text, nowEmotionUsed, "text")) {
				delItemForKey(_item.text, nowEmotionUsed, "text");
			}
			nowEmotionUsed.unshift(_item);
			nowEmotionUsed = nowEmotionUsed.slice(0, 7);
			setPrefs("emotionUsed", JSON.stringify(nowEmotionUsed));
		};
		YChatbox.prototype.additionalEnter = function(_item) {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("additional", {data: _item});
			}, 0);
		};
		YChatbox.prototype.inputBox = function() {
			var this$1 = this;

			if (api.systemType != "ios") {
				return;
			}
			setTimeout(function() {
				this$1.props.dataMore.mode = "";
			}, 50);
			setTimeout(function() {
				$("#" + this$1.props.dataMore.id).focus();
			}, 200);
		};
		YChatbox.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					{
						class: "chatbox_box",
						style:
							"padding-bottom:" +
							((!this.data.isFocus && this.props.bottom && !this.props.dataMore.mode
								? footerBottom()
								: 0) +
								5) +
							"px;"
					},
					apivm.h("view", null, false),
					apivm.h(
						"view",
						{class: "chatbox_input_box", onClick: this.inputBox},
						apivm.h("textarea", {
							value: this.props.dataMore.value,
							disabled: api.systemType == "ios" && this.props.dataMore.mode != "",
							id: this.props.dataMore.id,
							style: "" + loadConfiguration(),
							class: "chatbox_input",
							"auto-height": true,
							"keyboard-type": "default",
							"confirm-type": "return",
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							onInput: this.inputIng,
							"confirm-hold": true
						})
					),
					apivm.h(
						"view",
						null,
						!this.props.dataMore.hideExpression &&
							apivm.h(
								"view",
								{
									class: "chatbox_img_box",
									onClick: function() {
										return this$1.switchMode("expression");
									}
								},
								apivm.h("image", {
									style: loadConfigurationSize(12),
									src:
										shareAddress(1) +
										"image/chat_box/" +
										(this.props.dataMore.mode == "expression" ? "key" : "face") +
										".png"
								})
							)
					),
					apivm.h(
						"view",
						null,
						!this.props.dataMore.hideAdd &&
							!this.props.dataMore.value &&
							apivm.h(
								"view",
								{
									class: "chatbox_img_box",
									onClick: function() {
										return this$1.switchMode("additional");
									}
								},
								apivm.h("image", {
									style: loadConfigurationSize(12),
									src:
										shareAddress(1) +
										"image/chat_box/" +
										(this.props.dataMore.mode == "additional" ? "key" : "add") +
										".png"
								})
							)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.value &&
							apivm.h(
								"z-button",
								{
									onClick: function() {
										return this$1.chatSend();
									},
									style: "padding:6px 8px;",
									color: G.appTheme
								},
								"发送"
							)
					)
				),
				this.props.dataMore.mode &&
					apivm.h(
						"view",
						{class: "chatbox_add"},
						apivm.h(
							"scroll-view",
							{
								style:
									"height:" +
									(footerBottom(this.props.bottom) +
										(this.props.dataMore.mode == "additional" ? 218 : 280)) +
									"px;",
								class: "chatbox_expression_box",
								"scroll-y": true
							},
							apivm.h(
								"view",
								null,
								this.props.dataMore.mode == "expression" &&
									apivm.h(
										"view",
										{
											style:
												"padding: 15px 0px " +
												(footerBottom(this.props.bottom) + 15 + 45) +
												"px 15px;"
										},
										apivm.h(
											"view",
											{
												style: {
													display:
														this.data.emotionUsed && this.data.emotionUsed.length
															? "flex"
															: "none"
												}
											},
											apivm.h(
												"text",
												{style: loadConfiguration(-1), class: "chatbox_emotions_hint"},
												"最近使用"
											),
											apivm.h(
												"view",
												{class: "chatbox_emotions_box"},
												(Array.isArray(this.data.emotionUsed)
													? this.data.emotionUsed
													: Object.values(this.data.emotionUsed)
												).map(function(item$1, index$1) {
													return apivm.h("image", {
														onClick: function() {
															return this$1.emotionEnter(item$1);
														},
														class: "chatbox_emotion_item",
														style: loadConfigurationSize(17),
														src:
															shareAddress(1) +
															"image/chat_box/emotion/" +
															item$1.name +
															".png"
													});
												})
											)
										),
										apivm.h(
											"text",
											{style: loadConfiguration(-1), class: "chatbox_emotions_hint"},
											"所有表情"
										),
										apivm.h(
											"view",
											{class: "chatbox_emotions_box"},
											(Array.isArray(this.data.emotions)
												? this.data.emotions
												: Object.values(this.data.emotions)
											).map(function(item$1, index$1) {
												return apivm.h("image", {
													onClick: function() {
														return this$1.emotionEnter(item$1);
													},
													class: "chatbox_emotion_item",
													style: loadConfigurationSize(17),
													src:
														shareAddress(1) + "image/chat_box/emotion/" + item$1.name + ".png"
												});
											})
										)
									)
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.mode == "additional" &&
									apivm.h(
										"view",
										{
											style:
												"flex-direction:row; flex-wrap: wrap;padding: 15px 15px " +
												(footerBottom(this.props.bottom) + 15) +
												"px 15px;"
										},
										(Array.isArray(this.data.extras)
											? this.data.extras
											: Object.values(this.data.extras)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.additionalEnter(item$1);
													},
													class: "chatbox_additional_item",
													style: {display: item$1.show ? "flex" : "none"}
												},
												apivm.h("image", {
													style: loadConfigurationSize(37),
													src: item$1.normalImg
												}),
												apivm.h(
													"text",
													{style: loadConfiguration(-3), class: "chatbox_additional_text"},
													item$1.title
												)
											);
										})
									)
							)
						),
						apivm.h(
							"view",
							{
								class: "chatbox_option_box",
								style:
									"display:" +
									(this.props.dataMore.mode == "expression" ? "flex" : "none") +
									";bottom:" +
									(footerBottom(this.props.bottom) + 15) +
									"px;"
							},
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.chatDel();
									},
									class: "chatbox_option_item"
								},
								apivm.h("a-iconfont", {
									name: "a-14Bshanchu",
									color: this.props.dataMore.value ? "#333" : "rgba(0,0,0,0.3)",
									size: G.appFontSize + 4
								})
							)
						)
					)
			);
		};

		return YChatbox;
	})(Component);
	YChatbox.css = {
		".chatbox_box": {
			width: "100%",
			minHeight: "45px",
			flexDirection: "row",
			alignItems: "flex-end",
			padding: "5px"
		},
		".chatbox_input_box": {
			width: "1px",
			flex: "1",
			background: "rgba(0,0,0,0.05)",
			borderRadius: "5px",
			marginLeft: "5px",
			minHeight: "35px",
			maxHeight: "110px",
			justifyContent: "center"
		},
		".chatbox_input": {
			background: "transparent",
			width: "100%",
			minHeight: "32px",
			borderColor: "transparent",
			boxSizing: "border-box",
			padding: "8px 10px"
		},
		".chatbox_send": {padding: "0 8px", margin: "0 5px"},
		".chatbox_img_box": {
			width: "auto",
			height: "35px",
			alignItems: "center",
			justifyContent: "center",
			padding: "0 5px"
		},
		".chatbox_expression_box": {width: "100%", background: "rgba(0,0,0,0.05)"},
		".chatbox_emotions_box": {flexDirection: "row", flexWrap: "wrap"},
		".chatbox_emotion_item": {margin: "0 15px 15px 0"},
		".chatbox_emotions_hint": {color: "#333", marginBottom: "15px"},
		".chatbox_option_box": {
			position: "absolute",
			zIndex: "1",
			right: "10px",
			flexDirection: "row"
		},
		".chatbox_option_item": {
			width: "53px",
			height: "41px",
			background: "#FFF",
			borderTopLeftRadius: "5px",
			borderTopRightRadius: "5px",
			borderBottomRightRadius: "5px",
			borderBottomLeftRadius: "5px",
			alignItems: "center",
			justifyContent: "center",
			marginLeft: "10px",
			boxShadow: "-5px -5px 25px 15px rgba(255,255,255,0.7)"
		},
		".chatbox_additional_box": {width: "100%", background: "rgba(0,0,0,0.05)"},
		".chatbox_additional_item": {
			width: "25%",
			height: "94px",
			alignItems: "center",
			justifyContent: "center",
			padding: "5px 5px"
		},
		".chatbox_additional_text": {color: "#333", marginTop: "5px"}
	};
	apivm.define("y-chatbox", YChatbox);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var Root = /*@__PURE__*/ (function(Component) {
		function Root(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				MG: !this.props.dataMore ? G : null,

				scrollView: {
					scroll_view: "",
					scroll_animation: true
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				listData: [],

				chatPanel: {
					id: "chat_input",
					value: "",
					mode: "", //空 正常	record 录音面板	expression 表情	additional 附加面板
					conversationType: ""
				},

				canChat: false, //是否可以聊天
				cantChatText: "", //不能聊天提示

				notice: {show: false},
				online: {show: false, is: false},

				unreadMentioned: [], //是否有未读@消息
				newMsgInfo: [], //收到新消息info

				unreadMsgInfo: {show: false},

				animateMessageId: "",

				fileListPop: {
					//选择聊天 云盘文件
					show: false
				},

				mapChatPop: {
					show: false
				},

				favoriteOkBox: {
					show: false
				}
			};
		}

		if (Component) Root.__proto__ = Component;
		Root.prototype = Object.create(Component && Component.prototype);
		Root.prototype.constructor = Root;
		Root.prototype.onShow = function() {
			G.onShowNum++;
		};
		Root.prototype.baseInit = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			this.conversationType = this.data.pageParam.conversationType || "GROUP";
			this.targetId =
				this.data.pageParam.targetId || "platform5rds1680787110711291905";
			if (this.targetId.indexOf(chatHeader()) != 0) {
				this.targetId = chatHeader() + this.targetId;
			}
			this.data.chatPanel.conversationType = this.conversationType;
			console.log(
				"当前聊天：targetId：" +
					this.targetId +
					"，conversationType：" +
					this.conversationType
			);
			if (this.data.pageParam.messageId) {
				showProgress("加载中");
				this.unreadMentionedItem = {messageId: this.data.pageParam.messageId};
			}
			//搜索后回显
			addEventListener(
				"chat_" + this.conversationType + "_" + this.targetId,
				function(ret) {
					setTimeout(function() {
						showProgress("加载中");
						this$1.unreadMentionedItem = ret.value;
					}, 300);
				}
			);
			//发送消息后回调
			addEventListener(
				"rongCloud_sendMessage_" + this.conversationType + "_" + this.targetId,
				function(ret) {
					this$1.msgCallback(ret.value.success, ret.value.error);
				}
			);
			//获取@的消息
			addEventListener(
				"rongCloud_getUnreadMentionedMessages_" +
					this.conversationType +
					"_" +
					this.targetId,
				function(ret) {
					ret = ret.value.success;
					var nowList = [];
					(ret.result || []).forEach(function(nItem) {
						//item index 原数组对象
						if (
							!getItemForKey(nItem.messageId, this$1.data.unreadMentioned, "messageId")
						) {
							nowList.push(nItem);
						}
					});
					nowList = this$1.data.unreadMentioned.concat(nowList);
					this$1.getChatInfos(nowList);
					this$1.data.unreadMentioned = nowList;
				}
			);
			//获取历史消息
			addEventListener(
				"rongCloud_getHistoryMessages_" +
					this.conversationType +
					"_" +
					this.targetId,
				function(ret) {
					var param = ret.value.param;
					ret = ret.value.success;
					var data = ret ? ret.result || [] : [];
					console.log("历史消息参数:" + JSON.stringify(param));
					var isFirst =
						param[platform() == "app" ? "oldestMessageId" : "timestamp"] ==
						(platform() == "app" ? -1 : 0);
					//先清除未读
					var newSendTime = 0;
					if (this$1.data.listData.length > 0) {
						newSendTime =
							this$1.data.listData[this$1.data.listData.length - 1].sentTime;
					}
					var nowList = [];
					if (isArray(data) && data.length != 0) {
						var hasData = this$1.data.listData.length > 0;
						data.forEach(function(nItem) {
							if (
								isFirst ||
								!getItemForKey(nItem.messageId, this$1.data.listData, "messageId")
							) {
								nowList.push(nItem);
							}
						});
						if (!isFirst) {
							nowList = this$1.data.listData.concat(nowList);
						}
						nowList.sort(function(a, b) {
							return a.sentTime - b.sentTime;
						}); //随便加 然后按发送时间升序排序
						newSendTime = nowList[nowList.length - 1].sentTime; //得到数据后
						this$1.getChatInfos(nowList);
						this$1.data.listData = nowList;
						this$1.data.pageSize =
							this$1.data.listData.length < 15 ? 15 : this$1.data.listData.length + 1;
						console.log("会话详情JSON：" + JSON.stringify(this$1.data.listData));

						if (isFirst) {
							if (
								(this$1.scrollFooter || 0) < 100 &&
								this$1.newSendTime != newSendTime
							) {
								//在底部100px并且收到新消息的时候
								this$1.goButtom(null, hasData, 300);
							}
							if (this$1.unreadMentionedItem) {
								setTimeout(function() {
									this$1.clickUnreadMentioned(1);
								}, 100);
							}
						} else {
							this$1.goButtom(
								"item_" + param[platform() == "app" ? "oldestMessageId" : "timestamp"],
								false
							);
						}
					} else if (isFirst) {
						this$1.data.listData = nowList;
					}
					if (this$1.newSendTime != newSendTime) {
						//此时收到新消息 解决多端同步疯狂刷新的问题。
						this$1.newSendTime = newSendTime;
						this$1.getUnreadMentionedMessages();
						setTimeout(function() {
							sendEvent({
								name: "rongCloud",
								extra: {
									type: "clearMessagesUnreadStatus",
									param: {
										conversationType: this$1.conversationType,
										targetId: this$1.targetId,
										sentTime: newSendTime
									}
								}
							});
						}, 300);
					}
				}
			);
			//收到刷新监听
			addEventListener("chat_refresh_mo_chat", function(ret) {
				this$1.getData(0);
			});
			//获取草稿后回调
			addEventListener(
				"rongCloud_getTextMessageDraft_" +
					this.conversationType +
					"_" +
					this.targetId,
				function(ret) {
					ret = ret.value.success;
					this$1.data.chatPanel.value = ret.result;
					this$1.inputendLength = this$1.data.chatPanel.value.length; //保存当前文字长度
					this$1.mentionedInfo = JSON.parse(
						getPrefs(
							"mentionedInfo_" + this$1.conversationType + "_" + this$1.targetId
						) || "[]"
					); //回显@信息
					setTimeout(function() {
						this$1.inputIng();
					}, 0);
				}
			);
			sendEvent({
				name: "rongCloud",
				extra: {
					type: "getTextMessageDraft",
					param: {conversationType: this.conversationType, targetId: this.targetId}
				}
			});

			this.pageRefresh();
		};
		Root.prototype.collectRefresh = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.favoriteOkBox.show = true;
			}, 300);
			clearTimeout(this.data.favoriteOkBox.task);
			this.data.favoriteOkBox.task = setTimeout(function() {
				this$1.data.favoriteOkBox.show = false;
			}, 5000);
		};
		Root.prototype.cTitle = function() {
			if (this.originalName != G.headTitle) {
				alert(this.originalName);
			}
		};
		Root.prototype.getChatInfos = function(_list, _last) {
			var this$1 = this;

			_list.forEach(function(_item, _index) {
				_item.messageDirection =
					_item.senderUserId == chatHeader() + G.userId ? "SEND" : "RECEIVE";
				if (_last && _index != _list.length - 1) {
					return;
				}
				if (!_item.url) {
					_item.url = " ";
					_item.showName = " ";
					getBaseChatInfos(
						{
							targetId: _item["senderUserId"],
							conversationType: "PRIVATE",
							index: _index
						},
						function(ret) {
							if (!ret) {
								return;
							}
							_item.url = ret.url;
							_item.showName = ret.name;
							if (_item.objectName == "RC:RcNtf") {
								this$1.dealMsgBody(_item);
							}
						}
					);
				}
				this$1.dealMsgBody(_item);
				this$1.conversionTime(_item, _index, _list);
			});
		};
		Root.prototype.clickUnreadMentioned = function(_ajax) {
			var this$1 = this;

			if (!this.unreadMentionedItem && !this.data.unreadMentioned.length) {
				return;
			}
			var _item = this.unreadMentionedItem;
			if (!_item) {
				_item = this.data.unreadMentioned[this.data.unreadMentioned.length - 1];
				this.unreadMentionedItem = _item;
			}
			var nowDom = document.getElementById("item_" + _item.messageId); //这条消息的dom
			if (nowDom != null) {
				hideProgress();
				this.goButtom("item_" + _item.messageId, true);
				this.setAnimateFrequency(_item.messageId, 3);
				this.unreadMentionedItem = null;
				this.data.unreadMentioned.pop(); //删除最后一个
			} else {
				if (!_ajax) {
					showProgress("加载中");
				}
				var pageSize =
					this.data.listData[this.data.listData.length - 1].messageId -
					_item.messageId;
				setTimeout(function() {
					this$1.getData(0, {pageSize: (pageSize < 15 ? 15 : pageSize) + 2});
				}, 150);
			}
		};
		Root.prototype.setAnimateFrequency = function(_messageId, _frequency) {
			var this$1 = this;

			this.data.animateMessageId = _messageId;
			setTimeout(function() {
				this$1.data.animateMessageId = "";
				_frequency--;
				if (_frequency > 0) {
					setTimeout(function() {
						this$1.setAnimateFrequency(_messageId, _frequency);
					}, 400);
				}
			}, 300);
		};
		Root.prototype.noticeClose = function() {
			if (this.data.notice.key == "groupNotice") {
				setPrefs(
					"groupNotice_" + this.targetId + "_" + this.data.notice.data,
					"true"
				);
			}
			this.data.notice.show = false;
		};
		Root.prototype.getUnreadMentionedMessages = function() {
			sendEvent({
				name: "rongCloud",
				extra: {
					type: "getUnreadMentionedMessages",
					param: {conversationType: this.conversationType, targetId: this.targetId}
				}
			});
		};
		Root.prototype.pageRefresh = function() {
			var this$1 = this;

			getBaseChatInfos(
				{
					targetId: this.targetId,
					conversationType: this.conversationType,
					force: 1
				},
				function(_item, _ret) {
					if (!_item) {
						this$1.data.canChat = false;
						this$1.data.cantChatText = _ret.message || _ret.data || NET_ERR$1;
						return;
					}
					if (_item && !_item.id) {
						//有数据 但是没有id了
						sendEvent({
							name: "rongCloud",
							extra: {
								type: "clearMessagesUnreadStatus",
								param: {
									targetId: this$1.targetId,
									conversationType: this$1.conversationType
								}
							}
						});
						sendEvent({
							name: "rongCloud",
							extra: {
								type: "removeConversation",
								param: {
									targetId: this$1.targetId,
									conversationType: this$1.conversationType
								}
							}
						});
						alert(
							"该" +
								(this$1.conversationType == "GROUP" ? "群组" : "用户") +
								"已不存在",
							function() {
								this$1.close();
							}
						);
						return;
					}
					this$1.originalName = _item.name;
					var title = "";
					if (this$1.conversationType == "GROUP" && _item) {
						this$1.data.canChat = getItemForKey(G.userId, _item.memberUserIds);
						if (!this$1.data.canChat) {
							this$1.data.cantChatText = "无法在已退出的群聊中接收和发送消息";
							this$1.getData(0);
							return;
						}
						this$1.memberUserIds = _item.memberUserIds;
						title = _item.name + "(" + _item.memberUserIds.length + ")";
						title = showTextSize(
							title,
							Math.floor((G.pageWidth - 120) / (G.appFontSize + 5)),
							true
						);
						//扫码入群公告
						if (!this$1.isNotification) {
							this$1.isNotification = true;
							_item.joinType = this$1.data.pageParam.joinType;
						}
						dealGroupUser(_item, function(ret) {
							this$1.isOwner = ret.isOwner;
						});
						var callBoard = (_ret.data || {}).callBoard || "";
						if (
							callBoard &&
							getPrefs("groupNotice_" + this$1.targetId + "_" + callBoard) != "true"
						) {
							this$1.data.notice.key = "groupNotice";
							this$1.data.notice.title = "群公告";
							this$1.data.notice.data = callBoard;
							this$1.data.notice.show = true;
						}
					} else {
						title = showTextSize(
							_item.name,
							(G.pageWidth - 100) / (G.appFontSize + 5),
							true
						);
						this$1.data.canChat = true;
						ajax(
							{
								u:
									tomcatAddress() +
									"push/rongCloud?type=checkOnline&userId=" +
									this$1.targetId
							},
							"checkOnline",
							function(ret, err) {
								if (ret && ret.code == "200") {
									this$1.data.online.show = true;
									this$1.data.online.is = ret.status == 1;
								}
							},
							"是否在线"
						);
					}
					G.headTitle = title;
					this$1.update();
					this$1.getData(0);
				}
			);
		};
		Root.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		Root.prototype.getData = function(_type, _param) {
			_param = _param || {};
			var param = {
				conversationType: this.conversationType,
				targetId: this.targetId,
				count:
					_param.pageSize ||
					(!_type && (this.scrollFooter || 0) > 100 ? this.data.pageSize : 15),
				order: 0,
				type: _param.type,
				sentTime:
					_type && this.data.listData.length ? this.data.listData[0]["sentTime"] : ""
			};

			param[platform() == "app" ? "oldestMessageId" : "timestamp"] =
				_type && this.data.listData.length
					? this.data.listData[0][platform() == "app" ? "messageId" : "sentTime"]
					: platform() == "app"
					? -1
					: 0;
			sendEvent({
				name: "rongCloud",
				extra: {type: "getHistoryMessages", param: param}
			});
		};
		Root.prototype.openMore = function() {
			openWin("mo_chat_more", "../mo_chat_more/mo_chat_more.stml", {
				targetId: this.targetId,
				conversationType: this.conversationType
			});
		};
		Root.prototype.inputFocus = function() {
			this.goButtom();
		};
		Root.prototype.inputIng = function() {
			var this$1 = this;

			var inputText = this.data.chatPanel.value || "";
			var inputendLength = this.inputendLength || 0;
			var mentionedInfo = this.mentionedInfo || [];
			this.changeAite();
			console.log(
				"@信息：" + JSON.stringify(mentionedInfo) + ",当前：" + inputText
			);
			if (
				this.conversationType == "GROUP" &&
				inputText.length > inputendLength &&
				inputText.substring(inputText.length - 1, inputText.length) == "@"
			) {
				openWin_chat_user(
					{
						title: "选择提醒的人",
						id: this.targetId.split(chatHeader())[1],
						optionType: "aite",
						callback: "aite_" + this.conversationType + "_" + this.targetId
					},
					function(ret) {
						var data = ret.value;
						this$1.setAite(data, "");
					}
				);
			}
			this.inputendLength = inputText.length;
			setPrefs(
				"mentionedInfo_" + this.conversationType + "_" + this.targetId,
				JSON.stringify(mentionedInfo)
			);
			sendEvent({
				name: "rongCloud",
				extra: {
					type: inputText ? "saveTextMessageDraft" : "clearTextMessageDraft",
					param: {
						conversationType: this.conversationType,
						targetId: this.targetId,
						content: inputText
					}
				}
			});
		};
		Root.prototype.setAite = function(_item, _re) {
			var this$1 = this;

			if (!this.mentionedInfo) {
				this.mentionedInfo = [];
			}
			var showName = (_item.targetId ? "@" : "") + _item.name;
			this.mentionedInfo.push({id: _item.targetId, name: showName});
			this.data.chatPanel.value += showName.replace("@", _re) + " ";
			this.inputIng();
			setTimeout(function() {
				$("#" + this$1.data.chatPanel.id).focus();
				setTimeout(function() {
					this$1.inputFocus();
				}, 300);
			}, 300);
		};
		Root.prototype.changeAite = function() {
			var mentionedInfo = this.mentionedInfo || [];
			var _text = this.data.chatPanel.value || "";
			for (var i = 0; i < mentionedInfo.length; i++) {
				if (_text.indexOf(mentionedInfo[i].name) == -1) {
					mentionedInfo.splice(i, 1);
					this.changeAite();
					break;
				}
			}
		};
		Root.prototype.sendMsg = function() {
			this.sendTextMessage(this.data.chatPanel.value);
			//解决安卓上清空输入无效的问题
			if (!this.i) {
				this.data.chatPanel.value = "";
				this.i = 1;
			} else {
				this.data.chatPanel.value = " ".repeat(this.i++ % 2);
				this.data.chatPanel.value = " ".repeat(this.i++ % 2);
			}
			this.inputIng();
		};
		Root.prototype.goButtom = function(_view, _animated, _time, callback) {
			var this$1 = this;

			if (this.data.listData.length) {
				setTimeout(
					function() {
						this$1.data.scrollView.scroll_animation = _animated;
						this$1.data.scrollView.scroll_view = _view || "chat_bottom";
						callback && callback();
					},
					isParameters(_time) ? _time : 100
				);
			}
		};
		Root.prototype.pageScroll = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			if (this.upTask) {
				return;
			}
			getBoundingClientRect("chat_box", function(ret) {
				this$1.chatBoxH = ret.height;
				this$1.scrollHeight = detail.scrollHeight;
				this$1.scrollTop = detail.scrollTop;
				this$1.scrollFooter =
					this$1.scrollHeight - (this$1.chatBoxH + this$1.scrollTop);
				if (detail.scrollTop < 50) {
					this$1.upTask = true;
					setTimeout(function() {
						this$1.upTask = false;
					}, 1500);
					this$1.getData(1);
				}
			});
		};
		Root.prototype.additionalEnter = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			console.log(JSON.stringify(detail));
			var nowType = detail.data.type;
			switch (nowType) {
				case "image":
					this.selectPic("library");
					break;
				case "camera":
					this.selectPic("camera");
					break;
				case "location":
					if (!confirmPer("location", "chat", "用于聊天中发送位置消息")) {
						//地图权限
						addEventListener("locationPer_chat", function(ret, err) {
							if (ret.value.granted) {
								//允许了权限
								this$1.openLocation(null);
							}
						});
						return;
					}
					this.openLocation(null);
					break;
				case "files":
					var buttons = ["聊天文件", "我的云盘", "本机文件"];
					actionSheet(
						{
							title: "请选择",
							buttons: buttons
						},
						function(ret, err) {
							var _index = ret.buttonIndex;
							if (_index <= buttons.length) {
								switch (buttons[_index - 1]) {
									case "聊天文件":
										var chatGroupId = this$1.targetId.split(chatHeader())[1];
										if (this$1.conversationType == "PRIVATE") {
											chatGroupId =
												Number(G.userId) > Number(chatGroupId) ? G.userId : chatGroupId;
										}
										this$1.data.fileListPop = {
											key: "chat_file",
											id: chatGroupId,
											show: true,
											select: true,
											callback: function(ret) {
												this$1.fileCallback(ret.listSelect);
											}
										};

										break;
									case "我的云盘":
										this$1.data.fileListPop = {
											key: "cloud_file",
											show: true,
											select: true,
											callback: function(ret) {
												this$1.fileCallback(ret.listSelect);
											}
										};

										break;
									case "本机文件":
										this$1.data.fileListPop.key = "local_file";
										chooseFile({showToast: true}, function(ret) {
											if (ret.otherInfo) {
												this$1.addGroupFile(ret.otherInfo);
											} else {
												toast(ret.error);
											}
										});
										break;
								}
							}
						}
					);
					break;
				case "vote": //群投票
					openWin_vote({
						code: "30",
						businessType: "chatGroup",
						businessId: this.targetId.split(chatHeader())[1]
					});
					break;
				case "phone":
				case "video":
					if (
						!confirmPer(
							"microphone",
							"chat",
							"用于聊天中发起" + (nowType == "phone" ? "语音" : "视频") + "通话"
						)
					) {
						addEventListener("microphonePer_chat", function(ret, err) {
							if (ret.value.granted) {
								//允许了权限
								this$1.additionalEnter({detail: {data: {type: nowType}}});
							}
						});
						return;
					}
					if (
						nowType == "video" &&
						!confirmPer(
							"camera",
							"chat",
							"用于聊天中发起" + (nowType == "phone" ? "语音" : "视频") + "通话"
						)
					) {
						addEventListener("cameraPer_chat", function(ret, err) {
							if (ret.value.granted) {
								//允许了权限
								this$1.additionalEnter({detail: {data: {type: nowType}}});
							}
						});
						return;
					}
					this.startCall(nowType);
					break;
			}
		};
		Root.prototype.startCall = function(nowType) {
			var this$1 = this;

			if (this.conversationType == "PRIVATE") {
				api.require("zyRongCloudRTC").startSingleCall(
					{
						targetId: this.targetId,
						mediaType: nowType == "phone" ? "audio" : "video"
					},
					function(ret) {
						if (ret.status != "success") {
							toast(JSON.stringify(ret));
						}
					}
				);
			} else {
				openWin_chat_user(
					{
						title: "选择通话成员",
						id: this.targetId.split(chatHeader())[1],
						optionType: "call",
						callback: "call_" + this.conversationType + "_" + this.targetId
					},
					function(ret) {
						var userIds = ret.value.users;
						setPrefs(
							"rongGroupUser",
							JSON.stringify(
								this$1.memberUserIds.map(function(obj) {
									return chatHeader() + obj;
								})
							)
						);
						setRongChatInfo();
						api.require("zyRongCloudRTC").startMultiCall(
							{
								targetId: this$1.targetId,
								mediaType: nowType == "phone" ? "audio" : "video",
								userIdList: userIds.map(function(obj) {
									return chatHeader() + obj.targetId;
								})
							},
							function(ret) {
								if (ret.status != "success") {
									toast(JSON.stringify(ret));
								}
							}
						);
					}
				);
			}
		};
		Root.prototype.fileCallback = function(_list) {
			var this$1 = this;

			_list.forEach(function(_eItem) {
				ajax(
					{u: appUrl() + "file/info/" + _eItem.fileInfoId},
					"info" + _eItem.fileInfoId,
					function(ret, err) {
						if (ret && ret.code == 200 && ret.data) {
							this$1.addGroupFile(ret.data);
						}
					},
					"附件详情",
					"get"
				);
			});
		};
		Root.prototype.addGroupFile = function(_obj) {
			//TODO 这里发文件
			var filesType = "[文件]";
			this.sendRichContentMessage(filesType, filesType + "," + _obj.id);

			if (this.data.fileListPop.key == "chat_file") {
				return;
			}
			var chatGroupId = this.targetId.split(chatHeader())[1];
			if (this.conversationType == "PRIVATE") {
				chatGroupId =
					Number(G.userId) > Number(chatGroupId) ? G.userId : chatGroupId;
			}
			ajax(
				{u: appUrl() + "chatGroupFile/add", _this: this},
				"chatGroupFile/add" + _obj.id,
				function(ret, err) {},
				"添加聊天文件",
				"post",
				{
					body: JSON.stringify({form: {fileId: _obj.id, chatGroupId: chatGroupId}})
				}
			);
		};
		Root.prototype.selectPic = function(_sourceType) {
			var this$1 = this;

			getPicture(
				{
					sourceType: _sourceType,
					destinationType: platform() != "app" ? "base64" : "url"
				},
				function(ret, err) {
					var dataUrl = ret ? ret.data || ret.base64Data : "";
					if (dataUrl) {
						if (platform() == "app") {
							this$1.sendImageMessage(dataUrl);
						} else {
							showProgress("发送中");
							uploadFile({url: dataUrl}, function(ret) {
								hideProgress();
								if (ret.state == 2) {
									this$1.sendImageMessage(
										appUrl() + "image/" + ret.otherInfo.newFileName
									);
								} else {
									toast(ret.error);
								}
							});
						}
					}
				}
			);
		};
		Root.prototype.calculatingTime = function(_index, _list) {
			if (_index == 0 || !_list[_index - 1].sentTime) {
				return true;
			} else {
				var beginTime = Math.round(
					new Date(_list[_index - 1].sentTime).getTime() / 1000
				);
				var endTime = Math.round(new Date(_list[_index].sentTime).getTime() / 1000);
				return endTime - beginTime > 180; //当前时间减上一个时间 大于3分钟 就显示时间
			}
		};
		Root.prototype.conversionTime = function(_item, _index, _list) {
			var begin = new Date(_item.sentTime);
			var end = new Date();
			_item.showTime = this.calculatingTime(_index, _list)
				? getConversionTime(begin, end, 1)
				: "";
		};
		Root.prototype.msgImgTap = function(_item, _index) {
			console.log("我单击了头像：" + JSON.stringify(_item));
			openWin_npcinfo({
				id: _item.senderUserId.split(chatHeader())[1],
				reqType: "account"
			});
		};
		Root.prototype.msgImgPress = function(_item, _index) {
			console.log("我长按了头像：" + JSON.stringify(_item));
			this.setAite(
				{targetId: _item.senderUserId.split(chatHeader())[1], name: _item.showName},
				"@"
			);
		};
		Root.prototype.msgTap = function(_item, _index) {
			var this$1 = this;

			console.log("我单击了正文：" + JSON.stringify(_item));
			if (_item.objectName == "RC:ImgMsg") {
				var imgs = this.data.listData.filter(function(item, index) {
					return item.objectName == "RC:ImgMsg";
				});
				openWin_imgPreviewer({
					index: getItemForKey(_item.messageId, imgs, "messageId")._i,
					imgs: imgs.map(function(obj) {
						return obj.content.imageUrl;
					})
				});
			} else if (_item.objectName == "RC:VcMsg");
			else if (_item.objectName == "RC:LBSMsg") {
				this.openLocation(_item);
			} else if (_item.objectName == "RC:ImgTextMsg") {
				this.openMsgDetails(_item);
			} else if (
				_item.objectName == "RC:VSTMsg" ||
				_item.objectName == "RC:MACEMsg" ||
				_item.objectName == "RC:VCSummary"
			) {
				alert(
					{
						title: "提示",
						msg:
							"是否拨通" +
							(_item.content.mediaType == "video" ? "视频" : "语音") +
							"电话吗？",
						buttons: ["确定", "取消"]
					},
					function(ret) {
						if (ret.buttonIndex == 1) {
							this$1.startCall(_item.content.mediaType == "video" ? "video" : "phone");
						}
					}
				);
			}
		};
		Root.prototype.openLocation = function(_item) {
			var this$1 = this;

			this.data.mapChatPop = {
				show: true,
				item: _item,
				callback: function(ret) {
					this$1.sendLocationMessage(ret.lat, ret.lon, ret.poi);
				}
			};
		};
		Root.prototype.openMsgDetails = function(_item) {
			if (!_item.content.dealBody || !_item.content.dealBody.id) {
				return;
			}
			var description = _item.content.description || "";
			var descriptions = description.split(",");
			var url = _item.content.url || "";
			switch (descriptions[0]) {
				case "[文件]":
					openWin_filePreviewer({
						id: _item.content.dealBody.id,
						suffix: _item.content.dealBody.fileInfo.type,
						fileSource: "5"
					});
					return;
				case "[投票]":
					openWin_vote({id: _item.content.dealBody.id});
					return;
				case "[资讯]":
				case "[资料]":
				case "[履职技巧]":
					openWin_news({id: _item.content.dealBody.id});
					return;
				case "[驻站" + (G.sysSign == "rd" ? "代表" : "委员") + "]":
					openWin_workstation_members({id: _item.content.dealBody.id});
					break;
			}
		};
		Root.prototype.msgPress = function(_item, _index) {
			var this$1 = this;

			console.log("我长按了正文：" + _index + JSON.stringify(_item));
			var buttons = [];
			if (
				_item.objectName == "RC:TxtMsg" ||
				_item.objectName == "RC:ImgMsg" ||
				_item.objectName == "RC:ImgTextMsg"
			) {
				buttons.push({id: "forward", name: "转发", icon: ""});
			}
			if (_item.objectName == "RC:TxtMsg") {
				buttons.push({id: "collect", name: "收藏", icon: ""});
				buttons.push({id: "copy", name: "复制", icon: ""});
			}
			buttons.push({id: "delete", name: "删除", icon: ""});
			if (
				(_item.messageDirection == "SEND" || this.isOwner == 1) &&
				this.data.canChat
			) {
				buttons.push({id: "withdraw", name: "撤回", icon: ""});
			}
			actionSheet(
				{
					title: "请选择",
					buttons: buttons
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					if (_index <= buttons.length) {
						switch (ret.name) {
							case "转发":
								var param = {
									fType: {
										"RC:TxtMsg": "sendTextMessage",
										"RC:ImgMsg": "sendImageMessage",
										"RC:ImgTextMsg": "sendRichContentMessage"
									}[_item.objectName]
								};

								switch (param.fType) {
									case "sendRichContentMessage":
										param.fName = _item.content.title;
										param.description = _item.content.description;
										break;
									case "sendImageMessage":
										param.fImagePath = _item.content.imageUrl;
										break;
									case "sendTextMessage":
										param.fText = _item.content.text;
										break;
								}

								openWin_chat_share(param, function() {});
								break;
							case "收藏":
								G.favoritePop = {
									show: true,
									key: "long_add",
									data: {
										title: _item.content.text,
										code: "chatMsg",
										id: _item.targetId + "-" + _item.messageId
									}
								};

								break;
							case "复制":
								copyText(_item.content.text, function(ret, err) {
									toast(ret ? "复制成功" : "复制失败");
								});
								break;
							case "删除":
								alert(
									{
										title: "提示",
										msg:
											"是否确认删除本地消息？" +
											(_item.messageDirection == "SEND"
												? "（此操作只针对本人，要对方或群内成员都删除，请点击撤回消息。）"
												: ""),
										buttons: ["确定", "取消"]
									},
									function(ret) {
										if (ret.buttonIndex == 1) {
											var param = {
												conversationType: _item.conversationType,
												targetId: this$1.targetId,
												messages: [
													{
														messageUId: _item.messageUId,
														sentTime: _item.sentTime,
														messageDirection: _item.messageDirection
													}
												],

												messageIds: [_item.messageId]
											};

											sendEvent({
												name: "rongCloud",
												extra: {type: "deleteMessages", param: param}
											});
										}
									}
								);
								break;
							case "撤回":
								alert(
									{title: "提示", msg: "是否确认撤回消息？", buttons: ["确定", "取消"]},
									function(ret) {
										if (ret.buttonIndex == 1) {
											var param = {
												conversationType: _item.conversationType,
												targetId: this$1.targetId,
												messages: {
													messageUId: _item.messageUId,
													sentTime: _item.sentTime
												},

												messageId: _item.messageId
											};

											sendEvent({
												name: "rongCloud",
												extra: {type: "recallMessage", param: param}
											});
										}
									}
								);
								break;
						}
					}
				}
			);
		};
		Root.prototype.iTouchstart = function(_item, _index, _type) {
			var this$1 = this;

			if (platform() != "web") {
				return;
			}
			this.iTouchmove();
			this.itemTask = setTimeout(function() {
				if (_type == 1) {
					this$1.msgPress(_item, _index);
				} else if (_type == 2) {
					this$1.msgImgPress(_item, _index);
				}
				this$1.iTouchmove();
			}, 500);
		};
		Root.prototype.iTouchmove = function() {
			this.itemTask && clearTimeout(this.itemTask);
			this.itemTask = null;
		};
		Root.prototype.msgCallback = function(ret, err) {
			console.log("发送消息：" + JSON.stringify(ret) + JSON.stringify(err));
			var message = (ret.result || {}).message || {};
			var status = ret.status || "";
			var nItem = {};
			if (status == "prepare") {
				var newList = this.data.listData.concat([message]);
				this.getChatInfos(newList, true);
				this.data.listData = newList;
				this.goButtom(null, true);
			} else if (status == "success") {
				if (platform() == "web") {
					sendEvent({name: "chat_refresh"});
				} else {
					nItem = getItemForKey(message.messageId, this.data.listData, "messageId");
					nItem.sentStatus = "SENT";
				}
			} else if (status == "error") {
				if (platform() == "web") {
					sendEvent({name: "chat_refresh"});
				} else {
					nItem = getItemForKey(message.messageId, this.data.listData, "messageId");
					nItem.sentStatus = "FAILED";
				}
				toast("请联系管理员，错误代码：" + (err ? err.code : ret.result.errorCode));
			}
		};
		Root.prototype.getExtra = function() {
			return "";
		};
		Root.prototype.resend = function(_item, _index) {
			var this$1 = this;

			console.error(JSON.stringify(_item));
			alert(
				{title: "提示", msg: "是否重新发送该消息？", buttons: ["确定", "取消"]},
				function(ret) {
					if (ret.buttonIndex == 1) {
						sendEvent({
							name: "rongCloud",
							extra: {type: "deleteMessages", param: {messageIds: [_item.messageId]}}
						});
						if (_item.objectName == "RC:TxtMsg") {
							this$1.msgMentioned = null; //设置@信息
							var oldMentionedInfo = _item.content.mentionedInfo || [];
							if (oldMentionedInfo.length) {
								//重发有@信息
								this$1.msgMentioned = {
									type: oldMentionedInfo.type == 1 ? "all" : "part",
									userIdList: oldMentionedInfo.userIdList || []
								};
							}
							this$1.sendTextMessage(_item.content.text);
						} else if (_item.objectName == "RC:ImgMsg") {
							this$1.sendImageMessage(_item.content.imageUrl);
						} else if (_item.objectName == "RC:VcMsg") {
							this$1.sendVoiceMessage(_item.content.voicePath, _item.content.duration);
						} else if (_item.objectName == "RC:LBSMsg") {
							this$1.sendLocationMessage(
								_item.content.latitude,
								_item.content.longitude,
								_item.content.poi
							);
						} else if (_item.objectName == "RC:ImgTextMsg") {
							this$1.sendRichContentMessage(
								_item.content.title,
								_item.content.description
							);
						}
						this$1.data.listData.splice(_index, 1);
					}
				}
			);
		};
		Root.prototype.sendTextMessage = function(_text) {
			if (!_text) {
				toast("请输入内容");
				return;
			}
			this.msgMentioned = null; //设置@信息
			var mentionedInfo = this.mentionedInfo || [];
			if (mentionedInfo.length != 0) {
				this.msgMentioned = {type: "part", userIdList: []};
				for (var i = 0; i < mentionedInfo.length; i++) {
					var aiteId = mentionedInfo[i].id;
					this.msgMentioned.userIdList.push(chatHeader() + aiteId);
					if (!aiteId) {
						this.msgMentioned.type = "all";
						this.msgMentioned.userIdList = [];
						break;
					}
				}
			}
			var param = {
				conversationType: this.conversationType,
				targetId: this.targetId,
				text: _text,
				mentionedInfo: this.msgMentioned,
				extra: this.getExtra()
			};

			console.log("sendTextMessage:" + JSON.stringify(param));
			sendEvent({
				name: "rongCloud",
				extra: {type: "sendTextMessage", param: param}
			});
		};
		Root.prototype.sendImageMessage = function(_image) {
			var param = {
				conversationType: this.conversationType,
				targetId: this.targetId,
				imagePath: _image,
				isFull: true,
				extra: this.getExtra()
			};

			console.log("sendImageMessage:" + JSON.stringify(param));
			sendEvent({
				name: "rongCloud",
				extra: {type: "sendImageMessage", param: param}
			});
		};
		Root.prototype.sendLocationMessage = function(_lat, _lon, _poi) {
			var param = {
				conversationType: this.conversationType,
				targetId: this.targetId,
				latitude: _lat,
				longitude: _lon,
				poi: _poi,
				extra: this.getExtra()
			};

			console.log("sendLocationMessage:" + JSON.stringify(param));
			sendEvent({
				name: "rongCloud",
				extra: {type: "sendLocationMessage", param: param}
			});
		};
		Root.prototype.sendRichContentMessage = function(_title, _description) {
			var param = {
				conversationType: this.conversationType,
				targetId: this.targetId,
				title: _title,
				description: _description,
				imageUrl: "",
				extra: this.getExtra()
			};

			console.log("sendRichContentMessage:" + JSON.stringify(param));
			sendEvent({
				name: "rongCloud",
				extra: {type: "sendRichContentMessage", param: param}
			});
		};
		Root.prototype.dealMsgBody = function(_item) {
			switch (_item.objectName) {
				case "RC:TxtMsg":
					this.dealEmoticons(_item, "text");
					break;
				case "RC:ImgTextMsg":
					this.dealRichContent(_item);
					break;
				case "RC:CmdNtf":
					this.dealCmdNtf(_item);
					break;
				case "RC:RcNtf":
					_item.rcNtf =
						_item.senderUserId != chatHeader() + G.userId
							? _item.conversationType == "GROUP"
								? _item.showName + "撤回了一条消息"
								: "对方撤回了一条消息"
							: "你撤回了一条消息";
					break;
				case "RC:VSTMsg": //安卓语音视频
				case "RC:MACEMsg":
				case "RC:VCSummary": //苹果语音视频
					_item.content.conversionText = (
						(_item.content.reasonText || "出现错误") + (_item.content.extra || "")
					).split("");
					break;
			}
		};
		Root.prototype.dealCmdNtf = function(_item) {
			var nData = _item.content.data || _item.content.name || "";
			var showTexts = [];
			if (nData) {
				nData.split("||").forEach(function(_nItem) {
					if (!_nItem) {
						return;
					}
					var item = {key: ""};
					if (_nItem.indexOf("|") == -1) {
						item.text = _nItem;
					} else {
						var cmdMore = _nItem.split("|");
						if (cmdMore[1] == "OUI" && cmdMore[2] == G.uId) {
							//当前人
							item.text = "你";
							item.key = "";
						} else {
							item.text = cmdMore[0];
							item.key = cmdMore[1];
						}
						item.more = _nItem;
					}
					showTexts.push(item);
				});
			}
			_item.content.conversionText = showTexts;
		};
		Root.prototype.dealRichContent = function(_item) {
			var description = (_item.content.description || "").replace(/\\+"/gm, '"');
			var descriptionKey = description.split(",")[0];
			_item.content.dealwith = "";
			var dealId =
				description.substring(
					description.indexOf(",") + 1,
					description.indexOf(",") + 2
				) == "{"
					? JSON.parse(description.substring(description.indexOf(",") + 1)).id
					: description.split(",")[1];
			var code = "",
				param = {};
			switch (descriptionKey) {
				case "[文件]":
					code = "file";
					param.id = dealId;
					break;
				case "[投票]":
					code = "30";
					param.detailId = dealId;
					_item.content.dealSystem = true;
					_item.content.titleIcon = "toupiao";
					break;
				case "[资讯]":
				case "[资料]":
				case "[履职技巧]":
					code = "5";
					param.detailId = dealId;
					_item.content.dealSystem = true;
					break;
				case "[驻站" + (G.sysSign == "rd" ? "代表" : "委员") + "]":
					code = "50_member";
					param.detailId = dealId;
					_item.content.dealSystem = true;
					break;
			}

			var cacheInfo = getPrefs(code + "_" + dealId);
			if (cacheInfo) {
				_item.content.dealBody = JSON.parse(cacheInfo);
				_item.content.dealwith = descriptionKey;
				return;
			}
			this.getModuleDetails(
				{code: code, tag: "info_" + dealId + "_" + _item.sentTime, param: param},
				function(ret, err) {
					if (ret && ret.dealWith) {
						var _data = ret.dealWith;
						_item.content.dealBody = _data;
						_item.content.dealwith = descriptionKey;
						setPrefs(code + "_" + dealId, JSON.stringify(_data));
					} else {
						_item.content.dealSystem = true;
						_item.content.dealBody = {title: "数据加载失败"};
						_item.content.dealwith = "error";
						console.log(JSON.stringify(_item));
					}
				}
			);
		};
		Root.prototype.getModuleDetails = function(_param, _callback) {
			switch (_param.code) {
				case "file":
					getDetailsFile(_param, _callback);
					break;
				case "30":
					getDetails30(_param, _callback);
					break;
				case "5":
					getDetails5(_param, _callback);
					break;
				case "50_member":
					getDetails50(_param, function(ret, err) {
						if (ret && ret.dealWith) {
							ret.dealWith.title =
								ret.dealWith.title + "驻站" + (G.sysSign == "rd" ? "代表" : "委员");
							ret.dealWith.name = "驻站" + (G.sysSign == "rd" ? "代表" : "委员");
							_callback(ret, err);
						}
					});
					break;
			}
		};
		Root.prototype.dealEmoticons = function(_item, _key) {
			var _text = _item.content[_key] || "";
			_text = decodeCharacter(_text); //先转义内容
			var emoReplace = "犇",
				emojiReplace = "骉",
				enterReplace = "猋";
			var emotionList = [],
				emojiList = [];
			_text = _text.replace(/\[(.*?)\]/gm, function(match) {
				var imgSrc = "";
				for (var i = 0; i < emotion.length; i++) {
					if (emotion[i].text == match) {
						imgSrc =
							shareAddress(1) + "image/chat_box/emotion/" + emotion[i].name + ".png";
						break;
					}
				}
				if (!imgSrc) {
					return match;
				}
				emotionList.push({text: match, url: imgSrc});
				return emoReplace;
			});
			_text = _text.replace(
				/(?:[\xA9\xAE\u2122\u23E9-\u23EF\u23F3\u23F8-\u23FA\u24C2\u25B6\u2600-\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDE51\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F\uDE80-\uDEFF]|\uD83E[\uDD00-\uDDFF])/g,
				function(match) {
					emojiList.push(match);
					return emojiReplace;
				}
			);
			_text = _text.replace(/\n/gm, enterReplace);
			var showTexts = [];
			for (var i = 0; i < _text.length; i++) {
				var item = {type: "text", text: null},
					_eItem = _text[i];
				if (_eItem == emoReplace) {
					item.type = "img";
					var nowImg = emotionList.shift();
					item.url = nowImg.url;
					item.key = nowImg.text;
				} else if (_eItem == emojiReplace) {
					var nowText = emojiList.shift();
					item.text = nowText;
				} else if (_eItem == enterReplace) {
					item.type = "br";
				} else {
					item.text = _eItem;
				}
				showTexts.push(item);
			}
			_item.content.conversionText = showTexts;
		};
		Root.prototype.openCmdNtf = function(_item) {
			console.log(JSON.stringify(_item));
			if (!_item.key) {
				return;
			}
			var cmdMore = _item.more.split("|");
			switch (_item.key) {
				case "OUI": //openUserInfo
					openWin_npcinfo({id: cmdMore[2], reqType: "account"});
					break;
				case "details30":
					openWin_vote({id: cmdMore[2]});
					break;
			}
		};
		Root.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{
					_this: this,
					dataMore: this.props.dataMore,
					more: true,
					onCTitle: this.cTitle
				},
				apivm.h("view", null),
				apivm.h(
					"view",
					{style: "height:100%;flex-direction:row-reverse;"},
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.openMore();
							},
							class: "header_btn",
							style: {display: this.data.canChat ? "flex" : "none"}
						},
						apivm.h("a-iconfont", {
							name: "gengduo",
							color: G.headColor,
							size: G.appFontSize + 4
						})
					),
					apivm.h(
						"view",
						{
							class: "header_btn",
							style: {
								display: this.data.canChat && this.data.online.show ? "flex" : "none"
							}
						},
						apivm.h("view", {
							style:
								"width:10px;height:10px;border-radius:50%;background:#" +
								(this.data.online.is ? "18ff00" : "ccc") +
								";"
						})
					)
				),
				apivm.h("view", null),
				apivm.h(
					"view",
					{class: "flex_h"},

					apivm.h(
						"view",
						{
							style:
								"display:" +
								(this.data.notice.show ? "flex" : "none") +
								";position: absolute;left: 0;top: 0;right: 0;z-index:1;"
						},
						this.data.notice.show &&
							apivm.h(
								"view",
								{style: "padding:10px 16px;"},
								apivm.h(
									"view",
									{
										style:
											"padding:10px;background: #FFFFFF;box-shadow: 0px 2px 10px 1px rgba(24,64,118,0.08);border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px;"
									},
									apivm.h(
										"view",
										{style: "flex-direction:row; align-items: center;"},
										apivm.h("a-iconfont", {
											name: "gonggao",
											color: G.appTheme,
											size: G.appFontSize
										}),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-2) +
													"font-weight: 600;color: #333333;flex:1;margin:0 6px;"
											},
											this.data.notice.title
										),
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.noticeClose();
												}
											},
											apivm.h("a-iconfont", {
												name: "qingkong",
												color: "#AAADAF",
												size: G.appFontSize + 5
											})
										)
									),
									apivm.h(
										"scroll-view",
										{style: "margin:10px 0 5px;max-height: 300px;", "scroll-y": true},
										apivm.h("z-rich-text", {expand: 60, nodes: this.data.notice.data})
									)
								)
							)
					),

					apivm.h(
						"view",
						{
							style:
								"display:" +
								(this.data.unreadMentioned.length != 0 ? "flex" : "none") +
								";",
							class: "aiteBody"
						},
						this.data.unreadMentioned.length != 0 &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.clickUnreadMentioned();
									},
									style: "flex-direction:row; align-items: center;"
								},
								apivm.h(
									"view",
									{style: loadConfigurationSize(20)},
									apivm.h("z-avatar", {
										src: showImg(
											this.data.unreadMentioned[this.data.unreadMentioned.length - 1]
										)
									})
								),
								", ",
								apivm.h(
									"text",
									{style: loadConfiguration(-1) + "color:#FFF;margin:0 10px;"},
									this.data.unreadMentioned[this.data.unreadMentioned.length - 1]
										.showName,
									"@我"
								)
							)
					),
					apivm.h(
						"y-scroll-view",
						{
							id: "chat_box",
							_this: this,
							dataMore: this.data.scrollView,
							style: "background:rgba(0,0,0,0.05)"
						},
						apivm.h(
							"view",
							null,
							this.data.listData.map(function(item, index) {
								return [
									apivm.h(
										"view",
										{id: "item_" + item.messageId},
										apivm.h(
											"view",
											null,
											item.objectName == "MC:RcNtf" &&
												apivm.h(
													"view",
													{style: "margin:16px 0;", class: "flex_row"},
													apivm.h("text", {style: loadConfiguration(-4)}, item.content)
												)
										),
										apivm.h(
											"view",
											null,
											item.objectName != "MC:RcNtf" &&
												apivm.h(
													"view",
													null,

													apivm.h(
														"view",
														null,
														item.showTime &&
															apivm.h(
																"text",
																{class: "chat_hint", style: loadConfiguration(-2)},
																item.showTime
															)
													),

													apivm.h(
														"view",
														null,
														item.objectName == "RC:RcNtf" &&
															apivm.h(
																"text",
																{class: "chat_hint", style: loadConfiguration(-4)},
																item.rcNtf
															)
													),

													apivm.h(
														"view",
														null,
														item.objectName == "RC:CmdNtf" &&
															apivm.h(
																"view",
																{
																	style:
																		"flex-direction:row;flex-wrap: wrap;padding: 15px;justify-content: center;"
																},
																(item.content.conversionText || []).map(function(nItem) {
																	return nItem.text.split("").map(function(uItem) {
																		return apivm.h(
																			"text",
																			{
																				onClick: function() {
																					return this$1.openCmdNtf(nItem);
																				},
																				style:
																					loadConfiguration() +
																					"color:" +
																					(nItem.key ? G.appTheme : "#999") +
																					";"
																			},
																			platform() != "app" && uItem == " " ? " " : uItem
																		);
																	});
																})
															)
													),
													apivm.h(
														"view",
														null,
														item.objectName != "RC:RcNtf" &&
															item.objectName != "RC:CmdNtf" &&
															apivm.h(
																"view",
																{
																	class: "chat_msg_box",
																	style:
																		"flex-direction:row" +
																		(item.messageDirection == "SEND" ? "-reverse" : "") +
																		";background: " +
																		(this$1.data.animateMessageId == item.messageId
																			? "rgba(0,0,0,0.1)"
																			: "transparent") +
																		";"
																},
																apivm.h(
																	"view",
																	{
																		style: loadConfigurationSize(20),
																		onClick: function() {
																			return this$1.msgImgTap(item, index);
																		},
																		onLongpress: function() {
																			return this$1.msgImgPress(item, index);
																		},
																		onTouchStart: function() {
																			return this$1.iTouchstart(item, index, 2);
																		},
																		onTouchMove: this$1.iTouchmove,
																		onTouchEnd: this$1.iTouchmove
																	},
																	apivm.h("z-avatar", {src: showImg(item)})
																),
																apivm.h(
																	"view",
																	{
																		class: "flex_w",
																		style:
																			"margin-left:" +
																			(item.messageDirection == "SEND" ? "0" : "14") +
																			"px;margin-right: " +
																			(item.messageDirection == "SEND" ? "14" : "0") +
																			"px;"
																	},
																	apivm.h(
																		"view",
																		null,
																		item.messageDirection != "SEND" &&
																			apivm.h(
																				"view",
																				{
																					style:
																						"flex-direction:row" +
																						(item.messageDirection == "SEND" ? "-reverse" : "") +
																						";"
																				},
																				apivm.h(
																					"text",
																					{class: "chat_msg_name", style: loadConfiguration(-4)},
																					item.showName
																				)
																			)
																	),
																	apivm.h(
																		"view",
																		{
																			class: "chat_msg_content",
																			style:
																				"flex-direction:row" +
																				(item.messageDirection == "SEND" ? "-reverse" : "") +
																				";"
																		},
																		apivm.h(
																			"view",
																			{
																				onClick: function() {
																					return this$1.msgTap(item, index);
																				},
																				onLongpress: function() {
																					return this$1.msgPress(item, index);
																				},
																				onTouchStart: function() {
																					return this$1.iTouchstart(item, index, 1);
																				},
																				onTouchMove: this$1.iTouchmove,
																				onTouchEnd: this$1.iTouchmove,
																				class: "chat_msg_content_p",
																				style:
																					"width:" +
																					(item.objectName == "RC:ImgTextMsg" ? "80%" : "auto") +
																					";background:" +
																					(item.objectName == "RC:ImgMsg" ||
																					item.objectName == "RC:ImgTextMsg"
																						? "transparent"
																						: item.messageDirection == "SEND"
																						? G.appTheme
																						: "#FFF") +
																					";padding:" +
																					(item.objectName == "RC:ImgMsg" ||
																					item.objectName == "RC:ImgTextMsg"
																						? "0"
																						: "12px 13px") +
																					";"
																			},
																			apivm.h(
																				"view",
																				null,
																				item.objectName == "RC:TxtMsg" &&
																					isArray(item.content.conversionText) &&
																					apivm.h(
																						"view",
																						{style: "flex-direction:row;flex-wrap: wrap;"},
																						(Array.isArray(item.content.conversionText)
																							? item.content.conversionText
																							: Object.values(item.content.conversionText)
																						).map(function(_tItem) {
																							return apivm.h(
																								"view",
																								{
																									style:
																										"width:" +
																										(_tItem.type == "br" ? "100%" : "auto") +
																										";"
																								},
																								_tItem.type == "img"
																									? apivm.h(
																											"view",
																											{
																												class: "xy_center",
																												style: "" + loadConfigurationSize(5)
																											},
																											apivm.h("image", {
																												style:
																													"width:100%;max-width: 100%; max-height: 100%;",
																												mode: "aspectFill",
																												thumbnail: "false",
																												src: showImg(_tItem)
																											})
																									  )
																									: apivm.h(
																											"text",
																											{
																												style:
																													loadConfiguration() +
																													"color:" +
																													(item.messageDirection == "SEND"
																														? "#FFF"
																														: "#333") +
																													";"
																											},
																											_tItem.text
																									  )
																							);
																						})
																					)
																			),
																			apivm.h(
																				"view",
																				null,
																				(item.objectName == "RC:VSTMsg" ||
																					item.objectName == "RC:MACEMsg" ||
																					item.objectName == "RC:VCSummary") &&
																					isArray(item.content.conversionText) &&
																					apivm.h(
																						"view",
																						{style: "flex-direction:row;flex-wrap: wrap;"},
																						apivm.h(
																							"view",
																							{
																								style: loadConfigurationSize(4) + "margin-right:6px;",
																								class: "xy_center"
																							},
																							apivm.h("a-iconfont", {
																								name:
																									item.content.mediaType == "video"
																										? "shipintianchong"
																										: "dianhua",
																								color:
																									item.messageDirection == "SEND" ? "#FFF" : "#333",
																								size: G.appFontSize
																							})
																						),
																						(Array.isArray(item.content.conversionText)
																							? item.content.conversionText
																							: Object.values(item.content.conversionText)
																						).map(function(_tItem) {
																							return apivm.h(
																								"view",
																								null,
																								apivm.h(
																									"text",
																									{
																										style:
																											loadConfiguration() +
																											"color:" +
																											(item.messageDirection == "SEND" ? "#FFF" : "#333") +
																											";"
																									},
																									_tItem
																								)
																							);
																						})
																					)
																			),
																			apivm.h(
																				"view",
																				null,
																				item.objectName == "RC:ImgMsg" &&
																					apivm.h("image", {
																						style: "height:auto;",
																						src: showImg(item.content.imageUrl),
																						thumbnail: false
																					})
																			),
																			apivm.h(
																				"view",
																				null,
																				item.objectName == "RC:LBSMsg" &&
																					apivm.h(
																						"view",
																						null,
																						apivm.h(
																							"text",
																							{
																								style:
																									loadConfiguration(1) +
																									"color: " +
																									(item.messageDirection == "SEND" ? "#FFF" : "#333") +
																									";word-break: break-all;"
																							},
																							item.content.poi
																						),
																						apivm.h("image", {
																							style: "width:100%;height:120px;margin-top:10px;",
																							src:
																								"https://restapi.amap.com/v3/staticmap?location=" +
																								item.content.longitude +
																								"," +
																								item.content.latitude +
																								"&zoom=13&size=300*300&markers=mid,0xff0000,:" +
																								item.content.longitude +
																								"," +
																								item.content.latitude +
																								"&key=" +
																								getPrefs("sys_appMapWebKey"),
																							mode: "aspectFill",
																							thumbnail: "false"
																						})
																					)
																			),
																			apivm.h(
																				"view",
																				null,
																				item.objectName == "RC:ImgTextMsg" &&
																					item.content.dealwith &&
																					apivm.h(
																						"view",
																						null,
																						apivm.h(
																							"view",
																							null,
																							item.content.dealwith == "[文件]" &&
																								apivm.h(
																									"view",
																									{
																										style:
																											"background:#FFF;padding:10px 20px 10px 12px;flex-direction:row; align-items: center;"
																									},
																									apivm.h(
																										"view",
																										{style: "flex:1;"},
																										apivm.h(
																											"text",
																											{
																												class: "text_two",
																												style:
																													loadConfiguration(1) +
																													"color: #333;word-break: break-all;height:" +
																													G.appFontSize * 1.4 * 2 +
																													"px;"
																											},
																											item.content.dealBody.originalFileName
																										),
																										apivm.h(
																											"text",
																											{
																												style:
																													loadConfiguration(-4) +
																													"color: #666;margin-top:4px;"
																											},
																											item.content.dealBody.dealFileSize
																										)
																									),
																									apivm.h(
																										"view",
																										{style: "height:auto;margin-left:15px;"},
																										apivm.h("a-iconfont", {
																											name: item.content.dealBody.fileInfo.name,
																											color: item.content.dealBody.fileInfo.color,
																											size: G.appFontSize + 16
																										})
																									)
																								)
																						),
																						apivm.h(
																							"view",
																							null,
																							item.content.dealSystem && [
																								apivm.h(
																									"view",
																									{
																										style:
																											"background:#FFF;padding:10px 12px;border-radius: 5px;overflow: hidden;"
																									},
																									apivm.h(
																										"view",
																										{
																											style:
																												"flex-direction:row;align-items: center;flex-wrap: wrap;"
																										},
																										item.content.titleIcon &&
																											apivm.h("a-iconfont", {
																												style: "margin-right:6px;",
																												name: item.content.titleIcon,
																												color: "#F6931C",
																												size: G.appFontSize
																											}),
																										(item.content.dealBody.title || "")
																											.split("")
																											.map(function(nItem, nIndex) {
																												return (
																													nIndex <
																														Math.floor(
																															((G.pageWidth - 130) * 0.8) / (G.appFontSize + 1)
																														) *
																															2 &&
																													apivm.h(
																														"text",
																														{
																															style:
																																loadConfiguration(1) +
																																"margin:2px 0;font-weight: 600;color: #333;"
																														},
																														nIndex !=
																															Math.floor(
																																((G.pageWidth - 130) * 0.8) /
																																	(G.appFontSize + 1)
																															) *
																																2 -
																																1
																															? nItem
																															: "..."
																													)
																												);
																											})
																									),
																									apivm.h(
																										"view",
																										null,
																										item.content.dealBody.url &&
																											apivm.h(
																												"view",
																												{
																													style:
																														"margin-top:10px;width:100%;height:" +
																														((G.pageWidth - 70) * 1) / 3 +
																														"px;"
																												},
																												apivm.h("image", {
																													class: "xy_100",
																													src: showImg(item.content.dealBody),
																													mode: "aspectFill",
																													thumbnail: "false"
																												})
																											)
																									)
																								),
																								item.content.dealBody.name &&
																									apivm.h(
																										"z-tag",
																										{
																											style: "margin-top:5px;",
																											size: 0,
																											type: "3",
																											color: "#ccc",
																											roundSize: "5"
																										},
																										item.content.dealBody.name
																									)
																							]
																						)
																					)
																			)
																		),
																		apivm.h(
																			"view",
																			null,
																			item.sentStatus == "FAILED" &&
																				apivm.h(
																					"view",
																					{
																						onClick: function() {
																							return this$1.resend(item, index);
																						},
																						style: "padding:10px 10px;"
																					},
																					apivm.h("a-iconfont", {
																						name: "gantanhao-xianxingyuankuang",
																						color: "red",
																						size: G.appFontSize + 10
																					})
																				)
																		),
																		apivm.h("view", {style: "flex:1;"})
																	)
																)
															)
													)
												)
										)
									)
								];
							})
						),
						apivm.h("view", {id: "chat_bottom"})
					),
					apivm.h(
						"view",
						null,
						this.data.canChat &&
							apivm.h("y-chatbox", {
								dataMore: this.data.chatPanel,
								bottom: this.data.pageType == "page",
								onAdditional: this.additionalEnter,
								onConfirm: this.sendMsg,
								onFocus: this.inputFocus,
								onInput: this.inputIng
							})
					),
					apivm.h(
						"view",
						null,
						!this.data.canChat &&
							this.data.cantChatText &&
							apivm.h(
								"view",
								{
									style:
										"background:rgba(0,0,0,0.35);padding:19px 19px " +
										(footerBottom(this.data.pageType == "page") || 19) +
										"px;flex-direction:row; align-items: center;justify-content: center;"
								},
								apivm.h("a-iconfont", {
									name: "gantanhao-xianxingyuankuang",
									color: "#FFF",
									size: G.appFontSize + 4
								}),
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-2) +
											"color:#FFF;text-align: center;margin-left:10px;"
									},
									this.data.cantChatText
								)
							)
					)
				),

				apivm.h("file-list", {dataMore: this.data.fileListPop}),

				apivm.h("map-chat", {dataMore: this.data.mapChatPop}),

				apivm.h("collect-add", {
					dataMore: G.favoritePop,
					onRefresh: this.collectRefresh
				}),

				apivm.h("collect-ok", {dataMore: this.data.favoriteOkBox})
			);
		};

		return Root;
	})(Component);
	Root.css = {
		".aiteBody": {
			position: "absolute",
			zIndex: "2",
			background: "rgba(0,0,0,0.35)",
			right: "0",
			top: "50px",
			padding: "5px",
			borderTopLeftRadius: "25px",
			borderBottomLeftRadius: "25px"
		},
		".text_two": {
			WebkitLineClamp: "2",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden",
			wordWrap: "break-word",
			textOverflow: "ellipsis",
			whiteSpace: "normal !important"
		},
		".chat_hint": {
			textAlign: "center",
			padding: "15px",
			color: "#999999",
			fontWeight: "bold"
		},
		".chat_msg_box": {
			width: "100%",
			height: "auto",
			padding: "10px 15px",
			flexDirection: "row",
			alignItems: "flex-start"
		},
		".chat_msg_name": {color: "#333", fontWeight: "600"},
		".chat_msg_content": {
			marginTop: "8px",
			height: "auto",
			minHeight: "40px",
			position: "relative",
			justifyContent: "flex-start"
		},
		".chat_msg_content_p": {
			height: "auto",
			borderRadius: "5px",
			overflow: "hidden",
			maxWidth: "80%"
		}
	};
	apivm.define("root", Root);
	apivm.render(apivm.h("root", null), "body");
})();
