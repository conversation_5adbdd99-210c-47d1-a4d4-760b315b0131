<html>
<script> var _avm_root_config={"appName":"product5","rootPage":"index.stml"}</script><head><meta charset="utf-8"><meta name="referrer" content="no-referrer"><meta name="viewport" content="maximum-scale=1,minimum-scale=1,user-scalable=0,initial-scale=1,width=device-width"><meta name="format-detection" content="telephone=no,email=no,date=no,address=no"><style>*{margin:0;padding:0}body,html{height:100%;background:0 0;display:-webkit-flex;flex-direction:column;justify-content:flex-start;align-items:stretch;flex-wrap:nowrap;align-content:stretch;-webkit-touch-callout:none;-webkit-text-size-adjust:none;-webkit-tap-highlight-color:transparent;-webkit-user-select:none;overflow:hidden}cell,div,label,li,list-footer,list-header,swiper-item,ul,view{display:-webkit-flex;flex-direction:column;justify-content:flex-start;align-items:stretch;flex-wrap:nowrap;align-content:stretch;position:relative;box-sizing:border-box}label{flex-direction:row}li,ul{padding:0;margin:0;list-style:none}img{border:none}button{outline:0;border:1px solid #dfdfdf;border-radius:4px;padding:6px 20px;color:#000;text-align:center;background:#f8f8f8}input{outline:0;border:1px solid #dfdfdf;background:#fff;width:160px;height:31px}textarea{border:1px solid #dfdfdf;background:#fff;width:160px;height:40px}progress{width:160px;height:6px}checkbox{color:#006bff;width:25px;height:25px}radio{color:#006bff;width:25px;height:25px}slider{width:160px;height:31px}switch{width:51px;height:31px}dialog{border:1px solid #ccc;background-color:#fff;padding:0}dialog::backdrop{background-color:rgba(0,0,0,.6)}</style><style>.avm-hide{display:none!important}.avm-iframe{position:absolute;height:100%;width:100%;background-color:#fff}.avm-iframe-frame{background-color:transparent;z-index:9999}.avm-win-tabbar{height:100%;flex:1;-webkit-flex:1}.avm-win-tabbar__page{flex:1;-webkit-flex:1;position:relative}.avm-win-tabbar__footer{display:flex;display:-webkit-flex;flex-direction:row;-webkit-flex-direction:row;border-top:1px solid #eee}.avm-win-tabbar__item{flex:1;-webkit-flex:1;display:inline-block;text-align:center;padding:8px 0 4px}.avm-win-tabbar__img-wrap{width:28px;height:28px;margin-bottom:2px;position:relative;display:inline-block}.avm-win-tabbar__img,.avm-win-tabbar__selectimg{width:100%;height:100%}.avm-win-tabbar__selectimg{visibility:hidden;position:absolute}.avm-win-tabbar__item-active .avm-win-tabbar__img{visibility:hidden}.avm-win-tabbar__item-active .avm-win-tabbar__selectimg{visibility:visible;top:0;left:0}.avm-win-tabbar__text{color:#999;font-size:10px;line-height:1.4}.avm-win-tabbar__item-active>.avm-win-tabbar__text{color:#333}.avm-navigation-bar{height:44px;position:relative}.avm-navigation-bar__left{position:absolute;left:16px;display:flex;display:-webkit-flex;-webkit-flex-direction:row;flex-direction:row;align-items:center;-webkit-align-items:center;height:100%}.avm-navigation-bar__back{width:44px;height:44px;background-size:16px;background-repeat:no-repeat;background-position:0 center;background-image:url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E")}.avm-navigation-bar__title{font-size:inherit;line-height:44px;width:70%;margin:auto;white-space:nowrap;text-overflow:ellipsis;text-align:center;overflow:hidden}body::-webkit-scrollbar,div::-webkit-scrollbar,html::-webkit-scrollbar{width:3px;height:3px}body::-webkit-scrollbar-thumb,div::-webkit-scrollbar-thumb,html::-webkit-scrollbar-thumb{background:rgba(174,174,174,.5)}.avm-confirm-mask,.avm-toast{z-index:999}input::placeholder,textarea::placeholder{color:#999}</style></head><body></body><script src="../../script/avm.min.js"></script><script src="../../script/jweixin-1.6.2.js"></script><script src="../../script/xfyun.js"></script><script>!function(){var m=_avm_root_config.tabBar;if(window===window.top)window.history.state||window.history.replaceState({ct:Date.now(),$routerArr:[]},null),setTimeout(function(){window._T.api=window._T.apiweb,d($route)},350),apiready=function(){window.trans=api.require("trans")},window.addEventListener("popstate",function(e){if($route=apiweb._getRoute(),m&&"root"==$route.name){var t=document.getElementsByClassName("avm-win-tabbar")[0];if(t){for(;t.nextElementSibling&&t.nextElementSibling.name;)s(t.nextElementSibling.name);var o=top.document.getElementsByClassName("avm-win-tabbar__item-active")[0];if(o){var a=o.dataset.index;if(-1!=a){var n=top.document.querySelector(".avm-win-tabbar__page__"+a);(w=n.contentWindow.$document.$rootvm).onShow&&w.onShow()}}}else _avmState.ct>e.state.ct&&s((i=window.frames[window.frames.length-1]).name),d($route);window._avmState=e.state}else if(window.frames[$route.name]){for(var r=window.frames.length-1;0<=r&&window.frames[r]!==window.frames[$route.name];r--)s(window.frames[r].name);window._avmState=e.state,window.frames[$route.name].$document.$rootvm.onShow&&window.frames[$route.name].$document.$rootvm.onShow()}else{var i=window.frames[window.frames.length-1];_avmState.ct>e.state.ct&&s(i.name),d($route)}});else{window._T.api=window._T.apiweb;var e=top._avmState.$routerArr;if(1<e.length){var w={},t=e[e.length-2];if("root"==t&&m){var o=top.document.getElementsByClassName("avm-win-tabbar__item-active")[0];if(o){var a=o.dataset.index;if(-1!=a){var n=top.document.querySelector(".avm-win-tabbar__page__"+a);w=n.contentWindow.$document.$rootvm}}}else top.frames[t]&&(w=top.frames[t].$document.$rootvm);w.onHide&&w.onHide()}if($route.navigationBar&&apiweb._setNavigationBar({title:$route.title,navigationBar:$route.navigationBar}),-1==$route.url.split("/").pop().indexOf("."))var r=$route.url;else r=$route.url.substr(0,$route.url.lastIndexOf("."));var i="<script src='"+("index"==r||0==r.indexOf("..")?r:"../"+r+"/"+r)+".js?v="+apiweb.getPrefs({sync:!0,key:"sys_appVersion"})+"'><\/script>";document.write(i),window.onload=function(){if(apiweb$__vm.$_start(),$document.$rootvm.onShow&&$document.$rootvm.onShow(),$route.navigationBar){var e=$document.$rootvm.base;e.style["overflow-y"]="scroll",e.style.flex=1,e.style["-webkit-flex"]=1}}}function s(e){document.body.removeChild(document.querySelector("iframe[name="+e+"]"))}function d(e){var t=JSON.parse(JSON.stringify(history.state)),o=t.$routerArr.length,a=t.$routerArr[o-1];if(a&&a===$route.name||(t.$routerArr.push($route.name),history.replaceState(t,null)),window._avmState=t,"root"==e.name){if(m)return void apiweb._loadTabbar();_avm_root_config.rootPage&&(e.url=_avm_root_config.rootPage)}apiweb._insertIframe(e,window.parent.window.document.body)}}()</script></html>