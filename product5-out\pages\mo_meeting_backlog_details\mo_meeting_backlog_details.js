(function() {
	var myjs = {};

	//修改时 需同步修改_zy/myjs.js
	myjs.iszx = false;

	myjs.proxy = false;

	myjs.appName = (myjs.iszx ? "政协" : "人大") + "平台版";

	myjs.appUrl = function() {
		// if(T.platform() == "web"){//适配正式测试 不用重复切换
		// 	switch(location.hostname){
		// 		case "*************":
		// 			return "http://*************:810/lzt/";
		// 		case "***************":
		// 			return "http://***************:54386/lzt/";
		// 	}
		// }
		return (
			T.getPrefs("sys_appUrl") ||
			(myjs.iszx
				? "https://productpc.cszysoft.com:8081/lzt/"
				: "https://productpc.cszysoft.com:8080/lzt/")
		);
	};

	myjs.chatHeader = function() {
		return (
			T.getPrefs("sys_chatHeader") || "platform5" + (myjs.iszx ? "zx" : "rd")
		);
	};

	myjs.tomcatAddress = function() {
		return (
			T.getPrefs("sys_tomcatAddress") ||
			(T.platform() == "web"
				? window.location.protocol == "https:"
					? "https://cszysoft.com:9091/"
					: "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		); //http://**********:8080/ http://cszysoft.com:9090/ https://cszysoft.com:9091/
	};

	myjs.shareAddress = function(_type) {
		if (_type == 1 && T.platform() != "mp") {
			return "../../";
		}
		return (
			T.getPrefs("sys_shareAddress") ||
			(T.platform() == "web"
				? window.location.protocol.indexOf("http") == 0
					? window.location.protocol
					: "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(myjs.iszx ? "platform5zx/" : "platform5rd/")
		);
	};

	//默认当前页面 area下的地区 无则传参地区
	(myjs.areaId = function(_this) {
		return (
			(_this && _this.data && _this.data.area ? _this.data.area.key || "" : "") ||
			T.pageParam(_this).areaId ||
			T.getPrefs("sys_aresId") ||
			T.getPrefs("sys_platform") ||
			""
		);
	}),
		//系统类型：（平台版：platform）（标准版：standard）
		(myjs.systemType = function(_this) {
			return (
				T.pageParam(_this).platform || T.getPrefs("sys_systemType") || "platform"
			);
		});

	myjs.clientId = "zyrdV5TestAccount";

	myjs.clientSecret = "zyrdV5TestPassword";

	myjs.hw_project_id = "0611d8333100251b2fc1c01937b8e6d9";

	myjs.hw_bucket = "zy-soft";

	myjs.hw_header = "ZY";

	var G = {
		pageWidth:
			api.platform == "web"
				? api.winWidth > api.winHeight
					? 600
					: api.winWidth
				: api.winWidth,
		refreshPageSize: 0, //返回当前页刷新列表的条数
		dotRefsresh: false, // 返回当前页是否不刷新
		showSkeleton: true, //是否展示骨架屏
		seachText: "", //搜索词
		seachPlaceholder: "请输入搜索内容", //搜索提示
		firstAjax: false, //首次网络请求是否成功
		dotCloseListener: false, //当前页面不要划动返回
		hasCloseListener: false, //不管其它页面 直接添加关闭监听

		appName: "",
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		careMode: false, //是否启用了关怀模式
		htmlStyle: "", //html级别设置style 置灰等操作
		htmlClass: "", //html级别设置class
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		viewappearFrist: true, //是否首次进入页面

		touchmoveTask: null, //划动元素时 禁用页面划动返回事件
		nTouchmove: false,

		isAppReview: false, //app是否上架期间 隐藏和显示部分功能

		touchmove: function touchmove() {
			G.nTouchmove = true;
			G.touchmoveTask && clearTimeout(G.touchmoveTask);
			G.touchmoveTask = setTimeout(function() {
				G.nTouchmove = false;
			}, 1000);
		},
		//通用组件 start=====================================================

		imagePreviewer: {
			//全局图片预览组件
			show: false,
			imgs: [],
			activeIndex: 0,
			type: 1
		},

		openImgPreviewer: function openImgPreviewer(_param) {
			if (_param === void 0) {
				_param = {};
			}
			if ((_param.imgs || []).length <= 0) {
				return;
			}
			G.imagePreviewer.activeIndex = _param.index || 0;
			G.imagePreviewer.imgs = _param.imgs;
			G.imagePreviewer.show = true;
			T.sendEvent("updatePage");
		},

		areasBox: {
			//地区切换弹窗组件
			show: false,
			type: "half", //full全屏打开  half半屏打开
			pageParam: null
		},

		openAreas: function openAreas(_param, _callback) {
			if (_param === void 0) {
				_param = {};
			}
			G.areasBox.pageParam = _param;
			G.areasBox.show = true;
			T.addEventListener("base_areas_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_areas_callback");
			});
			T.sendEvent("updatePage");
		},

		alertBox: {
			// 确认提示框
			show: false,
			title: "",
			content: "",
			richText: false,
			input: false,
			textarea: false,
			placeholder: "",
			cancel: {show: false, text: "取消", color: "#333333"},
			sure: {show: true, text: "确定", color: "appTheme"}
		},

		alert: function alert(_param, _callback) {
			var o = {title: "", msg: "", buttons: ["确定"]};
			if (T.isObject(_param)) {
				o = T.setNewJSON(o, _param);
			} else {
				o.msg = T.isParameters(_param) ? _param : "";
			}
			G.alertBox.title = o.title;
			G.alertBox.content = (o.msg || o.content || "").toString();
			G.alertBox.input = o.input;
			G.alertBox.textarea = o.textarea;
			G.alertBox.placeholder = o.placeholder;
			G.alertBox.richText = o.richText;
			G.alertBox.cancel.show = o.buttons.length > 1;
			G.alertBox.cancel.text = o.buttons[1];
			G.alertBox.sure.text = o.buttons[0];
			G.alertBox.show = true;
			T.addEventListener("base_alert_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_alert_callback");
			});
			T.sendEvent("updatePage");
		},

		actionSheetBox: {
			show: false,
			cancel: false,
			title: "",
			active: null,
			data: []
		},

		actionSheet: function actionSheet(_param, _callback) {
			var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
			o = T.setNewJSON(o, _param);
			G.actionSheetBox.title = o.title;
			G.actionSheetBox.cancel = o.cancelTitle;
			var oldButton = o.buttons || [],
				newButton = [];
			oldButton.forEach(function(item) {
				newButton.push(T.isObject(item) ? item : {value: item});
			});
			G.actionSheetBox.data = newButton;
			G.actionSheetBox.active = _param.active;
			G.actionSheetBox.dotClose = _param.dotClose;
			G.actionSheetBox.show = true;
			T.addEventListener("base_actionSheet_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_actionSheet_callback");
			});
			T.sendEvent("updatePage");
		},
		//通用组件 end=====================================================

		installed: function installed(_this) {
			var _this2 = this;
			if (_this.props && _this.props.dataMore);
			else {
				G.fitWidth();
				G.changeConfiguration(_this);
				G.appGrayscale();
				G.initOther();
				T.addEventListener("index_login_ok", function(ret, err) {
					G.initOther();
				});
				//字体刷新
				T.addEventListener("changeConfiguration", function(ret, err) {
					G.changeConfiguration(_this);
				});
				//地区刷新监听
				T.addEventListener("areaChange", function(ret, err) {
					var notifas = ["module", "news", "my", "negotiable", "area"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "areaChange_" + _eItem, extra: ret.value});
					});
				});
				//红点刷新
				T.addEventListener("unreadChange", function(ret, err) {
					var notifas = ["module", "my"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "unreadChange_" + _eItem, extra: ret.value});
					});
				});
				if (
					T.isFunction(_this.close) &&
					!T.isParameters(_this.props.pageParam) &&
					!T.isParameters(_this.props.dataMore)
				) {
					T.addEventListener("keyback", function(ret, err) {
						if (G.imagePreviewer.show) {
							G.imagePreviewer.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show) {
							if (G.alertBox.cancel.show) {
								G.alertBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						if (G.actionSheetBox.show) {
							if (G.actionSheetBox.cancel) {
								G.actionSheetBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						_this2.close(_this);
					});
					T.addEventListener("swiperight", function(ret, err) {
						if (G.nTouchmove) {
							return;
						}
						if (G.imagePreviewer.show) {
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show && G.alertBox.cancel.show) {
							G.alertBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.actionSheetBox.show) {
							G.actionSheetBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						_this2.close(_this);
					});
				}
				setTimeout(function() {
					_this2.setHeader(_this);
				}, 10);
			}
			try {
				_this.init();
			} catch (e) {
				console.log(e);
			}
		},
		close: function close(_this) {
			_this.close();
		},
		setHeader: function setHeader(_this) {
			var title = _this.data.title;
			// console.log("=================="+title);
			// console.log(_this.props);
			// console.log(_this.data);
			if (!title) {
				return;
			}
			if (T.platform() == "web") {
				if (window.parent) {
					window.parent.document.title = title;
				} else {
					document.title = title;
				}
			} else if (T.platform() == "mp") {
				wx.setNavigationBarTitle({
					title: title
				});
			}
		},
		//多端页面显示回调 app、h5、小程序
		onShow: function onShow(_this) {
			var _this3 = this;
			if (_this.props.dataMore) {
				return;
			}
			if (G.viewappearFrist) {
				G.viewappearFrist = false;
				return;
			}
			console.log("返回了当前页面：");
			T.sendEvent({name: "changeConfiguration"});
			if (G.areaId != T.getPrefs("sys_aresId")) {
				G.areaId = T.getPrefs("sys_aresId") || "";
				T.sendEvent({name: "areaChange", extra: {key: G.areaId}});
			}
			setTimeout(function() {
				_this3.setHeader(_this);
			}, 10);
			if (_this.getData) {
				//返回刷新一下
				_this.getData(false, {refresh: 1});
			}
		},
		//初始化后其它配置
		initOther: function initOther() {
			G.areaId = T.getPrefs("sys_aresId") || "";
			G.systemtTypeIsPlatform = T.getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.appName = T.getPrefs("sys_systemName") || "";
			G.uId = T.getPrefs("sys_Id") || "";
			G.userId = T.getPrefs("sys_UserID") || "";
			G.userName = T.getPrefs("sys_UserName") || "";
			G.userImg = T.getPrefs("sys_AppPhoto") || "";
			G.specialRoleKeys = JSON.parse(T.getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				G.getItemForKey("dc_admin", G.specialRoleKeys) ||
				G.getItemForKey("admin", G.specialRoleKeys);
			G.v = T.getPrefs("sys_appVersion") || "";
			if (T.platform() == "app") {
				G.isAppReview =
					JSON.parse(T.getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
		},
		//全局配置
		changeConfiguration: function changeConfiguration(_this) {
			G.appFont =
				T.getPrefs("appFont") && T.getPrefs("appFont") != "0"
					? T.getPrefs("appFont")
					: "heitiSimplified";
			G.appFontSize = Number(
				T.getPrefs("appFontSize") && T.getPrefs("appFontSize") != "0"
					? T.getPrefs("appFontSize")
					: "16"
			);
			G.appTheme =
				T.pageParam(_this).appTheme ||
				T.getPrefs("appTheme" + (myjs.iszx ? "zx" : "rd")) ||
				(myjs.iszx ? "#3088FE" : "#C61414");
			var headTheme =
				_this.data.headTheme ||
				T.pageParam(_this).headTheme ||
				T.getPrefs("headTheme") ||
				"#FFF";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			if (T.platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
			}
			_this.update();
			T.sendEvent("updatePage");
		},
		//是否全局置灰
		appGrayscale: function appGrayscale() {
			var appGrayscale = T.getPrefs("appGrayscale") || "0";
			if (T.platform() == "app");
			else {
				// G.htmlStyle = "filter:"+(appGrayscale == 1?'grayscale(1)':'none')+";";//小程序不知道为啥style没用
				G.htmlClass = appGrayscale == 1 ? "filterGray" : "filterNone";
			}
		},
		//展示图片
		showImg: function showImg(_item, _add) {
			var baseUrl = T.isObject(_item) ? _item.url || "" : _item || "";
			baseUrl = G.showAllSystemImg(baseUrl, _add); //先显示系统图片
			if (
				baseUrl.indexOf("http") == 0 &&
				baseUrl.indexOf("http://127.0.0.1") != 0 &&
				baseUrl.indexOf(myjs.tomcatAddress()) != 0
			) {
				//是链接 不是小程序本地链接 不是处理过的链接
				if (myjs.proxy && T.platform() != "app" && baseUrl.indexOf("https") != 0) {
					baseUrl = myjs.tomcatAddress() + "utils/proxyPic?" + baseUrl;
				}
			}
			return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
		},
		showAllSystemImg: function showAllSystemImg(_url, _add) {
			return !_url ||
				_url.indexOf("http") == 0 ||
				_url.indexOf("/") == 0 ||
				_url.indexOf("../") == 0
				? _url
				: myjs.appUrl() +
						"image/" +
						(_add || "") +
						(_url.indexOf("-compress-") > -1 ? _url.split("-compress-")[1] : _url);
		},
		//图片处理
		cacheImg: function cacheImg(_item, _thumbnail, _url, _priority) {
			if (!T.isObject(_item) || !T.isParameters(_item.url)) return; //没有传对象 或者没有url的时候不处理
			var baseUrl = _item.webImg || _url || _item.url || ""; //存储当前缓存地址
		},
		//字体配置
		loadConfiguration: function loadConfiguration(_changeSize) {
			return (
				"font-size:" +
				((G.appFontSize || 0) + (_changeSize || 0)) +
				"px;font-family:" +
				G.appFont +
				";"
			);
		},
		//宽度配置
		loadConfigurationSize: function loadConfigurationSize(_changeSize, _who) {
			var changeSize = _changeSize || 0,
				returnCss = "",
				cssWidth,
				cssHeight;
			if (T.isArray(_changeSize)) {
				cssWidth = "width:" + (G.appFontSize + (_changeSize[0] || 0)) + "px;";
				cssHeight = "height:" + (G.appFontSize + (_changeSize[1] || 0)) + "px;";
			} else {
				cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
				cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
			}
			if (!_who) {
				returnCss = cssWidth + cssHeight;
			} else {
				returnCss = _who == "w" ? cssWidth : cssHeight;
			}
			return returnCss;
		},
		//获取item	只有一层级的时候 会返回 当前index	_i
		getItemForKey: function getItemForKey(_value, _list, _key, _child) {
			var hasChild = false;
			if (!T.isParameters(_list)) return;
			var listLength = _list.length;
			for (var i = 0; i < listLength; i++) {
				var listItem = _list[i];
				if (T.isArray(listItem)) {
					hasChild = true;
					var result = G.getItemForKey(_value, listItem, _key, true);
					if (result) return result;
				} else {
					if (!T.isObject(listItem)) {
						if (listItem === _value) {
							return listItem;
						}
					} else {
						if (T.isArray(listItem[_key || "key"])) {
							hasChild = true;
							var result = G.getItemForKey(
								_value,
								listItem[_key || "key"],
								_key,
								true
							);
							if (result) {
								listItem["_i"] = i;
								return listItem;
							}
						} else if (listItem[_key || "key"] === _value) {
							listItem["_i"] = i;
							return listItem;
						}
					}
					if (
						T.isObject(listItem) &&
						listItem.children &&
						T.isArray(listItem.children)
					) {
						hasChild = true;
						var result = G.getItemForKey(_value, listItem.children, _key, true);
						if (result) return result;
					}
				}
			}
			if (!_child && !hasChild) return false;
		},
		//在集合中删除第一个参数obj和index都可以或对比字符串	第二个传入集合	第三个为对比key
		delItemForKey: function delItemForKey(_obj, _list, _key) {
			if (T.isTargetType(_obj, "number") && _obj < _list.length) {
				_list.splice(_obj, 1);
			} else {
				var contrastObj = !T.isObject(_obj) ? _obj : _obj[_key || "key"];
				for (var i = 0; i < _list.length; i++) {
					if (
						(!T.isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) ==
						contrastObj
					) {
						_list.splice(i, 1);
						G.delItemForKey(_obj, _list, _key);
						break;
					}
				}
			}
		},
		//是否显示顶部
		showHeader: function showHeader(_this) {
			return T.platform() == "app" || T.pageParam(_this).showHeader;
		},
		//适配多端状态栏
		headerTop: function headerTop() {
			return T.platform() == "app" ? T.safeArea().top : 0;
		},
		//底部可视区域
		footerBottom: function footerBottom(_bottom) {
			return _bottom ? T.safeArea().bottom : 0;
		},
		//适配pc页打开宽度
		fitWidth: function fitWidth() {
			if (
				T.platform() == "web" &&
				document.documentElement.clientWidth > document.documentElement.clientHeight
			) {
				$("body").style.width = "100%";
				$("body").style.maxWidth = "600px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		},
		//获取当前主题色相对应前景色
		getHeadThemeRelatively: function getHeadThemeRelatively(_this) {
			var theme =
				(_this && _this.data && _this.data.headTheme
					? _this.data.headTheme || ""
					: "") || G.headTheme;
			return theme && T.isColorDarkOrLight(theme) == "dark" ? "#FFF" : "#333";
		},
		//转换成html格式
		convertRichText: function convertRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			var textList = value.split("\n");
			var str = "";
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
				if (addText) {
					str += "<p>" + addText + "</p>";
				}
			}
			return str;
		},
		//清空html格式
		clearRichText: function clearRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			//坑爹的后台管理了 & 符号
			value = value.replace(/&amp;/g, "&");
			// 空格处理
			value = value.replace(/(&nbsp;)/g, " ");
			// 换行处理
			value = value.replace(/<br\/?[^>]*>/g, "\n");

			value = value.replace(/<\/[p|div|h1|h2|h3|h4|h5|h6]>/g, "\n");
			value = value.replace(/<\/?[^>]*>/g, "");
			return value;
		},
		//阻止冒泡事件
		stopBubble: function stopBubble(e) {
			if (!e) return;
			if (T.platform() == "web") {
				e.preventDefault();
				e.stopPropagation();
			} else if (T.platform() == "mp") {
				e.$_canBubble = false;
			}
		},
		getTagColor: function getTagColor(_key) {
			if (_key === void 0) {
				_key = "";
			}
			var tagColors = [
				{key: "未处理", value: "#F6631C"},
				{key: "未开始", value: "#F6631C"},

				{key: "签到中", value: "#F6931C"},
				{key: "报名中", value: "#F6931C"},
				{key: "进行中", value: "#F6931C"},

				{key: "请假通过", value: "#50C614"},
				{key: "请假待审批", value: "#F6931C"},
				{key: "请假中", value: "#F6931C"},
				{key: "已参与", value: G.appTheme},
				{key: "待审核", value: "#F6931C"},
				{key: "已通过", value: "#50C614"},
				{key: "有效", value: G.appTheme},
				{key: "待审查", value: "#F6931C"},
				{key: "人大交办中", value: "#F6931C"},
				{key: "政协交办中", value: "#F6931C"},
				{key: "政府交办中", value: "#F6931C"},
				{key: "党委交办中", value: "#F6931C"},
				{key: "两院交办中", value: "#F6931C"},
				{key: "法院交办中", value: "#F6931C"},
				{key: "检察院交办中", value: "#F6931C"},
				{key: "转参阅件", value: "#C61414"},
				{key: "办理中", value: "#F6931C"},
				{key: "重新办理", value: "#F6931C"},
				{key: "已答复", value: "#50C614"},
				{key: "已办结", value: "#559FFF"},
				{key: "A类", value: "#F6931C"},
				{key: "B类", value: "#1A74DA"},
				{key: "待受理", value: "#F6931C"},
				{key: "已受理", value: "#50C614"},
				{key: "已回复", value: "#50C614"},
				{key: "待交付审议", value: "#F6931C"},
				{key: "专委会审议中", value: "#F6931C"},
				{key: "已上传相关资料", value: "#50C614"},
				{key: "留存", value: "#F6931C"},
				{key: "采用", value: "#50C614"}
			];

			var tagColor = G.getItemForKey(_key, tagColors);
			return tagColor ? tagColor.value : "#666666";
		},
		//获取文件类型 并返回数据
		getFileInfo: function getFileInfo(_name) {
			if (_name === void 0) {
				_name = "";
			}
			var name = _name.toLocaleLowerCase(),
				fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
			try {
				if (name.indexOf(".") != -1)
					name = name.split(".")[name.split(".").length - 1];
				switch (name) {
					case "xlsx":
					case "xlsm":
					case "xlsb":
					case "xltx":
					case "xltm":
					case "xls":
					case "xlt":
					case "et":
					case "csv":
					case "uos": //excel格式
						fileInfo.name = "file-excel-fill";
						fileInfo.color = "#00bd76";
						fileInfo.type = "excel";
						fileInfo.convertType = "0";
						break;
					case "doc":
					case "docx":
					case "docm":
					case "dotx":
					case "dotm":
					case "dot":
					case "xps":
					case "rtf":
					case "wps":
					case "wpt":
					case "uot": //word格式
						fileInfo.name = "file-word-fill";
						fileInfo.color = "#387efa";
						fileInfo.type = "word";
						fileInfo.convertType = "0";
						break;
					case "pdf": //pdf格式
						fileInfo.name = "file-pdf-fill";
						fileInfo.color = "#e9494a";
						fileInfo.type = "pdf";
						fileInfo.convertType = "20";
						break;
					case "ppt":
					case "pptx":
					case "pps":
					case "pot":
					case "pptm":
					case "potx":
					case "potm":
					case "ppsx":
					case "ppsm":
					case "ppa":
					case "ppam":
					case "dps":
					case "dpt":
					case "uop": //ppt
						fileInfo.name = "file-ppt-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "ppt";
						fileInfo.convertType = "0";
						break;
					case "bmp":
					case "gif":
					case "jpg":
					case "pic":
					case "png":
					case "tif":
					case "jpeg":
					case "jpe":
					case "icon":
					case "jfif":
					case "dib": //图片格式 case 'webp':
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "image";
						fileInfo.convertType = "440";
						break;
					case "txt": //文本
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#2696ff";
						fileInfo.type = "txt";
						fileInfo.convertType = "0";
						break;
					case "rar":
					case "zip":
					case "7z":
					case "tar":
					case "gz":
					case "jar":
					case "ios": //压缩格式
						fileInfo.name = "file-zip-fill";
						fileInfo.color = "#a5b0c0";
						fileInfo.type = "compression";
						fileInfo.convertType = "19";
						break;
					case "mp4":
					case "avi":
					case "flv":
					case "f4v":
					case "webm":
					case "m4v":
					case "mov":
					case "3gp":
					case "rm":
					case "rmvb":
					case "mkv":
					case "mpeg":
					case "wmv": //视频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#e14a4a";
						fileInfo.type = "video";
						fileInfo.convertType = "450";
						break;
					case "mp3":
					case "m4a":
					case "amr":
					case "pcm":
					case "wav":
					case "aiff":
					case "aac":
					case "ogg":
					case "wma":
					case "flac":
					case "alac":
					case "wma":
					case "cda": //音频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#8043ff";
						fileInfo.type = "voice";
						fileInfo.convertType = "660";
						break;
					case "folder": //文件夹
						fileInfo.name = "folder-2-fill";
						fileInfo.color = "#ffd977";
						fileInfo.type = "folder";
						break;
				}
			} catch (e) {
				console.log(e.message);
			}
			return fileInfo;
		},
		//获取文件大小
		getFileSize: function getFileSize(_fileSize) {
			if (!_fileSize && _fileSize != 0) return "";
			try {
				var size1 = parseFloat((_fileSize / 1024 / 1024).toFixed(1));
				var size2 = parseFloat((_fileSize / 1024).toFixed(1));
				if (size1 >= 1) {
					return size1 + "MB";
				} else if (size2 >= 1) {
					return size2 + "KB";
				} else {
					return parseInt(_fileSize) + "B";
				}
			} catch (e) {
				return _fileSize;
			}
		},
		//选择文件并上传
		chooseFile: function chooseFile(_this, _item, callback) {
			var max = T.isNumber(_item.max) ? _item.max : 0;
			if (T.platform() == "app") {
				if (T.systemType() == "ios") {
					if (!T.confirmPer("storage", "chooseFile")) {
						//存储权限
						T.addEventListener("storage" + "Per_" + "chooseFile", function(ret, err) {
							T.removeEventListener("storage" + "Per_" + "chooseFile");
							if (ret.value.granted) {
								G.chooseFile(_this, _item, callback);
							}
						});
						return;
					}
				} else {
					if (!api.require("zyRongCloud").hasAllFilesPermission()) {
						T.alert(
							{
								title: "提示",
								msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
								buttons: ["确定", "取消"]
							},
							function(ret) {
								if (ret.buttonIndex == "1") {
									api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
										if (ret.status) {
											G.chooseFile(_this, _item, callback);
										}
									});
								}
							}
						);
						return;
					}
				}
				var fileBrowser = api.require("fileBrowser");
				fileBrowser.open({}, function(ret, err) {
					fileBrowser.close();
					setTimeout(function() {
						_item.url = ret.url;
						G.uploadFile(_this, _item, function(ret) {
							callback && callback(ret);
						});
					}, 500);
				});
			} else if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.type = "file";
				h5Input.accept = "";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.click();
				h5Input.onchange = function() {
					var listLength =
						max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = h5Input.files[j];
							G.uploadFile(_this, nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				};
			} else if (T.platform() == "mp") {
				wx.chooseMessageFile({
					count: max != 0 ? max : 9,
					type: "file",
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							(function(j) {
								var nItem = JSON.parse(JSON.stringify(_item));
								nItem.url = res.tempFiles[j];
								G.uploadFile(_this, nItem, function(ret) {
									callback && callback(ret);
								});
							})(i);
						}
					}
				});
			}
		},
		//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
		uploadFile: function uploadFile(_this, _item, callback) {
			if (_item._fileAjax || _item.module == "-noUpload")
				//有传过 或者明确不传
				return;
			_item._fileAjax = true; //是否请求过	有就不再请求
			_item.state = 1;
			if (_item.showToast) {
				T.showProgress("上传中");
			}
			var nCallack = function nCallack(ret, err) {
				T.hideProgress();
				var code = ret ? ret.code : "";
				if (code == 200) {
					var data = ret.data || {};
					_item.state = 2;
					_item.uploadId = data.id;
					_item.otherInfo = data;
				} else {
					_item.state = 3;
					_item.error = ret ? ret.message || ret.data : err.data || "";
				}
				callback && callback(_item);
			};
			if (T.platform() == "mp") {
				wx.uploadFile({
					url: myjs.tomcatAddress() + "utils/proxy",
					filePath: _item.url.path,
					name: "file",
					header: {
						"Content-Type": "multipart/form-data",
						"u-login-areaId": myjs.areaId(_this),
						Authorization: T.getPrefs("sys_token") || ""
					},

					formData: {
						BASE_URL: myjs.appUrl() + "file/upload",
						BASE_TYPE: "file",
						fileName:
							_item.url.name ||
							_item.url.path.substring(_item.url.path.lastIndexOf("/") + 1)
					},

					success: function success(res) {
						nCallack(JSON.parse(res.data), null);
					},
					fail: function fail(err) {
						nCallack(null, JSON.parse(err.data));
					}
				});
			} else {
				T.ajax(
					{u: myjs.appUrl() + "file/upload", _this: _this, web: _item.web},
					"file/upload" + _item.url,
					nCallack,
					"上传附件",
					"post",
					{
						files: {file: _item.url},
						values: {a: 1}
					},
					{
						"content-type": "file" //安卓附件不能传 得用默认的
					}
				);
			}
		},
		getAreaForKey: function getAreaForKey(_key, _dotAll) {
			var rItem = null;
			var areas = JSON.parse(T.getPrefs("sys_areas") || "[]");
			if (!_dotAll || !areas.length) {
				areas = JSON.parse(T.getPrefs("sys_allAreas") || "[]");
			}
			if (areas.length) {
				rItem = G.getItemForKey(_key, areas, "id");
				if (rItem) {
					rItem.name =
						rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
				}
			}
			return rItem || {};
		},
		showTextSize: function showTextSize(_text, _size, _middle) {
			if (_size && _text) {
				_text =
					_text.length > _size
						? _middle
							? _text.substring(0, _size / 2) +
							  "..." +
							  _text.substring(_text.length - _size / 2)
							: _text.substring(0, _size) + "..."
						: _text;
			}
			return _text;
		},
		ajaxAlert: function ajaxAlert(_param, _this, _callback) {
			var _this4 = this;
			var param = {
				title: "提示",
				msg: _param.msg || "",
				buttons: _param.buttons || ["确定", "取消"]
			};

			if (_param.alertParam) {
				param = T.setNewJSON(param, _param.alertParam);
			}
			T.alert(param, function(ret) {
				if (ret.buttonIndex == "1") {
					_this4.ajaxProcess(_param, _this, _callback);
				}
			});
		},
		ajaxProcess: function ajaxProcess(_param, _this, _callback) {
			if (!_param.dotProgress) T.showProgress(_param.toast);
			T.ajax(
				{u: _param.url, _this: _this},
				"ajaxProcess",
				function(ret) {
					if (!_param.dotProgress) T.hideProgress();
					if (!_param.dotToast) T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					if (ret && ret.code == "200") {
						_callback && _callback(ret);
					}
				},
				"\u64CD\u4F5C",
				"post",
				{
					body: JSON.stringify(_param.param)
				}
			);
		}
	};

	function sm4decrypt(word, keyStr, ivStr) {
		switch (platform()) {
			case "app":
				return api.require("zyCryptoGM").decryptCBC({
					data: word,
					key: keyStr || "zysoft2025518888",
					iv: ivStr || "zysoft2025518888"
				});

			case "web":
				return new SM4Util(keyStr, ivStr).decryptData_CBC(word);
		}
	}

	var LOAD_ALL = "已加载完";
	var NET_ERR = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否数字
	function isNumber(_obj) {
		return isParameters(_obj) && typeof _obj === "number";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//去除首尾空格
	function trim(str) {
		if (String.prototype.trim) {
			return str == null ? "" : String.prototype.trim.call(str);
		} else {
			return str.replace(/(^\s*)|(\s*$)/g, "");
		}
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//移除字符串所有标签
	function removeTag(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//转义字符串
	function decodeCharacter(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;|&emsp;)/g, " ")
			.replace(/&zwnj;/g, "")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">")
			.replace(/&sup3;/g, "³")
			.replace(/&times;/g, "×")
			.replace(/&deg;/g, "°")
			.replace(/&permil;/g, "‰");
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//移除监听
	function removeEventListener(name) {
		if (
			platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			delItemForKey(name, window.baseEventList);
		} else {
			try {
				api.removeEventListener({name: name});
			} catch (e) {}
		}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G$1.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G$1.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G$1.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G$1.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G$1.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G$1.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G$1.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (
			G$1.headTheme != getPrefs("headTheme") &&
			G$1.headTheme != "transparent"
		) {
			o.pageParam.headTheme = G$1.headTheme;
		}
		if (
			G$1.appTheme != getPrefs("appTheme" + G$1.sysSign) &&
			G$1.appTheme != (G$1.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G$1.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global, _time) {
		var o = {
			msg: "",
			duration: _time || 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		setTimeout(function() {
			api.toast(o);
		}, 50);
	}

	//弹出actionSheet
	function actionSheet(_param, _callback) {
		var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
		o = setNewJSON(o, _param);
		var oldButton = o.buttons || [],
			newButton = [];
		oldButton.forEach(function(item) {
			newButton.push(isObject(item) ? item : {name: item});
		});
		var actionSheetBox = {
			title: o.title,
			cancel: o.cancelTitle,
			data: newButton,
			active: o.active,
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			},
			pageClose: function pageClose() {
				_param.pageClose && _param.pageClose();
			}
		};

		G$1.actionSheetPop = actionSheetBox;
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G$1.alertPop = alertBox;
	}

	function connectionType() {
		try {
			return api.connectionType || navigator.connection.effectiveType || "";
		} catch (e) {
			return "";
		}
	}

	function userAgent() {
		try {
			return navigator.userAgent || navigator.vendor || window.opera || "";
		} catch (e) {
			return "";
		}
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G$1.terminal || "APP"
				},
				header || {}
			)
		};

		if (o.url.indexOf("oauth/token") != "-1") {
			o.headers["u-mobile"] = JSON.stringify({
				appVersion: api.appVersion || "",
				systemType: api.systemType || "",
				systemVersion: api.systemVersion || "",
				version: api.version || "",
				deviceModel: api.deviceModel || "",
				deviceName: api.deviceName || "",
				uiMode: api.uiMode || "",
				platform: api.platform || "",
				operator: api.operator || "",
				connectionType: connectionType(),
				userAgent: userAgent()
			});
		}
		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					(getPrefs("sys_appRongKey") == "y745wfm84be5v" ? 1 : 2);
			} else if (o.data.values) {
				o.data.values.environment =
					getPrefs("sys_appRongKey") == "y745wfm84be5v" ? 1 : 2;
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (isObject(ret)) {
					var isEncrypt = ret.isEncrypt;
					if (isEncrypt && ret.data) {
						try {
							ret.data = sm4decrypt(ret.data);
							ret.data = JSON.parse(ret.data);
						} catch (e) {
							ret.data = ret.data;
						}
					}
				}
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	function getPicture(_param, callback) {
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = setNewJSON(o, _param);
		try {
			if (platform() == "web") {
				// 将文件转换为Base64的Promise函数
				var fileToBase64 = function fileToBase64(file, callback) {
					return new Promise(function(resolve, reject) {
						if (!file.type.startsWith("image/")) {
							return;
						}
						if (o.destinationType != "base64") {
							callback({data: file});
							return;
						}
						var reader = new FileReader();
						reader.onload = function(e) {
							callback({data: file, base64Data: e.target.result});
						};
						reader.onerror = function(err) {
							callback({data: file});
						};
						reader.readAsDataURL(file);
					});
				};
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = userAgent().toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if ((!version || Number(version) <= 13) && o.max != 1 && !o.useOne) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						fileToBase64(h5Input.files[i], callback);
					}
				};
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						if (ret.value.granted) {
							getPicture(_param, callback);
						}
						removeEventListener(preName + "Per_" + "getPicture");
					});
					return;
				}
				if (o.sourceType == "camera" || o.useOne) {
					api.getPicture(o, function(ret, err) {
						callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G$1.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G$1.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	}

	function hasPermission(one_per) {
		if (platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	}

	function requestPermission(one_per, callback, _fName) {
		if (platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				isFunction(callback) && callback(ret, err);
			});
		}
	}

	function confirmPer(perm, _fName, _reason) {
		if (platform() == "app") {
			var has = hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				alert(
					{
						title: "无法使用" + hintWord[perm],
						msg: api.systemType == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret) {
						if (1 == ret.buttonIndex) {
							requestPermission(perm, null, _fName);
						} else {
							sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "8080" : "8081";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//当前页面地区id
	function areaId() {
		return (
			(G$1._this && G$1._this.data.area ? G$1._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	//获取会议待办成果列表
	function getTaskAchievementList(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "zyMeetTaskAchievement/list",
				param: setNewJSON({}, _param.param),
				tag: "zyMeetTask",
				name: "会议待办"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					ret.dealWith = data;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取会议待办详情
	function getDetails55(_param, _callback) {
		ajax(
			{u: appUrl() + "zyMeetTask/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					data.priorityName =
						data.priority == "1" ? "高" : data.priority == "2" ? "中" : "低";
					data.taskTypeName = data.taskType.name;
					ret.dealWith = data;
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	var module5 = {
		name: "资讯",
		code: "5",
		businessCode: "informationContent",
		behaviorCode: "information_content"
	};
	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//打开链接
	function openWin_url(_param) {
		_param.url = handleSYSLink(_param.url);
		if (
			(_param.wopen || _param.url.indexOf("wopen") != -1) &&
			platform() == "web"
		) {
			window.open(_param.url);
		} else {
			openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
		}
	}

	//打开图片预览
	function openWin_imgPreviewer(_param) {
		G$1.imgPreviewerPop = {
			show: true,
			index: _param.index,
			imgs: _param.imgs
		};

		console.log(JSON.stringify(G$1.imgPreviewerPop));
	}

	//打开附件预览
	function openWin_filePreviewer(_param) {
		if (platform() == "mp") {
			showProgress("打开中");
			wx.downloadFile({
				url:
					_param.id.indexOf("http") != 0
						? appUrl() + ("file/preview/" + _param.id)
						: _param.id,
				success: function success(res) {
					hideProgress();
					if (res.statusCode != 200) {
						toast("打开文件失败:" + JSON.stringify(ret));
						return;
					}
					wx.openDocument({
						filePath: res.tempFilePath,
						fileType: _param.extName,
						showMenu: true
					});
				},
				fail: function fail(err) {
					toast("打开文件失败:" + JSON.stringify(err));
					hideProgress();
				}
			});
		} else {
			openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
		}
	}

	//打开详情------------------------------------------------------------------------------------

	//打开资讯
	function openWin_news(_item) {
		var openPage =
			_item.code == "7" ? "mo_news_topic" : _item.id ? "mo_details_n" : "mo_news";
		var param = {};
		param.id = _item.id;
		param.code = module5.code;
		if (_item.link) {
			openWin_url({url: _item.link});
		} else {
			openWin(
				openPage + (_item.id || _item.code),
				"../" + openPage + "/" + openPage + ".stml",
				param
			);
		}
		addBehaviorRecord({id: param.id, behaviorCode: module5.behaviorCode});
	}

	//打开AI助手 _param: id=123
	function openWin_ai(_param) {
		var url =
			shareAddress() +
			"html/smart_tools/aiSession.html?" +
			(_param + (_param ? "&" : "")) +
			"token={{token}}&appTheme={{appTheme}}";
		openWin_url({url: url, dotMore: "true"});
	}

	//需要通知到有融云的页面
	var refreshs = ["mo_chat_msg", "mo_chat"]; //每个页面有唯一名字
	function RONGRefresh() {
		setTimeout(function() {
			sendEvent({
				name: "rongCloud",
				extra: {
					type: "getUnreadCountByConversationTypes",
					monitor: "",
					param: {conversationTypes: ["PRIVATE", "GROUP"], containBlocked: false}
				}
			});
			refreshs.forEach(function(_eItem) {
				sendEvent({name: "chat_refresh_" + _eItem});
			});
		}, 50);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	// 增加阅读量
	function addBehaviorRecord(_param) {
		ajax(
			{u: appUrl() + ("behavior/record/" + _param.behaviorCode + "/" + _param.id)},
			"behavior/record",
			function(ret, err) {},
			"阅读",
			"post",
			{
				body: JSON.stringify({})
			}
		);
	}

	//弹窗提示
	function ajaxAlert(_param, _callback) {
		var param = {
			title: "提示",
			msg: _param.msg || "",
			buttons: ["确定", "取消"]
		};

		if (_param.alertParam) {
			param = setNewJSON(param, _param.alertParam);
		}
		console.log(_param.url + "\n" + JSON.stringify(_param.param));
		alert(param, function(ret) {
			if (ret.buttonIndex == "1") {
				ajaxProcess(_param, _callback);
			}
		});
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url, "150x150-compress-");
				}
				addInfo.push(_eItem);
			}
		});
		G$1.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G$1.chatInfos));
		return G$1.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G$1.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G$1.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						RONGRefresh();
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	function dealVideoId(_id) {
		return _id.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "");
	}

	//当前页面播放视频集合
	function videoPlayPush(_id) {
		if (!G$1.playVideos) {
			G$1.playVideos = [];
		}
		if (getItemForKey(_id, G$1.playVideos)) {
			return;
		}
		videoPlayRemoves();
		G$1.playVideos.push(_id);
		console.log("当前播放：" + JSON.stringify(G$1.playVideos));
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G$1.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G$1.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	var _G;
	//全局页面引用变量
	var G$1 =
		((_G = {
			sysSign: sysSign(), //人大政协标识
			pageWidth:
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth, //页面总宽度
			onShowNum: -1, //当前页面展示次数 1为首次
			appName: "", //app名字
			appFont: "", //app全局字体
			appFontSize: Number(getPrefs("appFontSize") || "16"), //app全局字体大小
			appTheme:
				getPrefs("appTheme" + sysSign()) ||
				(sysSign() == "rd" ? "#C61414" : "#3088FE"), //app全局主题色
			headTheme: "", //head全局主题色
			headColor: "", //标题前景色
			headTitle: "", //标题栏文字
			loginInfo: "",

			careMode: false, //是否启用了关怀模式
			systemtTypeIsPlatform: false, //系统类型是否是平台版
			isAppReview: false, //app是否上架期间 隐藏和显示部分功能
			v: "", //缓存版本号

			uId: "", //普通用户id
			userId: "", //当前用户id 账号id
			userName: "", //当前用户名字
			userImg: "", //当前用户头像
			areaId: "", //当前地区id
			specialRoleKeys: [], //当前用户角色集合
			isAdmin: false, //是否管理员 拥有所有权限
			grayscale: "", //全局置灰
			watermark: "", //全局水印

			inputIng: false, //输入框是否输入中

			// chatInfos:[],
			// chatInfoTask:[],

			alertPop: {
				// alert提示框
				show: false
			},

			actionSheetPop: {
				//actionSheet弹出框
				show: false
			},

			imgPreviewerPop: {
				//图片预览
				show: false
			},

			areasPop: {
				// 全局地区弹窗
				show: false
			},

			numInputPop: {
				// 全局数字弹窗
				show: false
			},

			favoritePop: {
				//收藏弹窗
				show: false
			},

			sharePosterPop: {
				//分享海报
				show: false
			},

			sharePop: {
				//分享
				show: false
			},

			identifyAudioPop: {
				//语音输入
				show: false
			},

			selectPop: {
				//单多选
				show: false
			},

			qrcodePop: {
				//h5扫码
				show: false
			},

			addressBookPop: {
				//通讯录
				show: false
			},

			fileListPop: {
				//选择文件
				show: false
			},

			signPop: {
				//手写签名
				show: false
			},

			modulePop: {
				//功能区更多弹窗
				show: false
			}
		}),
		(_G["identifyAudioPop"] = {
			//语音识别输入
			show: false
		}),
		_G);

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			(G$1.appFontSize + (size || 0)) +
			"px;" +
			(G$1.appFont != "heitiSimplified" ? "font-family:" + G$1.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G$1.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G$1.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G$1.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G$1.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//移动事件 不左滑关闭屏幕
	function touchmove() {
		G$1.nTouchmove = true;
		clearTimeout(G$1.touchmoveTask);
		G$1.touchmoveTask = setTimeout(function() {
			G$1.nTouchmove = false;
		}, 1000);
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G$1.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//列表无数据处理
	function dealData(_type, _this, ret) {
		if (!_type) {
			_this.data.listData = [];
			_this.data.emptyBox.type = ret ? "1" : "2";
			_this.data.emptyBox.text =
				ret && ret.code != 200 ? ret.message || ret.data : "";
		} else {
			_this.data.emptyBox.text = ret
				? ret.code == 200
					? LOAD_ALL
					: ret.message || ret.data
				: NET_ERR;
		}
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
	function uploadFile(_item, callback) {
		if (_item._fileAjax || _item.module == "-noUpload") return;
		_item._fileAjax = true;
		_item.state = 1;
		if (_item.showToast) {
			showProgress("上传中");
		}
		var nCallack = function nCallack(ret, err) {
			hideProgress();
			var code = ret ? ret.code : "";
			if (code == 200) {
				var data = ret.data || {};
				_item.state = 2;
				_item.uploadId = data.id;
				_item.otherInfo = data;
			} else {
				_item.state = 3;
				_item.error = ret ? ret.message || ret.data : err.data || err.body || "";
			}
			callback && callback(_item);
		};
		if (platform() == "mp") {
			wx.uploadFile({
				url: appUrl() + "file/upload",
				filePath: _item.url.path,
				name: "file",
				formData: {
					originalFileName: _item.url.name
				},

				header: {
					"Content-Type": "multipart/form-data",
					"u-login-areaId": areaId(),
					Authorization: getPrefs("sys_token") || ""
				},

				success: function success(res) {
					nCallack(JSON.parse(res.data), null);
				},
				fail: function fail(err) {
					nCallack(null, JSON.parse(err.data));
				}
			});
		} else {
			ajax(
				{u: appUrl() + "file/upload", web: _item.web},
				"file/upload" + _item.url,
				nCallack,
				"上传附件",
				"post",
				{
					files: {file: _item.url}
				},
				{"content-type": "file"}
			);
		}
	}

	//获取文件类型 并返回数据
	function getFileInfo(_name) {
		var name = (_name || "").toLocaleLowerCase(),
			fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
		try {
			if (name.indexOf(".") != -1)
				name = name.split(".")[name.split(".").length - 1];
			switch (name) {
				case "xlsx":
				case "xlsm":
				case "xlsb":
				case "xltx":
				case "xltm":
				case "xls":
				case "xlt":
				case "et":
				case "csv":
				case "uos": //excel格式
					fileInfo.name = "file-excel-fill";
					fileInfo.color = "#00bd76";
					fileInfo.type = "excel";
					fileInfo.convertType = "0";
					break;
				case "doc":
				case "docx":
				case "docm":
				case "dotx":
				case "dotm":
				case "dot":
				case "xps":
				case "rtf":
				case "wps":
				case "wpt":
				case "uot": //word格式
					fileInfo.name = "file-word-fill";
					fileInfo.color = "#387efa";
					fileInfo.type = "word";
					fileInfo.convertType = "0";
					break;
				case "pdf": //pdf格式
					fileInfo.name = "file-pdf-fill";
					fileInfo.color = "#e9494a";
					fileInfo.type = "pdf";
					fileInfo.convertType = "20";
					break;
				case "ppt":
				case "pptx":
				case "pps":
				case "pot":
				case "pptm":
				case "potx":
				case "potm":
				case "ppsx":
				case "ppsm":
				case "ppa":
				case "ppam":
				case "dps":
				case "dpt":
				case "uop": //ppt
					fileInfo.name = "file-ppt-fill";
					fileInfo.color = "#ff7440";
					fileInfo.type = "ppt";
					fileInfo.convertType = "0";
					break;
				case "bmp":
				case "gif":
				case "jpg":
				case "pic":
				case "png":
				case "tif":
				case "jpeg":
				case "jpe":
				case "icon":
				case "jfif":
				case "dib": //图片格式 case 'webp':
					fileInfo.name = "file-text-fill";
					fileInfo.color = "#ff7440";
					fileInfo.type = "image";
					fileInfo.convertType = "440";
					break;
				case "txt": //文本
					fileInfo.name = "file-text-fill";
					fileInfo.color = "#2696ff";
					fileInfo.type = "txt";
					fileInfo.convertType = "0";
					break;
				case "rar":
				case "zip":
				case "7z":
				case "tar":
				case "gz":
				case "jar":
				case "ios": //压缩格式
					fileInfo.name = "file-zip-fill";
					fileInfo.color = "#a5b0c0";
					fileInfo.type = "compression";
					fileInfo.convertType = "19";
					break;
				case "mp4":
				case "avi":
				case "flv":
				case "f4v":
				case "webm":
				case "m4v":
				case "mov":
				case "3gp":
				case "rm":
				case "rmvb":
				case "mkv":
				case "mpeg":
				case "wmv": //视频格式
					fileInfo.name = "file-music-fill";
					fileInfo.color = "#e14a4a";
					fileInfo.type = "video";
					fileInfo.convertType = "450";
					break;
				case "mp3":
				case "m4a":
				case "amr":
				case "pcm":
				case "wav":
				case "aiff":
				case "aac":
				case "ogg":
				case "wma":
				case "flac":
				case "alac":
				case "wma":
				case "cda": //音频格式
					fileInfo.name = "file-music-fill";
					fileInfo.color = "#8043ff";
					fileInfo.type = "voice";
					fileInfo.convertType = "660";
					break;
				case "folder": //文件夹
					fileInfo.name = "folder-2-fill";
					fileInfo.color = "#ffd977";
					fileInfo.type = "folder";
					break;
			}
		} catch (e) {
			console.log(e.message);
		}
		return fileInfo;
	}

	//展示省略文字
	function showTextSize(_text, _size, _middle) {
		if (_size && _text) {
			if (_text.length > _size) {
				if (_middle) {
					var mSize = _size / 2;
					var nLast = getSizeText(_text, mSize);
					var nNext = getSizeText(_text, mSize, 1);
					if (nLast.length + nNext.length < _text.length) {
						_text = nLast + "..." + nNext;
					}
				} else {
					var nText = getSizeText(_text, _size);
					_text = nText + (nText.length < _text.length ? "..." : "");
				}
			}
		}
		return _text;
	}

	function getSizeText(_text, _size, _next) {
		var texts = _text.split("");
		var nowSize = 0,
			nowLength = 0;
		if (_next) {
			for (var i = texts.length - 1; i >= 0; i--) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(texts.length - nowLength);
		} else {
			for (var i = 0; i < texts.length; i++) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(0, nowLength);
		}
	}

	//选择文件并上传
	function chooseFile(_item, callback) {
		var max = isNumber(_item.max) ? _item.max : 0;
		if (platform() == "app") {
			if (api.systemType == "ios") {
				if (!confirmPer("storage", "chooseFile")) {
					//存储权限
					addEventListener("storagePer_chooseFile", function(ret, err) {
						removeEventListener("storagePer_chooseFile");
						if (ret.value.granted) {
							chooseFile(_item, callback);
						}
					});
					return;
				}
			} else {
				if (!api.require("zyRongCloud").hasAllFilesPermission()) {
					alert(
						{
							title: "提示",
							msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
							buttons: ["确定", "取消"]
						},
						function(ret) {
							if (ret.buttonIndex == "1") {
								api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
									if (ret.status) {
										chooseFile(_item, callback);
									}
								});
							}
						}
					);
					return;
				}
			}
			var fileBrowser = api.require("fileBrowser");
			fileBrowser.open({}, function(ret, err) {
				fileBrowser.close();
				setTimeout(function() {
					_item.url = ret.url;
					uploadFile(_item, function(ret) {
						callback && callback(ret);
					});
				}, 500);
			});
		} else if (platform() == "web") {
			var h5Input = document.createElement("input");
			h5Input.type = "file";
			h5Input.accept = "";
			var ua = navigator.userAgent.toLowerCase();
			var version = "";
			if (ua.indexOf("android") > 0) {
				var reg = /android [\d._]+/gi;
				var v_info = ua.match(reg);
				version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
				version = parseInt(version.split(".")[0]);
			}
			if (!version || Number(version) <= 13) {
				h5Input.multiple = "multiple";
			}
			h5Input.click();
			h5Input.onchange = function() {
				var listLength =
					max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
				for (var i = 0; i < listLength; i++) {
					(function(j) {
						var nItem = JSON.parse(JSON.stringify(_item));
						nItem.url = h5Input.files[j];
						uploadFile(nItem, function(ret) {
							callback && callback(ret);
						});
					})(i);
				}
			};
		} else if (platform() == "mp") {
			wx.chooseMessageFile({
				count: max != 0 ? max : 9,
				type: "file",
				success: function success(res) {
					for (var i = 0; i < res.tempFiles.length; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = res.tempFiles[j];
							uploadFile(nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				}
			});
		}
	}

	//转换成html格式
	function convertRichText(value) {
		value = (isParameters(value) ? value : "") + "";
		if (isObject(value) || isArray(value)) {
			value = JSON.stringify(value);
		}
		var textList = value.split("\n");
		var str = "";
		for (var i = 0; i < textList.length; i++) {
			var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
			if (addText) {
				str += "<p>" + addText + "</p>";
			}
		}
		return str;
	}

	function identifyManager(_type, _callback) {
		if (_type == 1) {
			actionSheet(
				{
					title: "请选择图片来源",
					buttons: ["相机", "相册"]
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					getPicture(
						{
							sourceType: _index == 1 ? "camera" : "photos",
							destinationType: "base64",
							useOne: true
						},
						function(ret, err) {
							var dataUrl = ret ? ret.base64Data || ret.data : "";
							if (dataUrl) {
								identifyPic(dataUrl, _callback);
							}
						}
					);
				}
			);
		} else {
			G$1.identifyAudioPop = {
				show: true,
				type: 1,
				callback: function callback(ret) {
					_callback(ret);
				}
			};
		}
	}

	function identifyPic(_base64, _callback, again) {
		if (!again) {
			showProgress("转换中");
		}
		if (!G$1.authTokens) {
			ajax(
				{u: tomcatAddress() + "utils2/huawei?type=authTokens"},
				"HuaWeiTokens",
				function(ret, err) {
					G$1.authTokens = ret ? ret.result || "" : "";
					if (G$1.authTokens) {
						identifyPic(_base64, _callback, true);
					} else {
						hideProgress();
						toast(NET_ERR);
					}
				},
				"华为token",
				"get",
				{},
				{"content-type": "application/x-www-form-urlencoded"}
			);
			return;
		}
		var postValue = {
			image: _base64.replace(/data:image\/(.*?);base64,/, ""),
			detect_direction: true
		};

		ajax(
			{u: tomcatAddress() + "utils2/huawei"},
			"ocrText",
			function(ret, err) {
				hideProgress();
				if (!ret) {
					toast(NET_ERR);
					return;
				}
				var error_code = ret.error_code || "";
				if (error_code == "APIG.0301") {
					G$1.authTokens = "";
				}
				var result = ret.result || {};
				var words_block_list = result.words_block_list || [];
				if (!isArray(words_block_list) || words_block_list.length == 0) {
					toast("抱歉，未识别到文字");
					return;
				}
				var nowText = "",
					leftNum = -1;
				(rightNum = -1), (maxTextLength = 0), (mTextSize = 0);
				for (var i = 0; i < words_block_list.length; i++) {
					var current_rect = words_block_list[i].location;
					var current_text = (words_block_list[i].words || "").replace(
						/[^\u4e00-\u9fa5]/gi,
						""
					);
					if (current_text.length > mTextSize) mTextSize = current_text.length;
					if (isArray(current_rect)) {
						var nLeftNum = current_rect[0][0];
						var nRightNum = current_rect[1][0];
						if (leftNum == -1 || nLeftNum < leftNum) leftNum = nLeftNum;
						if (rightNum == -1 || nRightNum > rightNum) rightNum = nRightNum;
					}
				}
				if (leftNum != -1 && rightNum != -1) {
					maxTextLength = rightNum - leftNum;
				}
				for (var i = 0; i < words_block_list.length; i++) {
					var current_text = words_block_list[i].words;
					var current_rect = words_block_list[i].location;
					if (maxTextLength) {
						var nLeftNum = current_rect[0][0];
						var nRightNum = current_rect[1][0];
						if (Math.abs(nLeftNum - leftNum) > (maxTextLength / mTextSize) * 2) {
							current_text = "\n" + current_text;
						}
						if (Math.abs(nRightNum - rightNum) > (maxTextLength / mTextSize) * 2) {
							current_text = current_text + "\n";
						}
					}
					nowText += current_text;
				}
				alert("识别成功，请仔细核对该内容");
				_callback(nowText);
			},
			"图文识别",
			"post",
			{
				values: {
					type: "ocrText",
					token: G$1.authTokens,
					value: JSON.stringify(postValue)
				}
			},
			{"content-type": "application/x-www-form-urlencoded"}
		);
	}

	// import dayjs from "./dayjs";
	// import { MD5 } from './crypto-ts.js';
	/**
	 * 封装和适配 api相关所有接口 和一些别的
	 */
	var T = {};
	T.NET_ERR = "网络不小心断开了";
	T.NET_OK = "操作成功";
	T.NET_NO = "操作失败，请重试";
	T.JK_ERR = "接口异常，请联系技术";
	T.LOAD_ING = "加载中，请稍候...";
	T.LOAD_MORE = "点击加载更多";
	T.LOAD_ALL = "已加载完";
	T.LOAD_NO = "暂无数据";
	T.LOAD_NOT = "页面尚未加载完成，请刷新重试";

	T.isParameters = function(obj) {
		return obj != null && obj != undefined;
	};
	T.isTargetType = function(obj, typeString) {
		return typeof obj === typeString;
	};
	T.isNumber = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "number");
	};
	T.isObject = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "object");
	};
	T.isArray = function(obj) {
		return T.isParameters(obj) && toString.apply(obj) === "[object Array]";
	};
	T.isFunction = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "function");
	};

	T.setNewJSON = function(obj, newobj, _ifReplace) {
		obj = obj || {};
		newobj = newobj || {};
		var returnObj = {};
		for (var key in obj) {
			returnObj[key] = obj[key];
		}
		for (var key in newobj) {
			if (_ifReplace && returnObj.hasOwnProperty(key)) {
				//是否不替换前者 默认替换
				continue;
			}
			returnObj[key] = newobj[key];
		}
		return returnObj;
	};

	T.getNum = function() {
		return Math.round(Math.random() * 1000000000);
	};

	T.trim = function(str) {
		if (String.prototype.trim) {
			return str == null ? "" : String.prototype.trim.call(str);
		} else {
			return str.replace(/(^\s*)|(\s*$)/g, "");
		}
	};
	T.trimAll = function(str) {
		return str.replace(/\s*/g, "");
	};
	T.removeTag = function(str) {
		if (!str) return str;
		return T.decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	};
	T.decodeCharacter = function(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	};

	T.platform = function() {
		try {
			return api.platform || "";
		} catch (e) {
			return "";
		}
	};

	T.rebootApp = function() {
		if (T.platform() == "web") {
			window.location.reload();
		} else if (T.platform() == "mp") {
			wx.reLaunch({url: "/pages/index/index"});
		} else {
			api.rebootApp();
		}
	};

	T.appName = function() {
		try {
			return myjs.appName || "";
		} catch (e) {
			return "";
		}
	};

	T.systemType = function() {
		try {
			return api.systemType;
		} catch (e) {
			return "";
		}
	};

	T.pageParam = function(_this) {
		try {
			var pageParam =
				(_this && _this.props ? _this.props.pageParam : null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(T.getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (T.platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	};

	T.safeArea = function() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	};

	T.setPrefs = function(key, value) {
		if (!T.isParameters(value)) {
			T.removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {}
	};

	T.getPrefs = function(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			return "";
		}
	};

	T.removePrefs = function(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {}
	};

	T.addEventListener = function(name, callback) {
		var keyback = function keyback(ret, err) {
			T.isFunction(callback) && callback(ret, err);
		};
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			if (!window.baseEventList) {
				window.baseEventList = [];
			}
			if (G.getItemForKey(name, window.baseEventList)) {
				G.delItemForKey(name, window.baseEventList);
			}
			window.baseEventList.push({
				key: name,
				value: keyback
			});
		} else {
			api.addEventListener({name: name}, keyback);
		}
	};

	T.removeEventListener = function(name) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			G.delItemForKey(name, window.baseEventList);
		} else {
			api.removeEventListener({name: name});
		}
	};

	T.sendEvent = function(name, extra) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			var pageframes = window.parent.document.getElementsByTagName("iframe");
			for (var i = 0; i < pageframes.length; i++) {
				if (T.isArray(pageframes[i].contentWindow.baseEventList)) {
					var sendItem = G.getItemForKey(
						T.isObject(name) ? name.name : name,
						pageframes[i].contentWindow.baseEventList
					);
					if (sendItem) {
						sendItem.value({value: T.isObject(name) ? name.extra : extra});
					}
				}
			}
		} else {
			try {
				api.sendEvent(T.isObject(name) ? name : {name: name, extra: extra});
			} catch (e) {}
		}
	};

	T.removeLaunchView = function() {
		try {
			api.removeLaunchView();
		} catch (e) {}
	};

	T.setScreenOrientation = function(orientation) {
		try {
			api.setScreenOrientation({orientation: orientation});
		} catch (e) {}
	};

	T.cancelAjax = function(name) {
		try {
			api.cancelAjax({tag: name});
		} catch (e) {}
	};

	T.ajax = function(url, tag, callback, logText, method, data, header) {
		if (header === void 0) {
			header = {};
		}
		T.cancelAjax(tag);
		var getUrl = url; //请求链接
		var frequency = 0; //网络异常 重复请求次数
		var dataType = "json"; //返回数据类型
		var cacheType = ""; //请求类型
		var paramData = {};
		var areaId = "";
		var timeout = 0;
		var isWeb = "";
		if (T.isObject(url)) {
			getUrl = url.u; //请求链接
			dataType = url.dt || "json"; //
			cacheType = url.t || ""; //
			frequency = url.frequency || 0;
			paramData = url.paramData || {};
			areaId = url.areaId || myjs.areaId(url._this);
			timeout = url.timeout || 0;
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method ? method : "get",
			cache: false,
			timeout: 50,
			dataType: dataType,
			data: T.isObject(data) ? data : {},
			headers: T.setNewJSON(
				{
					"u-login-areaId": areaId,
					Authorization: T.getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header
			)
		};

		o = T.setNewJSON(o, paramData);
		if (T.platform() == "web") {
			delete o.tag;
		}
		if (o.url.indexOf("push/rongCloud") != -1) {
			//融云接口
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					(T.getPrefs("sys_appRongKey") == "y745wfm84be5v" ? 1 : 2);
			} else if (o.data.values) {
				o.data.values.environment =
					T.getPrefs("sys_appRongKey") == "y745wfm84be5v" ? 1 : 2;
			}
		}
		if (
			isWeb &&
			JSON.stringify(o.data) != "{}" &&
			(o.data.body || JSON.stringify(o.data.values) != "{}")
		) {
			//公众端通用 过期时间不传token且置空public_token
			var webToken = "";
			if (
				T.getPrefs("tokenEndTime") &&
				new Date().getTime() < T.getPrefs("tokenEndTime")
			) {
				webToken = T.getPrefs("public_token") || "";
			} else {
				T.removePrefs("public_token");
			}
			(o.headers.Authorization = header.Authorization || webToken || ""),
				(o.headers["u-terminal"] = "PUBLIC");
			// var isBody = o.data.body?true:false;
			// var postParam = isBody?JSON.parse(o.data.body):o.data.values;
			// var signParam = {};
			// function getParam(_obj){
			// 	// console.log(JSON.stringify(_obj));
			// 	for (var key in _obj) {
			// 		var kValue = _obj[key];
			// 		if(T.isObject(kValue) && !T.isArray(kValue)){
			// 			getParam(kValue);
			// 		}else{
			// 			kValue = T.isArray(kValue)?kValue.join(","):kValue;
			// 			if (signParam.hasOwnProperty(key)) {
			// 				signParam[key] += (signParam[key]?',':'') + kValue;
			// 			}else{
			// 				signParam[key] = kValue;
			// 			}
			// 		}
			// 	}
			// }
			// getParam(postParam);
			// var signStr = T.sort_ascii(signParam,"#");
			// postParam.clientId = M.clientId;
			// postParam.token = isWeb;
			// postParam.timestamp = dayjs().valueOf();
			// postParam.nonce = "zyrd";
			// postParam.sign = MD5(signStr + "#" + M.clientId + isWeb + postParam.timestamp + postParam.nonce).toString().toUpperCase()
			// if(isBody){
			// 	o.data.body = JSON.stringify(postParam);
			// }
		}
		var oldContentType = o.headers["content-type"];
		if (myjs.proxy && T.platform() != "app" && o.url.indexOf("https") != 0) {
			//小程序 使用代理 T.platform() == "mp" &&
			var oldUrl = o.url;
			var proxyUrl = myjs.tomcatAddress() + "utils/proxy";
			if (oldUrl.indexOf("?") != -1) {
				o.url = proxyUrl + oldUrl.substring(oldUrl.indexOf("?"));
				oldUrl = oldUrl.substring(0, oldUrl.indexOf("?"));
			} else {
				o.url = proxyUrl;
			}
			o.url +=
				(o.url.indexOf("?") != -1
					? o.url.substring(o.url.indexOf("?")) == "?"
						? ""
						: "&"
					: "?") +
				"BASE_URL=" +
				oldUrl;
			o.url += "&BASE_TYPE=" + oldContentType;
			o.headers["content-type"] = "application/x-www-form-urlencoded";
		}
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (T.platform() == "app" && logText) {
			if (o.method == "post") {
				console.log(logText + "post【" + frequency + "】：" + JSON.stringify(o));
			} else {
				console.log(logText + "get【" + frequency + "】：" + JSON.stringify(o));
			}
		}
		try {
			var cbFun = function cbFun(ret, err) {
				// if(T.isObject(url) && T.isParameters(url._this.ajax)){
				// 	url._this.ajax = true;
				// }
				if (T.isFunction(callback)) {
					if (err) {
						try {
							ret = JSON.parse(err.msg);
							err = null;
						} catch (e) {
							ret = JSON.parse(JSON.stringify(err));
							err = null;
						}
					}
					if (err) {
						// if (frequency > 0) {
						// 	var frequencyUrl = url;
						// 	frequencyUrl.frequency--;
						// 	T.ajax(frequencyUrl, tag, callback, logText, method, data, header);
						// 	return;
						// }
					}
					if (T.isObject(ret)) {
						var isEncrypt = ret.isEncrypt;
						if (isEncrypt && ret.data) {
							ret.data = sm4decrypt(ret.data);
							try {
								ret.data = JSON.parse(ret.data);
							} catch (e) {
								ret.data = ret.data;
							}
						}
					}
					if (T.platform() == "app" && logText) {
						if (ret)
							console.log("得到" + logText + "返回结果ret：" + JSON.stringify(ret));
						if (err)
							console.log("得到" + logText + "返回结果err：" + JSON.stringify(err));
					}
					if (ret) {
						ret.message = ret.message || ret.msg || "";
						var errcode = ret.code || "";
						if ((errcode == 302 || errcode == 2) && cacheType != "login") {
							//令牌失效
							T.hideProgress();
							T.sendEvent({
								name: "index",
								extra: {type: "verificationToken", errmsg: ret.message}
							});
							// return;
						}
					}
					callback(ret, err, true);
				}
			};
			setTimeout(function() {
				if (T.platform() == "web") {
					var xhr = new XMLHttpRequest();
					xhr.open(o.method, o.url);
					for (var header in o.headers) {
						xhr.setRequestHeader(header, o.headers[header]);
					}
					var sendValue = "";
					if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
						var dValue = o.data.values || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue +=
								(!sendValue ? "" : "&") +
								vItem +
								"=" +
								encodeURIComponent(dValue[vItem]);
						}
					} else if (oldContentType.indexOf("file") != -1) {
						sendValue = new FormData();
						var dValue = o.data.values || {};
						var fileValue = o.data.files || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
						}
						for (var vItem in fileValue) {
							sendValue.append(vItem, fileValue[vItem]);
						}
					} else {
						sendValue = o.data.body || JSON.stringify(o.data.values); //encodeURIComponent web加了之后 不能传递json了
					}
					xhr.send(sendValue);
					xhr.onreadystatechange = function() {
						if (xhr.readyState === XMLHttpRequest.DONE) {
							if (xhr.responseText) {
								var response = this.responseText;
								if (o.dataType == "json") {
									var isJSON = false;
									try {
										response = JSON.parse(response);
										isJSON = true;
									} catch (e) {
										isJSON = false;
									}
									if (isJSON) {
										cbFun(response, null);
									} else {
										cbFun(null, response);
									}
								} else {
									cbFun(response, null);
								}
							} else {
								cbFun(null, {});
							}
						}
					};
				} else {
					api.ajax(o, cbFun);
				}
			}, timeout);
		} catch (e) {
			console.log(e);
		}
	};

	T.openWin = function(name, url, pageParam, _this, allowEdit, _more) {
		var delay = 0;
		url = T.handleSYSLink(url, _this); //先处理跳转链接
		var o = {
			name: name,
			url: T.platform() == "web" ? url.substring(url.lastIndexOf("/") + 1) : url,
			pageParam: pageParam ? pageParam : {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: delay,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (T.isObject(_more)) {
			o = T.setNewJSON(o, _more);
		}
		o.pageParam.headTheme =
			(_this && _this.data && _this.data.headTheme
				? _this.data.headTheme || ""
				: "") ||
			G.headTheme ||
			"";
		o.pageParam.appTheme = G.appTheme || "";
		o.pageParam.areaId = o.pageParam.areaId || myjs.areaId(_this);
		o.pageParam.v = G.v;
		if (o.pageParam.paramSaveKey) {
			T.setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		api.openWin(o);
	};

	T.closeWin = function(_param) {
		var o = {};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				T.removePrefs(api.pageParam.paramSaveKey);
			}
			api.closeWin(o);
		} catch (e) {}
	};

	T.clearCache = function(callback) {
		var o = {};
		try {
			api.clearCache(o, function(ret, err) {
				T.isFunction(callback) && callback(ret, err);
			});
		} catch (e) {}
	};

	T.toast = function(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: "middle",
			global: false
		};

		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.msg = T.isParameters(_param) ? _param : "";
			o.location = location || "middle";
			o.global = global;
		}
		o.msg = o.msg.toString();
		try {
			api.toast(o);
		} catch (e) {}
	};

	T.showProgress = function(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.title = T.isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		try {
			api.showProgress(o);
		} catch (e) {}
	};

	T.hideProgress = function() {
		try {
			api.hideProgress();
		} catch (e) {}
	};

	T.alert = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ["确定"]
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = T.isParameters(_param)?_param:"";
		// }
		// o.msg = o.msg.toString();
		// try{
		// 	api.alert(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.confirm = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ['确定', '取消']
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = _param;
		// }
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.confirm(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.actionSheet = function(_param, _callback) {
		G.actionSheet(_param, _callback);
		// var o = {
		// 	title: '请选择',
		// 	cancelTitle: '取消',
		// 	destructiveTitle: "",
		// };
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.actionSheet(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.getPicture = function(_param, callback) {
		if (!callback) {
			T.toast("请先设置callback");
			return;
		}
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = T.setNewJSON(o, _param);
		try {
			if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				//ios拍照需要加到真实dom能才进onchange
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (T.platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (T.platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!T.confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					//相机相册权限
					T.addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						T.removeEventListener(preName + "Per_" + "getPicture");
						if (ret.value.granted) {
							T.getPicture(_param, callback);
						}
					});
					return;
				}
				if (o.sourceType == "camera" || o.userOne) {
					api.getPicture(o, function(ret, err) {
						T.isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	};

	T.hasPermission = function(one_per) {
		if (T.platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				//判断一堆时 就自己看	一般是一个一个判断
				T.alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	};

	T.requestPermission = function(one_per, callback, _fName) {
		if (T.platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				//把结果 发监听过去
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					T.sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				T.isFunction(callback) && callback(ret, err);
			});
		}
	};

	T.confirmPer = function(perm, _fName, _reason) {
		if (T.platform() == "app") {
			var has = T.hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				T.confirm(
					{
						title: "无法使用" + hintWord[perm],
						msg: T.systemType() == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret, err) {
						if (1 == ret.buttonIndex) {
							T.requestPermission(perm, null, _fName);
						} else {
							T.sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	};

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	T.handleSYSLink = function(_link, _this, myParam) {
		if (_link === void 0) {
			_link = "";
		}
		// if(_link.indexOf("http") != 0){
		// 	return _link;
		// }
		myParam = myParam || {};
		//index.html?token={{token}}&userId={{userId}}
		_link = _link.replace("{{tomcatAddress}}", myjs.tomcatAddress());
		_link = _link.replace("{{shareAddress}}", myjs.shareAddress());
		_link = _link.replace(
			"{{token}}",
			encodeURIComponent(T.getPrefs("sys_token"))
		); //当前app登录用户的token，例如：bearer eyJhbGciOiJ...
		_link = _link.replace("{{sysUrl}}", myjs.appUrl()); //当前app请求系统地址，例如：http://**************:54386/lzt/
		_link = _link.replace("{{areaId}}", myParam.areaId || myjs.areaId(_this)); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", myjs.iszx); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			//是app内页面 带上特有参数如果没有
			if (_link.indexOf("sysUrl-zy-") == -1) {
				//没有带地址
				_link += "-zyz-sysUrl-zy-" + myjs.appUrl();
			}
			if (_link.indexOf("sysAreaId-zy-") == -1) {
				//没有带地区
				_link += "-zyz-sysAreaId-zy-" + (myParam.areaId || myjs.areaId(_this));
			}
			if (_link.indexOf("iszx-zy-") == -1) {
				//没有带人大政协判断
				_link += "-zyz-iszx-zy-" + myjs.iszx;
			}
			if (_link.indexOf("appTheme-zy-") == -1) {
				//没有带主题
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			}
			if (_link.indexOf("careMode-zy-") == -1) {
				//没有唯一标识
				_link += "-zyz-careMode-zy-" + G.careMode;
			}
		}
		return _link;
	};

	//按照ascii 排序
	T.sort_ascii = function(obj, _default) {
		var arr = [];
		var num = 0;
		for (var i in obj) {
			arr[num] = i;
			num++;
		}
		var sortArr = arr.sort();
		var str = "";
		for (var _i = 0; _i < sortArr.length; _i++) {
			var sValue = obj[sortArr[_i]];
			str +=
				sortArr[_i] +
				"=" +
				(T.isTargetType(sValue, "number")
					? sValue
					: T.isObject(sValue)
					? JSON.stringify(sValue)
					: sValue || _default) +
				"&";
		}
		var char = "&";
		str = str.replace(new RegExp("^\\" + char + "+|\\" + char + "+$", "g"), "");
		return str;
	};

	/** 判断颜色属于深色还是浅色*/
	T.isColorDarkOrLight = function(hexcolor) {
		try {
			var colorrgb = T.colorRgb(hexcolor);
			var colors = colorrgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
			var red = colors[1];
			var green = colors[2];
			var blue = colors[3];
			var brightness;
			brightness = red * 299 + green * 587 + blue * 114;
			brightness = brightness / 255000;
			if (brightness >= 0.5) {
				return "light";
			} else {
				return "dark";
			}
		} catch (e) {
			return "";
		}
	};

	//16进制颜色转化为RGB颜色
	T.colorRgb = function(str) {
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var sColor = str.toLowerCase();
		if (sColor && reg.test(sColor)) {
			if (sColor.length === 4) {
				var sColorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
				}
				sColor = sColorNew;
			}
			var sColorChange = [];
			for (var i = 1; i < 7; i += 2) {
				sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
			}
			return "rgb(" + sColorChange.join(",") + ")";
		} else {
			return sColor;
		}
	};

	/** 16进制颜色 转换成rgba颜色	可设置透明 */
	T.colorRgba = function(_color, _alpha) {
		if (!_color) return;
		// 16进制颜色值的正则
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		// 把颜色值变成小写
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(T.isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	};

	//添加list中参数
	function addPostParam(_list) {
		var param = {};
		for (var i = 0; i < _list.length; i++) {
			var _eItem = _list[i];
			if (!_eItem.key || _eItem.notUpdate || _eItem.hide) {
				continue;
			}
			if (
				!_eItem.hide &&
				!_eItem.noRequired &&
				!(isArray(_eItem.value)
					? _eItem.value.length > 0
					: isNumber(_eItem.value)
					? true
					: _eItem.value)
			) {
				var dhint;
				switch (_eItem.type) {
					case "input":
					case "textarea":
					case "captcha":
						dhint = "请输入";
						break;
					case "attach":
					case "picture":
					case "signature":
					case "user":
						dhint = "请添加";
						break;
					default:
						dhint = "请选择";
						break;
				}

				dhint += _eItem.title || "";
				toast(_eItem.hint || dhint);
				return;
			}
			if (_eItem.type == "switch") {
				param[_eItem.key] = _eItem.value ? 1 : 0;
			} else if (isArray(_eItem.value)) {
				//数组转换
				if (isNumber(_eItem.min) && _eItem.value.length < _eItem.min) {
					toast(
						"\u8BF7\u9009\u62E9\u81F3\u5C11" + _eItem.min + "\u4E2A" + _eItem.title
					);
					return;
				}
				if (_eItem.valueType == "array") {
					param[_eItem.key] = _eItem.value.map(function(obj) {
						return isObject(obj) ? obj[_eItem.valueKey || "id"] : obj;
					});
				} else {
					param[_eItem.key] = _eItem.value
						.map(function(obj) {
							return isObject(obj) ? obj[_eItem.valueKey || "id"] : obj;
						})
						.join(_eItem.valueType || ",");
				}
			} else if (_eItem.type == "text" && _eItem.textType == "time") {
				//时间转换
				param[_eItem.key] = dayjs(_eItem.value).valueOf();
			} else if (_eItem.html) {
				//内容需要转换成html
				param[_eItem.key] = convertRichText(_eItem.value);
			} else {
				param[_eItem.key] = _eItem.value;
			}
		}
		return param;
	}

	//将二级提交参数 变更为一级
	function setParamToFirst(_list, _param) {
		if (!_param.form) return;
		(_list || []).forEach(function(_eItem) {
			if (!_eItem.key || _eItem.notUpdate || _eItem.hide) return;
			if (_eItem.paramFirst) {
				//将参数提到form外 到一级
				_param[_eItem.key] = _param.form[_eItem.key];
				delete _param.form[_eItem.key];
			}
		});
	}

	var ZTextarea = /*@__PURE__*/ (function(Component) {
		function ZTextarea(props) {
			Component.call(this, props);
			this.data = {
				textareaId: this.props.id || "z_textarea" + getNum()
			};
		}

		if (Component) ZTextarea.__proto__ = Component;
		ZTextarea.prototype = Object.create(Component && Component.prototype);
		ZTextarea.prototype.constructor = ZTextarea;
		ZTextarea.prototype.installed = function() {
			var this$1 = this;

			this.props.dataMore.textareaId = this.data.textareaId;
			if (this.props.dataMore.autoFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.textareaId) &&
						document.getElementById(this$1.data.textareaId).focus();
				}, 500);
			}
		};
		ZTextarea.prototype.input = function(e) {
			var value = e.detail.value;
			if (this.props.dataMore.expression) {
				value = value.replace(new RegExp(this.props.dataMore.expression, "g"), "");
			}
			this.props.dataMore.value = value;
			this.fire("input", e.detail);
		};
		ZTextarea.prototype.keyboardheightchange = function(e) {
			G$1.inputIng = e.detail.height > 0;
		};
		ZTextarea.prototype.blur = function(e) {
			G$1.inputIng = false;
			this.fire("blur", e.detail);
		};
		ZTextarea.prototype.focus = function(e) {
			document.getElementById(this.data.textareaId) &&
				document.getElementById(this.data.textareaId).focus();
			this.fire("focus", e.detail);
		};
		ZTextarea.prototype.render = function() {
			return apivm.h("textarea", {
				id: this.data.textareaId,
				style:
					"" +
					loadConfiguration() +
					(api.systemType == "android" ? "min-" : "") +
					"height: " +
					(this.props.dataMore.height || "250") +
					"px;" +
					(this.props.style || ""),
				class: "z_textarea " + (this.props.class || ""),
				placeholder:
					this.props.dataMore.replyPlaceholder ||
					this.props.dataMore.placeholder ||
					this.props.dataMore.hint ||
					"请输入" + (this.props.dataMore.title || ""),
				"placeholder-style": "color:#ccc;",
				"auto-height": api.systemType == "android",
				value: this.props.dataMore.value,
				onInput: this.input,
				onBlur: this.blur,
				onFocus: this.focus,
				onKeyboardheightchange: this.keyboardheightchange,
				maxlength: this.props.dataMore.maxlength || this.props.dataMore.max
			});
		};

		return ZTextarea;
	})(Component);
	ZTextarea.css = {
		".z_textarea": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			padding: "0",
			width: "100%",
			fontFamily: "none"
		},
		".z_textarea::placeholder": {color: "#ccc"}
	};
	apivm.define("z-textarea", ZTextarea);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						colorRgba(
							this.props.color || G$1.appTheme,
							this.props.type == 2 ? 0.1 : 1
						) +
						";border-width:" +
						(this.props.type == 2 ? 0 : 1) +
						"px;background:" +
						(this.props.plain
							? "#FFF"
							: colorRgba(
									this.props.color || G$1.appTheme,
									this.props.type == 2 ? 0.1 : 1
							  )) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G$1.appTheme : "#FFF",
						size: G$1.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain || this.props.type == 2
								? this.props.color || G$1.appTheme
								: "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var IdentifyAudio = /*@__PURE__*/ (function(Component) {
		function IdentifyAudio(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				isStart: false,
				volume: 0,
				textarea: {
					value: ""
				},

				countdown: 0
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.baseInit();
						} else {
							switch (platform()) {
								case "app":
									this.voiceRecognizer.recognizeCancel();
									break;
								case "web":
									try {
										stopRecognize();
									} catch (e) {}
									break;
							}
						}
					}
				}
			};
		}

		if (Component) IdentifyAudio.__proto__ = Component;
		IdentifyAudio.prototype = Object.create(Component && Component.prototype);
		IdentifyAudio.prototype.constructor = IdentifyAudio;
		IdentifyAudio.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			this.data.textarea.value = "";
			if (dm.type == 1) {
				switch (platform()) {
					case "app":
						this.voiceRecognizer = api.require("voiceRecognizer");
						this.voiceRecognizer.createUtility(
							{ios_appid: "5b581b4e", android_appid: "5b581b4e"},
							function(ret, err) {}
						);
						this.voiceRecognizer.recognizeConfig(
							{
								config: {
									vadbos: "5000",
									vadeos: "5000",
									timeout: "30000",
									netTimeout: "20000",
									rate: "16000",
									dot: true
								}
							},

							function(ret) {}
						);
						this.voiceRecognizer.addEventListener(
							{name: "recognizeResult", realTime: true},
							function(ret) {
								ret = ret.recognizeResult;
								this$1.data.textarea.value += ret.result;
							}
						);
						this.voiceRecognizer.addEventListener({name: "onError"}, function(ret) {
							console.log("onError" + JSON.stringify(ret));
						});
						this.voiceRecognizer.addEventListener({name: "onEndOfSpeech"}, function(
							ret
						) {
							if (this$1.data.isStart) {
								//还在识别 说明是超时了 但是还按着
								this$1.data.textarea.value += "。";
								this$1.voiceRecognizer.recognizeStart();
							}
						});
						this.voiceRecognizer.addEventListener({name: "volume"}, function(ret) {
							this$1.data.volume = ret.volume;
						});
						break;
				}
			}
		};
		IdentifyAudio.prototype.textwarp = function() {
			$("#" + this.data.textarea.textareaId) &&
				$("#" + this.data.textarea.textareaId).focus();
		};
		IdentifyAudio.prototype.penetrate = function() {};
		IdentifyAudio.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		IdentifyAudio.prototype.touchstart = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			if (!confirmPer("microphone")) {
				//麦克风权限
				return;
			}
			this.data.isStart = true;
			this.realStart = false;
			this.cacheText = this.data.textarea.value;
			clearTimeout(this.task);
			this.task = setTimeout(function() {
				this$1.realStart = true;
				if (dm.type == 1) {
					switch (platform()) {
						case "app":
							this$1.voiceRecognizer.recognizeStart();
							break;
						case "web":
							this$1.data.countdown = 60;
							this$1.timeStart();
							try {
								startRecognize(function(ret) {
									this$1.data.textarea.value = this$1.cacheText + ret;
								});
							} catch (e) {}
							break;
					}
				}
			}, 150);
		};
		IdentifyAudio.prototype.touchend = function() {
			var dm = this.props.dataMore;
			this.data.isStart = false;
			clearTimeout(this.task);
			this.data.countdown = 0;
			clearTimeout(this.countdownTask);
			if (this.realStart) {
				if (dm.type == 1) {
					switch (platform()) {
						case "app":
							this.voiceRecognizer.recognizeStop();
							break;
						case "web":
							try {
								stopRecognize();
							} catch (e) {}
							break;
					}
				}
			}
			this.realStart = false;
		};
		IdentifyAudio.prototype.confirmBtn = function() {
			if (this.data.isStart) {
				return;
			}
			this.props.dataMore.callback(this.data.textarea.value);
			this.closePage();
		};
		IdentifyAudio.prototype.timeStart = function() {
			var this$1 = this;

			if (this.data.countdown <= 0) {
				this.touchend();
				return;
			}
			this.countdownTask = setTimeout(function() {
				this$1.data.countdown--;
				this$1.timeStart();
			}, 1000);
		};
		IdentifyAudio.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h(
					"view",
					{
						onClick: function() {
							return this$1.closePage();
						},
						style: "height:20%;flex-shrink: 0;"
					},
					apivm.h("view", {class: "flex_h"}),
					apivm.h(
						"view",
						{class: "xy_center"},
						this.data.isStart &&
							apivm.h("image", {
								mode: "aspectFit",
								style: loadConfigurationSize([25, 5]) + "margin-bottom:5px;",
								thumbnail: "false",
								src: shareAddress(1) + "image/icon_voice_in.gif"
							})
					)
				),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G$1.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G$1.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) + "font-weight: 600;color:" + G$1.headColor
									},
									this.props.dataMore.title || "语音输入",
									this.data.countdown > 0 ? "(" + this.data.countdown + ")" : ""
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) +
												"color:" +
												(this.data.isStart ? "#B2B2B2" : G$1.appTheme) +
												";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{class: "flex_h"},
							apivm.h(
								"view",
								{class: "flex_h"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.textwarp();
										},
										class: "commentBig_box"
									},
									apivm.h("z-textarea", {dataMore: this.data.textarea})
								)
							),
							apivm.h(
								"view",
								null,
								this.data.isStart &&
									this.data.volume > 0 &&
									apivm.h("view", {
										style:
											"width:" +
											(this.data.volume * 100) / 30 +
											"%;height:3px;background:" +
											G$1.appTheme +
											";"
									})
							),
							apivm.h(
								"view",
								{style: "padding:30px;", class: "xy_center"},
								apivm.h(
									"view",
									{
										onTouchStart: this.touchstart,
										onTouchEnd: this.touchend,
										class: "ida_btn xy_center"
									},
									apivm.h("a-iconfont", {
										name: "yuyin",
										color: this.data.isStart ? "#B2B2B2" : G$1.appTheme,
										size: G$1.appFontSize + 12
									})
								),
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-4) +
											"color:#666;text-align: center;margin:15px 0;"
									},
									"长按识别录音，松手后请确定"
								)
							)
						),
						apivm.h("view", {style: "padding-bottom:" + safeArea().bottom + "px;"})
					)
			);
		};

		return IdentifyAudio;
	})(Component);
	IdentifyAudio.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".commentBig_box": {
			minHeight: "110px",
			maxHeight: "250px",
			marginBottom: "10px",
			padding: "10px 16px"
		},
		".ida_btn": {
			width: "100px",
			height: "100px",
			borderTopLeftRadius: "100px",
			borderTopRightRadius: "100px",
			borderBottomRightRadius: "100px",
			borderBottomLeftRadius: "100px",
			background: "#F9F9F9",
			boxShadow: "0px 3px 15px rgba(7, 20, 45, 0.15)"
		}
	};
	apivm.define("identify-audio", IdentifyAudio);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				dotClose: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.dotClose = true;
							setTimeout(function() {
								this$1.data.dotClose = false;
							}, 300);
						} else {
							G$1.actionSheetPop.pageClose();
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.closePage = function() {
			if (this.data.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			if (this.data.dotClose || _item.disabled) {
				return;
			}
			_item.buttonIndex = _index + 1;
			G$1.actionSheetPop.callback(_item);
			this.closePage();
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h("view", {
						onClick: function() {
							return this$1.closePage();
						},
						class: "actionSheet_cancel"
					}),
					apivm.h(
						"view",
						{
							class: this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "flex-shrink: 0;"
						},
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					apivm.h(
						"scroll-view",
						{
							class: !this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "height: auto;background: #FFF;flex-shrink: 1;",
							"scroll-y": true
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G$1.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G$1.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						(this.props.dataMore.data || []).map(function(item, index) {
							return (
								(isParameters(item.show) ? item.show : true) && [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.itemClick(item, index);
											},
											class: "actionSheet_item",
											style:
												"justify-content:" +
												(item.justify || "center") +
												";opacity: " +
												(item.disabled ? "0.3" : "1") +
												";"
										},
										apivm.h(
											"view",
											null,
											item.icon &&
												apivm.h("a-iconfont", {
													style: "margin-right:10px;",
													name: item.icon,
													color: (isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G$1.appTheme
														: item.color || "#333",
													size:
														G$1.appFontSize +
														(isParameters(item.size) ? Number(item.size) : 4)
												})
										),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-1) +
													";color:" +
													((isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G$1.appTheme
														: item.color || "#333")
											},
											item.name
										)
									),
									index != this$1.props.dataMore.data.length - 1 &&
										!this$1.props.dataMore.cancel &&
										apivm.h(
											"view",
											{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
											apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
										)
								]
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-shrink: 0;"},
						this.props.dataMore.cancel &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "actionSheet_item",
									style:
										"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + ";color:#333"},
									this.props.dataMore.cancel || "取消"
								)
							)
					),
					apivm.h("view", {
						style:
							"background:#fff;flex-shrink: 0;padding-bottom:" +
							safeArea().bottom +
							"px;"
					})
				]
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_cancel": {flex: "1", minHeight: "20%", flexShrink: "0"},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "5px 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var PreviewerImg = /*@__PURE__*/ (function(Component) {
		function PreviewerImg(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				frameName: "previewerImg"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.props.dataMore.watermark = G$1.watermark;
							this.baseInit();
						} else {
							if (platform() == "app" && this.UIPhotoViewer) {
								this.UIPhotoViewer.close();
							}
						}
					}
					if (this.data.show) {
						if (this.nowParam != JSON.stringify(this.props.dataMore)) {
							this.nowParam = JSON.stringify(this.props.dataMore);
							this.sendMessage();
						}
					}
				}
			};
		}

		if (Component) PreviewerImg.__proto__ = Component;
		PreviewerImg.prototype = Object.create(Component && Component.prototype);
		PreviewerImg.prototype.constructor = PreviewerImg;
		PreviewerImg.prototype.baseInit = function() {
			var this$1 = this;

			var data = this.props.dataMore;
			switch (platform()) {
				case "app":
					if (G$1.watermark) {
						//有水印使用web
						addEventListener(this.data.frameName + "_msg", function() {
							this$1.closePage();
						});
						api.setFrameClient({frameName: this.data.frameName}, function(ret, err) {
							if (ret.state == 2) {
								this$1.isLoading = true;
								setTimeout(function() {
									this$1.sendMessage();
								}, 400);
							}
						});
						return;
					}
					this.UIPhotoViewer = api.require("UIPhotoViewer");
					this.UIPhotoViewer.open(
						{
							images: data.imgs,
							activeIndex: data.index,
							gestureClose: true,
							bgColor: "#000"
						},
						function(ret, err) {
							switch (ret.eventType) {
								case "click":
									this$1.closePage();
									break;
								case "gestureColse":
									this$1.UIPhotoViewer = null;
									this$1.closePage();
									break;
								case "longPress":
									var nowImg = data.imgs[ret.index];
									api.actionSheet(
										{
											buttons: ["保存到相册"],
											cancelTitle: "取消"
										},
										function(ret) {
											switch (ret.buttonIndex) {
												case 1:
													api.saveMediaToAlbum(
														{
															path: nowImg
														},
														function(ret) {
															toast(ret && ret.status ? "已保存到手机相册" : "保存失败");
														}
													);
													break;
											}
										}
									);
									break;
							}
						}
					);
					break;
				case "mp":
					wx.previewImage({
						urls: data.imgs,
						current: data.imgs[data.index],
						complete: function() {
							this$1.closePage();
						}
					});

					break;
				case "web":
					window.addEventListener("message", function(event) {
						this$1.closePage();
					});
					document
						.getElementById(this.data.frameName)
						.addEventListener("load", function() {
							this$1.isLoading = true;
							this$1.sendMessage();
						});
					break;
			}
		};
		PreviewerImg.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		PreviewerImg.prototype.sendMessage = function() {
			if (this.isLoading && this.props.dataMore.show) {
				if (platform() == "web") {
					if (!document.getElementById(this.data.frameName)) {
						return;
					}
					var targetWindow = document.getElementById(this.data.frameName)
						.contentWindow;
					targetWindow.postMessage(this.props.dataMore, "*");
				} else if (platform() == "app") {
					sendEvent(this.data.frameName + "_open", this.props.dataMore);
				}
			}
		};
		PreviewerImg.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0);"
				},
				this.props.dataMore.show &&
					(platform() == "web" || G$1.watermark) &&
					apivm.h("frame", {
						id: this.data.frameName,
						class: "xy_100",
						name: this.data.frameName,
						url: shareAddress() + "html/previewerImg.html",
						pageParam: {name: this.data.frameName},
						useWKWebView: true,
						scaleEnabled: true,
						allowEdit: true
					})
			);
		};

		return PreviewerImg;
	})(Component);
	apivm.define("previewer-img", PreviewerImg);

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				controls: true
			};
			this.compute = {
				getSrc: function() {
					var src = this.props.src;
					if (src.indexOf("http") != 0) {
						src = appUrl() + "file/preview/" + src;
					}
					return src;
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.firstPause = function() {
			var this$1 = this;

			if (!this.isPause) {
				this.isPause = true;
				if (platform() == "app" && !this.props.poster) {
					this.data.controls = false;
					setTimeout(function() {
						if (!this$1.props.autoplay) {
							document.getElementById(dealVideoId(this$1.props.src)) &&
								document.getElementById(dealVideoId(this$1.props.src)).pause();
						}
						this$1.data.controls = true;
					}, 50);
				} else {
					this.data.controls = true;
				}
			}
		};
		ZVideo.prototype.loadedmetadata = function(e) {
			if (api.systemType != "ios") {
				this.isPause = false;
				this.firstPause();
			}
		};
		ZVideo.prototype.play = function() {
			videoPlayPush(dealVideoId(this.props.src));
		};
		ZVideo.prototype.pause = function() {
			videoPlayRemove(dealVideoId(this.props.src));
		};
		ZVideo.prototype.render = function() {
			return apivm.h("video", {
				id: dealVideoId(this.props.src),
				style: "width:100%;height:" + G$1.pageWidth * 0.56 + "px;",
				controls: this.data.controls,
				autoplay:
					(platform() == "app" && !this.props.poster) || this.props.autoplay,
				onLoadedmetadata: this.loadedmetadata,
				onWaiting: this.firstPause,
				onPlay: this.play,
				onPause: this.pause,
				onEnded: this.pause,
				src: this.getSrc,
				poster: showImg(this.props.poster)
			});
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichTextDeal = /*@__PURE__*/ (function(Component) {
		function ZRichTextDeal(props) {
			Component.call(this, props);
		}

		if (Component) ZRichTextDeal.__proto__ = Component;
		ZRichTextDeal.prototype = Object.create(Component && Component.prototype);
		ZRichTextDeal.prototype.constructor = ZRichTextDeal;
		ZRichTextDeal.prototype.isNewLine = function(_type, _item, _index) {
			var label = (_item || {}).label || (this.props.bItem || {}).label || "";
			if (_type == 1 && this.props.isNewLine && !this.dealNew) {
				this.hasNewIndex = _index;
				this.dealNew = true;
			}
			var result;
			if (!_type) {
				result =
					!label ||
					(!(this.props.bItem || {}).text && /^(p|div|br|video)$/.test(label));
			} else {
				result =
					(_type == 1 ? this.hasNewIndex == _index : !label) ||
					/^(p|div|br|video)$/.test(label);
			}
			return result;
		};
		ZRichTextDeal.prototype.getSize = function(_item) {
			return (
				(this.props.size || 0) + (/^(sub|sup)$/.test((_item || {}).label) ? -6 : 0)
			);
		};
		ZRichTextDeal.prototype.getStyle = function(_item) {
			var style = "";
			var td = this.getStyles(_item.style, "text-decoration");
			if (td) {
				style += "text-decoration:" + td + ";";
			}
			if (/^(sub)$/.test(_item.label)) {
				style += ";padding-top:10px;";
			} else if (
				/^(strong)$/.test(_item.label) ||
				/^(strong)$/.test(_item.jointEle)
			) {
				style += ";font-weight: 800;";
			} else if (/^(a)$/.test(_item.label)) {
				style += ";color:blue;";
			} else if (/^(ins)$/.test(_item.label)) {
				style += ";text-decoration: underline;";
			} else if (/^(del)$/.test(_item.label)) {
				style += ";text-decoration: line-through;";
			}
			return style;
		};
		ZRichTextDeal.prototype.getStyles = function(_text, _item) {
			var match = new RegExp("\\b" + _item + '\\s*:\\s*([^;"\\s]+)').exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichTextDeal.prototype.nTouchmove = function() {
			touchmove();
		};
		ZRichTextDeal.prototype.onclick = function(e) {
			this.fire("click", e.detail);
		};
		ZRichTextDeal.prototype.openText = function(e, _item) {
			stopBubble(e);
			this.onclick({detail: _item});
			if (!_item.href) {
				return;
			}
			openWin_url({url: _item.href});
		};
		ZRichTextDeal.prototype.openImages = function(e, _item, _index) {
			var this$1 = this;

			stopBubble(e);
			var bItems = this.props.bItems || [],
				hasA = false;
			bItems.forEach(function(_eItem) {
				if (_eItem.label == "a" && _eItem.href) {
					this$1.openText(e, _eItem);
					hasA = true;
					return;
				}
			});
			if (hasA) {
				return;
			}
			var imgs = this.props.data.filter(function(item, index) {
				return item.label == "img";
			});
			var openParam = {
				index: getItemForKey(_item.index, imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			};

			if (this.props._this && this.props._this.openImages) {
				this.props._this.openImages(openParam);
			} else {
				openWin_imgPreviewer(openParam);
			}
		};
		ZRichTextDeal.prototype.expandShow = function(e) {
			stopBubble(e);
			this.props._this.expandShow();
		};
		ZRichTextDeal.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style:
						"flex-direction:row;flex-wrap: wrap;" +
						(this.isNewLine() ? "width:100%;flex-shrink: 0;" : "flex-shrink: 1;")
				},
				(this.props.data || []).map(function(item, index) {
					return [
						this$1.isNewLine() &&
							this$1.isNewLine(1, item, index) &&
							apivm.h("view", {style: "width:100%;"}),
						item.text && [
							item.text.split("").map(function(nItem, nIndex) {
								return apivm.h(
									"text",
									{
										style:
											loadConfiguration(this$1.getSize(item)) +
											"color:#333;line-height:" +
											(G$1.appFontSize + this$1.getSize(item)) * 1.8 +
											"px;" +
											(this$1.isNewLine(1, item, index) &&
											!nIndex &&
											this$1.props.detail &&
											item.text.indexOf(" ") != 0 &&
											item.text.indexOf("　") != 0
												? "margin-left:" +
												  (G$1.appFontSize * 2 + (api.systemType == "ios" ? 6 : 4)) +
												  "px;"
												: "") +
											this$1.getStyle(item),
										onClick: function(e) {
											return this$1.openText(e, item, index);
										}
									},
									nItem
								);
							})
						],
						item.label == "img" &&
							apivm.h(
								"view",
								{
									class: "richImgBox",
									onClick: function(e) {
										return this$1.openImages(e, item, index);
									}
								},
								apivm.h("image", {
									style:
										(item.style || "") +
										";max-width: 100%" +
										(platform() == "app" ? "" : " !important") +
										";",
									mode: "widthFix",
									thumbnail: "false",
									src: item.src
								})
							),
						(item.label == "video" || item.label == "source") &&
							item.src &&
							apivm.h(
								"view",
								{class: "richImgBox"},
								apivm.h("z-video", {src: item.src})
							),
						item.label == "table" &&
							apivm.h(
								"scroll-view",
								{"scroll-x": true, "scroll-y": false, style: "width:100%;"},
								apivm.h(
									"view",
									{
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchEnd: this$1.nTouchmove
									},
									(item.data || []).map(function(nItem, nIndex) {
										return apivm.h(
											"view",
											{
												class: "richTable_item",
												style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
											},
											(nItem.data || []).map(function(uItem, uIndex) {
												return apivm.h(
													"view",
													{
														class: "richTable_item_td",
														style: uItem.style + "margin-left:" + (uIndex ? -1 : 0) + "px;"
													},
													apivm.h(
														"text",
														{
															style:
																loadConfiguration(this$1.getSize(item)) + "text-align: center;",
															class: "richText"
														},
														uItem.text
													)
												);
											})
										);
									})
								)
							),
						item.data &&
							item.data.length > 0 &&
							item.label != "table" &&
							apivm.h("z-rich-text-deal", {
								onClick: this$1.onclick,
								_this: this$1.props._this,
								isNewLine: this$1.isNewLine(1, item, index) && !item.text,
								bItems: [item].concat(this$1.props.bItems || []),
								bItem: item,
								size: this$1.props.size,
								detail: this$1.props.detail,
								data: item.data
							})
					];
				}),
				this.props.hasExpand &&
					apivm.h(
						"view",
						{
							style: "margin-left:5px;flex-direction:row;align-items: center;",
							onClick: this.expandShow
						},
						apivm.h(
							"text",
							{style: loadConfiguration(this.getSize() - 2) + "color:" + G$1.appTheme},
							this.props.isExpand ? "收起" : "展开"
						),
						apivm.h("a-iconfont", {
							name: "xiangxiagengduo",
							style:
								"margin-left:2px;transform: rotate(" +
								(this.props.isExpand ? "180" : "0") +
								"deg);",
							color: G$1.appTheme,
							size: G$1.appFontSize - 3
						})
					)
			);
		};

		return ZRichTextDeal;
	})(Component);
	ZRichTextDeal.css = {
		".richImgBox": {
			alignItems: "center",
			justifyContent: "center",
			margin: "10px 0",
			width: "100%"
		},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px",
			minHeight: "36px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text-deal", ZRichTextDeal);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				richId: this.props.id || "z_rich" + getNum(),
				showText: null,
				richText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false, //是否展开了
				appDetailsStyle: ""
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.data.listData = [];
						this.data.richText = null;
						setTimeout(function() {
							getBoundingClientRect(this$1.data.richId, function(ret) {
								this$1.richRect = ret;
								this$1.dealWithCon();
							});
						}, 50);
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.expandShow = function() {
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon({reset: true});
		};
		ZRichText.prototype.dealWithCon = function(_param) {
			var this$1 = this;
			if (_param === void 0) _param = {};

			var expText =
				(isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (isObject(expText) || isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			var notTagText = removeTag(expText);
			this.data.hasExpand =
				isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;
			if (_param.reset) {
				this.data.hasExpand = false;
				setTimeout(function() {
					this$1.data.hasExpand = true;
				}, 0);
			}
			this.data.appDetailsStyle = getPrefs("appDetailsStyle");
			if (this.data.appDetailsStyle == "edit") {
				this.data.richText = expText;
				return;
			}
			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gis,
				""
			);
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gis, "</br>");
			expText = decodeCharacter(expText);
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = expText.replace(/\n/gis, "</br>");
				expText = "<p>" + expText + "</p>";
			}
			// console.log("解析一："+expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gis;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = removeTag(match[2]);
				if (nText) {
					newText = newText.replace(match[2], nText);
				}
			}
			expText = newText;
			// console.log("解析二："+expText);
			var strRegex = /<\/?\w+[^>]*>/gis;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nStyle = styleMatch ? styleMatch[1] : "";
				if (nlabel == "img") {
					nStyle = nStyle.replace(/width:\s*auto;/g, "width:100%;");
					var imgWRegex = /width="(\d+)(px)?"/,
						imgHRegex = /height="(\d+)(px)?"/;
					var imgWMatch = item.match(imgWRegex),
						imgHMatch = item.match(imgHRegex);
					var width = imgWMatch ? imgWMatch[1] : null;
					var height = imgHMatch ? imgHMatch[1] : null;
					if (width) {
						if (Number(width) > this$1.richRect.width) {
							if (height) {
								height = (Number(height) * this$1.richRect.width) / Number(width);
							}
							width = this$1.richRect.width;
						}
						nStyle += "width:" + width + "px;";
						nStyle += "height:" + (height || "auto") + "px;";
					}
				}
				var nowItem = {label: nlabel, index: index, text: item, style: nStyle};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: nStyle
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: nStyle
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			var childItems = [],
				strongJointEle = "";
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = trim(expText.substring(minIndex, maxIndex));
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						var dealTab = JSON.parse(JSON.stringify(tableData));
						//所有的tr
						var maxCol = 0,
							widths = [];
						dealTab.data.forEach(function(obj) {
							if (obj.data.length > maxCol) {
								maxCol = obj.data.length;
							}
						});
						//最长宽度
						dealTab.data
							.filter(function(obj) {
								return obj.data.length == maxCol;
							})
							.forEach(function(obj, oIndex) {
								obj.data.forEach(function(obj2, oIndex2) {
									if (!oIndex) {
										widths.push(obj2.width);
									} else {
										if (obj2.width > widths[oIndex2]) {
											widths[oIndex2] = obj2.width;
										}
									}
								});
							});
						//赋予
						dealTab.data.forEach(function(obj, oIndex) {
							var nowWidths = widths.concat([]);
							obj.data.forEach(function(obj2, oIndex2) {
								var nowW = 0;
								for (var i = 0; i < obj2.colspan; i++) {
									nowW += nowWidths.shift();
								}
								obj2.style += "width:" + (nowW - obj2.colspan + 1) + "px;";
							});
						});
						showTexts.push(dealTab);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {
							label: "tr",
							data: [],
							index: minIndex,
							style: item.style,
							colspan: 0
						};
					} else if (item.text != "</td>" && item.label == "td") {
						var colspan = Number(this$1.getAttr(item.text, "colspan") || "1");
						tableItem.colspan = tableItem.colspan + colspan;
						var tdWidth = (
							this$1.getAttr(item.text, "width") ||
							this$1.getStyle(item.style, "width") ||
							"150"
						).replace("px", "");
						if (tdWidth.indexOf("%") != -1) {
							var nW = Number(tdWidth.replace(/%/g, ""));
							tdWidth = ((nW < 30 ? 30 : nW) * this$1.richRect.width) / 100;
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						// showStyle += `width:${Number(tdWidth) - colspan + 1}px;`;
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle,
							width: Number(tdWidth),
							colspan: colspan
						});
					}
					return;
				}
				var endregex = /^<\/[a-zA-Z][a-zA-Z0-9]*>$/;
				var isEndReg = endregex.test(item.text),
					endLabel = "";
				if (isEndReg && /^(?!br$).*/.test(item.label)) {
					if (/^(p|div|br|video)$/.test(item.label)) {
						//换行截止
						childItems.pop();
					}
					if (/^(strong)$/.test(item.label)) {
						//连带结束
						strongJointEle = "";
					}
					if (!viewText) {
						return;
					} else {
						endLabel = "span";
					}
				}
				var addItem = {
					label: endLabel || item.label,
					index: item.index,
					style: item.style,
					text: viewText,
					data: []
				};
				if (
					item.label == "br" &&
					!item.text &&
					showTexts.length &&
					showTexts[showTexts.length - 1].label == "br"
				) {
					return;
				}
				minIndex = minIndex + item.text.length;
				var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
				if (srcMatch) {
					addItem.src = srcMatch[1];
				}
				var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
				if (hrefMatch) {
					addItem.href = hrefMatch[1];
				}
				if (addItem.label == "img" && !addItem.src);
				else {
					if (/^(strong)$/.test(item.label) && !addItem.text) {
						//strong连带
						strongJointEle = item.label;
					}
					if (/^(span|strong)$/.test(item.label) && !addItem.text) {
						//非换行非内容跳过
						return;
					}
					if (strongJointEle) {
						addItem.jointEle = strongJointEle;
					}
					if (childItems.length) {
						var lastItem = childItems[childItems.length - 1];
						if (lastItem.text) {
							lastItem.data.push({
								label: "span",
								style: lastItem.style,
								text: lastItem.text
							});
							lastItem.text = "";
						}
						lastItem.data.push(addItem);
					} else {
						showTexts.push(addItem);
					}
					if (/^(?!.*\/\s*>$)(?!.*<\/br>$).+/.test(item.text) && !endLabel) {
						if (/^(p|div|br|video)$/.test(item.label)) {
							//换行中添加
							childItems.push(addItem);
						}
					}
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			var firstLength = 1000,
				otherLength = 600,
				nowSize = 0,
				addNowPage = 0,
				addPages = [0];
			var getShowSize = function(_list, _level) {
				_list.forEach(function(_item, _index) {
					nowSize += (_item.text || "").length;
					if (isArray(_item.data) && _item.data.length) {
						getShowSize(_item.data, _level + 1);
					}
					if (nowSize >= (addPages.length == 1 ? firstLength : otherLength)) {
						if (_level != 1) {
							return;
						}
						addPages.push(_index);
						nowSize = 0;
					}
				});
			};
			getShowSize(showTexts, 1);
			if (addPages[addPages.length - 1] < showTexts.length) {
				addPages.push(showTexts.length);
			}
			console.log(JSON.stringify(addPages));
			clearTimeout(this.showTask);
			var showData = function() {
				this$1.data.listData = this$1.data.listData.concat(
					showTexts.slice(addPages[addNowPage], addPages[addNowPage + 1])
				);
				addNowPage++;
				if (addNowPage < addPages.length) {
					this$1.showTask = setTimeout(function() {
						showData();
					}, 600);
				}
			};
			showData();
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp("\\b" + _item + '\\s*:\\s*([^;"\\s]+)').exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.getAttr = function(_text, _item) {
			var match = new RegExp("\\b" + _item + '\\s*=\\s*"([^"]*)"', "i").exec(
				_text
			);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.onclick = function(e) {
			this.fire("click", e.detail);
		};
		ZRichText.prototype.oninteract = function(e) {
			openWin_url({url: e.detail.url});
		};
		ZRichText.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: this.data.richId,
					style:
						"width:100%;flex-shrink: 1;margin-top:-1px;padding-top:1px;" +
						(this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				this.data.appDetailsStyle == "edit"
					? [
							this.data.richText &&
								apivm.h("rich-text", {
									nodes: this.data.richText,
									"user-select": true,
									onInteract: this.oninteract
								}),
							this.data.hasExpand &&
								apivm.h(
									"view",
									{
										style: "margin-top:5px;flex-direction:row;align-items: center;",
										onClick: this.expandShow
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(this.props.size - 4) + "color:" + G$1.appTheme
										},
										this.data.isExpand ? "收起" : "展开"
									),
									apivm.h("a-iconfont", {
										name: "xiangxiagengduo",
										style:
											"margin-left:2px;transform: rotate(" +
											(this.data.isExpand ? "180" : "0") +
											"deg);",
										color: G$1.appTheme,
										size: G$1.appFontSize - 3
									})
								)
					  ]
					: apivm.h("z-rich-text-deal", {
							onClick: this.onclick,
							_this: this,
							size: this.props.size,
							hasExpand: this.data.hasExpand,
							isExpand: this.data.isExpand,
							detail: this.props.detail,
							data: this.data.listData
					  })
			);
		};

		return ZRichText;
	})(Component);
	apivm.define("z-rich-text", ZRichText);

	var ItemTextarea = /*@__PURE__*/ (function(Component) {
		function ItemTextarea(props) {
			Component.call(this, props);
		}

		if (Component) ItemTextarea.__proto__ = Component;
		ItemTextarea.prototype = Object.create(Component && Component.prototype);
		ItemTextarea.prototype.constructor = ItemTextarea;
		ItemTextarea.prototype.input = function(e) {
			this.fire("input", this.props.item);
		};
		ItemTextarea.prototype.identify = function(_type) {
			var this$1 = this;

			identifyManager(_type, function(ret) {
				this$1.props.item.value +=
					(trim(this$1.props.item.value) ? "\n" : "") + ret;
				this$1.input();
			});
		};
		ItemTextarea.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding: 13px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:13px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"text",
							{style: loadConfiguration(1) + "color:#333;"},
							this.props.item.title
						),
						this.props.item.titleHint &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-2) +
										"flex:1;margin:2px 0 0 10px;color:#999;text-align: right;"
								},
								this.props.item.titleHint
							)
					),
					apivm.h(
						"view",
						null,
						apivm.h(
							"view",
							{style: "padding:7px 0;"},
							this.props.dataMore.readonly
								? apivm.h("z-rich-text", {detail: true, nodes: this.props.item.value})
								: [
										apivm.h("z-textarea", {
											dataMore: this.props.item,
											onInput: this.input
										}),
										platform() != "mp" &&
											apivm.h(
												"view",
												{
													class: "flex_row",
													style: "border-top:1px solid #f5f5f5;margin-top:5px;"
												},
												apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.identify(1);
														},
														class: "textarea_add_item"
													},
													apivm.h("a-iconfont", {
														name: "tupian",
														color: "#333",
														size: G$1.appFontSize + 5
													}),
													apivm.h(
														"text",
														{style: loadConfiguration() + "color: #333;margin-left:10px;"},
														"图片识别"
													)
												),
												apivm.h("view", {
													style: "height:30px;width:1px;background:#F5F5F5;"
												}),
												apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.identify(2);
														},
														class: "textarea_add_item"
													},
													apivm.h("a-iconfont", {
														name: "yuyin",
														color: "#333",
														size: G$1.appFontSize + 6
													}),
													apivm.h(
														"text",
														{style: loadConfiguration() + "color: #333;margin-left:10px;"},
														"语音输入"
													)
												)
											)
								  ]
						),
						apivm.h(
							"view",
							{style: "flex-direction:row-reverse;"},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color: #999999;"},
								"已有字数",
								this.props.item.value.length
							)
						)
					)
				)
			);
		};

		return ItemTextarea;
	})(Component);
	ItemTextarea.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		},
		".textarea_add_item": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center",
			flex: "1",
			padding: "8px"
		}
	};
	apivm.define("item-textarea", ItemTextarea);

	var YAttachments = /*@__PURE__*/ (function(Component) {
		function YAttachments(props) {
			Component.call(this, props);
			this.data = {
				oldData: "",
				imgList: [],
				fileList: []
			};
			this.compute = {
				monitor: function() {
					var data = this.props.data;
					if (isArray(data) && this.data.oldData != JSON.stringify(data)) {
						this.data.oldData = JSON.stringify(data);
						var imgList = [],
							fileList = [];
						data.forEach(function(_eItem, _eIndex, _eArr) {
							var fileInfo = getFileInfo(_eItem.extName);
							var item = {
								id: _eItem.id,
								name: _eItem.originalFileName,
								newName: _eItem.newFileName,
								extName: _eItem.extName,
								url:
									fileInfo.type == "image"
										? _eItem.newFileName
										: appUrl() + "file/preview/" + _eItem.id,
								fileInfo: fileInfo,
								dotSystemDel: _eItem.dotSystemDel
							};

							if (fileInfo.type != "image") {
								fileList.push(item);
							} else {
								imgList.push(item);
							}
						});
						this.data.imgList = imgList;
						this.data.fileList = fileList;
					}
				},
				id: function() {},
				name: function() {},
				newName: function() {},
				extName: function() {},
				url: function() {},
				fileInfo: function() {},
				dotSystemDel: function() {}
			};
		}

		if (Component) YAttachments.__proto__ = Component;
		YAttachments.prototype = Object.create(Component && Component.prototype);
		YAttachments.prototype.constructor = YAttachments;
		YAttachments.prototype.openFile = function(e, _item) {
			stopBubble(e);
			var param = {};
			param.id = _item.id || _item.url;
			param.extName = _item.extName;
			param.suffix = _item.fileInfo.type;
			param.fileSource = this.props.fileSource;
			openWin_filePreviewer(param);
		};
		YAttachments.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			openWin_imgPreviewer({
				index: _index,
				imgs: this.data.imgList.map(function(obj) {
					return showImg(obj.url);
				})
			});
		};
		YAttachments.prototype.delFile = function(e, _item, _index) {
			stopBubble(e);
			delItemForKey(_item, this.props.data, "id");
			this.fire("change", this.props.data);
			if (!_item.dotSystemDel) {
				ajax(
					{u: appUrl() + "file/clear"},
					"clear" + _item.id,
					function(ret, err) {},
					"删除附件",
					"post",
					{
						body: JSON.stringify({fileIds: [_item.id]})
					}
				);
			}
		};
		YAttachments.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{s: this.monitor},
				(this.data.imgList.length > 0 || this.data.fileList.length > 0) &&
					apivm.h(
						"view",
						{style: this.props.style || ""},
						apivm.h(
							"view",
							null,
							this.data.imgList.length > 0 &&
								apivm.h(
									"view",
									{
										class: "attach_img_box",
										style:
											"margin-bottom:" +
											(this.data.fileList.length > 0 ? "1" : "") +
											"0px;"
									},
									(Array.isArray(this.data.imgList)
										? this.data.imgList
										: Object.values(this.data.imgList)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											{
												class: "attach_img_item",
												onClick: function(e) {
													return this$1.openImages(e, item$1, index$1);
												}
											},
											apivm.h("image", {
												style: "width:100%;" + loadConfigurationSize(44, "h"),
												src: showImg(item$1, "264x180-compress-"),
												mode: "aspectFill"
											}),
											this$1.props.type == "2" &&
												apivm.h(
													"view",
													{
														class: "attach_img_clean",
														onClick: function(e) {
															return this$1.delFile(e, item$1, index$1);
														}
													},
													apivm.h("a-iconfont", {
														name: "qingkong",
														color: "rgba(0,0,0,0.65)",
														size: G$1.appFontSize + 2
													})
												)
										);
									})
								)
						),
						apivm.h(
							"view",
							null,
							this.data.fileList.length > 0 &&
								apivm.h(
									"view",
									null,
									(Array.isArray(this.data.fileList)
										? this.data.fileList
										: Object.values(this.data.fileList)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											{
												class: "attach_item",
												style:
													"margin-top:" + (index$1 ? 1 : 0) + "0px;background:transparent;",
												onClick: function(e) {
													return this$1.openFile(e, item$1, index$1);
												}
											},
											apivm.h(
												"view",
												{style: "margin-right:10px;"},
												apivm.h("a-iconfont", {
													name: item$1.fileInfo.name,
													color: item$1.fileInfo.color,
													size: G$1.appFontSize + 6
												})
											),
											apivm.h(
												"view",
												{class: "flex_w"},
												apivm.h(
													"text",
													{
														class: "text_one",
														style: loadConfiguration() + "color: #666;word-break: break-all;"
													},
													item$1.name
												)
											),
											this$1.props.type == "2" &&
												apivm.h(
													"view",
													{
														style: "padding:5px;margin-right:-5px;",
														onClick: function(e) {
															return this$1.delFile(e, item$1, index$1);
														}
													},
													apivm.h("a-iconfont", {
														name: "qingkong",
														color: "#333",
														size: G$1.appFontSize + 4
													})
												)
										);
									})
								)
						)
					)
			);
		};

		return YAttachments;
	})(Component);
	YAttachments.css = {
		".attach_item": {
			borderRadius: "4px",
			border: "1px solid #F4F5F7",
			padding: "2px 10px",
			minHeight: "36px",
			flexDirection: "row",
			alignItems: "center"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".attach_img_box": {flexDirection: "row", flexWrap: "wrap"},
		".attach_img_item": {
			padding: "10px 10px 0 0",
			width: "25%",
			height: "auto",
			boxSizing: "border-box"
		},
		".attach_img_clean": {position: "absolute", top: "1px", right: "3px"}
	};
	apivm.define("y-attachments", YAttachments);

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				size: G$1.appFontSize + (this.props.size || 0),
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: this.props.type == 3
						? "fangxingxuanzhongfill"
						: "yuanxingxuanzhongfill"
					: this.props.type == 3
					? "fangxingweixuanzhong"
					: "danxuan_weixuanzhong",
				color: this.props.checked ? this.props.color || G$1.appTheme : "#999"
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{
						class: "z_image",
						id: "box_" + this.data.imgId,
						style: "" + (api.systemType == "ios" ? "z-index: 1;" : "")
					},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
			this.data = {
				esrc: appUrl() + "img/default_user_head.jpg",
				asrc: null,
				bsrc: ""
			};
			this.compute = {
				monitor: function() {
					var url = isObject(this.props.src) ? this.props.src.url : this.props.src;
					if (this.data.asrc != url || !this.data.asrc) {
						this.data.asrc = url || this.data.esrc;
						this.data.bsrc = "";
					}
				}
			};
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.error = function() {
			this.data.bsrc = this.data.esrc;
		};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{s: this.monitor, class: "xy_100"},
				apivm.h("z-image", {
					round: true,
					mode: "scaleToFill",
					thumbnail: "false",
					src: showImg(this.data.bsrc || this.data.asrc, "150x150-compress-"),
					onError: this.error
				})
			);
		};

		return ZAvatar;
	})(Component);
	apivm.define("z-avatar", ZAvatar);

	var ItemFile = /*@__PURE__*/ (function(Component) {
		function ItemFile(props) {
			Component.call(this, props);
		}

		if (Component) ItemFile.__proto__ = Component;
		ItemFile.prototype = Object.create(Component && Component.prototype);
		ItemFile.prototype.constructor = ItemFile;
		ItemFile.prototype.openDetail = function(e) {
			if (this.props.item.fileInfo.type == "folder") {
				this.fire("folder", this.props.item);
				return;
			}
			if (this.props.select) {
				var nItem = getItemForKey(this.props.item.id, this.props.listSelect, "id");
				if (nItem) {
					if (!nItem.readonly) {
						delItemForKey(nItem, this.props.listSelect, "id");
					}
				} else {
					this.props.listSelect.push(this.props.item);
				}
			}
			stopBubble(e);
		};
		ItemFile.prototype.isSelectValue = function(_item, _type) {
			var selectItem = getItemForKey(_item.id, this.props.listSelect, "id");
			if (_type == "color") {
				return selectItem && !selectItem.readonly;
			}
			return selectItem;
		};
		ItemFile.prototype.showDot = function(_item) {
			return (
				_item.isRedDot == 1 ||
				getItemForKey(_item.id, this.props.dataMore.listUnread || [])
			);
		};
		ItemFile.prototype.openLeftMore = function(e, _item) {
			stopBubble(e);
			this.fire("leftMore", _item);
		};
		ItemFile.prototype.openRightMore = function(e, _item) {
			stopBubble(e);
			this.fire("rightMore", _item);
		};
		ItemFile.prototype.showType = function() {
			var dataMore = this.props.dataMore;
			var select = this.props.select;
			if (
				dataMore.type == "details" ||
				(select && this.props.item.fileInfo.type == "folder")
			) {
				return 1;
			}
			if (select) {
				return 2;
			}
			return 3;
		};
		ItemFile.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{onClick: this.openDetail, class: "item_file_item flex_row"},
				apivm.h(
					"view",
					null,
					this.showType() == 2 &&
						apivm.h(
							"view",
							{
								style: "padding:10px;",
								onClick: function(e) {
									return this$1.openLeftMore(e, this$1.props.item);
								}
							},
							apivm.h("a-iconfont", {
								name: "gengduo3",
								color: "#999",
								size: G$1.appFontSize + 10
							})
						)
				),
				apivm.h(
					"view",
					{style: "padding:4px;margin-right:10px;"},
					apivm.h("a-iconfont", {
						name: this.props.item.fileInfo.name,
						color: this.props.item.fileInfo.color,
						size: G$1.appFontSize + 20
					}),
					this.showDot(this.props.item)
						? apivm.h("view", {
								class: "item_file_redDot flex_row",
								style: "" + loadConfigurationSize(-6)
						  })
						: null
				),
				apivm.h(
					"view",
					{class: "flex_w"},
					apivm.h(
						"text",
						{
							class: "text_one",
							style:
								loadConfiguration() +
								"height:" +
								(G$1.appFontSize + 3) +
								"px;font-weight: 600;color:#333;"
						},
						this.props.item.name
					),
					this.props.dataMore.type != "details"
						? apivm.h(
								"view",
								{class: "flex_row", style: "margin-top:4px;"},
								apivm.h(
									"view",
									null,
									this.props.item.firstMsg &&
										apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(-4) +
														"color: " +
														(this.props.item.fristColor || "#999") +
														";"
												},
												this.props.item.firstMsg
											),
											apivm.h("view", {
												style:
													"width:1px;height:" +
													(G$1.appFontSize - 1) +
													"px;background:#eee;margin:0 10px;"
											})
										)
								),
								apivm.h(
									"view",
									null,
									this.props.item.firstIcon &&
										this.props.item.firstIcon.name &&
										apivm.h("a-iconfont", {
											style: "margin-right:6px;",
											name: this.props.item.firstIcon.name,
											color: this.props.item.firstIcon.color || "#999",
											size: G$1.appFontSize
										})
								),
								apivm.h(
									"text",
									{style: loadConfiguration(-4) + "color: #999;flex-shrink: 0;"},
									dayjs(this.props.item.time).format("YYYY-MM-DD HH:mm")
								),
								apivm.h(
									"view",
									null,
									isArray(this.props.item.addMsg) &&
										this.props.item.addMsg.length > 0 &&
										apivm.h(
											"view",
											{class: "flex_row"},
											(Array.isArray(this.props.item.addMsg)
												? this.props.item.addMsg
												: Object.values(this.props.item.addMsg)
											).map(function(nItem, nIndex) {
												return apivm.h(
													"view",
													{class: "flex_row"},
													apivm.h("view", {
														style:
															"width:1px;height:" +
															(G$1.appFontSize - 1) +
															"px;background:#eee;margin:0 10px;"
													}),
													apivm.h(
														"text",
														{
															style:
																loadConfiguration(-4) +
																"color: " +
																(nItem.color || "#999") +
																";"
														},
														nItem.text
													)
												);
											})
										)
								),
								apivm.h(
									"view",
									null,
									!isArray(this.props.item.addMsg) &&
										this.props.item.addMsg &&
										apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h("view", {
												style:
													"width:1px;height:" +
													(G$1.appFontSize - 1) +
													"px;background:#eee;margin:0 10px;"
											}),
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(-4) +
														"color: " +
														(this.props.item.addColor || "#999") +
														";"
												},
												this.props.item.addMsg
											)
										)
								)
						  )
						: null
				),
				apivm.h(
					"view",
					null,
					this.showType() == 1 &&
						apivm.h(
							"view",
							{style: "padding:8px;transform: rotate(-90deg);"},
							apivm.h("a-iconfont", {
								name: "xiangxia1",
								color: "#999999",
								size: G$1.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.showType() == 2 &&
						apivm.h(
							"view",
							{style: "margin-left:5px;"},
							apivm.h("z-radio", {
								checked: this.isSelectValue(this.props.item),
								size: 4,
								color: this.isSelectValue(this.props.item, "color")
									? G$1.appTheme
									: "#999"
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.showType() == 3 &&
						apivm.h(
							"view",
							{
								style: "padding:8px;margin: -8px 0;",
								onClick: function(e) {
									return this$1.openRightMore(e, this$1.props.item);
								}
							},
							apivm.h("a-iconfont", {
								name: "gengduo",
								color: "#999999",
								size: G$1.appFontSize
							})
						)
				)
			);
		};

		return ItemFile;
	})(Component);
	ItemFile.css = {
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".item_file_item": {minHeight: "70px", padding: "5px 16px"},
		".item_file_redDot": {
			position: "absolute",
			zIndex: "1",
			top: "2px",
			right: "2px",
			background: "#f92323",
			borderRadius: "50%",
			whiteSpace: "nowrap",
			justifyContent: "center"
		}
	};
	apivm.define("item-file", ItemFile);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId) &&
								document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId) &&
				document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.keyboardheightchange = function(e) {
			G$1.inputIng = e.detail.height > 0;
		};
		ZInput.prototype.inputBlur = function(e) {
			G$1.inputIng = false;
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId) &&
				document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId) &&
						document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "99" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color:
									this.props.dataMore.iconc || (this.props.tWhite ? "#FFF" : "#ccc"),
								size: G$1.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G$1.appFontSize + 14) +
									"px;color:#" +
									(this.props.tWhite ? "FFF" : "ccc") +
									";" +
									loadConfiguration(this.props.dataMore.size)
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration(this.props.dataMore.size) +
								"height:" +
								(G$1.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style":
								"color:#" + (this.props.tWhite ? "FFF" : "ccc") + ";",
							class:
								"flex_w " + (this.props.tWhite ? "z_input_white" : "z_input_input"),
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								this.props.dataMore.disabled ||
								this.props.dataMore.readonly ||
								(isParameters(this.props.readonly) ? this.props.readonly : false),
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onKeyboardheightchange: this.keyboardheightchange,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						!this.props.dataMore.disabled &&
						!this.props.dataMore.readonly &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G$1.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G$1.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"},
		".z_input_white": {
			background: "transparent",
			borderColor: "transparent",
			color: "#FFF",
			paddingRight: "5px"
		},
		".z_input_white::placeholder": {color: "#FFF"}
	};
	apivm.define("z-input", ZInput);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G$1.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G$1.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G$1.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G$1.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G$1.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G$1.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G$1.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G$1.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G$1.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "800", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G$1.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {
								view: nowView,
								position: nowView == "upper" || nowView == "lower" ? nowView : null,
								animated: _animated
						  }
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "xy_100 " + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view":
						platform() == "mp"
							? this.data.scroll_view
							: platform() == "web"
							? null
							: "-",
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : false,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var ZBreadcrumb = /*@__PURE__*/ (function(Component) {
		function ZBreadcrumb(props) {
			Component.call(this, props);
			this.data = {
				oldData: "",
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var data = this.props.dataMore.data;
					if (isArray(data) && this.data.oldData != JSON.stringify(data)) {
						if (this.data.oldData) {
							setTimeout(function() {
								this$1.tabClick(data[data.length - 1]);
							}, 30);
						}
						this.data.oldData = JSON.stringify(data);
					}
				}
			};
		}

		if (Component) ZBreadcrumb.__proto__ = Component;
		ZBreadcrumb.prototype = Object.create(Component && Component.prototype);
		ZBreadcrumb.prototype.constructor = ZBreadcrumb;
		ZBreadcrumb.prototype.tabClick = function(_item) {
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;

			var nLevel = [];
			for (var i = 0; i < this.props.dataMore.data.length; i++) {
				var item = this.props.dataMore.data[i];
				nLevel.push(item);
				if (item.key == _item.key) {
					break;
				}
			}
			this.props.dataMore.data = nLevel;

			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view = "breadcrumb_" + _item.key;
		};
		ZBreadcrumb.prototype.nTouchmove = function() {
			touchmove();
		};
		ZBreadcrumb.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1, list) {
						return apivm.h(
							"view",
							{
								id: "breadcrumb_" + item$1.key,
								style:
									"display: inline-block;margin-left:" +
									(!index$1 ? "16" : "0") +
									"px;margin-right:" +
									(index$1 == list.length - 1 ? "16" : "0") +
									"px;",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"view",
								{class: "flex_row"},
								apivm.h(
									"view",
									{style: "display:" + (index$1 ? "flex" : "none") + ";"},
									apivm.h("a-iconfont", {
										style: "margin:0 8px;transform: rotate(180deg) scale(0.7,0.7);",
										name: "fanhui1",
										color: index$1 ? "#999" : "rgba(0,0,0,0)",
										size: G$1.appFontSize - 4
									})
								),
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(this$1.props.size) +
											";color:" +
											(index$1 == list.length - 1 && index$1 ? "#333" : "#999")
									},
									item$1.value
								)
							)
						);
					})
				)
			);
		};

		return ZBreadcrumb;
	})(Component);
	apivm.define("z-breadcrumb", ZBreadcrumb);

	var FileList = /*@__PURE__*/ (function(Component) {
		function FileList(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				inputBox: {
					show: true,
					value: "",
					placeholder: "请输入关键词"
				},

				level: {key: "0", data: [{key: "0", value: "全部"}]},

				listData: [],
				listSelect: [],
				itemFileMore: {
					type: "select"
				},

				emptyBox: {
					type: "load",
					text: ""
				},

				orderBys: {
					key: "updatedate",
					direction: 1 //0 顺序 1倒序
				},
				select: false,
				hasAddFolder: false //新建按钮
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						this.data.level.key = "0";
						this.data.level.data = [this.data.level.data[0]];
						this.data.emptyBox.type = "load";
						if (this.data.show) {
							this.baseInit();
						} else {
							if (this.props.dataMore.pageClose) {
								this.props.dataMore.pageClose();
							}
						}
					}
				},
				titleName: function() {
					var name = "";
					switch (this.props.dataMore.key) {
						case "cloud_file":
							name = "云盘文件";
							break;
						case "chat_file":
							name = "聊天文件";
							break;
						case "cloud_resave":
							name =
								"转存到“" +
								(this.data.level.data.length == 1
									? "我的云盘"
									: this.data.level.data[this.data.level.data.length - 1].value) +
								"”";
							break;
					}

					return name;
				}
			};
		}

		if (Component) FileList.__proto__ = Component;
		FileList.prototype = Object.create(Component && Component.prototype);
		FileList.prototype.constructor = FileList;
		FileList.prototype.baseInit = function() {
			this.data.select = this.props.dataMore.select;
			this.data.hasAddFolder = this.props.dataMore.addFolder;
			this.data.listSelect = [];
			this.getData(0);
		};
		FileList.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		FileList.prototype.penetrate = function() {};
		FileList.prototype.confirmBtn = function() {
			var dataMore = this.props.dataMore;
			dataMore.toId = this.data.level.key;
			dataMore.toItem = this.data.level.data[this.data.level.data.length - 1];
			dataMore.listSelect = this.data.listSelect;
			if (dataMore.callback) {
				dataMore.callback(dataMore);
			} else {
				this.fire("click", dataMore);
			}
			this.closePage();
		};
		FileList.prototype.openFolder = function(e) {
			this.data.level.data.push({key: e.detail.id, value: e.detail.name});
		};
		FileList.prototype.openLeftMore = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			var buttons = ["打开"];
			if (detail.createBy == G$1.uId || detail.createBy == G$1.userId) {
				buttons.push("删除");
			}
			actionSheet(
				{
					title: "提示",
					buttons: buttons
				},
				function(ret, err) {
					if (ret.name == "打开") {
						openWin_filePreviewer({
							id: detail.fileInfoId,
							suffix: detail.fileInfo.type,
							fileSource: "5"
						});
					} else if (ret.name == "删除") {
						var delKey = "groupFileId";
						var url = appUrl() + "chatGroupFile/dels";
						var param = {ids: [detail[delKey]]};
						if (this$1.props.dataMore.key == "cloud_file") {
							url = appUrl() + "panfile/recycleflag";
							delKey = "id";
							param = {ids: [detail[delKey]], isRecycle: 1};
						}
						ajaxAlert(
							{
								msg: "确定删除吗?",
								url: url,
								param: param,
								toast: "删除中"
							},
							function(ret) {
								delItemForKey(detail[delKey], this$1.data.listData, delKey);
								delItemForKey(detail[delKey], this$1.data.listSelect, delKey);
							}
						);
					}
				}
			);
		};
		FileList.prototype.getData = function(_type) {
			var this$1 = this;

			var dataMore = this.props.dataMore;
			var url =
				appUrl() +
				(dataMore.key == "long_shared" ? "pubsharefile" : "panfile") +
				"/list";
			var tableId =
				dataMore.key == "long_shared" ? "id_pan_pubshare_file" : "id_pan_file_list";
			var postParam = {
				pageNo: 1,
				pageSize: 9999,
				keyword: "",
				tableId: tableId,
				query: {fileMenu: "1", fileStatus: "0"},
				orderBys: [
					{
						columnId: tableId + "_" + this.data.orderBys.key,
						isDesc: this.data.orderBys.direction
					}
				],
				wheres: [
					{
						columnId: tableId + "_parentid",
						queryType: "EQ",
						value: this.data.level.key || "0"
					}
				]
			};

			if (dataMore.key == "cloud_file") {
				delete postParam.query;
			}
			if (dataMore.key == "chat_file") {
				url = appUrl() + "chatGroupFile/list";
				postParam = {
					pageNo: 1,
					pageSize: 9999,
					keyword: "",
					query: {chatGroupId: dataMore.id}
				};
			}
			ajax(
				{u: url},
				"file_list",
				function(ret, err) {
					hideProgress();
					var code = ret ? ret.code : "";
					var data = ret ? ret.data || [] : [];
					if (!isArray(data) || !data.length) {
						dealData(_type, this$1, ret);
						return;
					}
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						if (dataMore.key == "chat_file") {
							item.groupFileId = _eItem.id;
							_eItem = _eItem.fileInfo || {};
						}
						item.id = _eItem.id || ""; //id
						item.name = _eItem.fileName || _eItem.originalFileName || ""; //标题
						item.time = _eItem.updateDate || _eItem.createDate;
						item.fileInfoId = _eItem.fileInfoId || _eItem.id;
						item.createBy = _eItem.createBy || _eItem.accountId;

						item.fileInfo = getFileInfo(_eItem.extName || "folder");
						if (dataMore.key == "cloud_file" || dataMore.key == "chat_file") {
							nowList.push(item);
						} else {
							if (!getItemForKey(item.id, this$1.data.listSelect, "id")) {
								nowList.push(item);
							}
						}
					});
					if (!_type) {
						this$1.data.listData = nowList;
					} else {
						this$1.data.listData = this$1.data.listData.concat(nowList);
					}
					this$1.data.emptyBox.type = "";
					this$1.data.emptyBox.text = LOAD_ALL;
				},
				"列表",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		FileList.prototype.levelChange = function() {
			this.getData(0);
		};
		FileList.prototype.addFolder = function() {
			var this$1 = this;

			alert(
				{
					title: "请输入文件夹名字",
					type: "input",
					msg: "",
					placeholder: "新建文件夹",
					buttons: ["确定", "取消"]
				},
				function(ret) {
					if (ret.buttonIndex == 1) {
						ajaxProcess(
							{
								toast: "新建中",
								url:
									appUrl() +
									(this$1.props.dataMore.key == "cloud_shared"
										? "pubsharefile"
										: "panfile") +
									"/addmenu",
								param: {
									parentId: this$1.data.level.key || "0",
									fileName: ret.content || ret.placeholder,
									pubshareName: ret.content || ret.placeholder
								},

								name: "新建文件夹"
							},
							function(ret, err) {
								if (ret && ret.code == 200) {
									this$1.getData(0);
								}
							}
						);
					}
				}
			);
		};
		FileList.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G$1.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G$1.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) + "font-weight: 600;color:" + G$1.headColor
									},
									this.titleName
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) + "color:" + G$1.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{
								class: "flex_row",
								style: "padding:10px 16px 10px 10px;margin-top:10px;"
							},
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h("z-breadcrumb", {
									size: -2,
									dataMore: this.data.level,
									onChange: this.levelChange
								})
							),
							this.data.hasAddFolder &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.addFolder();
										},
										class: "flex_row",
										style: "margin-left:10px;"
									},
									apivm.h("a-iconfont", {
										style: "margin-right:6px;",
										name: "folder-add-fill",
										color: "#F6931C",
										size: G$1.appFontSize + 4
									}),
									apivm.h("text", {style: loadConfiguration(1) + "color:#333;"}, "新建")
								)
						),
						apivm.h(
							"y-scroll-view",
							{_this: this},
							apivm.h(
								"view",
								null,
								!this.data.emptyBox.type &&
									this.data.listData.map(function(item, index) {
										return [
											apivm.h("item-file", {
												dataMore: this$1.data.itemFileMore,
												search: this$1.data.inputBox,
												_this: this$1,
												select: this$1.data.select,
												listSelect: this$1.data.listSelect,
												item: item,
												onRefresh: function() {
													return this$1.getData(0);
												},
												onFolder: this$1.openFolder,
												onLeftMore: this$1.openLeftMore
											}),
											apivm.h(
												"view",
												{style: "padding-left:56px;"},
												apivm.h("z-divider", null)
											)
										];
									})
							),
							apivm.h(
								"view",
								null,
								this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
							),
							apivm.h("z-empty", {
								_this: this,
								style: "margin:50px 0;",
								dataMore: this.data.emptyBox,
								onRefresh: this.getData
							})
						)
					)
				]
			);
		};

		return FileList;
	})(Component);
	FileList.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".folder_item": {padding: "0 16px"},
		".folder_item_body": {marginLeft: "15px"},
		".folder_item_warp": {minHeight: "65px", padding: "5px 0"},
		".folder_item_line": {borderTop: "1px solid #EEEEEE"},
		".folder_item_more": {width: "auto", height: "auto", padding: "6px"}
	};
	apivm.define("file-list", FileList);

	var ItemAttach = /*@__PURE__*/ (function(Component) {
		function ItemAttach(props) {
			Component.call(this, props);
		}

		if (Component) ItemAttach.__proto__ = Component;
		ItemAttach.prototype = Object.create(Component && Component.prototype);
		ItemAttach.prototype.constructor = ItemAttach;
		ItemAttach.prototype.input = function(e) {
			this.fire("input", this.props.item);
		};
		ItemAttach.prototype.add = function() {
			var this$1 = this;

			var keyItem = this.props.item;
			var max = -1;
			if (isNumber(keyItem.max) && keyItem.max > 0) {
				max = keyItem.max - keyItem.value.length;
			}
			if (max <= 0) {
				toast("最多选择" + keyItem.max + "个附件");
				return;
			}
			actionSheet(
				{
					title: "请选择附件来源",
					buttons: [
						"相机",
						"相册",
						platform() == "mp" ? "聊天文件" : "本机文件",
						"云盘文件"
					]
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					if (_index == 1 || _index == 2) {
						getPicture(
							{
								sourceType: _index == 1 ? "camera" : "photos",
								destinationType: "url",
								max: max
							},
							function(ret, err) {
								var dataUrl = ret ? ret.data || ret.base64Data : "";
								if (dataUrl) {
									var _item = {showToast: true, url: dataUrl};
									uploadFile(_item, function(ret) {
										if (ret.otherInfo) {
											ret.otherInfo.url = appUrl() + "image/" + ret.otherInfo.newFileName;
											keyItem.value.push(ret.otherInfo);
											this$1.fire("input", this$1.props.item);
										} else {
											toast(ret.error);
										}
									});
								}
							}
						);
					} else if (_index == 3) {
						chooseFile({showToast: true, max: max}, function(ret) {
							if (ret.otherInfo) {
								keyItem.value.push(ret.otherInfo);
								this$1.fire("input", this$1.props.item);
							} else {
								toast(ret.error);
							}
						});
					} else if (_index == 4) {
						G$1.fileListPop = {
							key: "cloud_file",
							show: true,
							select: true,
							callback: function(ret) {
								ret.listSelect.forEach(function(_eItem) {
									ajax(
										{u: appUrl() + "file/info/" + _eItem.fileInfoId},
										"info" + _eItem.fileInfoId,
										function(ret, err) {
											if (ret && ret.code == 200 && ret.data) {
												ret.data.dotSystemDel = true; //云盘增加的文件不能真删除
												keyItem.value.push(ret.data);
												this$1.fire("input", this$1.props.item);
											}
										},
										"附件详情",
										"get"
									);
								});
							}
						};
					}
				}
			);
		};
		ItemAttach.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding: " + (this.props.item.exhibit ? 13 : 18) + "px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:" +
								(this.props.item.exhibit ? 13 : 18) +
								"px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"text",
							{class: "flex_w", style: loadConfiguration() + "color:#333;width:auto;"},
							this.props.item.title,
							this.props.item.value.length > 0
								? "(" + this.props.item.value.length + ")"
								: ""
						),
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.add();
								},
								class: "flex_row flex_shrink"
							},
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										"color:" +
										G$1.appTheme +
										";margin-right:3px;flex-shrink: 0;"
								},
								"添加"
							),
							apivm.h("a-iconfont", {
								name: "gengduo1",
								color: G$1.appTheme,
								size: G$1.appFontSize + 2
							})
						)
					),
					apivm.h(
						"view",
						null,
						this.props.item.value.length > 0 &&
							apivm.h(
								"view",
								{style: "margin-top:10px;"},
								apivm.h("y-attachments", {
									type: 2,
									data: this.props.item.value,
									onChange: this.input
								})
							)
					)
				)
			);
		};

		return ItemAttach;
	})(Component);
	ItemAttach.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		}
	};
	apivm.define("item-attach", ItemAttach);

	var ZTag = /*@__PURE__*/ (function(Component) {
		function ZTag(props) {
			Component.call(this, props);
		}

		if (Component) ZTag.__proto__ = Component;
		ZTag.prototype = Object.create(Component && Component.prototype);
		ZTag.prototype.constructor = ZTag;
		ZTag.prototype.getTagStyle = function() {
			var color = this.props.color || G$1.appTheme;
			var rc = "",
				rbg = "",
				rbor = "";
			switch (this.props.type) {
				case "2":
					rc = color;
					rbg = "#FFF";
					rbor = colorRgba(color);
					break;
				case "3":
					rc = "#FFF";
					rbg = color;
					rbor = color;
					break;
				default:
					rc = color;
					rbg = colorRgba(color, 0.1);
					rbor = "transparent";
					break;
			}

			return (
				"color:" +
				rc +
				";background:" +
				rbg +
				";border: 1px solid " +
				rbor +
				";border-radius: " +
				(this.props.roundSize || 2) +
				"px;"
			);
		};
		ZTag.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "flex-direction:row;"},
				apivm.h(
					"text",
					{
						id: "" + (this.props.id || ""),
						class: "tag_box " + (this.props.class || ""),
						style:
							"" +
							loadConfiguration((this.props.size || 0) - 2) +
							this.getTagStyle() +
							(this.props.style || "")
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZTag;
	})(Component);
	ZTag.css = {".tag_box": {padding: "1px 10px"}};
	apivm.define("z-tag", ZTag);

	var Item5 = /*@__PURE__*/ (function(Component) {
		function Item5(props) {
			Component.call(this, props);
		}

		if (Component) Item5.__proto__ = Component;
		Item5.prototype = Object.create(Component && Component.prototype);
		Item5.prototype.constructor = Item5;
		Item5.prototype.handClick = function(e) {
			stopBubble(e);
		};
		Item5.prototype.openVideos = function(e, _item, _index) {
			this.handClick(e);
			_item.autoplay = true;
			_item.showCode = "QUANLANSP";
		};
		Item5.prototype.showAxis = function() {
			if (!this.props.index) {
				return true;
			}
			if (
				this.props.item.isTop == "1" &&
				this.props.list[this.props.index - 1].isTop == 1
			) {
				return false;
			}
			if (
				dayjs(this.props.item.time).format("YYYY-MM-DD") !=
				dayjs(this.props.list[this.props.index - 1].time).format("YYYY-MM-DD")
			) {
				return true;
			}
			return false;
		};
		Item5.prototype.getPlayState = function() {
			var playItem = {src: "shengyin1", text: "播放"};
			if (this.props.item.id == this.props.floatModule.id) {
				switch (this.props.floatModule.state + "") {
					case "0":
						playItem.src = "shengyin1";
						playItem.text = "播放";
						break;
					case "1":
						playItem.src = "shengyin";
						playItem.text = "暂停";
						break;
					case "2":
						playItem.src = "shengyinjingyin";
						playItem.text = "继续播放";
						break;
					case "3":
						playItem.src = "shengyin1";
						playItem.text = "重新播放";
						break;
				}
			} else {
				playItem.src = "shengyin1";
				playItem.text = "播放";
			}
			return playItem;
		};
		Item5.prototype.playing = function(e) {
			this.handClick(e);
			if (this.props.item.id == this.props.floatModule.id) {
				if (this.props.floatModule.state == "1") {
					sendEvent({name: "index", extra: {type: "pauseStartPlay"}});
				} else {
					sendEvent({name: "index", extra: {type: "reStartPlay"}});
				}
			} else {
				sendEvent({
					name: "index",
					extra: {
						type: "startPlay",
						floatType: this.props.item.code,
						id: this.props.item.id,
						src: this.props.item.content
					}
				});
			}
		};
		Item5.prototype.openDetail = function(e) {
			openWin_news(this.props.item);
			this.handClick(e);
		};
		Item5.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "news_item",
					style:
						"padding-left:" + (this.props.layoutId == "SJCXS" ? "24" : "16") + "px;",
					onClick: this.openDetail
				},
				apivm.h("view", {
					class: "news_point",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" && this.showAxis() ? "block" : "none") +
						";background:" +
						G$1.appTheme +
						";"
				}),
				apivm.h("view", {
					class: "news_line_top",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" ? "block" : "none") +
						";background:" +
						colorRgba(G$1.appTheme, this.props.index ? 0.15 : 0) +
						";"
				}),
				apivm.h("view", {
					class: "news_line_bottom",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" ? "block" : "none") +
						";background:" +
						colorRgba(
							G$1.appTheme,
							isArray(this.props.list) &&
								this.props.index == this.props.list.length - 1
								? 0
								: 0.15
						) +
						";"
				}),
				apivm.h(
					"view",
					null,
					this.props.layoutId == "SJCXS" &&
						apivm.h(
							"text",
							{
								style:
									"display:" +
									(this.showAxis() ? "block" : "none") +
									";" +
									loadConfiguration(-2) +
									"font-weight: 600;color:#333;margin-bottom:10px;"
							},
							this.props.item.isTop == 1
								? "置顶"
								: dayjs(this.props.item.time).format("YYYY-MM-DD")
						)
				),
				apivm.h(
					"view",
					null,
					((this.props.item.showCode == "QUANLANSP" && this.props.item.videoHrefs) ||
						isArray(this.props.item.url) ||
						this.props.item.code == "7") && [
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								this.props.item.code == "7" &&
									apivm.h("view", {
										style:
											loadConfigurationSize(-2, "h") +
											"width:3px;background:" +
											G$1.appTheme +
											";margin-right:4px;"
									})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "flex:1;flex-wrap: wrap;"},
								this.props.item.title.split("").map(function(nItem, nIndex) {
									return (
										nIndex <
											Math.floor((G$1.pageWidth - 40) / (G$1.appFontSize + 2)) -
												(this$1.props.item.code == "7" ? 3 : 1) &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"margin:2px 0;font-weight: 600;color: " +
													(this$1.props.search &&
													this$1.props.search.result &&
													this$1.props.search.result.indexOf(nItem) > -1
														? G$1.appTheme
														: "#333") +
													";"
											},
											nIndex ==
												Math.floor((G$1.pageWidth - 40) / (G$1.appFontSize + 2)) -
													(this$1.props.item.code == "7" ? 3 : 1) -
													1
												? "..."
												: nItem
										)
									);
								})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "margin-left:5px;"},
								this.props.item.code == "7" &&
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(-1) +
												"color:#999;flex-shrink: 0;margin-right:2px;"
										},
										"查看"
									),
								apivm.h(
									"view",
									null,
									apivm.h("a-iconfont", {
										name: "xiangxia1",
										style: "transform: rotate(-90deg);",
										color: "#666",
										size: "" + G$1.appFontSize
									})
								)
							)
						),
						apivm.h(
							"view",
							{style: "margin-top:10px;"},
							this.props.item.videoHrefs && this.props.item.showCode == "QUANLANSP"
								? apivm.h(
										"view",
										{onClick: this.handClick},
										apivm.h("z-video", {
											autoplay: this.props.item.autoplay,
											poster: this.props.item.poster,
											src: this.props.item.videoHrefs
										})
								  )
								: isArray(this.props.item.url)
								? apivm.h(
										"view",
										{class: "flex_row"},
										(Array.isArray(this.props.item.url)
											? this.props.item.url
											: Object.values(this.props.item.url)
										).map(function(nItem, nIndex) {
											return apivm.h(
												"view",
												{style: "width:33.33%;height:84px;margin: 0 2px;"},
												apivm.h("image", {
													class: "xy_100",
													src: showImg(nItem, "336x252-compress-"),
													mode: "aspectFill",
													thumbnail: "false"
												})
											);
										})
								  )
								: apivm.h("image", {
										style: "width:100%;height: " + G$1.pageWidth * 0.5 + "px;",
										src: showImg(this.props.item, "1029x516-compress-"),
										mode: "aspectFill",
										thumbnail: "false"
								  })
						),
						apivm.h(
							"view",
							null,
							this.props.item.code != "7" &&
								apivm.h(
									"view",
									{style: "margin-top:10px;", class: "flex_row"},
									apivm.h(
										"view",
										{class: "flex_w flex_row"},
										this.props.layoutId != "SJCXS" &&
											apivm.h(
												"text",
												{
													class: "c_999 " + (this.props.item.url ? "text_one" : ""),
													style: "margin-right:10px;" + loadConfiguration(-4) + ";"
												},
												dayjs(this.props.item.time).format("YYYY-MM-DD")
											),
										apivm.h(
											"text",
											{class: "c_999 text_one", style: loadConfiguration(-4) + ";"},
											this.props.item.source
										)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										isObject(this.props.floatModule) &&
											this.props.item.content &&
											platform() == "app" &&
											api.require("voiceRecognizer") &&
											apivm.h(
												"view",
												{onClick: this.playing, class: "flex_row"},
												apivm.h("a-iconfont", {
													name: this.getPlayState().src,
													color: G$1.appTheme,
													size: G$1.appFontSize
												}),
												apivm.h(
													"text",
													{
														style:
															"margin-left:2px;" +
															loadConfiguration(-3) +
															";color:" +
															G$1.appTheme +
															";"
													},
													this.getPlayState().text
												)
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										this.props.item.sourceModuleName &&
											apivm.h(
												"z-tag",
												{style: "margin-left:10px;", type: "2", color: G$1.appTheme},
												this.props.item.sourceModuleName
											)
									)
								)
						)
					]
				),
				apivm.h(
					"view",
					null,
					(this.props.item.showCode != "QUANLANSP" || !this.props.item.videoHrefs) &&
						!isArray(this.props.item.url) &&
						this.props.item.code != "7" &&
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								this.props.item.url &&
									apivm.h(
										"view",
										{class: "news_item_img"},
										apivm.h("image", {
											class: "xy_100",
											src: showImg(this.props.item, "336x252-compress-"),
											mode: "aspectFill",
											thumbnail: "false"
										}),
										this.props.item.videoHrefs &&
											this.props.item.showCode == "ZUOCESP" &&
											apivm.h(
												"view",
												{
													style:
														"position:absolute;z-index:1;left:0;top:0;right:0;bottom:0;align-items: center;justify-content: center;",
													onClick: function(e) {
														return this$1.openVideos(
															e,
															this$1.props.item,
															this$1.props.index
														);
													}
												},
												apivm.h("image", {
													mode: "aspectFill",
													style: loadConfigurationSize(22),
													thumbnail: "false",
													src: shareAddress(1) + "image/icon_play.png"
												})
											)
									)
							),
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h(
									"view",
									{class: "flex_row", style: "flex-wrap: wrap;"},
									this.props.item.title.split("").map(function(nItem, nIndex) {
										return (
											nIndex <
												Math.floor(
													(G$1.pageWidth - 40 - (this$1.props.item.url ? 122 : 0)) /
														(G$1.appFontSize + 2)
												) *
													2 &&
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(1) +
														"margin:2px 0;font-weight: 600;color: " +
														(this$1.props.search &&
														this$1.props.search.result &&
														this$1.props.search.result.indexOf(nItem) > -1
															? G$1.appTheme
															: "#333") +
														";"
												},
												nIndex ==
													Math.floor(
														(G$1.pageWidth - 40 - (this$1.props.item.url ? 122 : 0)) /
															(G$1.appFontSize + 2)
													) *
														2 -
														1
													? "..."
													: nItem
											)
										);
									})
								),
								apivm.h(
									"view",
									null,
									this.props.item.url &&
										apivm.h(
											"text",
											{
												class: "c_999 text_one",
												style: "margin-top:2px;" + loadConfiguration(-4) + ";"
											},
											this.props.item.source
										)
								),
								apivm.h(
									"view",
									{class: "flex_row", style: "margin-top:3px;"},
									apivm.h(
										"view",
										{class: "flex_w flex_row"},
										this.props.layoutId != "SJCXS" &&
											apivm.h(
												"text",
												{
													class: "c_999 " + (this.props.item.url ? "text_one" : ""),
													style: "margin-right:10px;" + loadConfiguration(-4) + ";"
												},
												dayjs(this.props.item.time).format("YYYY-MM-DD")
											),
										!this.props.item.url &&
											apivm.h(
												"text",
												{class: "c_999 text_one", style: loadConfiguration(-4) + ";"},
												this.props.item.source
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										isObject(this.props.floatModule) &&
											this.props.item.content &&
											platform() == "app" &&
											api.require("voiceRecognizer") &&
											apivm.h(
												"view",
												{onClick: this.playing, class: "flex_row"},
												apivm.h("a-iconfont", {
													name: this.getPlayState().src,
													color: G$1.appTheme,
													size: G$1.appFontSize
												}),
												apivm.h(
													"text",
													{
														style:
															"margin-left:2px;" +
															loadConfiguration(-4) +
															";color:" +
															G$1.appTheme +
															";"
													},
													this.getPlayState().text
												)
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										this.props.item.sourceModuleName &&
											apivm.h(
												"z-tag",
												{style: "margin-left:10px;", type: "2", color: G$1.appTheme},
												this.props.item.sourceModuleName
											)
									)
								)
							)
						)
				)
			);
		};

		return Item5;
	})(Component);
	Item5.css = {
		".c_999": {color: "#999"},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".news_item": {padding: "15px 16px"},
		".news_item_img": {
			width: "112px",
			height: "84px",
			marginRight: "10px",
			borderRadius: "2px"
		},
		".news_point": {
			position: "absolute",
			zIndex: "2",
			left: "10px",
			top: "21px",
			borderRadius: "50%",
			width: "7px",
			height: "7px"
		},
		".news_line_top": {
			position: "absolute",
			zIndex: "2",
			left: "13px",
			top: "0",
			height: "24px",
			width: "1px"
		},
		".news_line_bottom": {
			position: "absolute",
			zIndex: "2",
			left: "13px",
			top: "24px",
			bottom: "0",
			width: "1px"
		}
	};
	apivm.define("item5", Item5);

	var ZAiAssistant = /*@__PURE__*/ (function(Component) {
		function ZAiAssistant(props) {
			Component.call(this, props);
		}

		if (Component) ZAiAssistant.__proto__ = Component;
		ZAiAssistant.prototype = Object.create(Component && Component.prototype);
		ZAiAssistant.prototype.constructor = ZAiAssistant;
		ZAiAssistant.prototype.pageClick = function() {
			this.fire("click", {});
		};
		ZAiAssistant.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					onClick: function() {
						return this$1.pageClick();
					},
					class: "ai_box",
					style:
						"display:" +
						(this.props.show && getPrefs("sys_whetherAiChat") == "true"
							? "flex"
							: "none") +
						";"
				},
				this.props.show &&
					getPrefs("sys_whetherAiChat") == "true" && [
						apivm.h("image", {
							class: "ai_img",
							mode: "aspectFill",
							thumbnail: "false",
							src: appUrl() + "pageImg/open/IntelligentAssistant"
						}),
						apivm.h(
							"view",
							{
								class: "ai_warp",
								style:
									"background:linear-gradient(to bottom, " +
									colorRgba(G$1.appTheme, 1) +
									", " +
									colorRgba(G$1.appTheme, 0.6) +
									");"
							},
							apivm.h(
								"text",
								{style: "" + loadConfiguration(-2), class: "ai_text"},
								this.props.text || "AI助手"
							)
						)
					]
			);
		};

		return ZAiAssistant;
	})(Component);
	ZAiAssistant.css = {
		".ai_box": {position: "absolute", right: "0", top: "35%", padding: "5px 0"},
		".ai_warp": {
			padding: "5px 7px 5px 35px",
			minWidth: "94px",
			background: "#f92323",
			borderTopLeftRadius: "29px",
			borderBottomLeftRadius: "29px",
			alignContent: "center",
			justifyContent: "center"
		},
		".ai_img": {
			width: "30px",
			height: "30px",
			position: "absolute",
			left: "5px",
			top: "0",
			zIndex: "1"
		},
		".ai_text": {width: "100%", textAlign: "center", color: "#FFF"}
	};
	apivm.define("z-ai-assistant", ZAiAssistant);

	var shouji = "";
	var zhanghaomima = "";
	var yanzhengma2 = "";
	var bofang1 = "";
	var dingyue = "";
	var shijuan = "";
	var dingwei2 = "";
	var shijian = "";
	var dingyue1 = "";
	var shijian1 = "";
	var tianxie1 = "";
	var mingpian = "";
	var tongguo = "";
	var butongguo = "";
	var wenjian1 = "";
	var tongjifenxi = "";
	var tigong = "";
	var tishenghangdong = "";
	var dengdai = "";
	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		shouji: shouji,
		zhanghaomima: zhanghaomima,
		yanzhengma2: yanzhengma2,
		bofang1: bofang1,
		dingyue: dingyue,
		shijuan: shijuan,
		dingwei2: dingwei2,
		shijian: shijian,
		dingyue1: dingyue1,
		shijian1: shijian1,
		"biaoqian-shenpichehui": "",
		"biaoqian-shenpitongguo": "",
		"biaoqian-shenpibohui": "",
		tianxie1: tianxie1,
		mingpian: mingpian,
		tongguo: tongguo,
		butongguo: butongguo,
		wenjian1: wenjian1,
		tongjifenxi: tongjifenxi,
		"a-zu1263": "",
		"a-icon-person-nan2": "",
		"a-icon-person-nv2": "",
		tigong: tigong,
		tishenghangdong: tishenghangdong,
		dengdai: dengdai,
		"baojia-04": "",
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_kjjniy9oac8.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if ((this.headTitle || "") != G$1.headTitle) {
							this.headTitle = G$1.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G$1.showHeadTheme || G$1.headTheme)) {
							this.headTheme = G$1.showHeadTheme || G$1.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G$1.onShowNum != this.onShowNum
					) {
						this.onShowNum = G$1.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G$1.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G$1.chatInfos = [];
				G$1.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G$1.headTitle) {
							G$1.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G$1._this = that;
					var token = that.data.pageParam.token;
					if (token) {
						setPrefs("sys_token", decodeURIComponent(token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G$1.alertPop.show) {
							if (G$1.alertPop.cancel.show) {
								G$1.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G$1, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G$1.nTouchmove) {
							return;
						}
						if (!traverseList(G$1, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G$1.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G$1.showCaptcha = getPrefs("enableShortAuth") == "true";
			G$1.appName = getPrefs("sys_systemName") || "";
			G$1.terminal = getPrefs("terminal") || "";
			G$1.appFont = getPrefs("appFont") || "heitiSimplified";
			G$1.appFontSize = Number(getPrefs("appFontSize") || "16");
			G$1.sysSign = sysSign();
			G$1.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G$1.sysSign) ||
				G$1.appTheme;
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G$1.headTheme = headTheme == "appTheme" ? G$1.appTheme : headTheme;
			G$1.careMode = parseInt(G$1.appFontSize) > 16;
			G$1.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G$1.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G$1.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G$1.v = getPrefs("sys_appVersion") || "";

			G$1.uId = getPrefs("sys_Id") || "";
			G$1.userId = getPrefs("sys_UserID") || "";
			G$1.userName = getPrefs("sys_UserName") || "";
			G$1.userImg = getPrefs("sys_AppPhoto") || "";
			G$1.areaId = getPrefs("sys_aresId") || "";
			G$1.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G$1.isAdmin =
				G$1.userId == "1" ||
				getItemForKey("dc_admin", G$1.specialRoleKeys) ||
				getItemForKey("admin", G$1.specialRoleKeys);
			G$1.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G$1.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G$1.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G$1.appName);
				if (t) {
					G$1.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G$1.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G$1.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G$1.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G$1.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G$1.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G$1.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G$1.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G$1.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G$1.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G$1.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G$1.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G$1.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) +
																	"font-weight: 600;color:" +
																	G$1.headColor
															},
															showTextSize(
																this.data.title,
																Math.floor((G$1.pageWidth - 200) / (G$1.appFontSize + 5)),
																true
															)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G$1.headColor,
																size: G$1.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G$1.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G$1.areasPop}),
						apivm.h("select-item", {dataMore: G$1.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G$1.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G$1.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "888",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "888",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "888",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		"[scroll-y]": {overflowY: "overlay !important"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var MoMeetingBacklogDetails = /*@__PURE__*/ (function(Component) {
		function MoMeetingBacklogDetails(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				dTitle: "",
				MG: !this.props.dataMore ? G$1 : null,
				emptyBox: {
					type: "load",
					text: ""
				},

				showData: {},
				listData: [],
				addMore: {},
				isBacklog: false,
				isAudit: false,
				readonly: false, //只读
				submitNoPassText: "",
				submitText: "提交成果物",
				taskAchievement: null,
				ai: "",
				description: "",
				achievement: "",
				nowTime: "",
				flowInfo: {
					show: true,
					open: true,
					data: []
				}
			};
		}

		if (Component) MoMeetingBacklogDetails.__proto__ = Component;
		MoMeetingBacklogDetails.prototype = Object.create(
			Component && Component.prototype
		);
		MoMeetingBacklogDetails.prototype.constructor = MoMeetingBacklogDetails;
		MoMeetingBacklogDetails.prototype.onShow = function() {
			G$1.onShowNum++;
		};
		MoMeetingBacklogDetails.prototype.openAI = function() {
			openWin_ai(
				"type=" +
					this.data.ai +
					"&id=" +
					this.id +
					"&description=" +
					this.data.description +
					"&achievement=" +
					this.data.achievement
			);
		};
		MoMeetingBacklogDetails.prototype.baseInit = function(ref) {
			var detail = ref.detail;

			this.data.dTitle = "详情";
			this.id = this.data.pageParam.id || "1905455003686957058";
			this.data.nowTime = new Date().getTime();
			this.pageRefresh();
		};
		MoMeetingBacklogDetails.prototype.pageRefresh = function() {
			this.getData();
		};
		MoMeetingBacklogDetails.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		MoMeetingBacklogDetails.prototype.getData = function() {
			var this$1 = this;

			getDetails55({code: this.code, param: {detailId: this.id}}, function(
				ret,
				err
			) {
				hideProgress();
				this$1.getPageData();
				var data = ret ? ret.dealWith || "" : "";
				this$1.data.flowInfo.data = data.taskScheduleVoList;
				console.log(this$1.data.flowInfo.data);
				this$1.data.emptyBox.type = !data ? (ret ? "1" : "2") : "";
				this$1.data.emptyBox.text = !data
					? ret && ret.code != 200
						? ret.message || ret.data
						: ""
					: "";
				if (data.auditState == "0" && data.createBy == G$1.uId) {
					this$1.data.ai = "mara";
					this$1.data.isBacklog = true;
					this$1.data.submitNoPassText = "不通过";
					this$1.data.submitText = "通过";
					this$1.data.isAudit = true;
					this$1.data.listData = [
						{
							type: "textarea",
							html: false,
							title: "审核意见",
							titleHint: "",
							hint: "请输入意见内容",
							key: "auditOpinion",
							value: "",
							defaultValue: "",
							noRequired: false,
							height: "200",
							line: 1
						}
					];
				} else if (
					data.auditState == "no" &&
					data.userId == G$1.uId &&
					this$1.data.nowTime < data.endTime
				) {
					this$1.data.ai = "maru";
					this$1.data.isBacklog = true;
					this$1.data.listData = [
						{
							type: "textarea",
							html: false,
							title: "成果内容",
							titleHint: "",
							hint: "请输入成果内容",
							key: "content",
							value: "",
							defaultValue: "",
							noRequired: false,
							height: "200",
							line: 1
						},
						{
							type: "attach",
							title: "附件上传",
							hint: "请上传附件",
							key: "attachmentIds",
							value: [],
							defaultValue: [],
							max: 9,
							valueKey: "id",
							valueType: ",",
							noRequired: true,
							line: 10
						}
					];
				} else if (data.auditState == "0") {
					this$1.data.isBacklog = false;
				}
				this$1.data.description = data.description;
				this$1.data.showData = data;
			});
		};
		MoMeetingBacklogDetails.prototype.getPageData = function() {
			var this$1 = this;

			var postParam = {
				pageNo: 1,
				pageSize: 999,
				query: {
					taskId: this.id
				}
			};

			getTaskAchievementList({param: postParam}, function(ret) {
				var data = ret ? ret.dealWith || [] : [];
				if (data.length) {
					var item = {};
					item.content = data[0].content;
					item.attachments = data[0].attachments;
					this$1.data.achievement = item.content;
					this$1.data.taskAchievement = item;
				}
			});
		};
		MoMeetingBacklogDetails.prototype.getTagColor = function(_state) {
			var cs = {
				待完成: "#559fff",
				待审核: "#F6931C",
				已完成: "#50c614",
				审核通过: "#50c614",
				逾期: "#E02B2B"
			}[_state];
			return cs || "#666";
		};
		MoMeetingBacklogDetails.prototype.submit = function(_type) {
			var this$1 = this;

			var url = appUrl() + "zyMeetTaskAchievement/add ";
			var param = {};
			var getPostParam = addPostParam(this.data.listData);
			if (!getPostParam) {
				return;
			}
			if (this.data.isAudit) {
				url = appUrl() + "zyMeetTask/auditState";
				param.ids = [this.id];
				param.auditState = _type == 2 ? _type : 1;
				param = setNewJSON(param, getPostParam);
			} else {
				param = {form: setNewJSON(param, getPostParam)};
				param.form.taskId = this.id;
			}
			setParamToFirst(this.data.listData, param);
			var hintText = _type ? this.data.submitNoPassText : this.data.submitText;
			ajaxAlert(
				{
					msg: "确定" + hintText + "吗?",
					url: url,
					param: param,
					toast: hintText + "中"
				},
				function(ret) {
					if (ret && ret.code == "200") {
						this$1.data.isBacklog = false;
						this$1.data.isAudit = false;
						toast(ret.message);
						this$1.getData();
					}
				}
			);
		};
		MoMeetingBacklogDetails.prototype.inputIng = function(e) {
			var detail = e.detail || {};
			switch (detail.key) {
				case "content":
					this.data.achievement = detail.value;
					break;
			}
		};
		MoMeetingBacklogDetails.prototype.openFlow = function() {
			this.data.flowInfo.open = !this.data.flowInfo.open;
		};
		MoMeetingBacklogDetails.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{_this: this, dataMore: this.props.dataMore, more: true},
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h(
					"view",
					{class: "flex_h"},
					apivm.h(
						"y-scroll-view",
						{_this: this, refresh: true},
						apivm.h(
							"view",
							null,
							this.data.showData &&
								apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{class: "details_title_box"},
										apivm.h(
											"text",
											{style: "" + loadConfiguration(4), class: "details_title_text"},
											this.data.showData.name
										)
									),
									apivm.h(
										"view",
										{style: "flex-direction:row; align-items: center;"},
										apivm.h(
											"view",
											{class: "details_add_box"},
											apivm.h(
												"text",
												{style: loadConfiguration(-1) + "color: #666;padding: 1px 0px;"},
												"截止时间：",
												dayjs(this.data.showData.endTime).format("YYYY-MM-DD")
											)
										),
										apivm.h(
											"view",
											{class: "details_add_right_box"},
											this.data.showData.auditStateName &&
												apivm.h("z-tag", {
													style: "" + loadConfiguration(-2),
													color: this.getTagColor(this.data.showData.auditStateName),
													text: this.data.showData.auditStateName
												})
										)
									),
									apivm.h(
										"text",
										{style: loadConfiguration(-1) + "color: #666;padding: 5px 16px"},
										"任务创建人：",
										this.data.showData.createName
									),
									apivm.h(
										"text",
										{style: loadConfiguration(-1) + "color: #666;padding: 5px 16px"},
										"指派人员：",
										this.data.showData.userName
									),
									apivm.h(
										"text",
										{style: loadConfiguration(-1) + "color: #666;padding: 5px 16px"},
										"优先级：",
										this.data.showData.priorityName
									),
									apivm.h(
										"text",
										{style: loadConfiguration(-1) + "color: #666;padding: 0 16px"},
										"待办类型：",
										this.data.showData.taskTypeName
									),
									apivm.h(
										"view",
										{style: "padding: 10px 16px;"},
										apivm.h("z-rich-text", {
											size: 2,
											detail: true,
											nodes: this.data.showData.description
										})
									),
									apivm.h(
										"view",
										{class: "details_backlog_achievement_box"},
										apivm.h("view", {
											style:
												loadConfigurationSize(-1, "h") +
												"width:3px; border-radius: 10px;background:" +
												G$1.appTheme +
												";"
										}),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"flex:1;font-weight: 600;color: #333333;margin-left:5px;"
											},
											"待办成果"
										)
									),
									apivm.h(
										"view",
										null,
										this.data.taskAchievement &&
											apivm.h(
												"view",
												{style: "padding: 10px 16px"},
												apivm.h("z-rich-text", {
													size: 2,
													detail: true,
													nodes: this.data.taskAchievement.content
												}),
												apivm.h(
													"view",
													null,
													this.data.taskAchievement.attachments.length > 0 &&
														apivm.h("y-attachments", {
															style: "margin-top:7px;",
															data: this.data.taskAchievement.attachments
														})
												)
											)
									),
									apivm.h(
										"view",
										null,
										this.data.isAudit &&
											apivm.h(
												"view",
												{
													class: "details_backlog_achievement_box",
													style: "padding-bottom:0;"
												},
												apivm.h("view", {
													style:
														loadConfigurationSize(-1, "h") +
														"width:3px; border-radius: 10px;background:" +
														G$1.appTheme +
														";"
												}),
												apivm.h(
													"text",
													{
														style:
															loadConfiguration(1) +
															"flex:1;font-weight: 600;color: #333333;margin-left:5px;"
													},
													"待办审核"
												)
											)
									),
									apivm.h(
										"view",
										null,
										this.data.isBacklog &&
											apivm.h(
												"view",
												null,
												apivm.h(
													"view",
													{style: "padding:0 5px"},
													this.data.listData.map(function(item, index) {
														return [
															item.type == "textarea"
																? [
																		apivm.h("item-textarea", {
																			dataMore: this$1.data.addMore,
																			item: item,
																			index: index,
																			onInput: this$1.inputIng
																		})
																  ]
																: item.type == "attach"
																? [
																		apivm.h("item-attach", {
																			dataMore: this$1.data.addMore,
																			item: item,
																			index: index
																		})
																  ]
																: []
														];
													})
												),
												apivm.h(
													"view",
													{class: "flex_row", style: "margin:20px 0;padding:0 20px;"},
													this.data.submitNoPassText &&
														apivm.h("z-button", {
															onClick: function() {
																return this$1.submit(2);
															},
															plain: true,
															disabled: this.data.readonly,
															round: true,
															style: "flex:1;padding:7px 0px;margin-right:15px;",
															color: G$1.appTheme,
															text: this.data.submitNoPassText
														}),
													apivm.h("z-button", {
														onClick: function() {
															return this$1.submit();
														},
														disabled: this.data.readonly,
														round: true,
														style: "flex:1;padding:7px 0px;",
														color: G$1.appTheme,
														text: this.data.submitText
													})
												)
											)
									),

									apivm.h(
										"view",
										{style: "padding:15px 16px;border-top:10px solid #F8F8F8;"},
										apivm.h(
											"view",
											{style: "flex-direction:row;align-items: center;"},
											apivm.h("view", {
												style:
													"width:3px;height:" +
													(G$1.appFontSize - 1) +
													"px;border-radius: 10px;background: " +
													G$1.appTheme +
													";"
											}),
											apivm.h(
												"view",
												{style: "margin:0px 5px;flex:1;flex-direction:row;"},
												apivm.h(
													"text",
													{style: loadConfiguration(1) + "font-weight: 600;color: #333;"},
													"任务进度记录"
												)
											),
											apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.openFlow();
													},
													style: "flex-direction:row;align-items: center;"
												},
												apivm.h(
													"text",
													{
														style:
															loadConfiguration(-2) +
															"font-weight: 400;color: #333;margin-right:2px;"
													},
													this.data.flowInfo.open ? "收起" : "展开"
												),
												apivm.h("a-iconfont", {
													name: "xiangxia1",
													style:
														"transform: rotate(" +
														(this.data.flowInfo.open ? "18" : "") +
														"0deg);",
													color: "#333",
													size: G$1.appFontSize - 2
												})
											)
										),
										this.data.flowInfo.open &&
											apivm.h(
												"view",
												null,
												(Array.isArray(this.data.flowInfo.data)
													? this.data.flowInfo.data
													: Object.values(this.data.flowInfo.data)
												).map(function(item$1, index$1, list) {
													return apivm.h(
														"view",
														{style: "flex-direction:row;"},
														apivm.h(
															"view",
															{style: "width:34px;align-items: center;"},
															apivm.h("view", {
																class: "suggestion_line_top",
																style:
																	"background:" +
																	T.colorRgba(G$1.appTheme, index$1 ? 0.15 : 0) +
																	";"
															}),
															apivm.h("view", {
																class: "suggestion_line_bottom",
																style:
																	"background:" +
																	T.colorRgba(
																		G$1.appTheme,
																		index$1 == list.length - 1 ? 0 : 0.15
																	) +
																	";"
															}),
															apivm.h(
																"view",
																{class: "suggestion_point"},
																apivm.h("a-iconfont", {
																	name: "yuanxingxuanzhongfill",
																	color: G$1.appTheme,
																	size: 15
																})
															)
														),
														apivm.h(
															"view",
															{style: "margin:15px 0;width:1px;flex:1;"},
															apivm.h(
																"view",
																{
																	style:
																		"flex-direction:row;align-items: center;margin-bottom:10px;"
																},
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration() + "font-weight: 600;color: #333;flex:1;"
																	},
																	item$1.name
																),
																apivm.h(
																	"text",
																	{
																		style: loadConfiguration(-2) + "font-weight: 400;color: #999;"
																	},
																	dayjs(item$1.date).format("YYYY-MM-DD HH:mm")
																)
															),
															apivm.h(
																"view",
																{style: "flex-direction:row;align-items: flex-start;"},
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(-2) +
																			"font-weight: 600;color: #333;flex-shrink: 0;"
																	},
																	item$1.name == "创建任务" ? "创建人" : "提交人",
																	"："
																),
																apivm.h(
																	"text",
																	{
																		style: loadConfiguration(-2) + "font-weight: 400;color: #333;"
																	},
																	item$1.createName
																)
															),
															item$1.auditState &&
																apivm.h(
																	"view",
																	null,
																	apivm.h(
																		"view",
																		{style: "flex-direction:row;align-items: flex-start;"},
																		apivm.h(
																			"text",
																			{
																				style:
																					loadConfiguration(-2) +
																					"font-weight: 600;color: #333;flex-shrink: 0;"
																			},
																			"审核结果："
																		),
																		apivm.h(
																			"text",
																			{
																				style:
																					loadConfiguration(-2) + "font-weight: 400;color: #333;"
																			},
																			item$1.auditState
																		)
																	),
																	apivm.h(
																		"view",
																		{style: "flex-direction:row;align-items: flex-start;"},
																		apivm.h(
																			"text",
																			{
																				style:
																					loadConfiguration(-2) +
																					"font-weight: 600;color: #333;flex-shrink: 0;"
																			},
																			"审核意见："
																		),
																		apivm.h(
																			"text",
																			{
																				style:
																					loadConfiguration(-2) + "font-weight: 400;color: #333;"
																			},
																			item$1.auditOpinion
																		)
																	)
																)
														)
													);
												})
											)
									)
								)
						),
						apivm.h(
							"view",
							null,
							this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
						),
						apivm.h("z-empty", {
							_this: this,
							dataMore: this.data.emptyBox,
							onRefresh: this.pageRefresh
						}),
						apivm.h("view", {
							style:
								"padding-bottom:" +
								(!this.data.pageParam.footerH ? safeArea().bottom : 0) +
								"px;"
						})
					),
					apivm.h("z-ai-assistant", {
						show: this.data.ai,
						onClick: function() {
							return this$1.openAI();
						}
					})
				)
			);
		};

		return MoMeetingBacklogDetails;
	})(Component);
	MoMeetingBacklogDetails.css = {
		".details_title_box": {padding: "10px 16px 0", marginBottom: "10px"},
		".details_title_text": {fontWeight: "600", color: "#333333"},
		".details_add_box": {
			paddingLeft: "16px",
			flexDirection: "row",
			width: "1px",
			flex: "1"
		},
		".details_add_right_box": {paddingRight: "16px", flexDirection: "row"},
		".details_backlog_achievement_box": {
			borderTop: "10px solid rgb(248, 248, 248)",
			padding: "15px 16px 0",
			flexDirection: "row",
			alignItems: "center"
		},
		".suggestion_point": {
			position: "absolute",
			zIndex: "1",
			left: "9px",
			top: "17px"
		},
		".suggestion_line_top": {
			position: "absolute",
			zIndex: "1",
			left: "16px",
			top: "0",
			height: "18px",
			width: "1px"
		},
		".suggestion_line_bottom": {
			position: "absolute",
			zIndex: "1",
			left: "16px",
			top: "18px",
			bottom: "0",
			width: "1px"
		}
	};
	apivm.define("mo-meeting-backlog-details", MoMeetingBacklogDetails);
	apivm.render(apivm.h("mo-meeting-backlog-details", null), "body");
})();
