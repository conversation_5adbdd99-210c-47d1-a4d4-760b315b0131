(function() {
	var LOAD_ING = "加载中，请稍候...";
	var LOAD_MORE = "点击加载更多";
	var LOAD_ALL = "已加载完";
	var NET_ERR = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否数字
	function isNumber(_obj) {
		return isParameters(_obj) && typeof _obj === "number";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//移除字符串所有标签
	function removeTag(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//转义字符串
	function decodeCharacter(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;|&emsp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//重启
	function rebootApp() {
		if (platform() == "web") {
			window.location.reload();
		} else if (platform() == "mp") {
			wx.reLaunch({url: "/pages/index/index"});
		} else {
			api.rebootApp();
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//移除监听
	function removeEventListener(name) {
		if (
			platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			delItemForKey(name, window.baseEventList);
		} else {
			try {
				api.removeEventListener({name: name});
			} catch (e) {}
		}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (G.headTheme != getPrefs("headTheme") && G.headTheme != "transparent") {
			o.pageParam.headTheme = G.headTheme;
		}
		if (
			G.appTheme != getPrefs("appTheme" + G.sysSign) &&
			G.appTheme != (G.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出actionSheet
	function actionSheet(_param, _callback) {
		var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
		o = setNewJSON(o, _param);
		var oldButton = o.buttons || [],
			newButton = [];
		oldButton.forEach(function(item) {
			newButton.push(isObject(item) ? item : {name: item});
		});
		var actionSheetBox = {
			title: o.title,
			cancel: o.cancelTitle,
			data: newButton,
			active: o.active,
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			},
			pageClose: function pageClose() {
				_param.pageClose && _param.pageClose();
			}
		};

		G.actionSheetPop = actionSheetBox;
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	function getPicture(_param, callback) {
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = setNewJSON(o, _param);
		try {
			if (platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						if (ret.value.granted) {
							getPicture(_param, callback);
						}
						removeEventListener(preName + "Per_" + "getPicture");
					});
					return;
				}
				if (o.sourceType == "camera") {
					api.getPicture(o, function(ret, err) {
						isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	}

	function hasPermission(one_per) {
		if (platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	}

	function requestPermission(one_per, callback, _fName) {
		if (platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				isFunction(callback) && callback(ret, err);
			});
		}
	}

	function confirmPer(perm, _fName, _reason) {
		if (platform() == "app") {
			var has = hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				alert(
					{
						title: "无法使用" + hintWord[perm],
						msg: api.systemType == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret) {
						if (1 == ret.buttonIndex) {
							requestPermission(perm, null, _fName);
						} else {
							sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	function getMemberIdentity(role) {
		var rName = G.sysSign == "rd" ? "代表" : "委员";
		switch (role) {
			case "nation":
				return "国" + rName;
			case "province":
				return "省" + rName;
			case "city":
				return "市" + rName;
			case "county":
				return "区/县" + rName;
			case "town":
				return "乡镇(街道)" + rName;
		}
	}

	//获取工作站留言列表
	function getList50_2(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationLetter/managelist",
				param: setNewJSON({tableId: "id_station_letter"}, _param.param),
				areaId: _param.areaId,
				tag: "list50_2",
				name: "工作站留言"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module50_2.code;
						item.id = _eItem.id || "";
						item.title = _eItem.title || "";
						item.checkStatus = _eItem.checkStatus || 0;
						item.hasAnswer = _eItem.hasAnswer;
						item.hasEvaluate = _eItem.hasEvaluate;
						item.evaluateStatus = _eItem.evaluateStatus || 0;
						var tag = item.checkStatus;
						if (item.hasEvaluate && item.hasEvaluate != "0") {
							tag = 4;
						} else if (item.hasAnswer) {
							tag = 3;
						}
						item.tagNum = tag;
						item.tag = [
							{text: "待审核", color: "#e6a23c"},
							{text: "未回复", color: "#F6931C"},
							{text: "审核不通过", color: "#BF2222"},
							{text: "已回复", color: "#3894FF"},
							{text: "已评价", color: "#2BBD4B"}
						][tag];
						item.tag1 = [
							{text: "未回复", color: "#F6931C"},
							{text: "已回复", color: "#3894FF"},
							{text: "已评价", color: "#2BBD4B"}
						][
							item.hasEvaluate && item.hasEvaluate != "0" ? 2 : item.hasAnswer ? 1 : 0
						];
						if (item.hasEvaluate && !item.evaluateStatus) {
							item.tag2 = {text: "待审核", color: "#e6a23c"};
						} else if (item.checkStatus == 0 || item.checkStatus == 2) {
							item.tag2 = [
								{text: "待审核", color: "#e6a23c"},
								{text: "审核不通过", color: "#BF2222"}
							][item.checkStatus == 0 ? 0 : 1];
						}
						item.userId = _eItem.senderId || "";
						item.source = replaceExceptFirst(_eItem.senderUserName || "");
						item.time = dayjs(_eItem.receiveTime).format("YYYY-MM-DD HH:mm");
						item.receiverId = _eItem.receiverId || "";
						item.stationId = _eItem.stationId || "";
						item.mine = (_param.pageParam || {}).mine;
						item.btn = "";
						if (item.mine) {
							item.btn = tag == 3 && item.userId == G.userId ? "2" : "";
						} else {
							var reviews =
								getItemForKey("station_worker", G.specialRoleKeys) ||
								getItemForKey("station_member", G.specialRoleKeys);
							if (tag == 0 && _eItem.showCheckStatus == 0 && reviews) {
								item.btn = "0";
							} else {
								var review =
									G.isAdmin || getItemForKey("station_worker", G.specialRoleKeys); // || getItemForKey('station_admin',G.specialRoleKeys)
								item.btn = tag == 1 && review && item.userId != G.userId ? "1" : "";
							}
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取配置信息 公开的
	function getAppInfos(_param, _callback) {
		var postParam = {
			codes: [
				"systemName",
				"appOnlyHeader",
				"appTomcatAddress",
				"appDownloadUrl",
				"systemNameAreaPrefix",
				"platformAreaId",
				"systemType",
				"infomationTop",
				"appShareAddress",
				"appVersion",
				"appReviewVersion",
				"reddotSwitch",
				"systemGrayscale",
				"systemWatermark",
				"systemLoginContact",
				"appMapWebKey",
				"appInitUserGroup"
			]
		};

		ajax(
			{u: appUrl() + "config/openRead"},
			"config/openRead",
			function(ret, err) {
				var data = ret ? ret.data || "" : "";
				if (data) {
					G.headTitle = data.systemName || "";
					setPrefs("sys_systemName", G.headTitle);
					setPrefs(
						"sys_chatHeader",
						data.appOnlyHeader ||
							appUrl()
								.split("://")[1]
								.replace(/[^a-zA-Z0-9]/g, "")
					);
					setPrefs("sys_appVersion", data.appVersion || "");
					setPrefs("sys_shareAddress", data.appShareAddress); //app分享地址
					setPrefs("sys_tomcatAddress", data.appTomcatAddress); //保存一下系统服务地址
					setPrefs("sys_appDownloadUrl", data.appDownloadUrl || ""); //app下载地址
					setPrefs("sys_platform", data.platformAreaId || ""); //平台系统最高地区id
					setPrefs("sys_systemType", data.systemType || ""); //系统类型：（平台版：platform）（标准版：standard）
					setPrefs("infomationTop", data.infomationTop || "5"); //资讯头条数量
					setPrefs("sys_appReviewVersion", data.appReviewVersion || "{}"); //上架期间版本号{"ios":"","android":""}
					setPrefs("sys_reddotSwitch", data.reddotSwitch || "0"); //开启红点提示 1是0否
					setPrefs("sys_appInitUserGroup", data.appInitUserGroup || "0"); //是否获取全局通讯录 1是0否
					setPrefs("sys_grayscale", data.systemGrayscale || ""); //置灰
					setPrefs("sys_watermark", data.systemWatermark || ""); //水印
					setPrefs("sys_systemLoginContact", data.systemLoginContact || ""); //登录页面运维人员信息
					setPrefs("sys_systemNameAreaPrefix", data.systemNameAreaPrefix || ""); //是否系统名称加地区前缀
					setPrefs(
						"sys_appMapWebKey",
						data.appMapWebKey || "5e2e9231ea0d9fedc723a86b8f8a5ed2"
					);
				}
				sendEvent({name: "sys_refresh"});
				_callback && _callback();
			},
			"取app配置",
			"post",
			{
				body: JSON.stringify(postParam)
			},
			{Authorization: ""}
		);
	}

	//获取配置信息 登录后
	function getAppConfig(_param, _callback) {
		var postParam = {
			codes: isArray(_param) ? _param : _param.codes
		};

		ajax(
			{u: appUrl() + "config/read"},
			"config/read",
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			"取app配置",
			"post",
			{
				body: JSON.stringify(postParam)
			}
		);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	//弹窗提示
	function ajaxAlert(_param, _callback) {
		var param = {
			title: "提示",
			msg: _param.msg || "",
			buttons: ["确定", "取消"]
		};

		if (_param.alertParam) {
			param = setNewJSON(param, _param.alertParam);
		}
		console.log(_param.url + "\n" + JSON.stringify(_param.param));
		alert(param, function(ret) {
			if (ret.buttonIndex == "1") {
				ajaxProcess(_param, _callback);
			}
		});
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	// 点赞/取消点赞
	function optionPraises(_param, _other) {
		var param = {
			businessCode: _param.code,
			businessId: _param.id
		};

		param = setNewJSON(param, _other);
		if (_param.likeIs) {
			param = {form: param};
		}
		ajax(
			{u: appUrl() + ("praises/" + (_param.likeIs ? "add" : "dels"))},
			"praises/add",
			function(ret, err) {},
			(_param.likeIs ? "" : "取消") + "\u70B9\u8D5E",
			"post",
			{
				body: JSON.stringify(param)
			}
		);
	}

	//获取评论点赞总数
	function getCommentCount(_param) {
		ajax(
			{u: appUrl() + "praises/count"},
			"praises/count",
			function(ret, err) {
				var data = ret ? ret.data || {} : {};
				_param.commentNum = data.commentCount || 0;
				_param.likeNum = data.praisesCount || 0;
				_param.likeIs = data.hasClickPraises || false;
			},
			"评论点赞总数",
			"post",
			{
				body: JSON.stringify({
					businessCode: _param.code,
					businessId: _param.id,
					isExcludeSubComment: _param.isExcludeSubComment
				})
			}
		);
	}

	function dealVideoId(_id) {
		return _id.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "");
	}

	//当前页面播放视频集合
	function videoPlayPush(_id) {
		if (!G.playVideos) {
			G.playVideos = [];
		}
		if (getItemForKey(_id, G.playVideos)) {
			return;
		}
		videoPlayRemoves();
		G.playVideos.push(_id);
		console.log("当前播放：" + JSON.stringify(G.playVideos));
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	// 发短信验证码
	function sendCode(_param, _callback) {
		var reg = /^1[3-9]\d{9}$/;
		if (!reg.test(_param.phone)) {
			toast("请输入正确的手机号");
			return;
		}
		showProgress("发送中");
		ajax(
			{u: appUrl() + "open_api/verifyCode/send", web: true},
			"sendcode",
			function(ret, err) {
				hideProgress();
				var code = ret ? ret.code : "";
				if (code == 200) {
					toast("发送成功");
					_callback(ret, err);
				} else {
					toast((ret ? ret.message || ret.data : "") || "发送失败");
				}
			},
			"\u53D1\u9001\u77ED\u4FE1",
			"post",
			{
				body: JSON.stringify({
					sendType: "no_login",
					mobile: _param.phone
				})
			},

			{
				Authorization: ""
			}
		);
	}

	//工作站
	function workStationFocus(_param, _callback) {
		toast(_param.param.focusStatus ? "关注成功" : "已取消关注");
		ajax(
			{u: appUrl() + "workerStation/app/focus"},
			"app/focus",
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			"关注",
			"post",
			{
				body: JSON.stringify(_param.param)
			}
		);
	}

	//生成分享短链接
	function shareLink(_param, _callback) {
		var shareUrl =
			shareAddress() +
			"pages/index/?" +
			JSON.stringify(_param)
				.replace(/\{/g, "%7B")
				.replace(/\}/g, "%7D")
				.replace(/\"/g, "%22");
		ajax(
			{u: appUrl() + "longShortLink/exchange"},
			"longShortLink",
			function(ret, err) {
				var data = ret ? ret.data || "" : "";
				var url = data ? appUrl() + "viewing/" + data : shareUrl;
				console.log(url);
				_callback && _callback(url, data);
			},
			"转短链接",
			"post",
			{
				values: {
					longUrl: shareUrl
				}
			},

			{
				"content-type": "application/x-www-form-urlencoded"
			}
		);
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//底部可视区域
	function footerBottom() {
		return safeArea().bottom;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取微信扫码
	function loadParam(query, _this) {
		var q = decodeURIComponent(query.q || ""),
			qparam = "";
		if (q) {
			qparam = q.substring(q.indexOf("?") + 1);
			if (qparam) {
				_this.data.addPageParam = {};
				qparam.split("&").forEach(function(_eItem) {
					_this.data.addPageParam[_eItem.split("=")[0]] = _eItem.split("=")[1];
				});
				_this.data.pageParam = setNewJSON(
					_this.data.pageParam,
					_this.data.addPageParam
				);
			}
		}
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//移动事件 不左滑关闭屏幕
	function touchmove() {
		G.nTouchmove = true;
		clearTimeout(G.touchmoveTask);
		G.touchmoveTask = setTimeout(function() {
			G.nTouchmove = false;
		}, 1000);
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//列表无数据处理
	function dealData(_type, _this, ret) {
		if (!_type) {
			_this.data.listData = [];
			_this.data.emptyBox.type = ret ? "1" : "2";
			_this.data.emptyBox.text =
				ret && ret.code != 200 ? ret.message || ret.data : "";
		} else {
			_this.data.emptyBox.text = ret
				? ret.code == 200
					? LOAD_ALL
					: ret.message || ret.data
				: NET_ERR;
		}
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
	function uploadFile(_item, callback) {
		if (_item._fileAjax || _item.module == "-noUpload") return;
		_item._fileAjax = true;
		_item.state = 1;
		if (_item.showToast) {
			showProgress("上传中");
		}
		var nCallack = function nCallack(ret, err) {
			hideProgress();
			var code = ret ? ret.code : "";
			if (code == 200) {
				var data = ret.data || {};
				_item.state = 2;
				_item.uploadId = data.id;
				_item.otherInfo = data;
			} else {
				_item.state = 3;
				_item.error = ret ? ret.message || ret.data : err.data || err.body || "";
			}
			callback && callback(_item);
		};
		if (platform() == "mp") {
			wx.uploadFile({
				url: appUrl() + "file/upload",
				filePath: _item.url.path,
				name: "file",
				header: {
					"Content-Type": "multipart/form-data",
					"u-login-areaId": areaId(),
					Authorization: getPrefs("sys_token") || ""
				},

				success: function success(res) {
					nCallack(JSON.parse(res.data), null);
				},
				fail: function fail(err) {
					nCallack(null, JSON.parse(err.data));
				}
			});
		} else {
			ajax(
				{u: appUrl() + "file/upload", web: _item.web},
				"file/upload" + _item.url,
				nCallack,
				"上传附件",
				"post",
				{
					files: {file: _item.url}
				},
				{"content-type": "file"}
			);
		}
	}

	//转换成html格式
	function convertRichText(value) {
		value = (isParameters(value) ? value : "") + "";
		if (isObject(value) || isArray(value)) {
			value = JSON.stringify(value);
		}
		var textList = value.split("\n");
		var str = "";
		for (var i = 0; i < textList.length; i++) {
			var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
			if (addText) {
				str += "<p>" + addText + "</p>";
			}
		}
		return str;
	}

	// 判断是否为微信环境
	function isWeChat() {
		return /micromessenger/i.test(window.navigator.userAgent);
	}

	// 判断是否为小程序环境
	function isMiniProgram() {
		// 通过wx对象存在与否进行判断
		if (typeof wx === "object" && typeof wx.miniprogram === "object") {
			return true;
		} else {
			return false;
		}
	}

	//替换非第一个字符为*
	function replaceExceptFirst(inputString) {
		return inputString.replace(/(\S)(\S*)/g, function(match, firstChar, rest) {
			return firstChar + "*".repeat(rest.length);
		});
	}

	//获取app文本详情
	function getDetailsAppText(_param, _callback) {
		ajax(
			{u: appUrl() + ("apptext/type/" + _param.pt)},
			_param.tag || "details" + _param.pt,
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var id = _param.param.detailId;
					var nowItem = data.length > 1 && id ? getItemForKey(id, data, "id") : null;
					data = nowItem || data[0];
					ret.dealWith = {
						title: "",
						content: data.content || "",
						data: data
					};

					if (!_param.dt) {
						G.headTitle = data.title || "";
					}
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			{Authorization: ""}
		);
	}

	//获取工作站详情
	function getDetails50(_param, _callback) {
		ajax(
			{u: appUrl() + "workerStation/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var item = {
						code: module50.code,
						name: module50.name,
						id: data.id,
						title: data.name || "",
						content: data.introduction || "暂无",
						url: data.showImgs || appUrl() + "pageImg/open/workstation_default"
					};

					item.userName = data.contactUserName || "";
					item.userPhone = data.contactTelephone || "";
					item.openTime = data.openTime || "";
					item.address = data.address || "";
					item.hasFocus = data.hasFocus || 0;
					item.excellentStationType = data.excellentStationType || {};
					item.talkGroupId = data.talkGroupId;
					item.workers = (data.workers || []).map(function(obj) {
						return {id: obj.userId, targetId: obj.accountId, name: obj.userName};
					});
					item.lawPointId = data.lawPointId || "";
					item.cityName = data.cityName || "";
					item.countryName = data.countryName || "";
					ret.dealWith = item;
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站代表详情
	function getDetails50_member(_param, _callback) {
		ajax(
			{u: appUrl() + "stationMember/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var areaName = data.areaName || "";
					var item = {
						id: data.accountId,
						name: data.userName || "",
						url: data.photo || data.headImg || "",
						nation: (data.nation || {}).name || "",
						position: data.position || "",
						stationNames: data.stationNames || "",
						stationIds: data.stationIds || [],
						mobile: data.mobile || "",
						label:
							getMemberIdentity(data.topUserRole) ||
							(areaName ? areaName.split(",")[0] : ""),
						labels: areaName
							? areaName.split(",").map(function(obj) {
									return obj + (G.sysSign == "rd" ? "人大代表" : "政协委员");
							  })
							: [],
						codeType: data.commentCodeType
					};

					ret.dealWith = item;
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站代表履职信息
	function getDetails50_member_other(_param, _callback) {
		ajax(
			{u: appUrl() + "stationTotal/singlemember"},
			_param.tag || "singlemember" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.accountId) {
					var item = {
						activityCount: data.activityCount,
						letterCount: data.letterCount,
						dutyCount: data.dutyCount
					};

					ret.dealWith = item;
				}
				_callback(ret, err);
			},
			"代表履职",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};
	var module50 = {name: "工作站", code: "50"};
	var module50_2 = {name: "工作站留言", code: "50_2"};

	//打开链接
	function openWin_url(_param) {
		_param.url = handleSYSLink(_param.url);
		if (
			(_param.wopen || _param.url.indexOf("wopen") != -1) &&
			platform() == "web"
		) {
			window.open(_param.url);
		} else {
			openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
		}
	}

	//打开修改密码
	function openWin_password(_param) {
		openWin("mo_password", "../mo_password/mo_password.stml", _param);
	}

	//打开app文本
	function openWin_apptext(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		_item.code = _item.code || "apptext";
		openWin(
			openPage + (_item.id || _item.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开图片预览
	function openWin_imgPreviewer(_param) {
		G.imgPreviewerPop = {
			show: true,
			index: _param.index,
			imgs: _param.imgs
		};

		console.log(JSON.stringify(G.imgPreviewerPop));
	}

	//打开聊天转发
	function openWin_chat_share(_param, _callback) {
		_param.title = _param.title || "发送给";
		_param.callback = _param.callback || "chat_share_callback";
		addEventListener(_param.callback, function(ret) {
			_callback && _callback(ret);
		});
		openWin("mo_chat_share", "../mo_chat_share/mo_chat_share.stml", _param);
	}

	//打开代表工作站留言
	function openWin_workstation_letter(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module50_2.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module50_2.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//添加list中参数
	function addPostParam(_list) {
		var param = {};
		for (var i = 0; i < _list.length; i++) {
			var _eItem = _list[i];
			if (!_eItem.key || _eItem.notUpdate || _eItem.hide) {
				continue;
			}
			if (
				!_eItem.hide &&
				!_eItem.noRequired &&
				!(isArray(_eItem.value)
					? _eItem.value.length > 0
					: isNumber(_eItem.value)
					? true
					: _eItem.value)
			) {
				var dhint;
				switch (_eItem.type) {
					case "input":
					case "textarea":
						dhint = "请输入";
						break;
					case "attach":
					case "picture":
					case "signature":
					case "user":
						dhint = "请添加";
						break;
					default:
						dhint = "请选择";
						break;
				}

				dhint += _eItem.title || "";
				toast(_eItem.hint || dhint);
				return;
			}
			if (_eItem.type == "switch") {
				param[_eItem.key] = _eItem.value ? 1 : 0;
			} else if (isArray(_eItem.value)) {
				//数组转换
				if (isNumber(_eItem.min) && _eItem.value.length < _eItem.min) {
					toast(
						"\u8BF7\u9009\u62E9\u81F3\u5C11" + _eItem.min + "\u4E2A" + _eItem.title
					);
					return;
				}
				if (_eItem.valueType == "array") {
					param[_eItem.key] = _eItem.value.map(function(obj) {
						return isObject(obj) ? obj[_eItem.valueKey || "id"] : obj;
					});
				} else {
					param[_eItem.key] = _eItem.value
						.map(function(obj) {
							return isObject(obj) ? obj[_eItem.valueKey || "id"] : obj;
						})
						.join(_eItem.valueType || ",");
				}
			} else if (_eItem.type == "text" && _eItem.textType == "time") {
				//时间转换
				param[_eItem.key] = dayjs(_eItem.value).valueOf();
			} else if (_eItem.html) {
				//内容需要转换成html
				param[_eItem.key] = convertRichText(_eItem.value);
			} else {
				param[_eItem.key] = _eItem.value;
			}
		}
		return param;
	}

	//将二级提交参数 变更为一级
	function setParamToFirst(_list, _param) {
		if (!_param.form) return;
		(_list || []).forEach(function(_eItem) {
			if (!_eItem.key || _eItem.notUpdate || _eItem.hide) return;
			if (_eItem.paramFirst) {
				//将参数提到form外 到一级
				_param[_eItem.key] = _param.form[_eItem.key];
				delete _param.form[_eItem.key];
			}
		});
	}

	//获取字典数据
	function getDictData(_list, _callback, _edit) {
		function getDictCodes(_list, _data) {
			_list.forEach(function(_eItem) {
				if (_eItem.dictKey) _data.push({key: _eItem.key, dict: _eItem.dictKey});
				if (_eItem.type == "addMore") {
					getDictCodes(_eItem.children, _data);
				}
			});
		}
		var dictCodes = [];
		getDictCodes(_list, dictCodes);
		if (!dictCodes.length) return;
		//获取字典数据并赋值
		ajax(
			{u: appUrl() + "dictionary/selector"},
			"dictionaryselector",
			function(ret, err) {
				if (ret && ret.code == "200") {
					var data = ret ? ret.data || {} : {};
					dictCodes.forEach(function(_eItem) {
						var datas = data[_eItem.dict] || [];
						var dataItem = getItemForKey(_eItem.key, _list);
						if (isArray(datas) && datas.length && dataItem) {
							var showData = [];
							datas.forEach(function(_eItem, _eIndex, _eArr) {
								var key = _eItem.key || _eItem.id;
								var value = _eItem.name;
								showData.push({key: key, value: value, readonly: false, hide: false});
							});
							dataItem.data = showData;

							if (
								!dataItem.value &&
								(isParameters(dataItem.dValue) || isParameters(dataItem.dIndex)) &&
								!_edit
							) {
								var nowItem = isParameters(dataItem.dValue)
									? getItemForKey(dataItem.dValue, dataItem.data)
									: dataItem.data[dataItem.dIndex];
								if (nowItem) {
									dataItem.value = nowItem["key"];
								}
							}
							_callback && _callback();
						}
					});
				}
			},
			"字典列表",
			"post",
			{
				body: JSON.stringify({
					dictCodes: dictCodes.map(function(obj) {
						return obj.dict;
					})
				})
			}
		);
	}

	function _inheritsLoose(subClass, superClass) {
		subClass.prototype = Object.create(superClass.prototype);
		subClass.prototype.constructor = subClass;

		_setPrototypeOf(subClass, superClass);
	}

	function _setPrototypeOf(o, p) {
		_setPrototypeOf = Object.setPrototypeOf
			? Object.setPrototypeOf.bind()
			: function _setPrototypeOf(o, p) {
					o.__proto__ = p;
					return o;
			  };
		return _setPrototypeOf(o, p);
	}

	var Hex = /*#__PURE__*/ (function() {
		function Hex() {}
		Hex.stringify = function stringify(wordArray) {
			var hexChars = [];
			for (var i = 0; i < wordArray.sigBytes; i++) {
				var bite = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				hexChars.push((bite >>> 4).toString(16));
				hexChars.push((bite & 0x0f).toString(16));
			}
			return hexChars.join("");
		};
		Hex.parse = function parse(hexStr) {
			var hexStrLength = hexStr.length;
			var words = [];
			for (var i = 0; i < hexStrLength; i += 2) {
				words[i >>> 3] |= parseInt(hexStr.substr(i, 2), 16) << (24 - (i % 8) * 4);
			}
			return new WordArray(words, hexStrLength / 2);
		};
		return Hex;
	})();
	var WordArray = /*#__PURE__*/ (function() {
		WordArray.random = function random(nBytes) {
			var words = [];
			var r = function r(m_w) {
				var m_z = 0x3ade68b1;
				var mask = 0xffffffff;
				return function() {
					m_z = (0x9069 * (m_z & 0xffff) + (m_z >> 0x10)) & mask;
					m_w = (0x4650 * (m_w & 0xffff) + (m_w >> 0x10)) & mask;
					var result = ((m_z << 0x10) + m_w) & mask;
					result /= 0x100000000;
					result += 0.5;
					return result * (Math.random() > 0.5 ? 1 : -1);
				};
			};
			for (var i = 0, rcache; i < nBytes; i += 4) {
				var _r = r((rcache || Math.random()) * 0x100000000);
				rcache = _r() * 0x3ade67b7;
				words.push((_r() * 0x100000000) | 0);
			}
			return new WordArray(words, nBytes);
		};
		function WordArray(words, sigBytes) {
			this.words = words || [];
			if (sigBytes !== undefined) {
				this.sigBytes = sigBytes;
			} else {
				this.sigBytes = this.words.length * 4;
			}
		}
		var _proto = WordArray.prototype;
		_proto.toString = function toString(encoder) {
			return (encoder || Hex).stringify(this);
		};
		_proto.concat = function concat(wordArray) {
			this.clamp();
			if (this.sigBytes % 4) {
				for (var i = 0; i < wordArray.sigBytes; i++) {
					var thatByte = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
					this.words[(this.sigBytes + i) >>> 2] |=
						thatByte << (24 - ((this.sigBytes + i) % 4) * 8);
				}
			} else {
				for (var _i = 0; _i < wordArray.sigBytes; _i += 4) {
					this.words[(this.sigBytes + _i) >>> 2] = wordArray.words[_i >>> 2];
				}
			}
			this.sigBytes += wordArray.sigBytes;
			return this;
		};
		_proto.clamp = function clamp() {
			this.words[this.sigBytes >>> 2] &=
				0xffffffff << (32 - (this.sigBytes % 4) * 8);
			this.words.length = Math.ceil(this.sigBytes / 4);
		};
		_proto.clone = function clone() {
			return new WordArray(this.words.slice(0), this.sigBytes);
		};
		return WordArray;
	})();
	var Latin1 = /*#__PURE__*/ (function() {
		function Latin1() {}
		Latin1.stringify = function stringify(wordArray) {
			var latin1Chars = [];
			for (var i = 0; i < wordArray.sigBytes; i++) {
				var bite = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				latin1Chars.push(String.fromCharCode(bite));
			}
			return latin1Chars.join("");
		};
		Latin1.parse = function parse(latin1Str) {
			var latin1StrLength = latin1Str.length;
			var words = [];
			for (var i = 0; i < latin1StrLength; i++) {
				words[i >>> 2] |= (latin1Str.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8);
			}
			return new WordArray(words, latin1StrLength);
		};
		return Latin1;
	})();
	var Utf8 = /*#__PURE__*/ (function() {
		function Utf8() {}
		Utf8.stringify = function stringify(wordArray) {
			try {
				return decodeURIComponent(escape(Latin1.stringify(wordArray)));
			} catch (e) {
				throw new Error("Malformed UTF-8 data");
			}
		};
		Utf8.parse = function parse(utf8Str) {
			return Latin1.parse(unescape(encodeURIComponent(utf8Str)));
		};
		return Utf8;
	})();
	var BufferedBlockAlgorithm = /*#__PURE__*/ (function() {
		function BufferedBlockAlgorithm(cfg) {
			this._minBufferSize = 0;
			this.cfg = Object.assign({blockSize: 1}, cfg);
			this._data = new WordArray();
			this._nDataBytes = 0;
		}
		var _proto2 = BufferedBlockAlgorithm.prototype;
		_proto2.reset = function reset() {
			this._data = new WordArray();
			this._nDataBytes = 0;
		};
		_proto2._append = function _append(data) {
			if (typeof data === "string") {
				data = Utf8.parse(data);
			}
			this._data.concat(data);
			this._nDataBytes += data.sigBytes;
		};
		_proto2._process = function _process(doFlush) {
			if (!this.cfg.blockSize) {
				throw new Error("missing blockSize in config");
			}
			var blockSizeBytes = this.cfg.blockSize * 4;
			var nBlocksReady = this._data.sigBytes / blockSizeBytes;
			if (doFlush) {
				nBlocksReady = Math.ceil(nBlocksReady);
			} else {
				nBlocksReady = Math.max((nBlocksReady | 0) - this._minBufferSize, 0);
			}
			var nWordsReady = nBlocksReady * this.cfg.blockSize;
			var nBytesReady = Math.min(nWordsReady * 4, this._data.sigBytes);
			var processedWords;
			if (nWordsReady) {
				for (var offset = 0; offset < nWordsReady; offset += this.cfg.blockSize) {
					this._doProcessBlock(this._data.words, offset);
				}
				processedWords = this._data.words.splice(0, nWordsReady);
				this._data.sigBytes -= nBytesReady;
			}
			return new WordArray(processedWords, nBytesReady);
		};
		_proto2.clone = function clone() {
			var clone = this.constructor();
			for (var attr in this) {
				if (this.hasOwnProperty(attr)) {
					clone[attr] = this[attr];
				}
			}
			clone._data = this._data.clone();
			return clone;
		};
		return BufferedBlockAlgorithm;
	})();
	var Base = function Base() {};
	var CipherParams = /*#__PURE__*/ (function(_Base) {
		_inheritsLoose(CipherParams, _Base);
		function CipherParams(cipherParams) {
			var _this;
			_this = _Base.call(this) || this;
			_this.ciphertext = cipherParams.ciphertext;
			_this.key = cipherParams.key;
			_this.iv = cipherParams.iv;
			_this.salt = cipherParams.salt;
			_this.algorithm = cipherParams.algorithm;
			_this.mode = cipherParams.mode;
			_this.padding = cipherParams.padding;
			_this.blockSize = cipherParams.blockSize;
			_this.formatter = cipherParams.formatter;
			return _this;
		}
		var _proto3 = CipherParams.prototype;
		_proto3.extend = function extend(additionalParams) {
			if (additionalParams.ciphertext !== undefined) {
				this.ciphertext = additionalParams.ciphertext;
			}
			if (additionalParams.key !== undefined) {
				this.key = additionalParams.key;
			}
			if (additionalParams.iv !== undefined) {
				this.iv = additionalParams.iv;
			}
			if (additionalParams.salt !== undefined) {
				this.salt = additionalParams.salt;
			}
			if (additionalParams.algorithm !== undefined) {
				this.algorithm = additionalParams.algorithm;
			}
			if (additionalParams.mode !== undefined) {
				this.mode = additionalParams.mode;
			}
			if (additionalParams.padding !== undefined) {
				this.padding = additionalParams.padding;
			}
			if (additionalParams.blockSize !== undefined) {
				this.blockSize = additionalParams.blockSize;
			}
			if (additionalParams.formatter !== undefined) {
				this.formatter = additionalParams.formatter;
			}
			return this;
		};
		_proto3.toString = function toString(formatter) {
			if (formatter) {
				return formatter.stringify(this);
			} else if (this.formatter) {
				return this.formatter.stringify(this);
			} else {
				throw new Error(
					"cipher needs a formatter to be able to convert the result into a string"
				);
			}
		};
		return CipherParams;
	})(Base);
	var Base64 = /*#__PURE__*/ (function() {
		function Base64() {}
		Base64.stringify = function stringify(wordArray) {
			wordArray.clamp();
			var base64Chars = [];
			for (var i = 0; i < wordArray.sigBytes; i += 3) {
				var byte1 = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				var byte2 =
					(wordArray.words[(i + 1) >>> 2] >>> (24 - ((i + 1) % 4) * 8)) & 0xff;
				var byte3 =
					(wordArray.words[(i + 2) >>> 2] >>> (24 - ((i + 2) % 4) * 8)) & 0xff;
				var triplet = (byte1 << 16) | (byte2 << 8) | byte3;
				for (var j = 0; j < 4 && i + j * 0.75 < wordArray.sigBytes; j++) {
					base64Chars.push(this._map.charAt((triplet >>> (6 * (3 - j))) & 0x3f));
				}
			}
			var paddingChar = this._map.charAt(64);
			if (paddingChar) {
				while (base64Chars.length % 4) {
					base64Chars.push(paddingChar);
				}
			}
			return base64Chars.join("");
		};
		Base64.parse = function parse(base64Str) {
			var base64StrLength = base64Str.length;
			if (this._reverseMap === undefined) {
				this._reverseMap = [];
				for (var j = 0; j < this._map.length; j++) {
					this._reverseMap[this._map.charCodeAt(j)] = j;
				}
			}
			var paddingChar = this._map.charAt(64);
			if (paddingChar) {
				var paddingIndex = base64Str.indexOf(paddingChar);
				if (paddingIndex !== -1) {
					base64StrLength = paddingIndex;
				}
			}
			return this.parseLoop(base64Str, base64StrLength, this._reverseMap);
		};
		Base64.parseLoop = function parseLoop(
			base64Str,
			base64StrLength,
			reverseMap
		) {
			var words = [];
			var nBytes = 0;
			for (var i = 0; i < base64StrLength; i++) {
				if (i % 4) {
					var bits1 = reverseMap[base64Str.charCodeAt(i - 1)] << ((i % 4) * 2);
					var bits2 = reverseMap[base64Str.charCodeAt(i)] >>> (6 - (i % 4) * 2);
					words[nBytes >>> 2] |= (bits1 | bits2) << (24 - (nBytes % 4) * 8);
					nBytes++;
				}
			}
			return new WordArray(words, nBytes);
		};
		return Base64;
	})();
	Base64._map =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
	Base64._reverseMap = undefined;
	var OpenSSL = /*#__PURE__*/ (function() {
		function OpenSSL() {}
		OpenSSL.stringify = function stringify(cipherParams) {
			if (!cipherParams.ciphertext) {
				throw new Error("missing ciphertext in params");
			}
			var ciphertext = cipherParams.ciphertext;
			var salt = cipherParams.salt;
			var wordArray;
			if (salt) {
				if (typeof salt === "string") {
					throw new Error("salt is expected to be a WordArray");
				}
				wordArray = new WordArray([0x53616c74, 0x65645f5f])
					.concat(salt)
					.concat(ciphertext);
			} else {
				wordArray = ciphertext;
			}
			return wordArray.toString(Base64);
		};
		OpenSSL.parse = function parse(openSSLStr) {
			var ciphertext = Base64.parse(openSSLStr);
			var salt;
			if (
				ciphertext.words[0] === 0x53616c74 &&
				ciphertext.words[1] === 0x65645f5f
			) {
				salt = new WordArray(ciphertext.words.slice(2, 4));
				ciphertext.words.splice(0, 4);
				ciphertext.sigBytes -= 16;
			}
			return new CipherParams({ciphertext: ciphertext, salt: salt});
		};
		return OpenSSL;
	})();
	var SerializableCipher = /*#__PURE__*/ (function() {
		function SerializableCipher() {}
		SerializableCipher.encrypt = function encrypt(cipher, message, key, cfg) {
			var config = Object.assign({}, this.cfg, cfg);
			var encryptor = cipher.createEncryptor(key, config);
			var ciphertext = encryptor.finalize(message);
			return new CipherParams({
				ciphertext: ciphertext,
				key: key,
				iv: encryptor.cfg.iv,
				algorithm: cipher,
				mode: encryptor.cfg.mode,
				padding: encryptor.cfg.padding,
				blockSize: encryptor.cfg.blockSize,
				formatter: config.format
			});
		};
		SerializableCipher.decrypt = function decrypt(
			cipher,
			ciphertext,
			key,
			optionalCfg
		) {
			var cfg = Object.assign({}, this.cfg, optionalCfg);
			if (!cfg.format) {
				throw new Error("could not determine format");
			}
			ciphertext = this._parse(ciphertext, cfg.format);
			if (!ciphertext.ciphertext) {
				throw new Error("could not determine ciphertext");
			}
			var plaintext = cipher
				.createDecryptor(key, cfg)
				.finalize(ciphertext.ciphertext);
			return plaintext;
		};
		SerializableCipher._parse = function _parse(ciphertext, format) {
			if (typeof ciphertext === "string") {
				return format.parse(ciphertext);
			} else {
				return ciphertext;
			}
		};
		return SerializableCipher;
	})();
	SerializableCipher.cfg = {
		blockSize: 4,
		iv: new WordArray([]),
		format: OpenSSL
	};
	var Hasher = /*#__PURE__*/ (function(_BufferedBlockAlgorit) {
		_inheritsLoose(Hasher, _BufferedBlockAlgorit);
		Hasher._createHelper = function _createHelper(hasher) {
			function helper(message, cfg) {
				var hasherClass = hasher;
				var hasherInstance = new hasherClass(cfg);
				return hasherInstance.finalize(message);
			}
			return helper;
		};
		function Hasher(cfg) {
			var _this2;
			_this2 =
				_BufferedBlockAlgorit.call(
					this,
					Object.assign({blockSize: 512 / 32}, cfg)
				) || this;
			_this2.reset();
			return _this2;
		}
		var _proto4 = Hasher.prototype;
		_proto4.update = function update(messageUpdate) {
			this._append(messageUpdate);
			this._process();
			return this;
		};
		_proto4.finalize = function finalize(messageUpdate) {
			if (messageUpdate) {
				this._append(messageUpdate);
			}
			var hash = this._doFinalize();
			return hash;
		};
		return Hasher;
	})(BufferedBlockAlgorithm);
	var T = [];
	for (var i = 0; i < 64; i++) {
		T[i] = (Math.abs(Math.sin(i + 1)) * 0x100000000) | 0;
	}
	var MD5 = /*#__PURE__*/ (function(_Hasher) {
		_inheritsLoose(MD5, _Hasher);
		function MD5() {
			return _Hasher.apply(this, arguments) || this;
		}
		MD5.FF = function FF(a, b, c, d, x, s, t) {
			var n = a + ((b & c) | (~b & d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.GG = function GG(a, b, c, d, x, s, t) {
			var n = a + ((b & d) | (c & ~d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.HH = function HH(a, b, c, d, x, s, t) {
			var n = a + (b ^ c ^ d) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.II = function II(a, b, c, d, x, s, t) {
			var n = a + (c ^ (b | ~d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		var _proto5 = MD5.prototype;
		_proto5.reset = function reset() {
			_Hasher.prototype.reset.call(this);
			this._hash = new WordArray([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476]);
		};
		_proto5._doProcessBlock = function _doProcessBlock(M, offset) {
			for (var _i2 = 0; _i2 < 16; _i2++) {
				var offset_i = offset + _i2;
				var M_offset_i = M[offset_i];
				M[offset_i] =
					(((M_offset_i << 8) | (M_offset_i >>> 24)) & 0x00ff00ff) |
					(((M_offset_i << 24) | (M_offset_i >>> 8)) & 0xff00ff00);
			}
			var H = this._hash.words;
			var M_offset_0 = M[offset + 0];
			var M_offset_1 = M[offset + 1];
			var M_offset_2 = M[offset + 2];
			var M_offset_3 = M[offset + 3];
			var M_offset_4 = M[offset + 4];
			var M_offset_5 = M[offset + 5];
			var M_offset_6 = M[offset + 6];
			var M_offset_7 = M[offset + 7];
			var M_offset_8 = M[offset + 8];
			var M_offset_9 = M[offset + 9];
			var M_offset_10 = M[offset + 10];
			var M_offset_11 = M[offset + 11];
			var M_offset_12 = M[offset + 12];
			var M_offset_13 = M[offset + 13];
			var M_offset_14 = M[offset + 14];
			var M_offset_15 = M[offset + 15];
			var a = H[0];
			var b = H[1];
			var c = H[2];
			var d = H[3];
			a = MD5.FF(a, b, c, d, M_offset_0, 7, T[0]);
			d = MD5.FF(d, a, b, c, M_offset_1, 12, T[1]);
			c = MD5.FF(c, d, a, b, M_offset_2, 17, T[2]);
			b = MD5.FF(b, c, d, a, M_offset_3, 22, T[3]);
			a = MD5.FF(a, b, c, d, M_offset_4, 7, T[4]);
			d = MD5.FF(d, a, b, c, M_offset_5, 12, T[5]);
			c = MD5.FF(c, d, a, b, M_offset_6, 17, T[6]);
			b = MD5.FF(b, c, d, a, M_offset_7, 22, T[7]);
			a = MD5.FF(a, b, c, d, M_offset_8, 7, T[8]);
			d = MD5.FF(d, a, b, c, M_offset_9, 12, T[9]);
			c = MD5.FF(c, d, a, b, M_offset_10, 17, T[10]);
			b = MD5.FF(b, c, d, a, M_offset_11, 22, T[11]);
			a = MD5.FF(a, b, c, d, M_offset_12, 7, T[12]);
			d = MD5.FF(d, a, b, c, M_offset_13, 12, T[13]);
			c = MD5.FF(c, d, a, b, M_offset_14, 17, T[14]);
			b = MD5.FF(b, c, d, a, M_offset_15, 22, T[15]);
			a = MD5.GG(a, b, c, d, M_offset_1, 5, T[16]);
			d = MD5.GG(d, a, b, c, M_offset_6, 9, T[17]);
			c = MD5.GG(c, d, a, b, M_offset_11, 14, T[18]);
			b = MD5.GG(b, c, d, a, M_offset_0, 20, T[19]);
			a = MD5.GG(a, b, c, d, M_offset_5, 5, T[20]);
			d = MD5.GG(d, a, b, c, M_offset_10, 9, T[21]);
			c = MD5.GG(c, d, a, b, M_offset_15, 14, T[22]);
			b = MD5.GG(b, c, d, a, M_offset_4, 20, T[23]);
			a = MD5.GG(a, b, c, d, M_offset_9, 5, T[24]);
			d = MD5.GG(d, a, b, c, M_offset_14, 9, T[25]);
			c = MD5.GG(c, d, a, b, M_offset_3, 14, T[26]);
			b = MD5.GG(b, c, d, a, M_offset_8, 20, T[27]);
			a = MD5.GG(a, b, c, d, M_offset_13, 5, T[28]);
			d = MD5.GG(d, a, b, c, M_offset_2, 9, T[29]);
			c = MD5.GG(c, d, a, b, M_offset_7, 14, T[30]);
			b = MD5.GG(b, c, d, a, M_offset_12, 20, T[31]);
			a = MD5.HH(a, b, c, d, M_offset_5, 4, T[32]);
			d = MD5.HH(d, a, b, c, M_offset_8, 11, T[33]);
			c = MD5.HH(c, d, a, b, M_offset_11, 16, T[34]);
			b = MD5.HH(b, c, d, a, M_offset_14, 23, T[35]);
			a = MD5.HH(a, b, c, d, M_offset_1, 4, T[36]);
			d = MD5.HH(d, a, b, c, M_offset_4, 11, T[37]);
			c = MD5.HH(c, d, a, b, M_offset_7, 16, T[38]);
			b = MD5.HH(b, c, d, a, M_offset_10, 23, T[39]);
			a = MD5.HH(a, b, c, d, M_offset_13, 4, T[40]);
			d = MD5.HH(d, a, b, c, M_offset_0, 11, T[41]);
			c = MD5.HH(c, d, a, b, M_offset_3, 16, T[42]);
			b = MD5.HH(b, c, d, a, M_offset_6, 23, T[43]);
			a = MD5.HH(a, b, c, d, M_offset_9, 4, T[44]);
			d = MD5.HH(d, a, b, c, M_offset_12, 11, T[45]);
			c = MD5.HH(c, d, a, b, M_offset_15, 16, T[46]);
			b = MD5.HH(b, c, d, a, M_offset_2, 23, T[47]);
			a = MD5.II(a, b, c, d, M_offset_0, 6, T[48]);
			d = MD5.II(d, a, b, c, M_offset_7, 10, T[49]);
			c = MD5.II(c, d, a, b, M_offset_14, 15, T[50]);
			b = MD5.II(b, c, d, a, M_offset_5, 21, T[51]);
			a = MD5.II(a, b, c, d, M_offset_12, 6, T[52]);
			d = MD5.II(d, a, b, c, M_offset_3, 10, T[53]);
			c = MD5.II(c, d, a, b, M_offset_10, 15, T[54]);
			b = MD5.II(b, c, d, a, M_offset_1, 21, T[55]);
			a = MD5.II(a, b, c, d, M_offset_8, 6, T[56]);
			d = MD5.II(d, a, b, c, M_offset_15, 10, T[57]);
			c = MD5.II(c, d, a, b, M_offset_6, 15, T[58]);
			b = MD5.II(b, c, d, a, M_offset_13, 21, T[59]);
			a = MD5.II(a, b, c, d, M_offset_4, 6, T[60]);
			d = MD5.II(d, a, b, c, M_offset_11, 10, T[61]);
			c = MD5.II(c, d, a, b, M_offset_2, 15, T[62]);
			b = MD5.II(b, c, d, a, M_offset_9, 21, T[63]);
			H[0] = (H[0] + a) | 0;
			H[1] = (H[1] + b) | 0;
			H[2] = (H[2] + c) | 0;
			H[3] = (H[3] + d) | 0;
		};
		_proto5._doFinalize = function _doFinalize() {
			var data = this._data;
			var dataWords = data.words;
			var nBitsTotal = this._nDataBytes * 8;
			var nBitsLeft = data.sigBytes * 8;
			dataWords[nBitsLeft >>> 5] |= 0x80 << (24 - (nBitsLeft % 32));
			var nBitsTotalH = Math.floor(nBitsTotal / 0x100000000);
			var nBitsTotalL = nBitsTotal;
			dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 15] =
				(((nBitsTotalH << 8) | (nBitsTotalH >>> 24)) & 0x00ff00ff) |
				(((nBitsTotalH << 24) | (nBitsTotalH >>> 8)) & 0xff00ff00);
			dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 14] =
				(((nBitsTotalL << 8) | (nBitsTotalL >>> 24)) & 0x00ff00ff) |
				(((nBitsTotalL << 24) | (nBitsTotalL >>> 8)) & 0xff00ff00);
			data.sigBytes = (dataWords.length + 1) * 4;
			this._process();
			var hash = this._hash;
			var H = hash.words;
			for (var _i3 = 0; _i3 < 4; _i3++) {
				var H_i = H[_i3];
				H[_i3] =
					(((H_i << 8) | (H_i >>> 24)) & 0x00ff00ff) |
					(((H_i << 24) | (H_i >>> 8)) & 0xff00ff00);
			}
			return hash;
		};
		return MD5;
	})(Hasher);
	var EvpKDF = /*#__PURE__*/ (function() {
		function EvpKDF(cfg) {
			this.cfg = Object.assign(
				{keySize: 128 / 32, hasher: MD5, iterations: 1},
				cfg
			);
		}
		var _proto6 = EvpKDF.prototype;
		_proto6.compute = function compute(password, salt) {
			var hasher = new this.cfg.hasher();
			var derivedKey = new WordArray();
			var block;
			while (derivedKey.words.length < this.cfg.keySize) {
				if (block) {
					hasher.update(block);
				}
				block = hasher.update(password).finalize(salt);
				hasher.reset();
				for (var _i4 = 1; _i4 < this.cfg.iterations; _i4++) {
					block = hasher.finalize(block);
					hasher.reset();
				}
				derivedKey.concat(block);
			}
			derivedKey.sigBytes = this.cfg.keySize * 4;
			return derivedKey;
		};
		return EvpKDF;
	})();
	var OpenSSLKdf = /*#__PURE__*/ (function() {
		function OpenSSLKdf() {}
		OpenSSLKdf.execute = function execute(password, keySize, ivSize, salt) {
			if (!salt) {
				salt = WordArray.random(64 / 8);
			}
			var key = new EvpKDF({keySize: keySize + ivSize}).compute(password, salt);
			var iv = new WordArray(key.words.slice(keySize), ivSize * 4);
			key.sigBytes = keySize * 4;
			return new CipherParams({key: key, iv: iv, salt: salt});
		};
		return OpenSSLKdf;
	})();
	var PasswordBasedCipher = /*#__PURE__*/ (function() {
		function PasswordBasedCipher() {}
		PasswordBasedCipher.encrypt = function encrypt(
			cipher,
			message,
			password,
			cfg
		) {
			var config = Object.assign({}, this.cfg, cfg);
			if (config.kdf === undefined) {
				throw new Error("missing kdf in config");
			}
			var derivedParams = config.kdf.execute(
				password,
				cipher.keySize,
				cipher.ivSize
			);
			if (derivedParams.iv !== undefined) {
				config.iv = derivedParams.iv;
			}
			var ciphertext = SerializableCipher.encrypt.call(
				this,
				cipher,
				message,
				derivedParams.key,
				config
			);
			return ciphertext.extend(derivedParams);
		};
		PasswordBasedCipher.decrypt = function decrypt(
			cipher,
			ciphertext,
			password,
			cfg
		) {
			var config = Object.assign({}, this.cfg, cfg);
			if (config.format === undefined) {
				throw new Error("missing format in config");
			}
			ciphertext = this._parse(ciphertext, config.format);
			if (config.kdf === undefined) {
				throw new Error("the key derivation function must be set");
			}
			var derivedParams = config.kdf.execute(
				password,
				cipher.keySize,
				cipher.ivSize,
				ciphertext.salt
			);
			if (derivedParams.iv !== undefined) {
				config.iv = derivedParams.iv;
			}
			var plaintext = SerializableCipher.decrypt.call(
				this,
				cipher,
				ciphertext,
				derivedParams.key,
				config
			);
			return plaintext;
		};
		PasswordBasedCipher._parse = function _parse(ciphertext, format) {
			if (typeof ciphertext === "string") {
				return format.parse(ciphertext);
			} else {
				return ciphertext;
			}
		};
		return PasswordBasedCipher;
	})();
	PasswordBasedCipher.cfg = {
		blockSize: 4,
		iv: new WordArray([]),
		format: OpenSSL,
		kdf: OpenSSLKdf
	};
	var Cipher = /*#__PURE__*/ (function(_BufferedBlockAlgorit2) {
		_inheritsLoose(Cipher, _BufferedBlockAlgorit2);
		function Cipher(xformMode, key, cfg) {
			var _this3;
			_this3 =
				_BufferedBlockAlgorit2.call(this, Object.assign({blockSize: 1}, cfg)) ||
				this;
			_this3._xformMode = xformMode;
			_this3._key = key;
			_this3.reset();
			return _this3;
		}
		Cipher.createEncryptor = function createEncryptor(key, cfg) {
			var thisClass = this;
			return new thisClass(this._ENC_XFORM_MODE, key, cfg);
		};
		Cipher.createDecryptor = function createDecryptor(key, cfg) {
			var thisClass = this;
			return new thisClass(this._DEC_XFORM_MODE, key, cfg);
		};
		Cipher._createHelper = function _createHelper(cipher) {
			function encrypt(message, key, cfg) {
				if (typeof key === "string") {
					return PasswordBasedCipher.encrypt(cipher, message, key, cfg);
				} else {
					return SerializableCipher.encrypt(cipher, message, key, cfg);
				}
			}
			function decrypt(ciphertext, key, cfg) {
				if (typeof key === "string") {
					return PasswordBasedCipher.decrypt(cipher, ciphertext, key, cfg);
				} else {
					return SerializableCipher.decrypt(cipher, ciphertext, key, cfg);
				}
			}
			return {encrypt: encrypt, decrypt: decrypt};
		};
		var _proto7 = Cipher.prototype;
		_proto7.process = function process(dataUpdate) {
			this._append(dataUpdate);
			return this._process();
		};
		_proto7.finalize = function finalize(dataUpdate) {
			if (dataUpdate) {
				this._append(dataUpdate);
			}
			var finalProcessedData = this._doFinalize();
			return finalProcessedData;
		};
		return Cipher;
	})(BufferedBlockAlgorithm);
	Cipher._ENC_XFORM_MODE = 1;
	Cipher._DEC_XFORM_MODE = 2;
	Cipher.keySize = 4;
	Cipher.ivSize = 4;
	var BlockCipherModeAlgorithm = /*#__PURE__*/ (function() {
		function BlockCipherModeAlgorithm(cipher, iv) {
			this.init(cipher, iv);
		}
		var _proto8 = BlockCipherModeAlgorithm.prototype;
		_proto8.init = function init(cipher, iv) {
			this._cipher = cipher;
			this._iv = iv;
		};
		return BlockCipherModeAlgorithm;
	})();
	var BlockCipherMode = /*#__PURE__*/ (function() {
		function BlockCipherMode() {}
		BlockCipherMode.createEncryptor = function createEncryptor(cipher, iv) {
			var encryptorClass = this.Encryptor;
			return new encryptorClass(cipher, iv);
		};
		BlockCipherMode.createDecryptor = function createDecryptor(cipher, iv) {
			var decryptorClass = this.Decryptor;
			return new decryptorClass(cipher, iv);
		};
		return BlockCipherMode;
	})();
	BlockCipherMode.Encryptor = BlockCipherModeAlgorithm;
	BlockCipherMode.Decryptor = BlockCipherModeAlgorithm;
	var CBCEncryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor) {
		_inheritsLoose(CBCEncryptor, _BlockCipherModeAlgor);
		function CBCEncryptor() {
			return _BlockCipherModeAlgor.apply(this, arguments) || this;
		}
		var _proto9 = CBCEncryptor.prototype;
		_proto9.processBlock = function processBlock(words, offset) {
			if (this._cipher.cfg.blockSize === undefined) {
				throw new Error("missing blockSize in cipher config");
			}
			this.xorBlock(words, offset, this._cipher.cfg.blockSize);
			this._cipher.encryptBlock(words, offset);
			this._prevBlock = words.slice(offset, offset + this._cipher.cfg.blockSize);
		};
		_proto9.xorBlock = function xorBlock(words, offset, blockSize) {
			var block;
			if (this._iv) {
				block = this._iv;
				this._iv = undefined;
			} else {
				block = this._prevBlock;
			}
			if (block !== undefined) {
				for (var _i5 = 0; _i5 < blockSize; _i5++) {
					words[offset + _i5] ^= block[_i5];
				}
			}
		};
		return CBCEncryptor;
	})(BlockCipherModeAlgorithm);
	var CBCDecryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor2) {
		_inheritsLoose(CBCDecryptor, _BlockCipherModeAlgor2);
		function CBCDecryptor() {
			return _BlockCipherModeAlgor2.apply(this, arguments) || this;
		}
		var _proto10 = CBCDecryptor.prototype;
		_proto10.processBlock = function processBlock(words, offset) {
			if (this._cipher.cfg.blockSize === undefined) {
				throw new Error("missing blockSize in cipher config");
			}
			var thisBlock = words.slice(offset, offset + this._cipher.cfg.blockSize);
			this._cipher.decryptBlock(words, offset);
			this.xorBlock(words, offset, this._cipher.cfg.blockSize);
			this._prevBlock = thisBlock;
		};
		_proto10.xorBlock = function xorBlock(words, offset, blockSize) {
			var block;
			if (this._iv) {
				block = this._iv;
				this._iv = undefined;
			} else {
				block = this._prevBlock;
			}
			if (block !== undefined) {
				for (var _i6 = 0; _i6 < blockSize; _i6++) {
					words[offset + _i6] ^= block[_i6];
				}
			}
		};
		return CBCDecryptor;
	})(BlockCipherModeAlgorithm);
	var CBC = /*#__PURE__*/ (function(_BlockCipherMode) {
		_inheritsLoose(CBC, _BlockCipherMode);
		function CBC() {
			return _BlockCipherMode.apply(this, arguments) || this;
		}
		return CBC;
	})(BlockCipherMode);
	CBC.Encryptor = CBCEncryptor;
	CBC.Decryptor = CBCDecryptor;
	var PKCS7 = /*#__PURE__*/ (function() {
		function PKCS7() {}
		PKCS7.pad = function pad(data, blockSize) {
			var blockSizeBytes = blockSize * 4;
			var nPaddingBytes = blockSizeBytes - (data.sigBytes % blockSizeBytes);
			var paddingWord =
				(nPaddingBytes << 24) |
				(nPaddingBytes << 16) |
				(nPaddingBytes << 8) |
				nPaddingBytes;
			var paddingWords = [];
			for (var _i7 = 0; _i7 < nPaddingBytes; _i7 += 4) {
				paddingWords.push(paddingWord);
			}
			var padding = new WordArray(paddingWords, nPaddingBytes);
			data.concat(padding);
		};
		PKCS7.unpad = function unpad(data) {
			var nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;
			data.sigBytes -= nPaddingBytes;
		};
		return PKCS7;
	})();
	var BlockCipher = /*#__PURE__*/ (function(_Cipher) {
		_inheritsLoose(BlockCipher, _Cipher);
		function BlockCipher(xformMode, key, cfg) {
			return (
				_Cipher.call(
					this,
					xformMode,
					key,
					Object.assign({blockSize: 4, mode: CBC, padding: PKCS7}, cfg)
				) || this
			);
		}
		var _proto11 = BlockCipher.prototype;
		_proto11.reset = function reset() {
			_Cipher.prototype.reset.call(this);
			if (this.cfg.mode === undefined) {
				throw new Error("missing mode in config");
			}
			var modeCreator;
			if (this._xformMode === this.constructor._ENC_XFORM_MODE) {
				modeCreator = this.cfg.mode.createEncryptor;
			} else {
				modeCreator = this.cfg.mode.createDecryptor;
				this._minBufferSize = 1;
			}
			if (this._mode && this._mode.__creator === modeCreator) {
				this._mode.init(this, this.cfg.iv && this.cfg.iv.words);
			} else {
				this._mode = modeCreator.call(
					this.cfg.mode,
					this,
					this.cfg.iv && this.cfg.iv.words
				);
				this._mode.__creator = modeCreator;
			}
		};
		_proto11._doProcessBlock = function _doProcessBlock(words, offset) {
			this._mode.processBlock(words, offset);
		};
		_proto11._doFinalize = function _doFinalize() {
			if (this.cfg.padding === undefined) {
				throw new Error("missing padding in config");
			}
			var finalProcessedBlocks;
			if (this._xformMode === this.constructor._ENC_XFORM_MODE) {
				if (this.cfg.blockSize === undefined) {
					throw new Error("missing blockSize in config");
				}
				this.cfg.padding.pad(this._data, this.cfg.blockSize);
				finalProcessedBlocks = this._process(!!"flush");
			} else {
				finalProcessedBlocks = this._process(!!"flush");
				this.cfg.padding.unpad(finalProcessedBlocks);
			}
			return finalProcessedBlocks;
		};
		return BlockCipher;
	})(Cipher);
	var SBOX = [];
	var INV_SBOX = [];
	var SUB_MIX_0 = [];
	var SUB_MIX_1 = [];
	var SUB_MIX_2 = [];
	var SUB_MIX_3 = [];
	var INV_SUB_MIX_0 = [];
	var INV_SUB_MIX_1 = [];
	var INV_SUB_MIX_2 = [];
	var INV_SUB_MIX_3 = [];
	(function() {
		var d = [];
		for (var _i8 = 0; _i8 < 256; _i8++) {
			if (_i8 < 128) {
				d[_i8] = _i8 << 1;
			} else {
				d[_i8] = (_i8 << 1) ^ 0x11b;
			}
		}
		var x = 0;
		var xi = 0;
		for (var _i9 = 0; _i9 < 256; _i9++) {
			var sx = xi ^ (xi << 1) ^ (xi << 2) ^ (xi << 3) ^ (xi << 4);
			sx = (sx >>> 8) ^ (sx & 0xff) ^ 0x63;
			SBOX[x] = sx;
			INV_SBOX[sx] = x;
			var x2 = d[x];
			var x4 = d[x2];
			var x8 = d[x4];
			var t = (d[sx] * 0x101) ^ (sx * 0x1010100);
			SUB_MIX_0[x] = (t << 24) | (t >>> 8);
			SUB_MIX_1[x] = (t << 16) | (t >>> 16);
			SUB_MIX_2[x] = (t << 8) | (t >>> 24);
			SUB_MIX_3[x] = t;
			t = (x8 * 0x1010101) ^ (x4 * 0x10001) ^ (x2 * 0x101) ^ (x * 0x1010100);
			INV_SUB_MIX_0[sx] = (t << 24) | (t >>> 8);
			INV_SUB_MIX_1[sx] = (t << 16) | (t >>> 16);
			INV_SUB_MIX_2[sx] = (t << 8) | (t >>> 24);
			INV_SUB_MIX_3[sx] = t;
			if (!x) {
				x = xi = 1;
			} else {
				x = x2 ^ d[d[d[x8 ^ x2]]];
				xi ^= d[d[xi]];
			}
		}
	})();
	var RCON = [0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36];
	var AES = /*#__PURE__*/ (function(_BlockCipher) {
		_inheritsLoose(AES, _BlockCipher);
		function AES(xformMode, key, cfg) {
			return _BlockCipher.call(this, xformMode, key, cfg) || this;
		}
		var _proto12 = AES.prototype;
		_proto12.reset = function reset() {
			_BlockCipher.prototype.reset.call(this);
			if (this._nRounds && this._keyPriorReset === this._key) {
				return;
			}
			var key = (this._keyPriorReset = this._key);
			var keyWords = key.words;
			var keySize = key.sigBytes / 4;
			var nRounds = (this._nRounds = keySize + 6);
			var ksRows = (nRounds + 1) * 4;
			var keySchedule = (this._keySchedule = []);
			for (var ksRow = 0; ksRow < ksRows; ksRow++) {
				if (ksRow < keySize) {
					keySchedule[ksRow] = keyWords[ksRow];
				} else {
					var t = keySchedule[ksRow - 1];
					if (!(ksRow % keySize)) {
						t = (t << 8) | (t >>> 24);
						t =
							(SBOX[t >>> 24] << 24) |
							(SBOX[(t >>> 16) & 0xff] << 16) |
							(SBOX[(t >>> 8) & 0xff] << 8) |
							SBOX[t & 0xff];
						t ^= RCON[(ksRow / keySize) | 0] << 24;
					} else if (keySize > 6 && ksRow % keySize === 4) {
						t =
							(SBOX[t >>> 24] << 24) |
							(SBOX[(t >>> 16) & 0xff] << 16) |
							(SBOX[(t >>> 8) & 0xff] << 8) |
							SBOX[t & 0xff];
					}
					keySchedule[ksRow] = keySchedule[ksRow - keySize] ^ t;
				}
			}
			var invKeySchedule = (this._invKeySchedule = []);
			for (var invKsRow = 0; invKsRow < ksRows; invKsRow++) {
				var _ksRow = ksRows - invKsRow;
				var _t = void 0;
				if (invKsRow % 4) {
					_t = keySchedule[_ksRow];
				} else {
					_t = keySchedule[_ksRow - 4];
				}
				if (invKsRow < 4 || _ksRow <= 4) {
					invKeySchedule[invKsRow] = _t;
				} else {
					invKeySchedule[invKsRow] =
						INV_SUB_MIX_0[SBOX[_t >>> 24]] ^
						INV_SUB_MIX_1[SBOX[(_t >>> 16) & 0xff]] ^
						INV_SUB_MIX_2[SBOX[(_t >>> 8) & 0xff]] ^
						INV_SUB_MIX_3[SBOX[_t & 0xff]];
				}
			}
		};
		_proto12.encryptBlock = function encryptBlock(M, offset) {
			this._doCryptBlock(
				M,
				offset,
				this._keySchedule,
				SUB_MIX_0,
				SUB_MIX_1,
				SUB_MIX_2,
				SUB_MIX_3,
				SBOX
			);
		};
		_proto12.decryptBlock = function decryptBlock(M, offset) {
			var t = M[offset + 1];
			M[offset + 1] = M[offset + 3];
			M[offset + 3] = t;
			this._doCryptBlock(
				M,
				offset,
				this._invKeySchedule,
				INV_SUB_MIX_0,
				INV_SUB_MIX_1,
				INV_SUB_MIX_2,
				INV_SUB_MIX_3,
				INV_SBOX
			);
			t = M[offset + 1];
			M[offset + 1] = M[offset + 3];
			M[offset + 3] = t;
		};
		_proto12._doCryptBlock = function _doCryptBlock(
			M,
			offset,
			keySchedule,
			sub_mix_0,
			sub_mix_1,
			sub_mix_2,
			sub_mix_3,
			sbox
		) {
			var s0 = M[offset] ^ keySchedule[0];
			var s1 = M[offset + 1] ^ keySchedule[1];
			var s2 = M[offset + 2] ^ keySchedule[2];
			var s3 = M[offset + 3] ^ keySchedule[3];
			var ksRow = 4;
			for (var round = 1; round < this._nRounds; round++) {
				var t0 =
					sub_mix_0[s0 >>> 24] ^
					sub_mix_1[(s1 >>> 16) & 0xff] ^
					sub_mix_2[(s2 >>> 8) & 0xff] ^
					sub_mix_3[s3 & 0xff] ^
					keySchedule[ksRow++];
				var t1 =
					sub_mix_0[s1 >>> 24] ^
					sub_mix_1[(s2 >>> 16) & 0xff] ^
					sub_mix_2[(s3 >>> 8) & 0xff] ^
					sub_mix_3[s0 & 0xff] ^
					keySchedule[ksRow++];
				var t2 =
					sub_mix_0[s2 >>> 24] ^
					sub_mix_1[(s3 >>> 16) & 0xff] ^
					sub_mix_2[(s0 >>> 8) & 0xff] ^
					sub_mix_3[s1 & 0xff] ^
					keySchedule[ksRow++];
				var t3 =
					sub_mix_0[s3 >>> 24] ^
					sub_mix_1[(s0 >>> 16) & 0xff] ^
					sub_mix_2[(s1 >>> 8) & 0xff] ^
					sub_mix_3[s2 & 0xff] ^
					keySchedule[ksRow++];
				s0 = t0;
				s1 = t1;
				s2 = t2;
				s3 = t3;
			}
			var t0g =
				((sbox[s0 >>> 24] << 24) |
					(sbox[(s1 >>> 16) & 0xff] << 16) |
					(sbox[(s2 >>> 8) & 0xff] << 8) |
					sbox[s3 & 0xff]) ^
				keySchedule[ksRow++];
			var t1g =
				((sbox[s1 >>> 24] << 24) |
					(sbox[(s2 >>> 16) & 0xff] << 16) |
					(sbox[(s3 >>> 8) & 0xff] << 8) |
					sbox[s0 & 0xff]) ^
				keySchedule[ksRow++];
			var t2g =
				((sbox[s2 >>> 24] << 24) |
					(sbox[(s3 >>> 16) & 0xff] << 16) |
					(sbox[(s0 >>> 8) & 0xff] << 8) |
					sbox[s1 & 0xff]) ^
				keySchedule[ksRow++];
			var t3g =
				((sbox[s3 >>> 24] << 24) |
					(sbox[(s0 >>> 16) & 0xff] << 16) |
					(sbox[(s1 >>> 8) & 0xff] << 8) |
					sbox[s2 & 0xff]) ^
				keySchedule[ksRow++];
			M[offset] = t0g;
			M[offset + 1] = t1g;
			M[offset + 2] = t2g;
			M[offset + 3] = t3g;
		};
		return AES;
	})(BlockCipher);
	AES.keySize = 8;
	var H$1 = [];
	var K = [];
	var W$1 = [];
	var SHA256 = /*#__PURE__*/ (function(_Hasher2) {
		_inheritsLoose(SHA256, _Hasher2);
		function SHA256() {
			return _Hasher2.apply(this, arguments) || this;
		}
		var _proto13 = SHA256.prototype;
		_proto13.reset = function reset() {
			_Hasher2.prototype.reset.call(this);
			this._hash = new WordArray(H$1.slice(0));
		};
		_proto13._doProcessBlock = function _doProcessBlock(M, offset) {
			var Hl = this._hash.words;
			var a = Hl[0];
			var b = Hl[1];
			var c = Hl[2];
			var d = Hl[3];
			var e = Hl[4];
			var f = Hl[5];
			var g = Hl[6];
			var h = Hl[7];
			for (var _i10 = 0; _i10 < 64; _i10++) {
				if (_i10 < 16) {
					W$1[_i10] = M[offset + _i10] | 0;
				} else {
					var gamma0x = W$1[_i10 - 15];
					var gamma0 =
						((gamma0x << 25) | (gamma0x >>> 7)) ^
						((gamma0x << 14) | (gamma0x >>> 18)) ^
						(gamma0x >>> 3);
					var gamma1x = W$1[_i10 - 2];
					var gamma1 =
						((gamma1x << 15) | (gamma1x >>> 17)) ^
						((gamma1x << 13) | (gamma1x >>> 19)) ^
						(gamma1x >>> 10);
					W$1[_i10] = gamma0 + W$1[_i10 - 7] + gamma1 + W$1[_i10 - 16];
				}
				var ch = (e & f) ^ (~e & g);
				var maj = (a & b) ^ (a & c) ^ (b & c);
				var sigma0 =
					((a << 30) | (a >>> 2)) ^
					((a << 19) | (a >>> 13)) ^
					((a << 10) | (a >>> 22));
				var sigma1 =
					((e << 26) | (e >>> 6)) ^
					((e << 21) | (e >>> 11)) ^
					((e << 7) | (e >>> 25));
				var t1 = h + sigma1 + ch + K[_i10] + W$1[_i10];
				var t2 = sigma0 + maj;
				h = g;
				g = f;
				f = e;
				e = (d + t1) | 0;
				d = c;
				c = b;
				b = a;
				a = (t1 + t2) | 0;
			}
			Hl[0] = (Hl[0] + a) | 0;
			Hl[1] = (Hl[1] + b) | 0;
			Hl[2] = (Hl[2] + c) | 0;
			Hl[3] = (Hl[3] + d) | 0;
			Hl[4] = (Hl[4] + e) | 0;
			Hl[5] = (Hl[5] + f) | 0;
			Hl[6] = (Hl[6] + g) | 0;
			Hl[7] = (Hl[7] + h) | 0;
		};
		_proto13._doFinalize = function _doFinalize() {
			var nBitsTotal = this._nDataBytes * 8;
			var nBitsLeft = this._data.sigBytes * 8;
			this._data.words[nBitsLeft >>> 5] |= 0x80 << (24 - (nBitsLeft % 32));
			this._data.words[(((nBitsLeft + 64) >>> 9) << 4) + 14] = Math.floor(
				nBitsTotal / 0x100000000
			);
			this._data.words[(((nBitsLeft + 64) >>> 9) << 4) + 15] = nBitsTotal;
			this._data.sigBytes = this._data.words.length * 4;
			this._process();
			return this._hash;
		};
		return SHA256;
	})(Hasher);
	var NoPadding = /*#__PURE__*/ (function() {
		function NoPadding() {}
		NoPadding.pad = function pad(data, blockSize) {};
		NoPadding.unpad = function unpad(data) {};
		return NoPadding;
	})();
	var ECBEncryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor3) {
		_inheritsLoose(ECBEncryptor, _BlockCipherModeAlgor3);
		function ECBEncryptor() {
			return _BlockCipherModeAlgor3.apply(this, arguments) || this;
		}
		var _proto14 = ECBEncryptor.prototype;
		_proto14.processBlock = function processBlock(words, offset) {
			this._cipher.encryptBlock(words, offset);
		};
		return ECBEncryptor;
	})(BlockCipherModeAlgorithm);
	var ECBDecryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor4) {
		_inheritsLoose(ECBDecryptor, _BlockCipherModeAlgor4);
		function ECBDecryptor() {
			return _BlockCipherModeAlgor4.apply(this, arguments) || this;
		}
		var _proto15 = ECBDecryptor.prototype;
		_proto15.processBlock = function processBlock(words, offset) {
			this._cipher.decryptBlock(words, offset);
		};
		return ECBDecryptor;
	})(BlockCipherModeAlgorithm);
	var ECB = /*#__PURE__*/ (function(_BlockCipherMode2) {
		_inheritsLoose(ECB, _BlockCipherMode2);
		function ECB() {
			return _BlockCipherMode2.apply(this, arguments) || this;
		}
		return ECB;
	})(BlockCipherMode);
	ECB.Encryptor = ECBEncryptor;
	ECB.Decryptor = ECBDecryptor;
	var lib = {
		BlockCipher: BlockCipher,
		WordArray: WordArray,
		CipherParams: CipherParams,
		Hasher: Hasher,
		SerializableCipher: SerializableCipher,
		PasswordBasedCipher: PasswordBasedCipher
	};
	var algo = {AES: AES, SHA256: SHA256, MD5: MD5};
	var enc = {Utf8: Utf8, Hex: Hex};
	var pad = {NoPadding: NoPadding, PKCS7: PKCS7};
	var mode = {CBC: CBC, ECB: ECB};
	var AES$1 = lib.BlockCipher._createHelper(algo.AES);
	var SHA256$1 = lib.Hasher._createHelper(algo.SHA256);
	var MD5$1 = lib.Hasher._createHelper(algo.MD5);

	// 加密
	function encrypt(word, keyStr) {
		var key = enc.Utf8.parse(keyStr);
		var srcs = enc.Utf8.parse(word);
		var encrypted = AES$1.encrypt(srcs, key, {
			mode: mode.ECB,
			padding: pad.PKCS7
		});
		return encrypted.toString();
	}

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var ZCheckbox = /*@__PURE__*/ (function(Component) {
		function ZCheckbox(props) {
			Component.call(this, props);
		}

		if (Component) ZCheckbox.__proto__ = Component;
		ZCheckbox.prototype = Object.create(Component && Component.prototype);
		ZCheckbox.prototype.constructor = ZCheckbox;
		ZCheckbox.prototype.render = function() {
			return apivm.h("a-iconfont", {
				size: G.appFontSize + (this.props.size || 0),
				name: this.props.checked ? "fangxingxuanzhongfill" : "fangxingweixuanzhong",
				color: this.props.checked ? this.props.color || G.appTheme : "#999"
			});
		};

		return ZCheckbox;
	})(Component);
	apivm.define("z-checkbox", ZCheckbox);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				dotClose: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.dotClose = true;
							setTimeout(function() {
								this$1.data.dotClose = false;
							}, 300);
						} else {
							G.actionSheetPop.pageClose();
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.closePage = function() {
			if (this.data.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			if (this.data.dotClose || _item.disabled) {
				return;
			}
			_item.buttonIndex = _index + 1;
			G.actionSheetPop.callback(_item);
			this.closePage();
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h("view", {
						onClick: function() {
							return this$1.closePage();
						},
						class: "actionSheet_cancel"
					}),
					apivm.h(
						"view",
						{
							class: this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "flex-shrink: 0;"
						},
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					apivm.h(
						"scroll-view",
						{
							class: !this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "height: auto;background: #FFF;flex-shrink: 1;",
							"scroll-y": true
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						(this.props.dataMore.data || []).map(function(item, index) {
							return (
								(isParameters(item.show) ? item.show : true) && [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.itemClick(item, index);
											},
											class: "actionSheet_item",
											style:
												"justify-content:" +
												(item.justify || "center") +
												";opacity: " +
												(item.disabled ? "0.3" : "1") +
												";"
										},
										apivm.h(
											"view",
											null,
											item.icon &&
												apivm.h("a-iconfont", {
													style: "margin-right:10px;",
													name: item.icon,
													color: (isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333",
													size:
														G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
												})
										),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-1) +
													";color:" +
													((isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333")
											},
											item.name
										)
									),
									index != this$1.props.dataMore.data.length - 1 &&
										!this$1.props.dataMore.cancel &&
										apivm.h(
											"view",
											{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
											apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
										)
								]
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-shrink: 0;"},
						this.props.dataMore.cancel &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "actionSheet_item",
									style:
										"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + ";color:#333"},
									this.props.dataMore.cancel || "取消"
								)
							)
					),
					apivm.h("view", {
						style:
							"background:#fff;flex-shrink: 0;padding-bottom:" +
							safeArea().bottom +
							"px;"
					})
				]
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_cancel": {flex: "1", minHeight: "20%", flexShrink: "0"},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "5px 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.inputBlur = function(e) {
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color: "#999",
								size: G.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G.appFontSize + 14) +
									"px;color:#999;" +
									loadConfiguration()
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style": "color:#ccc;",
							class: "z_input_input flex_w",
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								isParameters(this.props.readonly)
									? this.props.readonly
									: false,
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"}
	};
	apivm.define("z-input", ZInput);

	var YLogin = /*@__PURE__*/ (function(Component) {
		function YLogin(props) {
			Component.call(this, props);
			this.data = {
				accountInfo: {
					show: true,
					icon: "shoujihaoma",
					confirmType: "next",
					cleanFocus: true,
					iconSize: 4,
					maxlength: 32,
					value: "",
					placeholder: "账号"
				}, //账号信息
				passwordInfo: {
					show: true,
					icon: "mima1",
					confirmType: "go",
					cleanFocus: true,
					iconSize: 4,
					maxlength: 32,
					value: "",
					placeholder: "密码",
					inputType: "password",
					isLook: false
				}, //密码信息

				captchaInfo: {
					show: false,
					cleanFocus: true,
					confirmType: "go",
					icon: "yanzhengma1",
					iconSize: 4,
					maxlength: 6,
					value: "",
					placeholder: "验证码",
					hint: "获取验证码",
					timeAll: 60,
					nowTimeout: 0,
					expression: "\\D"
				}, //验证码
				loginErrorInfo: {text: "", type: -1}, //登录失败请重试 登录失败相关	0账号处不对	1密码处不对
				agreement: false, //是否同意用户协议
				agreementText: "我已阅读并同意《服务协议》和《隐私政策》"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.show) {
						this.show = this.props.dataMore.show;
						if (this.show) {
							this.baseInit();
						} else {
							this.baseclose();
						}
					}
				}
			};
		}

		if (Component) YLogin.__proto__ = Component;
		YLogin.prototype = Object.create(Component && Component.prototype);
		YLogin.prototype.constructor = YLogin;
		YLogin.prototype.uninstall = function() {
			this.baseclose();
		};
		YLogin.prototype.baseInit = function() {
			var this$1 = this;

			this.netizen = this.props.dataMore.netizen || "";
			G.showHeadTheme = G.appTheme;
			setTimeout(function() {
				G._this.update();
			}, 0);
			this.data.accountInfo.value = getPrefs("loginUserName") || "";
			this.data.passwordInfo.value = getPrefs("loginPassword") || "";
			if (this.netizen == 1) {
				this.data.accountInfo.placeholder = "手机号";
				this.data.passwordInfo.show = false;
				this.data.captchaInfo.show = true;
			} else {
				ajax(
					{u: appUrl() + "open_api/verifyCode/enableStatus"},
					"enableStatus",
					function(ret, err) {
						this$1.data.captchaInfo.show = ret && ret.data;
					},
					"开启短信验证",
					"post",
					{
						body: JSON.stringify({})
					},
					{Authorization: ""}
				);
			}
			if (!G.appName) {
				getAppInfos();
			}
		};
		YLogin.prototype.baseclose = function() {
			G.showHeadTheme = "";
		};
		YLogin.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		YLogin.prototype.inputFocus = function(_who) {
			this.data.loginErrorInfo.text = "";
		};
		YLogin.prototype.inputConfirm = function(_who) {
			switch (_who) {
				case "accountInfo":
					if (this.data.passwordInfo.show) {
						$("#passwordInput").focus();
					} else if (this.data.captchaInfo.show) {
						$("#captchaInput").focus();
					}
					break;
				case "passwordInfo":
					if (this.data.captchaInfo.show) {
						$("#captchaInput").focus();
					} else {
						this.login();
					}
					break;
				case "captchaInfo":
					this.login();
					break;
			}
		};
		YLogin.prototype.login = function() {
			var this$1 = this;

			if (!this.data.agreement) {
				toast({msg: "请" + this.data.agreementText.substring(2)});
				return;
			}
			this.data.loginErrorInfo.text = "";
			if (this.data.accountInfo.show && !this.data.accountInfo.value) {
				this.data.loginErrorInfo.text = "账号不能为空！";
				this.data.loginErrorInfo.type = 0;
				return;
			}
			if (this.data.passwordInfo.show && !this.data.passwordInfo.value) {
				this.data.loginErrorInfo.text = "密码不能为空！";
				this.data.loginErrorInfo.type = 1;
				return;
			}
			if (this.data.captchaInfo.show && !this.data.captchaInfo.value) {
				this.data.loginErrorInfo.text = "验证码不能为空！";
				this.data.loginErrorInfo.type = 2;
				return;
			}
			removePrefs("sys_aresId"); //登录的时候删除地区
			removePrefs("sys_token");
			showProgress("登录中");
			var param = {};

			if (this.netizen == 1) {
				param.grant_type = "anonymous";
				param.mobile = this.data.accountInfo.value;
				param.userName = "公众" + param.mobile.substr(-4);
			} else {
				param.grant_type = "password";
				param.username = this.data.accountInfo.value;
				param.password = encrypt(this.data.passwordInfo.value, "zysofthnzx202002");
			}
			if (this.data.captchaInfo.show) {
				param.verifyCodeId = this.data.captchaInfo.id;
				param.verifyCode = this.data.captchaInfo.value;
			}
			ajax(
				{u: appUrl() + "oauth/token?", t: "login", web: this.netizen == 1},
				"login",
				function(ret, err) {
					var code = ret ? ret.code : "";
					if (code == 200) {
						var token = ret.data.token || "";
						setPrefs("sys_token", token);
						setPrefs("loginUserName", this$1.data.accountInfo.value);
						setPrefs("terminal", this$1.netizen == 1 ? "PUBLIC" : "APP");
						if (this$1.netizen == 1) {
							hideProgress();
							setPrefs("public_token", token); //公众
							setPrefs(
								"tokenEndTime",
								"" + new Date(new Date() * 1 + 1800 * 1000).getTime()
							); //公众30min过期
							setPrefs("isAutoLogin", "true");
							G.loginResult = {
								accountId: null,
								mobile: param.mobile,
								userName: param.userName
							};

							this$1.verifyScuress();
						} else {
							setPrefs("loginPassword", this$1.data.passwordInfo.value);
							this$1.loginAllBack();
						}
					} else {
						hideProgress();
						this$1.data.loginErrorInfo.text = ret
							? ret.message || ret.body
							: err.msg || err.body || NET_ERR;
						this$1.data.loginErrorInfo.type = -1;
					}
				},
				"登录token",
				"post",
				{
					values: param
				},
				{
					"u-login-areaId": "",
					Authorization: "basic enlzb2Z0Onp5c29mdCo2MDc5",
					"content-type": "application/x-www-form-urlencoded"
				}
			);
		};
		YLogin.prototype.loginAllBack = function(_progress) {
			var this$1 = this;

			if (_progress) {
				showProgress("登录中", true);
			}
			getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {
				hideProgress();
				if (ret) {
					var code = ret.code || "";
					if (code == 200) {
						setPrefs("loginPassword", this$1.data.passwordInfo.value);
						setPrefs("isAutoLogin", "true");
						G.loginResult = ret.data;
						this$1.verifyScuress();
					} else {
						this$1.data.loginErrorInfo.text = ret.message;
						this$1.data.loginErrorInfo.type = -1;
					}
				} else {
					this$1.data.loginErrorInfo.text = ret
						? ret.message || ret.body
						: err.msg || err.body || NET_ERR;
					this$1.data.loginErrorInfo.type = -1;
				}
			});
		};
		YLogin.prototype.verifyScuress = function() {
			var this$1 = this;

			saveLogin(G.loginResult);
			setTimeout(function() {
				this$1.fire("result");
			}, 0);
			if (!G.appName) {
				getAppInfos();
			}
		};
		YLogin.prototype.getVerification = function() {
			var this$1 = this;

			if (this.data.captchaInfo.nowTimeout > 0) {
				return;
			}
			sendCode({phone: this.data.accountInfo.value}, function(ret) {
				this$1.data.captchaInfo.id = ret.data;
				this$1.data.captchaInfo.isSend = this$1.data.accountInfo.value;
				this$1.data.captchaInfo.nowTimeout = this$1.data.captchaInfo.timeAll;
				this$1.countdown();
			});
		};
		YLogin.prototype.countdown = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.captchaInfo.nowTimeout--;
				if (this$1.data.captchaInfo.nowTimeout > 0) {
					this$1.countdown();
				}
			}, 1000);
		};
		YLogin.prototype.chageAgreement = function(_index) {
			if (isParameters(_index)) {
				if (_index > 5 && _index < 12) {
					openWin_apptext({title: "服务协议", pt: "fwxy", id: "fwxy"});
					return;
				} else if (_index > 12 && _index < 19) {
					openWin_apptext({title: "隐私政策", pt: "yszc", id: "yszc"});
					return;
				}
			}
			this.data.agreement = !this.data.agreement;
		};
		YLogin.prototype.openOther = function(_who) {
			switch (_who) {
				case "password":
					openWin_password({title: "重置密码", type: 1});
					break;
				case "helpLogin":
					openWin_apptext({title: "其他帮助", pt: "qtbz"});
					break;
				case "register":
					openWin_password({title: "立即注册", type: 3});
					break;
			}
		};
		YLogin.prototype.hiddenEvent = function(_type) {
			if (!G.loginDoubleClick) {
				G.loginDoubleClick = true;
				setTimeout(function() {
					G.loginDoubleClick = false;
				}, 500);
				return;
			}
			var buttons = [];
			if (_type == "left") {
				buttons = [
					"还原为初始地址",
					"自定义平台(rd人大，zx政协)",
					"自定义系统地址",
					"一键改为标准环境",
					"是否提示被挤下线(0是1否)",
					"修改融云chatHeader",
					"伪装成~(不懂不要点)",
					"修改融云正测环境(1正2测)"
				];
			} else {
				buttons = ["app版本"];
			}
			function reboot() {
				setTimeout(function() {
					alert("已修改，立即重启生效", function(ret) {
						cleanAllMsg();
						rebootApp();
					});
				}, 50);
			}
			actionSheet(
				{
					title: "隐藏操作Public beta-V 0.0.4",
					buttons: buttons
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					if (_index <= buttons.length) {
						var nowType = 1; // 正常提示文本  2输入框文本 3确定文本
						var msgText = buttons[_index - 1];
						var alertParam = {
							title: "",
							msg: msgText,
							buttons: ["我知道了"]
						};

						switch (msgText) {
							case "app版本":
								alertParam.title = msgText;
								alertParam.msg = api.appVersion;
								break;
							case "自定义平台(rd人大，zx政协)":
								nowType = 2;
								alertParam.msg = G.sysSign;
								break;
							case "自定义系统地址":
								nowType = 2;
								alertParam.msg = appUrl();
								break;
							case "是否提示被挤下线(0是1否)":
								nowType = 2;
								alertParam.msg = getPrefs("downLine") || "0";
								break;
							case "修改融云chatHeader":
								nowType = 2;
								alertParam.msg = chatHeader();
								break;
							case "修改融云正测环境(1正2测)":
								nowType = 2;
								alertParam.msg = chatEnvironment();
								break;
							case "伪装成~(不懂不要点)":
								nowType = 2;
								alertParam.msg = getPrefs("camouflageId");
								break;
							case "还原为初始地址":
							case "一键改为标准环境":
								nowType = 3;
								break;
						}

						if (nowType == 2) {
							alertParam.title = msgText;
							alertParam.type = "input";
							alertParam.buttons = ["确定", "取消"];
						} else if (nowType == 3) {
							alertParam.buttons = ["确定", "取消"];
						}
						alert(alertParam, function(ret) {
							if ((nowType == 2 || nowType == 3) && ret.buttonIndex == 1) {
								switch (msgText) {
									case "还原为初始地址":
										removePrefs("sys_appUrl");
										removePrefs("sys_sign");
										removePrefs("sys_chatEnvironment");
										removePrefs("sys_chatHeader");
										removePrefs("camouflageId");
										reboot();
										break;
									case "一键改为标准环境":
										setPrefs(
											"sys_appUrl",
											"https://bz.cszysoft.com:" +
												(G.sysSign == "rd" ? "8088" : "8089") +
												"/lzt/"
										);
										reboot();
										break;
									case "自定义平台(rd人大，zx政协)":
										if (ret.content != alertParam.msg) {
											setPrefs("sys_sign", ret.content);
											reboot();
										}
										break;
									case "自定义系统地址":
										if (ret.content != alertParam.msg) {
											setPrefs("sys_appUrl", ret.content);
											reboot();
										}
										break;
									case "修改融云正测环境(1正2测)":
										if (ret.content != alertParam.msg) {
											setPrefs("sys_chatEnvironment", ret.content);
											reboot();
										}
										break;
									case "是否提示被挤下线(0是1否)":
										if (ret.content != alertParam.msg) {
											setPrefs("downLine", ret.content);
											reboot();
										}
										break;
									case "修改融云chatHeader":
										if (ret.content != alertParam.msg) {
											setPrefs("sys_chatHeader", ret.content);
											reboot();
										}
										break;
									case "伪装成~(不懂不要点)":
										if (ret.content != alertParam.msg) {
											setPrefs("camouflageId", ret.content);
											reboot();
										}
										break;
								}
							}
						});
					}
				}
			);
		};
		YLogin.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:" +
						(this.props.dataMore.bg ? "#FFF" : "transparent") +
						";"
				},
				apivm.h(
					"y-scroll-view",
					{class: "flex_h"},
					apivm.h(
						"view",
						{
							style:
								"background:" +
								G.appTheme +
								";border-bottom-right-radius:50px;padding-top:" +
								headerTop() +
								"px;height: " +
								(166 + headerTop()) +
								"px;"
						},
						apivm.h(
							"view",
							{class: "login_bg_default"},
							apivm.h("image", {
								style: loadConfigurationSize(39) + "border-radius:15px;",
								src: showImg(appUrl() + "pageImg/open/logo"),
								mode: "aspectFit"
							}),
							apivm.h(
								"text",
								{class: "login_bg_default_text", style: loadConfiguration(14)},
								G.appName
							)
						)
					),
					apivm.h(
						"view",
						{style: "width:50px;height:50px;background:" + G.appTheme + ";"},
						apivm.h("view", {
							style:
								"width:100%;height:100%;background:#FFF;border-top-left-radius:50px;"
						})
					),
					apivm.h(
						"view",
						{style: "padding:0 30px;"},
						apivm.h(
							"view",
							{
								class: "login_input_warp",
								style:
									"border:1px solid " +
									(this.data.loginErrorInfo.text && this.data.loginErrorInfo.type == 0
										? "#ec1f2e"
										: "transparent") +
									";"
							},
							apivm.h("z-input", {
								id: "accountInput",
								class: "flex_w",
								dataMore: this.data.accountInfo,
								bg: "transparent",
								onConfirm: function() {
									return this$1.inputConfirm("accountInfo");
								},
								onFocus: function() {
									return this$1.inputFocus("accountInfo");
								}
							})
						),
						apivm.h(
							"view",
							null,
							this.data.passwordInfo.show &&
								apivm.h(
									"view",
									{
										class: "login_input_warp",
										style:
											"margin-top:20px;border:1px solid " +
											(this.data.loginErrorInfo.text && this.data.loginErrorInfo.type == 1
												? "#ec1f2e"
												: "transparent") +
											";"
									},
									apivm.h("z-input", {
										id: "passwordInput",
										class: "flex_w",
										dataMore: this.data.passwordInfo,
										bg: "transparent",
										onConfirm: function() {
											return this$1.inputConfirm("passwordInfo");
										},
										onFocus: function() {
											return this$1.inputFocus("passwordInfo");
										}
									})
								)
						),
						apivm.h(
							"view",
							null,
							this.data.captchaInfo.show &&
								apivm.h(
									"view",
									{
										class: "login_input_warp",
										style:
											"margin-top:20px;border:1px solid " +
											(this.data.loginErrorInfo.text && this.data.loginErrorInfo.type == 2
												? "#ec1f2e"
												: "transparent") +
											";"
									},
									apivm.h("z-input", {
										id: "captchaInput",
										class: "flex_w",
										dataMore: this.data.captchaInfo,
										bg: "transparent",
										onConfirm: function() {
											return this$1.inputConfirm("captchaInfo");
										},
										onFocus: function() {
											return this$1.inputFocus("captchaInfo");
										}
									}),
									apivm.h("z-button", {
										onClick: function() {
											return this$1.getVerification();
										},
										plain: true,
										text:
											this.data.captchaInfo.nowTimeout > 0
												? "重发" + this.data.captchaInfo.nowTimeout + "s"
												: this.data.captchaInfo.hint,
										size: -2,
										style: "padding:4px 10px;margin-right:10px;",
										color: this.data.captchaInfo.nowTimeout > 0 ? "#999" : G.appTheme
									})
								)
						),
						apivm.h(
							"view",
							null,
							this.data.loginErrorInfo.text &&
								apivm.h(
									"text",
									{style: loadConfiguration(), class: "login_msg_error"},
									this.data.loginErrorInfo.text
								)
						)
					),
					apivm.h(
						"view",
						{style: "margin-top: 15px;padding: 0 20px 0 30px;"},
						apivm.h(
							"view",
							{style: "flex-direction:row;"},
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.chageAgreement();
									},
									style: "margin-right: 5px;"
								},
								apivm.h("z-checkbox", {
									checked: this.data.agreement,
									size: 2,
									color: this.data.agreement ? G.appTheme : "#999"
								})
							),
							apivm.h(
								"view",
								{style: "flex:1;flex-direction:row;flex-wrap: wrap;"},
								(Array.isArray(this.data.agreementText)
									? this.data.agreementText
									: Object.values(this.data.agreementText)
								).map(function(item$1, index$1) {
									return apivm.h(
										"text",
										{
											onClick: function() {
												return this$1.chageAgreement(index$1);
											},
											style:
												loadConfiguration(-2) +
												"color:" +
												((index$1 > 6 && index$1 < 13) || (index$1 > 13 && index$1 < 20)
													? G.appTheme
													: "#333")
										},
										item$1
									);
								})
							)
						)
					),
					apivm.h(
						"view",
						{style: "margin-top: 47px;padding: 0 30px;"},
						apivm.h("z-button", {
							onClick: function() {
								return this$1.login();
							},
							disabled:
								(this.data.accountInfo.show && !this.data.accountInfo.value) ||
								(this.data.passwordInfo.show && !this.data.passwordInfo.value) ||
								(this.data.captchaInfo.show && !this.data.captchaInfo.value),
							text: "登录",
							size: 2,
							style: "box-shadow: 0px 2px 10px 1px rgba(198,20,20,0.12);height:48px;",
							color: G.appTheme
						})
					),
					apivm.h(
						"view",
						{
							style:
								"flex-direction:row;align-items : center;justify-content : center;margin-top:60px;"
						},
						apivm.h(
							"text",
							{
								style: loadConfiguration(-2),
								onClick: function() {
									return this$1.openOther("password");
								}
							},
							"忘记密码"
						),
						apivm.h("view", {
							style:
								"width:1px;height:" +
								G.appFontSize +
								"px;background:#666;margin:0 20px;"
						}),
						apivm.h(
							"text",
							{
								style: loadConfiguration(-2),
								onClick: function() {
									return this$1.openOther("helpLogin");
								}
							},
							"其他帮助"
						),
						G.isAppReview
							? [
									apivm.h("view", {
										style:
											"width:1px;height:" +
											G.appFontSize +
											"px;background:#666;margin:0 20px;"
									}),
									apivm.h(
										"text",
										{
											style: loadConfiguration(-2),
											onClick: function() {
												return this$1.openOther("register");
											}
										},
										"立即注册"
									)
							  ]
							: []
					),
					apivm.h(
						"view",
						{class: "xy_center"},
						G.loginInfo &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-2) +
										"color:#666;margin-top:30px;text-align: center;"
								},
								G.loginInfo
							)
					),
					apivm.h("view", {style: "height:50px;"})
				),
				apivm.h("text", {
					onClick: function() {
						return this$1.hiddenEvent("left");
					},
					style: "height:" + (footerBottom() + 50) + "px;left: 0;",
					class: "login_alertInfo"
				}),
				apivm.h("text", {
					onClick: function() {
						return this$1.hiddenEvent("right");
					},
					style: "height:" + (footerBottom() + 50) + "px;right: 0;",
					class: "login_alertInfo"
				})
			);
		};

		return YLogin;
	})(Component);
	YLogin.css = {
		".login_alertInfo": {
			position: "absolute",
			zIndex: "999",
			bottom: "0",
			width: "50px",
			height: "50px",
			background: "transparent"
		},
		".login_bg_default": {
			position: "absolute",
			zIndex: "999",
			alignItems: "center",
			bottom: "30px",
			left: "30px",
			right: "20px",
			flexDirection: "row",
			wordWrap: "break-word",
			wordBreak: "break-all"
		},
		".login_bg_default_text": {
			color: "#fff",
			marginLeft: "10px",
			fontWeight: "600"
		},
		".login_input_warp": {
			flexDirection: "row",
			alignItems: "center",
			width: "100%",
			height: "48px",
			marginTop: "20px",
			boxShadow: "0px 1px 6px 1px rgba(24,64,118,0.12)",
			background: "#FFF",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomRightRadius: "4px",
			borderBottomLeftRadius: "4px"
		},
		".login_msg_error": {
			color: "red",
			textAlign: "center",
			padding: "5px 10px",
			marginTop: "15px"
		}
	};
	apivm.define("y-login", YLogin);

	var ZTag = /*@__PURE__*/ (function(Component) {
		function ZTag(props) {
			Component.call(this, props);
		}

		if (Component) ZTag.__proto__ = Component;
		ZTag.prototype = Object.create(Component && Component.prototype);
		ZTag.prototype.constructor = ZTag;
		ZTag.prototype.getTagStyle = function() {
			var color = this.props.color || G.appTheme;
			var rc = "",
				rbg = "",
				rbor = "";
			switch (this.props.type) {
				case "2":
					rc = color;
					rbg = "#FFF";
					rbor = colorRgba(color);
					break;
				case "3":
					rc = "#FFF";
					rbg = color;
					rbor = color;
					break;
				default:
					rc = color;
					rbg = colorRgba(color, 0.1);
					rbor = "transparent";
					break;
			}

			return (
				"color:" +
				rc +
				";background:" +
				rbg +
				";border: 1px solid " +
				rbor +
				";border-radius: " +
				(this.props.roundSize || 2) +
				"px;"
			);
		};
		ZTag.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "flex-direction:row;"},
				apivm.h(
					"text",
					{
						id: "" + (this.props.id || ""),
						class: "tag_box " + (this.props.class || ""),
						style:
							"" +
							loadConfiguration((this.props.size || 0) - 2) +
							this.getTagStyle() +
							(this.props.style || "")
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZTag;
	})(Component);
	ZTag.css = {".tag_box": {padding: "1px 10px"}};
	apivm.define("z-tag", ZTag);

	//区域截图
	function screenshotIntoPic(_id, _callback, _dm) {
		switch (platform()) {
			case "app":
				api.screenCapture(
					{
						region: _id
					},
					function(ret) {
						_callback(ret ? ret.savePath || "" : "");
					}
				);
				break;
			case "web":
				var imgData = _dm.shareImg || document.querySelector(_id).toDataURL() || "";
				_dm.shareImg = imgData;
				_callback(imgData);
				break;
			case "mp":
				document
					.querySelector(_id)
					.$$getNodesRef()
					.then(function(nodesRef) {
						nodesRef
							.node(function(res) {
								var canvas = res.node;
								wx.canvasToTempFilePath({
									canvas: canvas,
									success: function success(res) {
										_callback(res.tempFilePath);
									},
									fail: function fail() {
										_callback("");
									}
								});
							})
							.exec();
					});
				break;
		}
	}

	//图片转成file
	function dataURLtoFile(_path) {
		var data = _path.split(",");
		var type = data[0].match(/:(.*?);/)[1];
		var suffix = type.split("/")[1];
		var bstr = window.atob(data[1]);
		var n = bstr.length;
		var u8Arr = new Uint8Array(n);
		while (n--) {
			u8Arr[n] = bstr.charCodeAt(n);
		}
		return new File([u8Arr], new Date().valueOf() + "." + suffix, {type: type});
	}

	//图片保存到相册
	function picInfoAlbum(_path, _callback) {
		switch (platform()) {
			case "app":
				api.saveMediaToAlbum(
					{
						path: _path
					},
					function(ret) {
						_callback(ret && ret.status ? "已保存到手机相册" : "");
					}
				);
				break;
			case "web":
				try {
					var base64 = _path.split(",")[1];
					var imgName = new Date().valueOf() + ".png";
					var trans = window.trans || window.parent.trans;
					if (trans) {
						trans.saveImage(
							{
								base64Str: base64,
								album: true,
								imgPath: "fs://file_temporary/",
								imgName: imgName
							},
							function(ret, err) {
								if (ret) {
									_callback(ret && ret.status ? "已保存到手机相册" : "");
								} else {
									_callback(false, err.msg);
								}
							}
						);
						return;
					}
					if (isWeChat() || isMiniProgram()) {
						toast("请长按图片保存或转发");
						return;
					}
					var bytes = atob(base64);
					var ab = new ArrayBuffer(bytes.length);
					var ia = new Uint8Array(ab);
					for (var i = 0; i < bytes.length; i++) {
						ia[i] = bytes.charCodeAt(i);
					}
					var blob = new Blob([ab], {type: "application/octet-stream"});
					var url = URL.createObjectURL(blob);
					var a = document.createElement("a");
					a.href = url;
					a.download = imgName;
					var e = document.createEvent("MouseEvents");
					e.initMouseEvent(
						"click",
						true,
						false,
						window,
						0,
						0,
						0,
						0,
						0,
						false,
						false,
						false,
						false,
						0,
						null
					);
					a.dispatchEvent(e);
					URL.revokeObjectURL(url);
					_callback("已保存");
				} catch (e) {
					_callback(false);
				}
				break;
			case "mp":
				wx.saveImageToPhotosAlbum({
					filePath: _path,
					success: function success() {
						_callback("已保存到手机相册");
					},
					fail: function fail() {
						_callback(false);
					}
				});

				break;
		}
	}

	//点
	function canvasPoint(x, y) {
		return {x: x, y: y};
	}

	//圆角：区域、角度、ctx对象
	function canvasRoundedRect(rect, r, ctx) {
		var lt, lb, rt, rb;
		if (isArray(r)) {
			lt = r[0];
			rt = r[1];
			rb = r[2];
			lb = r[3];
		} else {
			lt = r;
			rt = r;
			rb = r;
			lb = r;
		}
		var ptA = canvasPoint(rect.x + lt, rect.y);
		var ptB = canvasPoint(rect.x + rect.width, rect.y);
		var ptC = canvasPoint(rect.x + rect.width, rect.y + rect.height);
		var ptD = canvasPoint(rect.x, rect.y + rect.height);
		var ptE = canvasPoint(rect.x, rect.y);
		ctx.beginPath();
		ctx.moveTo(ptA.x, ptA.y);
		ctx.arcTo(ptB.x, ptB.y, ptC.x, ptC.y, rt);
		ctx.arcTo(ptC.x, ptC.y, ptD.x, ptD.y, rb);
		ctx.arcTo(ptD.x, ptD.y, ptE.x, ptE.y, lb);
		ctx.arcTo(ptE.x, ptE.y, ptA.x, ptA.y, lt);
		ctx.closePath();
	}

	//计算文字地区
	function canvasCalcText(_text, _width, _left, _top, _margin, ctx) {
		_text = _text + "";
		if (ctx.measureText(_text).width < _width) {
			ctx.fillText(_text, _left, _top);
			return;
		}
		var nowText = "",
			nowList = _text.split("");
		for (var i = 0; i < nowList.length; i++) {
			nowText += nowList[i];
			if (ctx.measureText(nowText).width > _width + 5) {
				nowText = nowText.substring(0, nowText.length - 1);
				break;
			}
		}
		ctx.fillText(nowText, _left, _top);
		if (nowText != _text) {
			canvasCalcText(
				_text.substring(nowText.length),
				_width,
				_left,
				_top + _margin,
				_margin,
				ctx
			);
		}
	}

	//展示文字
	function canvasShowText(_param, ctx) {
		getBoundingClientRect(_param.id, function(ret) {
			ctx.save();
			ctx.textBaseline = _param.tLine || "top";
			ctx.textAlign = _param.tAlign || "left";
			ctx.fillStyle = _param.color || "#333333";
			ctx.font =
				(_param.weight || "400") +
				" " +
				(G.appFontSize + (_param.size || 0)) +
				"px Arial";
			canvasCalcText(
				_param.text,
				ret.width,
				ret.left - _param.bRect.left,
				ret.top - _param.bRect.top + (_param.spacing || 5) / 2 + 2,
				G.appFontSize + (_param.size || 0) + (_param.spacing || 5),
				ctx
			);
			ctx.restore();
		});
	}

	//展示图片 type:none|cover|contain
	function canvasShowImg(_param, ctx, canvas) {
		getBoundingClientRect(_param.id, function(ret) {
			var image = platform() == "mp" ? canvas.createImage() : new Image();
			image.crossOrigin = "anonymous";
			image.onload = function() {
				ctx.save();
				var imgLeft = ret.left - _param.bRect.left,
					imgTop = ret.top - _param.bRect.top;
				canvasRoundedRect(
					{x: imgLeft, y: imgTop, width: ret.width, height: ret.height},
					_param.round || 0,
					ctx
				);
				if (_param.bg) {
					ctx.fillStyle = _param.bg;
					ctx.fill();
				}
				ctx.clip();
				if (_param.type == "cover" || _param.type == "contain") {
					var sx, sy, sw, sh, imgRatio, canvasRatio;
					canvasRatio = ret.width / ret.height;
					imgRatio = image.width / image.height;
					if (imgRatio <= canvasRatio) {
						sw = {cover: image.width, contain: imgRatio * ret.width}[_param.type];
						sh = {cover: image.width / canvasRatio, contain: ret.height}[_param.type];
						sx = {cover: 0, contain: (ret.width - imgRatio * ret.width) / 2}[
							_param.type
						];
						sy = {cover: (image.height - image.width / canvasRatio) / 2, contain: 0}[
							_param.type
						];
					} else {
						sw = {cover: image.height * canvasRatio, contain: ret.width}[_param.type];
						sh = {cover: image.height, contain: ret.width / imgRatio}[_param.type];
						sx = {cover: (image.width - image.height * canvasRatio) / 2, contain: 0}[
							_param.type
						];
						sy = {cover: 0, contain: (ret.height - ret.width / imgRatio) / 2}[
							_param.type
						];
					}
					if (_param.type == "cover") {
						ctx.drawImage(
							this,
							sx,
							sy,
							sw,
							sh,
							imgLeft,
							imgTop,
							ret.width,
							ret.height
						);
					} else {
						ctx.drawImage(this, imgLeft + sx, imgTop + sy, sw, sh);
					}
				} else {
					ctx.drawImage(this, imgLeft, imgTop, ret.width, ret.height);
				}
				ctx.restore();
				_param.callback && _param.callback(true);
			};
			image.onerror = function(e) {
				_param.callback && _param.callback(false);
			};
			image.src = _param.src || "";
		});
	}

	//画布中加载图片回调
	function canvasImgLoad(_dm) {
		if (_dm.canvasImg <= 0) {
			if (platform() == "web" && (isWeChat() || isMiniProgram())) {
				toast("请长按图片保存或转发");
			}
			setTimeout(function() {
				hideProgress();
				if (platform() == "web") {
					screenshotIntoPic("#sharePosterCanvas", function(_path) {}, _dm);
				}
			}, 50);
		}
	}

	//设置背景 阴影
	function canvasShowBG(_param, ctx) {
		getBoundingClientRect(_param.id, function(ret) {
			ctx.save();
			canvasRoundedRect(
				{
					x: ret.left - _param.bRect.left,
					y: ret.top - _param.bRect.top,
					width: ret.width,
					height: ret.height
				},
				_param.round || 0,
				ctx
			);
			if (_param.bg) {
				ctx.fillStyle = _param.bg;
			}
			if (_param.shadow) {
				ctx.shadowColor = _param.shadow;
				ctx.shadowBlur = 10;
			}
			ctx.fill();
			ctx.restore();
		});
	}

	var SharePoster = /*@__PURE__*/ (function(Component) {
		function SharePoster(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				btns: [
					{
						show: true,
						key: "platform",
						value: "平台好友",
						type: "img",
						src: appUrl() + "pageImg/open/logo"
					},
					// {show:true,key:"session",value:"微信好友",type:"icon",bg:"#50C614",color:"#FFF",src:"changyonglogo28",size:16},
					// {show:true,key:"timeline",value:"朋友圈",type:"icon",bg:"#50C614",color:"#FFF",src:"pengyouquan",size:13},
					{
						show: true,
						key: "QFriend",
						value: "QQ",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "QQ",
						size: 13
					},
					{
						show: true,
						key: "QZone",
						value: "QQ空间",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "pengyouquan",
						size: 13
					},
					{
						show: true,
						key: "save",
						value: "保存图片",
						type: "icon",
						bg: "",
						color: "",
						src: "zhixiangxia",
						size: 13
					}
				]
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.baseInit();
						}
					}
				}
			};
		}

		if (Component) SharePoster.__proto__ = Component;
		SharePoster.prototype = Object.create(Component && Component.prototype);
		SharePoster.prototype.constructor = SharePoster;
		SharePoster.prototype.baseInit = function() {
			var showBtns = this.props.dataMore.btns || [];
			this.data.btns.forEach(function(_item) {
				_item.show = !showBtns.length || getItemForKey(_item.key, showBtns);
				if (_item.key != "save" && _item.key != "platform") {
					_item.show = _item.show && platform() == "app";
				}
			});
		};
		SharePoster.prototype.penetrate = function() {};
		SharePoster.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		SharePoster.prototype.shareBtn = function(_item) {
			var this$1 = this;

			switch (_item.key) {
				case "platform":
					var openCallback = function(_path) {
						openWin_chat_share(
							{
								fType: "sendImageMessage",
								fImagePath: _path
							},
							function() {
								this$1.closePage();
								G.sharePop.show = false;
							}
						);
					};
					var savecallback = function(_path) {
						if (_path) {
							if (platform() != "web") {
								openCallback(_path);
							} else {
								showProgress("分享中");
								uploadFile({url: dataURLtoFile(_path)}, function(ret) {
									hideProgress();
									if (ret.state == 2) {
										openCallback(appUrl() + "image/" + ret.otherInfo.newFileName);
									} else {
										toast(ret.error);
									}
								});
							}
						} else {
							toast("保存失败");
						}
					};
					screenshotIntoPic(
						platform() == "app" ? "#sharePoster" : "#sharePosterCanvas",
						savecallback,
						this.props.dataMore
					);
					break;
				case "save":
					var savecallback = function(_path) {
						if (_path) {
							picInfoAlbum(_path, function(ret, err) {
								toast(ret || err || "保存失败");
								if (ret) {
									setTimeout(function() {
										this$1.closePage();
										G.sharePop.show = false;
									}, 500);
								}
							});
						} else {
							toast("保存失败");
						}
					};
					screenshotIntoPic(
						platform() == "app" ? "#sharePoster" : "#sharePosterCanvas",
						savecallback,
						this.props.dataMore
					);
					break;
				case "QFriend":
				case "QZone":
					var qq = api.require("QQPlus");
					if (!qq) {
						toast("未绑定模块，请联系管理员");
						return;
					}
					qq.setIsPermissionGranted({granted: true});
					qq.installed(function(ret, err) {
						if (!ret.status) {
							toast("当前设备未安装QQ客户端");
							return;
						}
						api.screenCapture(
							{
								region: "#sharePoster"
							},
							function(ret, err) {
								var param = {
									imgPath: ret.savePath,
									type: _item.key
								};

								console.log(JSON.stringify(param));
								qq.shareImage(param, function(ret, err) {
									toast(ret.status ? "分享成功！" : JSON.stringify(err));
								});
							}
						);
					});
					break;
				case "session":
				case "timeline":
					var wx = api.require("wxPlus");
					if (!wx) {
						toast("未绑定模块，请联系管理员");
						return;
					}
					wx.isInstalled(function(ret, err) {
						if (!ret.installed) {
							toast("当前设备未安装wx客户端");
							return;
						}
						api.screenCapture(
							{
								region: "#sharePoster"
							},
							function(ret, err) {
								var param = {
									contentUrl: ret.savePath,
									scene: _item.key
								};

								console.log(JSON.stringify(param));
								wx.shareImage(param, function(ret, err) {
									toast(ret.status ? "分享成功！" : JSON.stringify(err));
								});
							}
						);
					});
					break;
				default:
					toast("分享到" + _item.value);
					break;
			}
		};
		SharePoster.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.data.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h(
						"view",
						{class: "flex_h xy_center"},
						apivm.h(
							"view",
							{class: "poster_base"},
							this.props.dataMore.shareImg
								? apivm.h("image", {
										class: "poster_box",
										style: this.props.canvasStyle,
										src: this.props.dataMore.shareImg,
										mode: "aspectFill",
										thumbnail: "false"
								  })
								: [
										apivm.h("canvas", {
											type: "2d",
											id: "sharePosterCanvas",
											class: "canvas",
											style:
												"display:" +
												(platform() != "app" ? "flex" : "none") +
												";" +
												this.props.canvasStyle
										}),
										this.props.children
								  ]
						)
					),
					apivm.h(
						"view",
						{
							class: "share_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "share_btn_box"},
							this.data.btns.map(function(item, index, list) {
								return (
									(isParameters(item.show) ? item.show : true) &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.shareBtn(item);
											},
											class: "share_btn_item"
										},
										item.type == "img"
											? apivm.h("image", {
													style: "" + loadConfigurationSize(34),
													class: "share_btn_icon",
													src: item.src
											  })
											: apivm.h(
													"view",
													{
														style:
															loadConfigurationSize(34) +
															"background:" +
															(item.bg || "#F4F5F7") +
															";",
														class: "share_btn_icon"
													},
													apivm.h("a-iconfont", {
														name: item.src,
														color: item.color || "#333",
														size:
															G.appFontSize + (isParameters(item.size) ? Number(item.size) : 8)
													})
											  ),
										apivm.h(
											"text",
											{style: loadConfiguration(-2) + "color:#333;margin-top:8px;"},
											item.value
										)
									)
								);
							})
						),
						apivm.h(
							"view",
							null,
							!this.props.dataMore.dotCancel &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										style:
											"padding:18px;justify-content:center;border-top:10px solid rgba(0,0,0,0.03);"
									},
									apivm.h(
										"text",
										{style: loadConfiguration() + ";color:#333;text-align: center;"},
										"取消"
									)
								)
						),
						apivm.h("view", {style: "padding-bottom:" + safeArea().bottom + "px;"})
					)
				]
			);
		};

		return SharePoster;
	})(Component);
	SharePoster.css = {
		".poster_base": {width: "320px"},
		".canvas": {position: "absolute", left: "0", right: "0", zIndex: "11"},
		".poster_box": {
			background: "#FFFFFF",
			borderRadius: "10px",
			overflow: "hidden"
		},
		".share_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			flexShrink: "0",
			maxHeight: "80%"
		},
		".share_btn_box": {padding: "10px 0", flexDirection: "row", flexWrap: "wrap"},
		".share_btn_item": {
			padding: "10px 5px",
			width: "25%",
			alignItems: "center",
			justifyContent: "center"
		},
		".share_btn_icon": {
			borderRadius: "50%",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("share-poster", SharePoster);

	var SharePosterStationCard = /*@__PURE__*/ (function(Component) {
		function SharePosterStationCard(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				canvasStyle: ""
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show && platform() != "app") {
							setTimeout(function() {
								this$1.baseInit();
							}, 0);
						}
					}
				}
			};
		}

		if (Component) SharePosterStationCard.__proto__ = Component;
		SharePosterStationCard.prototype = Object.create(
			Component && Component.prototype
		);
		SharePosterStationCard.prototype.constructor = SharePosterStationCard;
		SharePosterStationCard.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			var dmd = this.props.dataMore.data;
			var createPic = function(canvas) {
				showProgress();
				var ctx;
				var bRect = null;

				dm.canvasImg = 0;
				dm.canvasErr = "";

				//设置画布大小背景圆角
				getBoundingClientRect("sharePoster", function(ret) {
					bRect = ret;
					this$1.data.canvasStyle =
						"width:" + bRect.width + "px;height:" + bRect.height + "px;";
					var dpr =
						platform() == "mp"
							? wx.getSystemInfoSync().pixelRatio
							: window.devicePixelRatio;
					canvas.width = bRect.width * dpr;
					canvas.height = bRect.height * dpr;
					ctx = canvas.getContext("2d");
					ctx.scale(dpr, dpr);
					canvasRoundedRect(
						{x: 0, y: 0, width: bRect.width, height: bRect.height},
						10,
						ctx
					);
					ctx.fillStyle = "white";
					ctx.fill();
					ctx.restore();
					var loadOtherElement = function() {
						//标题
						canvasShowText(
							{
								id: "posterTitle",
								text: dmd.title,
								weight: "600",
								size: 1,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);

						//大图片
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterImg",
								src: showImg(dmd.url),
								round: 5,
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
									//标签
									if (dmd.excellentStationType.value) {
										getBoundingClientRect("posterLabelBox", function(ret) {
											ctx.save();
											canvasRoundedRect(
												{
													x: ret.left - bRect.left,
													y: ret.top - bRect.top,
													width: ret.width,
													height: ret.height
												},
												[5, 0, 5, 0],
												ctx
											);
											var linearGradient = ctx.createLinearGradient(
												ret.left - bRect.left,
												ret.top - bRect.top,
												ret.width,
												ret.height
											);
											linearGradient.addColorStop(0, "#F6921C");
											linearGradient.addColorStop(1, "#F6631C");
											ctx.fillStyle = linearGradient;
											ctx.fill();
											ctx.restore();
										});
										canvasShowText(
											{
												id: "posterLabel",
												text: "" + dmd.excellentStationType.name,
												color: "#FFF",
												size: -4,
												spacing: 2,
												bRect: bRect
											},
											ctx
										);
									}
								}
							},
							ctx,
							canvas
						);

						//文字
						canvasShowText(
							{
								id: "posterText1",
								text: "联系人:" + dmd.userName,
								color: "#444444",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText2",
								text: "电话:" + dmd.userPhone,
								color: "#444444",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText3",
								text: "地址:" + dmd.address,
								color: "#666666",
								size: -4,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);

						//二维码
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterQr",
								src: showImg(tomcatAddress() + "utils/qr?text=" + dmd.shareUrl),
								round: 0,
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
								}
							},
							ctx,
							canvas
						);
						canvasImgLoad(dm);
					};
					//有水印
					if (G.watermark) {
						canvasShowImg(
							{
								id: "sharePoster",
								src: G.watermark,
								round: 10,
								bRect: bRect,
								callback: function(ret) {
									loadOtherElement();
								}
							},
							ctx,
							canvas
						);
					} else {
						loadOtherElement();
					}
				});
			};
			var _id = document.querySelector("#sharePosterCanvas");
			if (!_id) {
				return;
			}
			switch (platform()) {
				case "web":
					createPic(_id);
					break;
				case "mp":
					_id.$$getNodesRef().then(function(nodesRef) {
						nodesRef
							.node(function(res) {
								createPic(res.node);
							})
							.exec();
					});
					break;
			}
		};
		SharePosterStationCard.prototype.render = function() {
			return apivm.h(
				"share-poster",
				{
					s: this.monitor,
					dataMore: this.props.dataMore,
					canvasStyle: this.data.canvasStyle
				},
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{id: "sharePoster", class: "poster_box"},
						apivm.h("view", {
							class: "watermark_box",
							style:
								"display:" +
								(platform() != "app" ? "flex" : "none") +
								";z-index:2;background:#FFF;"
						}),
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{style: "padding: 15px 20px;"},
							apivm.h(
								"text",
								{
									id: "posterTitle",
									style:
										"color: #333;" +
										loadConfiguration(1) +
										";line-height:" +
										(G.appFontSize + 6) +
										"px;font-weight: 600;"
								},
								this.props.dataMore.data.title
							),
							apivm.h(
								"view",
								{class: "card_imgbox"},
								apivm.h("image", {
									id: "posterImg",
									class: "xy_100",
									src: showImg(this.props.dataMore.data),
									mode: "aspectFill",
									thumbnail: "false"
								}),
								this.props.dataMore.data.excellentStationType.value &&
									apivm.h(
										"view",
										{
											class: "flex_row",
											style: "position:absolute;z-index:1;left:0;top:0;right:0;"
										},
										apivm.h(
											"view",
											{
												id: "posterLabelBox",
												class: "demonstration",
												style: "background:linear-gradient(to right, #F6921C, #F6631C);"
											},
											apivm.h(
												"text",
												{
													id: "posterLabel",
													class: "text_one",
													style: "color: #FFF;" + loadConfiguration(-4)
												},
												this.props.dataMore.data.excellentStationType.name
											)
										)
									)
							),
							apivm.h(
								"view",
								{class: "flex_row"},
								apivm.h(
									"view",
									{class: "flex_w"},
									apivm.h(
										"text",
										{id: "posterText1", style: "color: #444;" + loadConfiguration(-2)},
										"联系人:",
										this.props.dataMore.data.userName
									),
									apivm.h(
										"text",
										{
											id: "posterText2",
											style: "color: #444;" + loadConfiguration(-2) + "margin-top:2px;"
										},
										"电话:",
										this.props.dataMore.data.userPhone
									),
									apivm.h(
										"text",
										{
											id: "posterText3",
											style: "color: #666;" + loadConfiguration(-4) + "margin-top:4px;"
										},
										"地址:",
										this.props.dataMore.data.address
									)
								),
								apivm.h(
									"view",
									{style: "width:62px;height:62px;"},
									apivm.h("image", {
										id: "posterQr",
										class: "xy_100",
										src: showImg(
											tomcatAddress() +
												"utils/qr?text=" +
												this.props.dataMore.data.shareUrl
										),
										mode: "aspectFill",
										thumbnail: "false"
									})
								)
							)
						)
					)
				]
			);
		};

		return SharePosterStationCard;
	})(Component);
	SharePosterStationCard.css = {
		".card_imgbox": {
			margin: "15px 0",
			width: "100%",
			height: "146.66px",
			marginRight: "13px",
			borderRadius: "5px",
			overflow: "hidden"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".demonstration": {borderRadius: "5px 0px 5px 0px", padding: "2px 10px"}
	};
	apivm.define("share-poster-station-card", SharePosterStationCard);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatarMember = /*@__PURE__*/ (function(Component) {
		function ZAvatarMember(props) {
			Component.call(this, props);
			this.data = {
				esrc: appUrl() + "img/default_user_head.jpg",
				asrc: null,
				bsrc: ""
			};
			this.compute = {
				monitor: function() {
					var url = isObject(this.props.src) ? this.props.src.url : this.props.src;
					if (this.data.asrc != url || !this.data.asrc) {
						this.data.asrc = url || this.data.esrc;
						this.data.bsrc = "";
					}
				}
			};
		}

		if (Component) ZAvatarMember.__proto__ = Component;
		ZAvatarMember.prototype = Object.create(Component && Component.prototype);
		ZAvatarMember.prototype.constructor = ZAvatarMember;
		ZAvatarMember.prototype.error = function() {
			this.data.bsrc = this.data.esrc;
		};
		ZAvatarMember.prototype.render = function() {
			return apivm.h(
				"view",
				{s: this.monitor, class: "xy_100"},
				apivm.h("z-image", {
					style: "border-radius: 2px;",
					scrollBox: this.props.scrollBox,
					mode: "scaleToFill",
					thumbnail: "false",
					src: showImg(this.data.bsrc || this.data.asrc, "117x157-compress-"),
					onError: this.error
				})
			);
		};

		return ZAvatarMember;
	})(Component);
	apivm.define("z-avatar-member", ZAvatarMember);

	var SharePosterStationMember = /*@__PURE__*/ (function(Component) {
		function SharePosterStationMember(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				canvasStyle: ""
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show && platform() != "app") {
							setTimeout(function() {
								this$1.baseInit();
							}, 0);
						}
					}
				}
			};
		}

		if (Component) SharePosterStationMember.__proto__ = Component;
		SharePosterStationMember.prototype = Object.create(
			Component && Component.prototype
		);
		SharePosterStationMember.prototype.constructor = SharePosterStationMember;
		SharePosterStationMember.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			var dmd = this.props.dataMore.data;
			var createPic = function(canvas) {
				showProgress();
				var ctx;
				var bRect = null;

				dm.canvasImg = 0;
				dm.canvasErr = "";

				//设置画布大小背景圆角
				getBoundingClientRect("sharePoster", function(ret) {
					bRect = ret;
					this$1.data.canvasStyle =
						"width:" + bRect.width + "px;height:" + bRect.height + "px;";
					var dpr =
						platform() == "mp"
							? wx.getSystemInfoSync().pixelRatio
							: window.devicePixelRatio;
					canvas.width = bRect.width * dpr;
					canvas.height = bRect.height * dpr;
					ctx = canvas.getContext("2d");
					ctx.scale(dpr, dpr);
					canvasRoundedRect(
						{x: 0, y: 0, width: bRect.width, height: bRect.height},
						10,
						ctx
					);
					ctx.fillStyle = "white";
					ctx.closePath();
					ctx.fill();
					ctx.restore();
					var loadOtherElement = function() {
						// 头像
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterAvatar",
								src: showImg(dmd.url) || appUrl() + "img/default_user_head.jpg",
								type: "cover",
								round: 2,
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
								}
							},
							ctx,
							canvas
						);

						// 线条背景
						canvasShowBG(
							{id: "posterLine1", round: 1, bg: G.appTheme, bRect: bRect},
							ctx
						);
						canvasShowBG(
							{
								id: "posterTextBox1",
								round: 4,
								bg: "#FFF",
								shadow: "rgba(24,64,118,0.08)",
								bRect: bRect
							},
							ctx
						);
						canvasShowBG(
							{
								id: "posterTextBox2",
								round: 4,
								bg: "#FFF",
								shadow: "rgba(24,64,118,0.08)",
								bRect: bRect
							},
							ctx
						);
						canvasShowBG(
							{
								id: "posterTextBox3",
								round: 4,
								bg: "#FFF",
								shadow: "rgba(24,64,118,0.08)",
								bRect: bRect
							},
							ctx
						);

						// 文字
						canvasShowText(
							{
								id: "posterText1",
								text: dmd.name,
								weight: "600",
								color: "#333",
								size: 1,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText2",
								text: "民族：",
								color: "#333",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText3",
								text: "" + dmd.nation,
								color: "#333",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText4",
								text: "单位职务：",
								color: "#333",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText5",
								text: "" + dmd.position,
								color: "#333",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText6",
								text: "入驻" + (G.sysSign == "rd" ? "站点" : "工作室") + "：",
								color: "#333",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText7",
								text: "" + dmd.stationNames,
								color: "#333",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText8",
								text: "" + (dmd.activityCount || 0),
								weight: "800",
								color: G.appTheme,
								size: 6,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText9",
								text: "活动参与",
								color: "#333",
								size: 0,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText10",
								text: "" + (dmd.letterCount || 0),
								weight: "800",
								color: G.appTheme,
								size: 6,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText11",
								text: "受理民意",
								color: "#333",
								size: 0,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText12",
								text: "" + (dmd.dutyCount || 0),
								weight: "800",
								color: G.appTheme,
								size: 6,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText13",
								text: (G.sysSign == "rd" ? "代表" : "委员") + "值班",
								color: "#333",
								size: 0,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText14",
								text: "扫一扫二维码，给我留言",
								color: "#333",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);

						//二维码
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterQr",
								src: showImg(tomcatAddress() + "utils/qr?text=" + dmd.shareUrl),
								round: 0,
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
								}
							},
							ctx,
							canvas
						);
						canvasImgLoad(dm);
					};
					//有水印
					if (G.watermark) {
						canvasShowImg(
							{
								id: "sharePoster",
								src: G.watermark,
								round: 10,
								bRect: bRect,
								callback: function(ret) {
									loadOtherElement();
								}
							},
							ctx,
							canvas
						);
					} else {
						loadOtherElement();
					}
				});
			};
			var _id = document.querySelector("#sharePosterCanvas");
			if (!_id) {
				return;
			}
			switch (platform()) {
				case "web":
					createPic(_id);
					break;
				case "mp":
					_id.$$getNodesRef().then(function(nodesRef) {
						nodesRef
							.node(function(res) {
								createPic(res.node);
							})
							.exec();
					});
					break;
			}
		};
		SharePosterStationMember.prototype.render = function() {
			return apivm.h(
				"share-poster",
				{
					s: this.monitor,
					dataMore: this.props.dataMore,
					canvasStyle: this.data.canvasStyle
				},
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{id: "sharePoster", class: "poster_box"},
						apivm.h("view", {
							class: "watermark_box",
							style:
								"display:" +
								(platform() != "app" ? "flex" : "none") +
								";z-index:2;background:#FFF;"
						}),
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{style: "padding: 15px 20px;"},
							apivm.h(
								"view",
								{style: "align-items: flex-start;", class: "flex_row"},
								apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{id: "posterAvatar", style: "width:74px;height:92px;"},
										apivm.h("z-avatar-member", {src: showImg(this.props.dataMore.data)})
									),
									apivm.h("view", {
										id: "posterLine1",
										style:
											"width:20px;height:2px;background:" +
											G.appTheme +
											";border-radius:5px;margin-top:10px;"
									})
								),
								apivm.h(
									"view",
									{class: "flex_w", style: "margin-left:15px;"},
									apivm.h(
										"view",
										{class: "flex_row"},
										apivm.h(
											"text",
											{
												id: "posterText1",
												style:
													"color: #333;" + loadConfiguration(1) + "flex:1;font-weight: 600;"
											},
											this.props.dataMore.data.name
										)
									),
									apivm.h(
										"view",
										{
											class: "flex_row",
											style: "align-items: flex-start;margin-top:10px;"
										},
										apivm.h(
											"text",
											{id: "posterText2", style: "color: #333;" + loadConfiguration(-2)},
											"民族："
										),
										apivm.h(
											"text",
											{
												id: "posterText3",
												style: "flex:1;color: #333;" + loadConfiguration(-2)
											},
											this.props.dataMore.data.nation
										)
									),
									apivm.h(
										"view",
										{class: "flex_row", style: "align-items: flex-start;margin-top:6px;"},
										apivm.h(
											"text",
											{id: "posterText4", style: "color: #333;" + loadConfiguration(-2)},
											"单位职务："
										),
										apivm.h(
											"text",
											{
												id: "posterText5",
												style: "flex:1;color: #333;" + loadConfiguration(-2)
											},
											this.props.dataMore.data.position
										)
									),
									apivm.h(
										"view",
										{class: "flex_row", style: "align-items: flex-start;margin-top:6px;"},
										apivm.h(
											"text",
											{id: "posterText6", style: "color: #333;" + loadConfiguration(-2)},
											"入驻",
											G.sysSign == "rd" ? "站点" : "工作室",
											"："
										),
										apivm.h(
											"text",
											{
												id: "posterText7",
												style: "flex:1;color: #333;" + loadConfiguration(-2)
											},
											this.props.dataMore.data.stationNames
										)
									)
								)
							),
							apivm.h(
								"view",
								{style: "margin-top:20px;", class: "flex_row"},
								apivm.h(
									"view",
									{
										id: "posterTextBox1",
										class: "lam_box xy_center",
										style: "width:33.33%;margin:0 4px;padding:15px 5px;"
									},
									apivm.h(
										"text",
										{
											id: "posterText8",
											style:
												"color: " +
												G.appTheme +
												";" +
												loadConfiguration(6) +
												"font-weight: 800;"
										},
										this.props.dataMore.data.activityCount || 0
									),
									apivm.h(
										"text",
										{
											id: "posterText9",
											style: "color: #333;" + loadConfiguration() + "margin-top:3px;"
										},
										"活动参与"
									)
								),
								apivm.h(
									"view",
									{
										id: "posterTextBox2",
										class: "lam_box xy_center",
										style: "width:33.33%;margin:0 4px;padding:15px 5px;"
									},
									apivm.h(
										"text",
										{
											id: "posterText10",
											style:
												"color: " +
												G.appTheme +
												";" +
												loadConfiguration(6) +
												"font-weight: 800;"
										},
										this.props.dataMore.data.letterCount || 0
									),
									apivm.h(
										"text",
										{
											id: "posterText11",
											style: "color: #333;" + loadConfiguration() + "margin-top:3px;"
										},
										"受理民意"
									)
								),
								apivm.h(
									"view",
									{
										id: "posterTextBox3",
										class: "lam_box xy_center",
										style: "width:33.33%;margin:0 4px;padding:15px 5px;"
									},
									apivm.h(
										"text",
										{
											id: "posterText12",
											style:
												"color: " +
												G.appTheme +
												";" +
												loadConfiguration(6) +
												"font-weight: 800;"
										},
										this.props.dataMore.data.dutyCount || 0
									),
									apivm.h(
										"text",
										{
											id: "posterText13",
											style: "color: #333;" + loadConfiguration() + "margin-top:3px;"
										},
										G.sysSign == "rd" ? "代表" : "委员",
										"值班"
									)
								)
							),
							apivm.h(
								"view",
								{style: "margin-top:20px;", class: "xy_center"},
								apivm.h(
									"view",
									{style: "width:114px;height:114px;"},
									apivm.h("image", {
										id: "posterQr",
										class: "xy_100",
										src: showImg(
											tomcatAddress() +
												"utils/qr?text=" +
												this.props.dataMore.data.shareUrl
										),
										mode: "aspectFill",
										thumbnail: "false"
									})
								)
							),
							apivm.h(
								"view",
								{class: "xy_center"},
								apivm.h(
									"text",
									{
										id: "posterText14",
										style: "color: #333;" + loadConfiguration(-2) + "margin-top:15px;"
									},
									"扫一扫二维码，给我留言"
								)
							)
						)
					)
				]
			);
		};

		return SharePosterStationMember;
	})(Component);
	SharePosterStationMember.css = {
		".lam_box": {
			background: "#FFFFFF",
			boxShadow: "0px 0px 4px 1px rgba(24,64,118,0.08)",
			borderRadius: "4px"
		}
	};
	apivm.define("share-poster-station-member", SharePosterStationMember);

	var PreviewerImg = /*@__PURE__*/ (function(Component) {
		function PreviewerImg(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				frameName: "previewerImg"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.props.dataMore.watermark = G.watermark;
							this.baseInit();
						} else {
							if (platform() == "app" && this.UIPhotoViewer) {
								this.UIPhotoViewer.close();
							}
						}
					}
					if (this.data.show) {
						if (this.nowParam != JSON.stringify(this.props.dataMore)) {
							this.nowParam = JSON.stringify(this.props.dataMore);
							this.sendMessage();
						}
					}
				}
			};
		}

		if (Component) PreviewerImg.__proto__ = Component;
		PreviewerImg.prototype = Object.create(Component && Component.prototype);
		PreviewerImg.prototype.constructor = PreviewerImg;
		PreviewerImg.prototype.baseInit = function() {
			var this$1 = this;

			var data = this.props.dataMore;
			switch (platform()) {
				case "app":
					if (G.watermark) {
						//有水印使用web
						addEventListener(this.data.frameName + "_msg", function() {
							this$1.closePage();
						});
						api.setFrameClient({frameName: this.data.frameName}, function(ret, err) {
							if (ret.state == 2) {
								this$1.isLoading = true;
								setTimeout(function() {
									this$1.sendMessage();
								}, 400);
							}
						});
						return;
					}
					this.UIPhotoViewer = api.require("UIPhotoViewer");
					this.UIPhotoViewer.open(
						{
							images: data.imgs,
							activeIndex: data.index,
							gestureClose: true,
							bgColor: "#000"
						},
						function(ret, err) {
							switch (ret.eventType) {
								case "click":
									this$1.closePage();
									break;
								case "gestureColse":
									this$1.UIPhotoViewer = null;
									this$1.closePage();
									break;
								case "longPress":
									var nowImg = data.imgs[ret.index];
									api.actionSheet(
										{
											buttons: ["保存到相册"],
											cancelTitle: "取消"
										},
										function(ret) {
											switch (ret.buttonIndex) {
												case 1:
													api.saveMediaToAlbum(
														{
															path: nowImg
														},
														function(ret) {
															toast(ret && ret.status ? "已保存到手机相册" : "保存失败");
														}
													);
													break;
											}
										}
									);
									break;
							}
						}
					);
					break;
				case "mp":
					wx.previewImage({
						urls: data.imgs,
						current: data.imgs[data.index],
						complete: function() {
							this$1.closePage();
						}
					});

					break;
				case "web":
					window.addEventListener("message", function(event) {
						this$1.closePage();
					});
					document
						.getElementById(this.data.frameName)
						.addEventListener("load", function() {
							this$1.isLoading = true;
							this$1.sendMessage();
						});
					break;
			}
		};
		PreviewerImg.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		PreviewerImg.prototype.sendMessage = function() {
			if (this.isLoading && this.props.dataMore.show) {
				if (platform() == "web") {
					if (!document.getElementById(this.data.frameName)) {
						return;
					}
					var targetWindow = document.getElementById(this.data.frameName)
						.contentWindow;
					targetWindow.postMessage(this.nowParam, "*");
				} else if (platform() == "app") {
					sendEvent(this.data.frameName + "_open", this.props.dataMore);
				}
			}
		};
		PreviewerImg.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0);"
				},
				this.props.dataMore.show &&
					(platform() == "web" || G.watermark) &&
					apivm.h("frame", {
						id: this.data.frameName,
						class: "xy_100",
						name: this.data.frameName,
						url: shareAddress() + "html/previewerImg.html",
						pageParam: {name: this.data.frameName},
						useWKWebView: true,
						scaleEnabled: true,
						allowEdit: true
					})
			);
		};

		return PreviewerImg;
	})(Component);
	apivm.define("previewer-img", PreviewerImg);

	var ItemPicture = /*@__PURE__*/ (function(Component) {
		function ItemPicture(props) {
			Component.call(this, props);
		}

		if (Component) ItemPicture.__proto__ = Component;
		ItemPicture.prototype = Object.create(Component && Component.prototype);
		ItemPicture.prototype.constructor = ItemPicture;
		ItemPicture.prototype.addPic = function() {
			var this$1 = this;

			var keyItem = this.props.item;
			var max = -1;
			if (isNumber(keyItem.max) && keyItem.max > 0) {
				max = keyItem.max - keyItem.value.length;
			}
			if (max <= 0) {
				toast("最多选择" + keyItem.max + "张图片");
				return;
			}
			actionSheet(
				{
					title: "请选择图片来源",
					buttons: ["相机", "相册"]
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					getPicture(
						{
							sourceType: _index == 1 ? "camera" : "photos",
							destinationType: "url",
							max: max
						},
						function(ret, err) {
							var dataUrl = ret ? ret.data || ret.base64Data : "";
							if (dataUrl) {
								var _item = {showToast: true, url: dataUrl};
								uploadFile(_item, function(ret) {
									if (ret.otherInfo) {
										ret.otherInfo.url = appUrl() + "image/" + ret.otherInfo.newFileName;
										keyItem.value.push(ret.otherInfo);
										this$1.fire("input", this$1.props.item);
									} else {
										toast(ret.error);
									}
								});
							}
						}
					);
				}
			);
		};
		ItemPicture.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			openWin_imgPreviewer({
				index: _index,
				imgs: this.props.item.value.map(function(obj) {
					return obj.url;
				})
			});
		};
		ItemPicture.prototype.delFile = function(e, _item, _index) {
			stopBubble(e);
			delItemForKey(_item, this.props.item.value, "id");
			this.fire("input", this.props.item);
			if (!_item.dotSystemDel) {
				ajax(
					{u: appUrl() + "file/clear"},
					"clear" + _item.id,
					function(ret, err) {},
					"删除附件",
					"post",
					{
						body: JSON.stringify({fileIds: [_item.id]})
					}
				);
			}
		};
		ItemPicture.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding: 13px 0 13px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:13px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						this.props.item.title &&
							apivm.h(
								"text",
								{style: loadConfiguration() + "color:#333;"},
								this.props.item.title,
								this.props.item.max
									? "(" + this.props.item.value.length + "/" + this.props.item.max + ")"
									: ""
							)
					),
					apivm.h(
						"view",
						{style: "flex-direction:row;flex-wrap: wrap;"},
						(Array.isArray(this.props.item.value)
							? this.props.item.value
							: Object.values(this.props.item.value)
						).map(function(nItem, nIndex) {
							return apivm.h(
								"view",
								{
									class: "aItem_pic",
									onClick: function(e) {
										return this$1.openImages(e, nItem, nIndex);
									}
								},
								apivm.h("image", {
									style:
										"width:100%;height:" +
										((G.pageWidth - 16 - (this$1.props.w || 0)) * 0.25 - 16) +
										"px;border:0px;",
									src: showImg(nItem),
									mode: "aspectFill"
								}),
								apivm.h(
									"view",
									{
										style:
											"position:absolute;top: 6px; right: 6px;z-index: 1;width:auto;height:auto;border:0px;",
										onClick: function(e) {
											return this$1.delFile(e, nItem, nIndex);
										}
									},
									apivm.h("a-iconfont", {
										name: "qingkong",
										color: "rgba(0,0,0,0.65)",
										size: G.appFontSize + 3
									})
								)
							);
						}),
						(!this.props.item.max ||
							this.props.item.max > this.props.item.value.length) &&
							apivm.h(
								"view",
								{class: "aItem_pic"},
								apivm.h(
									"view",
									{
										onClick: this.addPic,
										class: "xy_center",
										style:
											"position:relative;top:0;right:0;z-index:1;width:100%;height:" +
											((G.pageWidth - 16 - (this.props.w || 0)) * 0.25 - 16) +
											"px;border:1px solid #ddd;"
									},
									apivm.h("a-iconfont", {
										name: "xinzeng",
										color: "#ddd",
										size: G.appFontSize + 8
									})
								)
							)
					)
				)
			);
		};

		return ItemPicture;
	})(Component);
	ItemPicture.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		},
		".aItem_pic": {
			padding: "16px 16px 0 0",
			width: "25%",
			height: "auto",
			boxSizing: "border-box"
		}
	};
	apivm.define("item-picture", ItemPicture);

	var ZTextarea = /*@__PURE__*/ (function(Component) {
		function ZTextarea(props) {
			Component.call(this, props);
			this.data = {
				textareaId: this.props.id || "z_textarea" + getNum()
			};
		}

		if (Component) ZTextarea.__proto__ = Component;
		ZTextarea.prototype = Object.create(Component && Component.prototype);
		ZTextarea.prototype.constructor = ZTextarea;
		ZTextarea.prototype.installed = function() {
			var this$1 = this;

			this.props.dataMore.textareaId = this.data.textareaId;
			if (this.props.dataMore.autoFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.textareaId).focus();
				}, 500);
			}
		};
		ZTextarea.prototype.input = function(e) {
			var value = e.detail.value;
			if (this.props.dataMore.expression) {
				value = value.replace(new RegExp(this.props.dataMore.expression, "g"), "");
			}
			this.props.dataMore.value = value;
			this.fire("input", e.detail);
		};
		ZTextarea.prototype.blur = function(e) {
			this.fire("blur", e.detail);
		};
		ZTextarea.prototype.focus = function(e) {
			document.getElementById(this.data.textareaId).focus();
			this.fire("focus", e.detail);
		};
		ZTextarea.prototype.render = function() {
			return apivm.h("textarea", {
				id: this.data.textareaId,
				style:
					"" +
					loadConfiguration() +
					(api.systemType == "android" ? "min-" : "") +
					"height: " +
					(this.props.dataMore.height || "250") +
					"px;" +
					(this.props.style || ""),
				class: "z_textarea " + (this.props.class || ""),
				placeholder:
					this.props.dataMore.replyPlaceholder ||
					this.props.dataMore.placeholder ||
					this.props.dataMore.hint ||
					"请输入" + (this.props.dataMore.title || ""),
				"placeholder-style": "color:#ccc;",
				"auto-height": api.systemType == "android",
				value: this.props.dataMore.value,
				onInput: this.input,
				onBlur: this.blur,
				onFocus: this.focus,
				maxlength: this.props.dataMore.maxlength || this.props.dataMore.max
			});
		};

		return ZTextarea;
	})(Component);
	ZTextarea.css = {
		".z_textarea": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			padding: "0",
			width: "100%",
			fontFamily: "none"
		},
		".z_textarea::placeholder": {color: "#ccc"}
	};
	apivm.define("z-textarea", ZTextarea);

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				controls: true
			};
			this.compute = {
				getSrc: function() {
					var src = this.props.src;
					if (src.indexOf("http") != 0) {
						src = appUrl() + "file/preview/" + src;
					}
					return src;
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.firstPause = function() {
			var this$1 = this;

			if (!this.isPause) {
				this.isPause = true;
				if (platform() == "app" && !this.props.poster) {
					this.data.controls = false;
					setTimeout(function() {
						if (!this$1.props.autoplay) {
							document.getElementById(dealVideoId(this$1.props.src)) &&
								document.getElementById(dealVideoId(this$1.props.src)).pause();
						}
						this$1.data.controls = true;
					}, 50);
				} else {
					this.data.controls = true;
				}
			}
		};
		ZVideo.prototype.loadedmetadata = function(e) {
			if (api.systemType != "ios") {
				this.isPause = false;
				this.firstPause();
			}
		};
		ZVideo.prototype.play = function() {
			videoPlayPush(dealVideoId(this.props.src));
		};
		ZVideo.prototype.pause = function() {
			videoPlayRemove(dealVideoId(this.props.src));
		};
		ZVideo.prototype.render = function() {
			return apivm.h("video", {
				id: dealVideoId(this.props.src),
				style: "width:100%;height:" + G.pageWidth * 0.56 + "px;",
				controls: this.data.controls,
				autoplay:
					(platform() == "app" && !this.props.poster) || this.props.autoplay,
				onLoadedmetadata: this.loadedmetadata,
				onWaiting: this.firstPause,
				onPlay: this.play,
				onPause: this.pause,
				onEnded: this.pause,
				src: this.getSrc,
				poster: showImg(this.props.poster)
			});
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false, //是否展开了
				appDetailsStyle: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.expandShow = function(e) {
			stopBubble(e);
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (isObject(expText) || isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			this.data.appDetailsStyle = getPrefs("appDetailsStyle");
			if (this.data.appDetailsStyle == "edit") {
				this.data.showText = expText;
				return;
			}

			var notTagText = removeTag(expText);
			this.data.hasExpand =
				isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log("解析一："+expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			// console.log("解析二："+expText);
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nStyle = styleMatch ? styleMatch[1] : "";
				if (nlabel == "img") {
					nStyle = nStyle
						.replace("width: auto;", "width:100%;")
						.replace("width:auto;", "width:100%;");
				}
				var nowItem = {label: nlabel, index: index, text: item, style: nStyle};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: nStyle
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: nStyle
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var widthMatch = item.text.match(/width="([^"]*)"/);
						var tdWidth = widthMatch ? widthMatch[1] + "px" : null;
						if (!tdWidth) {
							tdWidth = this$1.getStyle(item.style, "width") || "150px";
						}
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					if (addItem.label == "img" && !addItem.src);
					else {
						showTexts.push(addItem);
					}
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			openWin_imgPreviewer({
				index: getItemForKey(_item.index, imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			stopBubble(e);
			if (!_item.href) {
				return;
			}
			if (platform() == "web") {
				window.open(_item.href);
				return;
			}
			openWin_url({url: _item.href});
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp('[;"]s*' + _item + "s*:s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle != "edit" &&
						this.data.listData.map(function(item, index) {
							return [
								apivm.h(
									"view",
									null,
									item.label == "text" &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(this$1.props.size || 0) +
													"line-height:" +
													(G.appFontSize + (this$1.props.size || 0)) * 1.8 +
													"px;" +
													(this$1.props.detail &&
													((platform() == "mp" && item.text.indexOf("　") != 0) ||
														(platform() == "web" &&
															item.text.indexOf(" ") != 0 &&
															item.text.indexOf("　") != 0))
														? "text-indent:2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail &&
												item.text.indexOf(" ") != 0 &&
												item.text.indexOf("　") != 0
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item.text
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "text_a" &&
										apivm.h(
											"view",
											{
												onClick: function(e) {
													return this$1.openHrefs(e, item, index);
												}
											},
											apivm.h(
												"text",
												{
													style: loadConfiguration(this$1.props.size || 0) + "color: blue;",
													class: "richText"
												},
												item.text
											)
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "br" &&
										apivm.h("view", {
											style:
												"height:" + (G.appFontSize - 6 + (this$1.props.size || 0)) + "px;"
										})
								),
								apivm.h(
									"view",
									null,
									item.label == "img" &&
										apivm.h(
											"view",
											{
												class: "richImgBox",
												onClick: function(e) {
													return this$1.openImages(e, item, index);
												}
											},
											apivm.h("image", {
												class: "richImg",
												style: item.style || "",
												mode: "widthFix",
												thumbnail: "false",
												src: item.src
											})
										)
								),
								apivm.h(
									"view",
									null,
									(item.label == "video" || item.label == "source") &&
										apivm.h(
											"view",
											{class: "richImgBox"},
											item.src && apivm.h("z-video", {src: item.src})
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "table" &&
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchEnd: this$1.nTouchmove
												},
												(item.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(this$1.props.size || 0) +
																			"text-align: center;",
																		class: "richText"
																	},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								)
							];
						})
				),
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle == "edit" &&
						apivm.h("rich-text", {nodes: this.data.showText})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: this.expandShow
							},
							apivm.h(
								"text",
								{style: loadConfiguration((this.props.size || 0) - 2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {
			alignItems: "center",
			justifyContent: "center",
			margin: "10px 0"
		},
		".richImg": {maxWidth: "100% !important"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	var ItemTextarea = /*@__PURE__*/ (function(Component) {
		function ItemTextarea(props) {
			Component.call(this, props);
		}

		if (Component) ItemTextarea.__proto__ = Component;
		ItemTextarea.prototype = Object.create(Component && Component.prototype);
		ItemTextarea.prototype.constructor = ItemTextarea;
		ItemTextarea.prototype.input = function(e) {
			this.fire("input", this.props.item);
		};
		ItemTextarea.prototype.render = function() {
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding: 13px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:13px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"text",
							{style: loadConfiguration(1) + "color:#333;"},
							this.props.item.title
						),
						this.props.item.titleHint &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-2) +
										"flex:1;margin:2px 0 0 10px;color:#999;text-align: right;"
								},
								this.props.item.titleHint
							)
					),
					apivm.h(
						"view",
						{style: "padding-top:7px;"},
						apivm.h(
							"view",
							null,
							this.props.dataMore.readonly
								? apivm.h("z-rich-text", {detail: true, nodes: this.props.item.value})
								: apivm.h("z-textarea", {
										dataMore: this.props.item,
										onInput: this.input
								  })
						),
						apivm.h(
							"view",
							{style: "flex-direction:row-reverse;margin:5px 0 2px;"},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color: #999999;"},
								"已有字数",
								this.props.item.value.length
							)
						)
					)
				)
			);
		};

		return ItemTextarea;
	})(Component);
	ItemTextarea.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		}
	};
	apivm.define("item-textarea", ItemTextarea);

	var ItemInput = /*@__PURE__*/ (function(Component) {
		function ItemInput(props) {
			Component.call(this, props);
		}

		if (Component) ItemInput.__proto__ = Component;
		ItemInput.prototype = Object.create(Component && Component.prototype);
		ItemInput.prototype.constructor = ItemInput;
		ItemInput.prototype.input = function(e) {
			this.fire("input", this.props.item);
		};
		ItemInput.prototype.render = function() {
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding:13px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:" +
								(this.props.item.exhibit ? 13 : 18) +
								"px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"text",
							{
								class: "" + (this.props.item.twa ? "text_default" : "text_one"),
								style:
									loadConfiguration() +
									"color:#333;width:" +
									(this.props.item.twa
										? "auto"
										: G.appFontSize * 4 + (api.systemType == "ios" ? 6 : 4) + "px") +
									";"
							},
							this.props.item.title
						),
						apivm.h(
							"view",
							{class: "flex_w"},
							!this.props.item.exhibit &&
								apivm.h(
									"view",
									{style: "margin-left:10px;"},
									apivm.h("z-input", {
										dataMore: this.props.item,
										confirmType: "done",
										bg: "rgba(0,0,0,0)",
										dotIcon: true,
										style: "padding:0;height:31px;",
										onInput: this.input
									})
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.item.exhibit == 1 &&
							apivm.h(
								"view",
								{style: "margin-top:10px;"},
								apivm.h("z-input", {
									dataMore: this.props.item,
									confirmType: "done",
									bg: "rgba(0,0,0,0)",
									dotIcon: true,
									style: "padding:0;",
									onInput: this.input
								})
							)
					)
				)
			);
		};

		return ItemInput;
	})(Component);
	ItemInput.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".text_default": {
			textOverflow: "clip",
			whiteSpace: "normal",
			overflow: "visible",
			display: "inline"
		}
	};
	apivm.define("item-input", ItemInput);

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				size: G.appFontSize + (this.props.size || 0),
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: this.props.type == 3
						? "fangxingxuanzhongfill"
						: "yuanxingxuanzhongfill"
					: this.props.type == 3
					? "fangxingweixuanzhong"
					: "danxuan_weixuanzhong",
				color: this.props.checked ? this.props.color || G.appTheme : "#999"
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var SelectItem = /*@__PURE__*/ (function(Component) {
		function SelectItem(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				emptyBox: {
					type: "load",
					text: ""
				}
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.baseInit();
						}
					}
				}
			};
		}

		if (Component) SelectItem.__proto__ = Component;
		SelectItem.prototype = Object.create(Component && Component.prototype);
		SelectItem.prototype.constructor = SelectItem;
		SelectItem.prototype.baseInit = function() {
			var _data = this.props.dataMore;
			if (isArray(_data.data) && _data.data.length > 0) {
				this.data.emptyBox.type = "";
			} else {
				this.data.emptyBox.type = "1";
			}
		};
		SelectItem.prototype.penetrate = function() {};
		SelectItem.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		SelectItem.prototype.confirmBtn = function() {
			var _data = this.props.dataMore;
			_data.callback && _data.callback(_data);
			this.fire("click", _data);
			this.closePage();
		};
		SelectItem.prototype.isSelectValue = function(_item) {
			var _data = this.props.dataMore;
			if (isArray(_data.value)) {
				return getItemForKey(_item.key, _data.value);
			} else {
				return _item.key == _data.value;
			}
		};
		SelectItem.prototype.cSelectbox = function(_item, _index) {
			var _data = this.props.dataMore;
			if (this.props.readonly || _data.readonly || _item.readonly) {
				return;
			}
			if (isArray(_data.value)) {
				if (getItemForKey(_item.key, _data.value)) {
					delItemForKey(_item.key, _data.value);
				} else {
					_data.value.push(_item.key);
				}
			} else {
				_data.value = _data.value == _item.key ? "" : _item.key;
			}
		};
		SelectItem.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "选择"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"y-scroll-view",
							{_this: this},
							apivm.h(
								"view",
								null,
								!this.data.emptyBox.type &&
									this.props.dataMore.data.map(function(item, index) {
										return isParameters(item.show)
											? item.show
											: [
													apivm.h(
														"view",
														{
															onClick: function() {
																return this$1.cSelectbox(item, index);
															}
														},
														apivm.h(
															"view",
															{class: "item_warp flex_row"},
															apivm.h(
																"text",
																{
																	style:
																		"flex:1;font-size:" +
																		G.appFontSize +
																		"px;font-family:" +
																		(this$1.props.dataMore.key == "appFont"
																			? item.key
																			: G.appFont) +
																		";"
																},
																item.value
															),
															apivm.h(
																"view",
																null,
																(!this$1.props.dataMore.selType ||
																	this$1.props.dataMore.selType == 1) &&
																	apivm.h(
																		"view",
																		{
																			style:
																				"opacity:" + (this$1.isSelectValue(item) ? 1 : 0) + ";"
																		},
																		apivm.h("a-iconfont", {
																			name: "duihao",
																			color: G.appTheme,
																			size: G.appFontSize + 4
																		})
																	)
															),
															apivm.h(
																"view",
																null,
																this$1.props.dataMore.selType == 2 &&
																	apivm.h("z-radio", {
																		checked: this$1.isSelectValue(item),
																		type: "1",
																		size: 4,
																		color: this$1.isSelectValue(item) ? G.appTheme : "#999"
																	})
															)
														),
														apivm.h("z-divider", null)
													)
											  ];
									})
							),
							apivm.h(
								"view",
								null,
								this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
							),
							apivm.h("z-empty", {
								_this: this,
								dataMore: this.data.emptyBox,
								onRefresh: this.pageRefresh
							}),
							apivm.h("view", {style: "padding-bottom:" + safeArea().bottom + "px;"})
						)
					)
			);
		};

		return SelectItem;
	})(Component);
	SelectItem.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".item_warp": {padding: "0 16px", minHeight: "58px"}
	};
	apivm.define("select-item", SelectItem);

	var ItemSelect = /*@__PURE__*/ (function(Component) {
		function ItemSelect(props) {
			Component.call(this, props);
		}

		if (Component) ItemSelect.__proto__ = Component;
		ItemSelect.prototype = Object.create(Component && Component.prototype);
		ItemSelect.prototype.constructor = ItemSelect;
		ItemSelect.prototype.input = function(e) {
			this.fire("input", this.props.item);
		};
		ItemSelect.prototype.openSelect = function() {
			var this$1 = this;

			var item = this.props.item;
			if (item.callback) {
				item.callback(item);
				return;
			}
			var newJson = setNewJSON(item, {show: true});
			newJson.callback = function(ret) {
				item.value = ret.value;
				this$1.input();
			};
			G.selectPop = newJson;
		};
		ItemSelect.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding: " + (this.props.item.exhibit ? 13 : 18) + "px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:" +
								(this.props.item.exhibit ? 13 : 18) +
								"px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"text",
							{
								class: "" + (this.props.item.twa ? "text_default" : "text_one"),
								style:
									loadConfiguration() +
									"color:#333;width:" +
									(this.props.item.twa
										? "auto"
										: G.appFontSize * 4 + (api.systemType == "ios" ? 6 : 4) + "px") +
									";"
							},
							this.props.item.title
						),
						apivm.h(
							"view",
							{class: "flex_w"},
							!this.props.item.exhibit &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.openSelect();
										},
										class: "flex_row",
										style: "margin-left:10px;"
									},
									apivm.h(
										"view",
										{class: "flex_row flex_w"},
										apivm.h(
											"view",
											null,
											this.props.item.icon &&
												apivm.h("a-iconfont", {
													name: this.props.item.icon.name,
													color: this.props.item.icon.color,
													style: "margin-right:6px;",
													size: G.appFontSize + 4
												})
										),
										apivm.h(
											"text",
											{
												class: "text_one",
												style:
													loadConfiguration() +
													"color:" +
													((isArray(this.props.item.value)
													? this.props.item.value.length
													: this.props.item.value)
														? "#333"
														: "#ccc") +
													";"
											},
											(isArray(this.props.item.value)
												? this.props.item.value.length > 0
													? this.props.item.value
															.map(function(obj) {
																return getItemForKey(obj, this$1.props.item.data).value;
															})
															.join("、")
													: ""
												: (getItemForKey(this.props.item.value, this.props.item.data) || {})
														.value) ||
												this.props.item.hint ||
												"请选择" + (this.props.item.title || "")
										)
									),
									apivm.h("a-iconfont", {
										name: "xiangxia1",
										style: "transform: rotate(-90deg);margin-left:6px;",
										color: "#303030",
										size: G.appFontSize
									})
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.item.exhibit == 1 &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.openSelect();
									},
									class: "flex_row",
									style: "margin-top:10px;"
								},
								apivm.h(
									"view",
									{class: "flex_row flex_w"},
									apivm.h(
										"view",
										null,
										this.props.item.icon &&
											apivm.h("a-iconfont", {
												name: this.props.item.icon.name,
												color: this.props.item.icon.color,
												style: "margin-right:6px;",
												size: G.appFontSize + 4
											})
									),
									apivm.h(
										"text",
										{
											class: "text_one",
											style:
												loadConfiguration() +
												"color:" +
												((isArray(this.props.item.value)
												? this.props.item.value.length
												: this.props.item.value)
													? "#333"
													: "#ccc") +
												";"
										},
										(isArray(this.props.item.value)
											? this.props.item.value.length > 0
												? this.props.item.value
														.map(function(obj) {
															return getItemForKey(obj, this$1.props.item.data).value;
														})
														.join("、")
												: ""
											: (getItemForKey(this.props.item.value, this.props.item.data) || {})
													.value) ||
											this.props.item.hint ||
											"请选择" + (this.props.item.title || "")
									)
								),
								apivm.h("a-iconfont", {
									name: "xiangxia1",
									style: "transform: rotate(-90deg);",
									color: "#303030",
									size: G.appFontSize
								})
							)
					)
				)
			);
		};

		return ItemSelect;
	})(Component);
	ItemSelect.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".text_default": {
			textOverflow: "clip",
			whiteSpace: "normal",
			overflow: "visible",
			display: "inline"
		}
	};
	apivm.define("item-select", ItemSelect);

	var Item502 = /*@__PURE__*/ (function(Component) {
		function Item502(props) {
			Component.call(this, props);
		}

		if (Component) Item502.__proto__ = Component;
		Item502.prototype = Object.create(Component && Component.prototype);
		Item502.prototype.constructor = Item502;
		Item502.prototype.openDetail = function(e) {
			stopBubble(e);
			var item = this.props.item;
			openWin_workstation_letter({id: item.id});
		};
		Item502.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{onClick: this.openDetail},
				apivm.h("view", {
					style:
						"margin: 0 13px;border-bottom:" +
						(this.props.index ? 1 : 0) +
						"px solid #F1F1F1;"
				}),
				apivm.h(
					"view",
					{class: "item50_2_box"},
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 60) / (G.appFontSize + 3)) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 500;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 60) / (G.appFontSize + 3)) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:15px;"},
						apivm.h(
							"view",
							null,
							this.props.item.source &&
								apivm.h(
									"text",
									{style: loadConfiguration(-4) + "color: #999999;margin-right:10px;"},
									this.props.item.source
								)
						),
						apivm.h(
							"view",
							null,
							this.props.item.time &&
								apivm.h(
									"text",
									{style: loadConfiguration(-4) + "color: #999999;margin-right:10px;"},
									this.props.item.time
								)
						),
						apivm.h("view", {class: "flex_w"}),
						apivm.h(
							"view",
							{style: "flex-shrink: 0;"},
							this.props.item.tag &&
								apivm.h(
									"z-tag",
									{type: this.props.item.tag.type, color: this.props.item.tag.color},
									this.props.item.tag.text
								)
						)
					)
				)
			);
		};

		return Item502;
	})(Component);
	Item502.css = {".item50_2_box": {padding: "13px"}};
	apivm.define("item50-2", Item502);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var Root = /*@__PURE__*/ (function(Component) {
		function Root(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				dTitle: "详情",
				MG: !this.props.dataMore ? G : null,
				emptyBox: {
					type: "load",
					text: ""
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				addMore: {},
				addData: [],
				listData: [],

				memberId: "",
				showData: "",

				showCard: false,
				loginBox: {
					show: false,
					close: true,
					capsule: false,
					bg: true,
					netizen: 1
				},

				likeBox: {
					likeNum: 0,
					likeIs: false
				}
			};
		}

		if (Component) Root.__proto__ = Component;
		Root.prototype = Object.create(Component && Component.prototype);
		Root.prototype.constructor = Root;
		Root.prototype.onShow = function() {
			G.onShowNum++;
		};
		Root.prototype.onLoad = function(query) {
			loadParam(query, this);
		};
		Root.prototype.baseInit = function() {
			if (!getPrefs("sys_Mobile")) {
				this.data.loginBox.show = true;
				return;
			}
			this.stationId = this.data.pageParam.stationId || "1738037923414044673";
			this.data.memberId = this.data.pageParam.memberId || ""; //1740207394249535490
			this.data.addData = [
				// { type:"input",twa:1,exhibit:1,title:"邮箱",max:200,hint:"请输入邮箱", key: "senderEmail", value:"",defaultValue:"",noRequired: true,line: 0, },
				{
					type: "select",
					twa: 1,
					exhibit: 1,
					title: "建议/意见分类",
					hint: "请选择建议/意见分类",
					key: "stationLetterType",
					value: "",
					defaultValue: "",
					dictKey: "station_letter_type",
					noRequired: false,
					line: 0,
					data: []
				},
				{
					type: "input",
					twa: 1,
					exhibit: 1,
					title: "建议/意见标题",
					max: 200,
					hint: "请输入建议/意见标题",
					key: "title",
					value: "",
					defaultValue: "",
					noRequired: false,
					line: 0
				},
				{
					type: "textarea",
					html: false,
					title: "建议/意见内容",
					titleHint: "",
					max: 2000,
					hint: "请输入建议/意见内容",
					key: "content",
					value: "",
					defaultValue: "",
					noRequired: false,
					height: "200",
					line: 0
				},
				{
					type: "picture",
					show: true,
					title: "问题随身拍",
					hint: "请上传随身拍",
					key: "pictures",
					value: [],
					defaultValue: [],
					max: 9,
					valueKey: "newFileName",
					valueType: ",",
					noRequired: true,
					line: 0
				},
				{
					type: "input",
					twa: 1,
					exhibit: 1,
					title: "姓名",
					max: 200,
					hint: "请输入姓名",
					key: "senderUserName",
					value: G.userName,
					defaultValue: "",
					noRequired: false,
					line: 0
				},
				{
					type: "input",
					twa: 1,
					exhibit: 1,
					title: "电话",
					max: 200,
					hint: "请输入电话",
					key: "senderMobile",
					value: getPrefs("sys_Mobile") || "",
					defaultValue: "",
					noRequired: false,
					line: 0
				},
				{
					type: "input",
					twa: 1,
					exhibit: 1,
					title: "居住地址",
					max: 200,
					hint: "请输入居住地址",
					key: "senderAddr",
					value: "",
					defaultValue: "",
					noRequired: true,
					line: 0
				}
			];

			this.pageRefresh();
			getDetailsAppText(
				{pt: "llzlyxz", dt: true, param: {detailId: "llzlyxz"}},
				function(ret) {
					var data = ret ? ret.dealWith || "" : "";
					if (isObject(data)) {
						alert(
							{
								title: data.data.title,
								msg: data.content,
								type: "richText",
								closeType: "4",
								timeout: 5,
								buttons: ["我已知晓"]
							},
							function(ret) {}
						);
					}
				}
			);
		};
		Root.prototype.loginOK = function() {
			this.data.loginBox.show = false;
			if (this.dRefresh) {
				return;
			}
			this.data.emptyBox.type = "load";
			this.data.emptyBox.text = "";
			toast("登录成功");
			this.baseInit();
		};
		Root.prototype.pageRefresh = function() {
			var this$1 = this;

			var callback = function(ret, err) {
				hideProgress();
				var data = ret ? ret.dealWith || "" : "";
				this$1.data.showData = data;
				this$1.short = "";
				if (!data) {
					if (ret && ret.code == "302") {
						//未登录
						this$1.data.loginBox.show = true;
					}
					this$1.data.emptyBox.type = !data ? (ret ? "1" : "2") : "";
					this$1.data.emptyBox.text = !data
						? ret && ret.code != 200
							? ret.message || ret.data
							: ""
						: "";
				} else {
					getDictData(this$1.data.addData, null);
					this$1.getData();
					this$1.shareData = data;
					if (this$1.data.memberId) {
						this$1.data.likeBox.code = "station_member";
						this$1.data.likeBox.id = this$1.stationId + "#" + data.id;
						getCommentCount(this$1.data.likeBox);
						if (this$1.data.showData.codeType == 2) {
							getDetails50({param: {detailId: this$1.stationId}}, function(ret) {
								this$1.shareData = ret ? ret.dealWith || "" : "";
							});
							return;
						}
						getDetails50_member_other(
							{param: {detailId: this$1.data.showData.id}},
							function(ret) {
								this$1.data.showData = setNewJSON(
									this$1.data.showData,
									ret ? ret.dealWith || {} : {}
								);
								this$1.shareData = this$1.data.showData;
							}
						);
					}
				}
			};
			if (this.data.memberId) {
				getDetails50_member({param: {detailId: this.data.memberId}}, callback);
				getAppConfig(["workStationNpcDeputyQrCode"], function(ret) {
					var data = ret ? ret.data || {} : {};
					this$1.data.showCard = data.workStationNpcDeputyQrCode == "true";
				});
			} else {
				this.data.showCard = true;
				getDetails50({param: {detailId: this.stationId}}, callback);
			}
		};
		Root.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		Root.prototype.getData = function(_type) {
			var this$1 = this;

			var postParam = {
				pageNo: !_type ? 1 : this.data.pageNo,
				pageSize:
					!_type && this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize,
				keyword: "",
				query: {
					checkStatus: 1
				}
			};

			if (this.data.memberId) {
				postParam.query.receiverId = this.data.showData.id;
			} else {
				postParam.query.stationId = this.stationId;
			}
			getList50_2({param: postParam}, function(ret) {
				var data = ret ? ret.dealWith || [] : [];
				if (!isArray(data) || !data.length) {
					dealData(_type, this$1, ret);
					this$1.data.emptyBox.type = "";
					this$1.data.emptyBox.text = "";
					return;
				}
				if (!_type) {
					this$1.data.listData = data;
				} else {
					this$1.data.listData = this$1.data.listData.concat(data);
				}
				this$1.data.emptyBox.type = "";
				this$1.data.emptyBox.text =
					data.length >= postParam.pageSize ? LOAD_MORE : LOAD_ALL;
				this$1.data.pageNo =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1;
				this$1.data.refreshPageSize =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
					this$1.data.pageSize;
			});
		};
		Root.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		Root.prototype.myLetters = function() {
			openWin_workstation_letter({mine: 1});
		};
		Root.prototype.reset = function() {
			this.data.addData.forEach(function(_eItem) {
				_eItem.value = _eItem.defaultValue;
			});
		};
		Root.prototype.submit = function() {
			var this$1 = this;

			var param = {
				senderId: G.userId,
				receiveTime: dayjs().valueOf(),
				stationId: this.stationId
			};

			if (this.data.memberId) {
				param.receiverId = this.data.showData.id;
				param.receiverMobile = this.data.showData.mobile;
			}
			var list = this.data.addData;
			var getPostParam = addPostParam(list);
			if (!getPostParam) {
				return;
			}
			param = setNewJSON(param, getPostParam);
			param.recorderMobile = param.senderMobile;
			param.recorder = G.userName;
			param = {form: param};
			setParamToFirst(list, param);
			ajaxAlert(
				{
					msg: "确定提交吗?",
					url: appUrl() + "stationLetter/add",
					param: param,
					toast: "提交中"
				},
				function(ret) {
					if (ret && ret.code == "200") {
						this$1.pageRefresh();
						this$1.reset();
						setTimeout(function() {
							this$1.myLetters();
						}, 800);
					}
				}
			);
		};
		Root.prototype.openCard = function(_type) {
			var this$1 = this;

			var shareParam,
				shareCallback = function() {
					G.sharePosterPop = {
						show: true,
						shareImg: "",
						data: this$1.shareData
					};
				};
			if (_type == 2 && this.data.showData.codeType != 2) {
				shareParam = {
					n: "mo_workstation_lam",
					u: "../mo_workstation_lam/mo_workstation_lam.stml",
					p: {stationId: this.stationId, memberId: this.data.memberId}
				};
			} else {
				shareParam = {
					n: "mo_workstation_details",
					u: "../mo_workstation_details/mo_workstation_details.stml",
					p: {id: this.stationId}
				};
			}
			if (this.short) {
				this.shareData.shareUrl = this.short;
				shareCallback();
				return;
			}
			showProgress("生成中");
			shareLink(shareParam, function(ret, short) {
				hideProgress();
				this$1.short = short ? ret : "";
				this$1.shareData.shareUrl = this$1.short;
				shareCallback();
			});
		};
		Root.prototype.focus = function() {
			this.data.showData.hasFocus = this.data.showData.hasFocus ? 0 : 1;
			workStationFocus(
				{
					param: {
						focusStatus: this.data.showData.hasFocus,
						stationId: this.stationId
					}
				},
				null
			);
		};
		Root.prototype.cckLike = function(e) {
			stopBubble(e);
			if (this.data.likeBox.likeIs) {
				if (this.data.likeBox.likeNum > 0) {
					this.data.likeBox.likeNum--;
				}
			} else {
				this.data.likeBox.likeNum++;
			}
			this.data.likeBox.likeIs = !this.data.likeBox.likeIs;
			optionPraises(
				{
					code: this.data.likeBox.code,
					id: this.data.likeBox.id,
					likeIs: this.data.likeBox.likeIs
				},
				{extBlue: this.stationId, extYellow: this.data.showData.id}
			);
		};
		Root.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{_this: this, dataMore: this.props.dataMore},
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h(
					"y-scroll-view",
					{
						_this: this,
						refresh: true,
						style:
							"background:linear-gradient(to bottom, " +
							colorRgba(G.appTheme, 0.1) +
							", #FFF);"
					},
					apivm.h(
						"view",
						null,
						this.data.showData && [
							apivm.h(
								"view",
								null,
								this.data.memberId && [
									apivm.h(
										"view",
										null,
										apivm.h(
											"view",
											{style: "padding:20px 16px;"},
											apivm.h(
												"view",
												{class: "lam_box"},
												apivm.h(
													"view",
													{
														style: "padding:18px;align-items: flex-start;",
														class: "flex_row"
													},
													apivm.h(
														"view",
														null,
														apivm.h(
															"view",
															{style: "width:74px;height:92px;"},
															apivm.h("z-avatar-member", {src: this.data.showData})
														),
														apivm.h("view", {
															style:
																"width:20px;height:2px;background:" +
																G.appTheme +
																";border-radius:5px;margin-top:10px;"
														})
													),
													apivm.h(
														"view",
														{class: "flex_w", style: "margin-left:15px;"},
														apivm.h(
															"view",
															{class: "flex_row"},
															apivm.h(
																"view",
																{
																	style:
																		"flex:1;flex-direction:row;flex-wrap: wrap;align-items: flex-start;"
																},
																apivm.h(
																	"text",
																	{
																		style:
																			"color: #333;" +
																			loadConfiguration(1) +
																			"font-weight: 600;margin:5px 9px 0 0;"
																	},
																	this.data.showData.name
																),
																this.data.showData.labels.length > 0 &&
																	this.data.showData.labels.map(function(item, index) {
																		return [
																			apivm.h(
																				"z-tag",
																				{
																					style: "margin:5px 9px 0 0;",
																					color: G.appTheme,
																					roundSize: "3"
																				},
																				item
																			)
																		];
																	})
															),
															apivm.h(
																"view",
																{
																	class: "flex_row",
																	style: "padding:5px 3px 0;margin-left:5px;",
																	onClick: this.cckLike
																},
																apivm.h("a-iconfont", {
																	name: this.data.likeBox.likeIs ? "like-fill" : "like",
																	color: this.data.likeBox.likeIs ? "#F6931C" : "#666666",
																	size: G.appFontSize + 4
																}),
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(-2) +
																			"color:" +
																			(this.data.likeBox.likeIs ? "#F6931C" : "#666666") +
																			";margin-left:5px;"
																	},
																	this.data.likeBox.likeNum
																)
															)
														),
														apivm.h(
															"view",
															{
																class: "flex_row",
																style: "align-items: flex-start;margin-top:10px;"
															},
															apivm.h(
																"text",
																{style: "color: #333;" + loadConfiguration(-2)},
																"民族："
															),
															apivm.h(
																"text",
																{style: "flex:1;color: #333;" + loadConfiguration(-2)},
																this.data.showData.nation
															),
															apivm.h(
																"view",
																{
																	onClick: function() {
																		return this$1.openCard(2);
																	},
																	style:
																		"display:" +
																		(this.data.showCard ? "flex" : "none") +
																		";padding:5px 3px;margin-left:5px;"
																},
																apivm.h("a-iconfont", {
																	name: "erweima",
																	color: "#999",
																	size: G.appFontSize + 6
																})
															)
														),
														apivm.h(
															"view",
															{
																class: "flex_row",
																style: "align-items: flex-start;margin-top:6px;"
															},
															apivm.h(
																"text",
																{style: "color: #333;" + loadConfiguration(-2)},
																"单位职务："
															),
															apivm.h(
																"text",
																{style: "flex:1;color: #333;" + loadConfiguration(-2)},
																this.data.showData.position
															)
														),
														apivm.h(
															"view",
															{
																class: "flex_row",
																style: "align-items: flex-start;margin-top:6px;"
															},
															apivm.h(
																"text",
																{style: "color: #333;" + loadConfiguration(-2)},
																"入驻",
																G.sysSign == "rd" ? "站点" : "工作室",
																"："
															),
															apivm.h(
																"text",
																{style: "flex:1;color: #333;" + loadConfiguration(-2)},
																this.data.showData.stationNames
															)
														)
													)
												)
											)
										),
										apivm.h(
											"view",
											{style: "padding:0 12px 20px;margin-top:-10px;", class: "flex_row"},
											apivm.h(
												"view",
												{
													class: "lam_box xy_center",
													style: "width:33.33%;margin:0 4px;padding:15px 5px;"
												},
												apivm.h(
													"text",
													{
														style:
															"color: " +
															G.appTheme +
															";" +
															loadConfiguration(6) +
															"font-weight: 800;"
													},
													this.data.showData.activityCount || 0
												),
												apivm.h(
													"text",
													{style: "color: #333;" + loadConfiguration() + "margin-top:3px;"},
													"活动参与"
												)
											),
											apivm.h(
												"view",
												{
													class: "lam_box xy_center",
													style: "width:33.33%;margin:0 4px;padding:15px 5px;"
												},
												apivm.h(
													"text",
													{
														style:
															"color: " +
															G.appTheme +
															";" +
															loadConfiguration(6) +
															"font-weight: 800;"
													},
													this.data.showData.letterCount || 0
												),
												apivm.h(
													"text",
													{style: "color: #333;" + loadConfiguration() + "margin-top:3px;"},
													"受理民意"
												)
											),
											apivm.h(
												"view",
												{
													class: "lam_box xy_center",
													style: "width:33.33%;margin:0 4px;padding:15px 5px;"
												},
												apivm.h(
													"text",
													{
														style:
															"color: " +
															G.appTheme +
															";" +
															loadConfiguration(6) +
															"font-weight: 800;"
													},
													this.data.showData.dutyCount || 0
												),
												apivm.h(
													"text",
													{style: "color: #333;" + loadConfiguration() + "margin-top:3px;"},
													G.sysSign == "rd" ? "代表" : "委员",
													"值班"
												)
											)
										)
									)
								]
							),
							apivm.h(
								"view",
								null,
								!this.data.memberId && [
									apivm.h(
										"view",
										{style: "padding:20px 16px;"},
										apivm.h(
											"view",
											{class: "lam_box"},
											apivm.h(
												"view",
												{style: "padding:18px;"},
												apivm.h(
													"view",
													{class: "flex_row", style: "align-items: flex-start;"},
													apivm.h(
														"view",
														{class: "flex_w"},
														apivm.h(
															"text",
															{
																style:
																	"color: #333;" + loadConfiguration(1) + ";font-weight: 600;"
															},
															this.data.showData.title
														)
													),
													apivm.h(
														"view",
														{
															onClick: function() {
																return this$1.focus();
															},
															style: "padding:3px;"
														},
														apivm.h("a-iconfont", {
															name: this.data.showData.hasFocus
																? "jushoucanggift"
																: "jushoucang",
															color: this.data.showData.hasFocus ? "#F6931C" : "#ccc",
															size: G.appFontSize + 6
														})
													),
													apivm.h(
														"view",
														{
															onClick: function() {
																return this$1.openCard(1);
															},
															style:
																"display:" +
																(this.data.showCard ? "flex" : "none") +
																";padding:3px;margin-left:5px;"
														},
														apivm.h("a-iconfont", {
															name: "erweima",
															color: "#999",
															size: G.appFontSize + 6
														})
													)
												),
												apivm.h(
													"view",
													{style: "flex-direction: row;flex-wrap: wrap;"},
													apivm.h(
														"view",
														{class: "flex_row", style: "margin:16px 15px 0 0;"},
														apivm.h(
															"text",
															{style: "color: #999;" + loadConfiguration(-2)},
															"联系人"
														),
														apivm.h(
															"text",
															{style: "margin-left:10px;color: #333;" + loadConfiguration(-2)},
															this.data.showData.userName
														)
													),
													apivm.h(
														"view",
														{class: "flex_row", style: "margin-top:16px;"},
														apivm.h(
															"text",
															{style: "color: #999;" + loadConfiguration(-2)},
															"电话"
														),
														apivm.h(
															"text",
															{style: "margin-left:3px;color: #333;" + loadConfiguration(-2)},
															this.data.showData.userPhone
														)
													)
												),
												apivm.h(
													"view",
													{
														class: "flex_row",
														style: "align-items: flex-start;margin-top:6px;"
													},
													apivm.h(
														"text",
														{style: "color: #999;" + loadConfiguration(-2)},
														"接待时间"
													),
													apivm.h(
														"text",
														{
															style:
																"flex:1;margin-left:10px;color: #333;" + loadConfiguration(-2)
														},
														this.data.showData.openTime || "暂无"
													)
												),
												apivm.h(
													"view",
													{
														class: "flex_row",
														style: "align-items: flex-start;margin-top:6px;"
													},
													apivm.h(
														"text",
														{style: "color: #999;" + loadConfiguration(-2)},
														G.sysSign == "rd" ? "站点" : "工作室",
														"地址"
													),
													apivm.h(
														"text",
														{
															style:
																"flex:1;margin-left:10px;color: #333;" + loadConfiguration(-2)
														},
														this.data.showData.address
													)
												)
											)
										)
									)
								]
							),
							apivm.h(
								"view",
								{class: "flex_row xy_center", style: "margin-top:10px;"},
								apivm.h("view", {
									style:
										"width:30px;height:2px;background:" +
										G.appTheme +
										";border-radius:5px;"
								}),
								apivm.h(
									"text",
									{
										style:
											"color: " +
											G.appTheme +
											";" +
											loadConfiguration(2) +
											"font-weight: 800;margin:0 20px;"
									},
									"给",
									this.data.memberId
										? G.sysSign == "rd"
											? "代表"
											: "委员"
										: G.sysSign == "rd"
										? "站点"
										: "工作室",
									"留言"
								),
								apivm.h("view", {
									style:
										"width:30px;height:2px;background:" +
										G.appTheme +
										";border-radius:5px;"
								})
							),
							apivm.h(
								"view",
								null,
								this.data.addData.map(function(item, index) {
									return [
										apivm.h(
											"view",
											{style: "padding:14px 16px 0;"},
											apivm.h(
												"view",
												{class: "lam_box"},
												item.type == "select"
													? [
															apivm.h("item-select", {
																dataMore: this$1.data.addMore,
																item: item,
																index: index
															})
													  ]
													: item.type == "input"
													? [
															apivm.h("item-input", {
																dataMore: this$1.data.addMore,
																item: item,
																index: index
															})
													  ]
													: item.type == "textarea"
													? [
															apivm.h("item-textarea", {
																dataMore: this$1.data.addMore,
																item: item,
																index: index
															})
													  ]
													: item.type == "picture"
													? [
															apivm.h("item-picture", {
																dataMore: this$1.data.addMore,
																item: item,
																index: index,
																w: 32
															})
													  ]
													: []
											)
										)
									];
								})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "padding:20px 26px;"},
								apivm.h(
									"view",
									{style: "width:50%;padding:5px 15px;"},
									apivm.h("z-button", {
										onClick: function() {
											return this$1.myLetters(0);
										},
										round: true,
										plain: true,
										style: "padding:7px 7px;width:100%;",
										color: G.appTheme,
										text: "我的留言"
									})
								),
								apivm.h(
									"view",
									{style: "width:50%;padding:5px 15px;"},
									apivm.h("z-button", {
										onClick: function() {
											return this$1.submit(0);
										},
										round: true,
										style: "padding:7px 7px;width:100%;",
										color: G.appTheme,
										text: "提交"
									})
								)
							),
							apivm.h(
								"view",
								{style: "background:#FFF;"},
								apivm.h(
									"view",
									null,
									!this.data.emptyBox.type &&
										this.data.listData.length > 0 &&
										apivm.h(
											"view",
											{class: "flex_row", style: "padding:16px 16px 0;"},
											apivm.h("view", {
												style:
													"width:3px;height:15px;border-radius: 2px;background:" + G.appTheme
											}),
											apivm.h(
												"view",
												{style: "margin:0 8px;", class: "flex_w"},
												apivm.h(
													"text",
													{
														style:
															"color: #333;" + loadConfiguration(1) + ";font-weight: 600;"
													},
													"受理民意"
												)
											)
										)
								),
								apivm.h(
									"view",
									null,
									!this.data.emptyBox.type &&
										this.data.listData.map(function(item, index) {
											return [
												apivm.h("item50-2", {
													_this: this$1,
													list: this$1.data.listData,
													item: item,
													index: index
												})
											];
										})
								)
							)
						]
					),
					apivm.h(
						"view",
						null,
						this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
					),
					apivm.h("z-empty", {
						_this: this,
						dataMore: this.data.emptyBox,
						onRefresh: this.pageRefresh
					}),
					apivm.h("view", {
						style:
							"padding-bottom:" +
							(!this.data.pageParam.footerH ? safeArea().bottom : 0) +
							"px;"
					})
				),
				apivm.h("y-login", {dataMore: this.data.loginBox, onResult: this.loginOK}),
				this.data.memberId && this.data.showData && this.data.showData.codeType != 2
					? apivm.h("share-poster-station-member", {dataMore: G.sharePosterPop})
					: apivm.h("share-poster-station-card", {dataMore: G.sharePosterPop})
			);
		};

		return Root;
	})(Component);
	Root.css = {
		".lam_box": {
			background: "#FFFFFF",
			boxShadow: "0px 0px 4px 1px rgba(24,64,118,0.08)",
			borderRadius: "4px"
		}
	};
	apivm.define("root", Root);
	apivm.render(apivm.h("root", null), "body");
})();
