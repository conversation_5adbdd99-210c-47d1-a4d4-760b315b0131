<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0" />
    <meta name="format-detection" content="telephone=no,email=no,date=no,address=no" />
    <title>政协直播</title>
    <style type="text/css">
      [v-cloak] {
        display: none;
      }

      html,
      body,
      #app {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        background: #1f1f1f;
      }

      /* 顶部标题栏 */
      .header {
        height: 60px;
        background: #1f1f1f;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-width: 0;
      }

      .header-left-logo {
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }

      .header-title {
        font-size: 14px;
        color: #fff;
        flex: 1;
        min-width: 0;
        /* overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap; */
      }

      .share-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 5px 8px;
        font-size: 12px;
        color: #fff;
      }

      .share-icon {
        width: 16px;
        height: 16px;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.50-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/></svg>')
          no-repeat center;
        background-size: contain;
      }

      /* 主视频区域 */
      .video-container {
        position: relative;
        height: 222px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background: #000;
      }

      .video-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }

      /* 视频播放器 */
      .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
      }

      .video-player video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      /* 全屏时的样式 */
      .video-player video:fullscreen {
        object-fit: contain;
      }

      .video-player video:-webkit-full-screen {
        object-fit: contain;
      }

      .video-player video:-moz-full-screen {
        object-fit: contain;
      }

      .video-player video:-ms-fullscreen {
        object-fit: contain;
      }

      /* 直播状态下隐藏进度条 */
      .video-player video.live-video::-webkit-media-controls-timeline {
        display: none !important;
      }

      .video-player video.live-video::-webkit-media-controls-current-time-display {
        display: none !important;
      }

      .video-player video.live-video::-webkit-media-controls-time-remaining-display {
        display: none !important;
      }

      /* 确保播放按钮显示 */
      .video-player video.live-video::-webkit-media-controls-play-button {
        display: block !important;
      }

      /* Firefox */
      .video-player video.live-video::-moz-media-controls-timeline {
        display: none !important;
      }

      /* 直播结束状态 */
      .live-ended-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.45);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        backdrop-filter: blur(2px);
      }

      .ended-title {
        color: #fff;
        font-size: 16px;
        margin-bottom: 10px;
      }

      .replay-button {
        background: linear-gradient(90deg, #2f04ff 0%, #00f0fe 100%);
        font-size: 17px;
        color: #ffffff;
        border: none;
        border-radius: 6px;
        padding: 10px 30px;
        transition: all 0.3s ease;
      }

      /* 无直播地址提示 */
      .no-stream-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        backdrop-filter: blur(2px);
      }

      .no-stream-title {
        color: #fff;
        font-size: 16px;
        font-weight: 500;
        text-align: center;
      }

      /* 状态区域样式 */
      .status-area {
        background: #1f1f1f;
        padding: 8px 20px;
        border: 1px solid #000000;
      }

      .status-content {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .status-countdown {
        text-align: center;
      }

      .status-countdown-info {
        color: rgba(255, 255, 255, 0.6);
        font-size: 12px;
        margin-bottom: 8px;
      }

      .status-countdown-info span {
        color: #3b82f6;
      }

      .status-countdown-time {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
      }

      .status-countdown-number {
        padding: 4px 8px;
        min-width: 40px;
        text-align: center;
      }

      .status-countdown-separator {
        color: rgba(255, 255, 255, 0.6);
        font-size: 16px;
      }

      .status-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
        font-weight: 500;
      }

      .status-live {
        color: #999999;
      }

      .status-ended {
        color: #999999;
      }

      /* 标签栏 */
      .tabs {
        display: flex;
        background: #1f1f1f;
      }

      .tab {
        flex: 1;
        padding: 6px 16px;
        text-align: center;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        position: relative;
        transition: color 0.3s;
      }

      .tab.active {
        color: #fff;
      }

      .tab.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 2px;
        background: #3b82f6;
        border-radius: 1px;
      }

      /* 内容区域 */
      .content {
        padding: 5px 16px;
      }

      .live-title {
        font-size: 15px;
        font-weight: 600;
        line-height: 1.4;
        margin-bottom: 16px;
        color: #fff;
      }

      .live-time {
        color: rgba(255, 255, 255, 0.6);
        font-size: 12px;
        margin-bottom: 10px;
      }

      .live-description {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        line-height: 1.6;
        text-indent: 2em;
      }

      /* 互动区域样式 */
      .interaction-content {
        height: calc(100vh - 222px - 60px - 42px - 36px);
        display: flex;
        flex-direction: column;
      }

      .comment-list {
        flex: 1;
        overflow-y: auto;
        padding: 0 16px;
      }

      .comment-item {
        padding: 12px 0;
        border-bottom: 1px solid #333;
      }

      .comment-item:last-child {
        border-bottom: none;
      }

      .comment-user {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }

      .user-name {
        font-size: 14px;
        font-weight: 500;
        margin-right: 8px;
        color: #3876ff !important;
      }

      .comment-time {
        color: rgba(255, 255, 255, 0.4);
        font-size: 12px;
      }

      .comment-text {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .comment-actions {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .action-btn {
        color: rgba(255, 255, 255, 0.6);
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: color 0.3s;
      }

      /* 二级评论样式 */
      .reply-list {
        margin-top: 12px;
        margin-left: 20px;
        border-left: 2px solid #333;
        padding-left: 12px;
      }

      .reply-item {
        padding: 8px 0;
        border-bottom: 1px solid #2a2a2a;
      }

      .reply-item:last-child {
        border-bottom: none;
      }

      .reply-user {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
      }

      .reply-text {
        color: rgba(255, 255, 255, 0.9);
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 6px;
      }

      .reply-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .reply-actions .action-btn {
        font-size: 11px;
      }

      .like-icon {
        width: 12px;
        height: 12px;
        margin-right: 4px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }

      .like-icon.liked {
        background-image: url('./images/fabulous_o.png');
      }

      .like-icon.unliked {
        background-image: url('./images/fabulous.png');
      }

      .no-comments {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
      }

      .no-comments-text {
        color: rgba(255, 255, 255, 0.4);
        font-size: 14px;
      }

      .comment-input-area {
        padding: 16px;
        border-top: 1px solid #333;
        background: #1f1f1f;
      }

      .input-container {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .comment-input {
        flex: 1;
        background: #333;
        border: 1px solid #444;
        border-radius: 20px;
        padding: 10px 16px;
        color: #fff;
        font-size: 14px;
        outline: none;
        transition: border-color 0.3s;
      }

      .comment-input:focus {
        border-color: #3b82f6;
      }

      .comment-input::placeholder {
        color: rgba(255, 255, 255, 0.4);
      }

      .send-btn {
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 16px;
        padding: 8px 16px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
        min-width: 60px;
      }

      .send-btn:hover:not(:disabled) {
        background: #2563eb;
      }

      .send-btn:disabled {
        background: #666;
        cursor: not-allowed;
      }

      /* 回复输入框样式 */
      .reply-input-area {
        margin-top: 12px;
        padding: 12px;
        background: #2a2a2a;
        border-radius: 8px;
        border: 1px solid #444;
      }

      .reply-input-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
      }

      .reply-to-text {
        color: #3876ff;
        font-size: 13px;
      }

      .reply-close-btn {
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        font-size: 16px;
        padding: 2px 6px;
        border-radius: 4px;
        transition: all 0.3s;
      }

      .reply-input-container {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .reply-input {
        flex: 1;
        background: #333;
        border: 1px solid #555;
        border-radius: 16px;
        padding: 8px 12px;
        color: #fff;
        font-size: 13px;
        outline: none;
        transition: border-color 0.3s;
      }

      .reply-input:focus {
        border-color: #3876ff;
      }

      .reply-input::placeholder {
        color: rgba(255, 255, 255, 0.4);
      }

      .reply-send-btn {
        background: #3876ff;
        color: white;
        border: none;
        border-radius: 12px;
        padding: 6px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s;
        min-width: 50px;
      }

      .reply-send-btn:hover:not(:disabled) {
        background: #2563eb;
      }

      .reply-send-btn:disabled {
        background: #666;
        cursor: not-allowed;
      }

      /* 分享弹窗样式 */
      .share-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        backdrop-filter: blur(4px);
      }

      .share-content {
        background: #2a2a2a;
        border-radius: 12px;
        padding: 24px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }

      .share-title {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        text-align: center;
      }

      .share-url-container {
        background: #1f1f1f;
        border: 1px solid #444;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .share-url {
        flex: 1;
        background: transparent;
        border: none;
        color: #fff;
        font-size: 14px;
        outline: none;
        word-break: break-all;
      }

      .copy-btn {
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s;
        white-space: nowrap;
      }

      .copy-btn.copied {
        background: #10b981;
      }

      .share-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
      }

      .share-close-btn {
        background: #666;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 10px 20px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
      }

      /* 手机验证弹窗样式 */
      .verify-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        backdrop-filter: blur(4px);
      }

      .verify-content {
        background: #fff;
        border-radius: 12px;
        padding: 24px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        position: relative;
      }

      .verify-title {
        color: #333;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        text-align: center;
      }

      .verify-close {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 24px;
        height: 24px;
        cursor: pointer;
        color: #999;
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .verify-form {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .verify-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.3s;
        box-sizing: border-box;
      }

      .verify-input:focus {
        border-color: #3b82f6;
      }

      .verify-code-container {
        display: flex;
        gap: 8px;
      }

      .verify-code-input {
        flex: 1;
      }

      .send-code-btn {
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
        white-space: nowrap;
        min-width: 100px;
      }

      .send-code-btn:hover:not(:disabled) {
        background: #2563eb;
      }

      .send-code-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .verify-submit-btn {
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s;
        margin-top: 8px;
      }

      .verify-submit-btn:hover:not(:disabled) {
        background: #2563eb;
      }

      .verify-submit-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      /* 响应式设计 */
      @media (max-width: 375px) {
        /* .header-title {
          max-width: 150px;
        } */

        .live-title {
          font-size: 16px;
        }

        .share-content {
          padding: 20px;
          margin: 0 16px;
        }

        .verify-content {
          padding: 20px;
          margin: 0 16px;
        }
      }
    </style>
  </head>

  <body>
    <div v-cloak id="app">
      <!-- 顶部标题栏 -->
      <header class="header">
        <div class="header-left">
          <img src="https://xaszzx.xa-cppcc.gov.cn:8131/lzt/pageImg/open/logo?v=5" alt="" class="header-left-logo" />
          <div class="header-title">{{details.theme}}</div>
        </div>
        <div class="share-btn" @click="shareStream" v-if="showShareButton">
          <div class="share-icon"></div>
          分享直播
        </div>
      </header>

      <!-- 主视频区域 -->
      <div class="video-container">
        <!-- 背景图片 -->
        <div class="video-bg" :style="{ backgroundImage: details.coverImg ? 'url(' + appUrl + 'image/' + details.coverImg + ')' : '' }"></div>

        <!-- 视频播放器 -->
        <div class="video-player" v-show="showVideoPlayer">
          <video
            ref="videoElement"
            controls
            autoplay
            playsinline
            x-webkit-airplay="allow"
            webkit-playsinline="true"
            controlslist="nodownload"
            @fullscreenchange="handleFullscreenChange"
            @webkitfullscreenchange="handleFullscreenChange"
            @webkitbeginfullscreen="handleFullscreenChange"
            @webkitendfullscreen="handleFullscreenChange"></video>
        </div>

        <!-- 直播结束状态 -->
        <div class="live-ended-overlay" v-show="currentStatus === 'ended'">
          <div class="ended-title">直播已结束</div>
          <button class="replay-button" v-if="details.isReplay == 1" @click="watchReplay">观看回放</button>
        </div>

        <!-- 无直播地址提示 -->
        <div class="no-stream-overlay" v-show="currentStatus === 'no-stream'">
          <div class="no-stream-title">暂无配置直播地址</div>
        </div>
      </div>

      <!-- 状态区域 -->
      <div class="status-area">
        <div class="status-content" id="statusContent">
          <!-- 倒计时状态 -->
          <div class="status-countdown" v-show="currentStatus === 'countdown'">
            <div class="status-countdown-info">
              距离
              <span>{{formatDateTime(details.startTime)}}</span>
              直播开始还有
            </div>
            <div class="status-countdown-time">
              <span v-if="countdownTime.days > 0" class="status-countdown-number">{{countdownTime.days}}</span>
              <span v-if="countdownTime.days > 0" class="status-countdown-separator">天</span>
              <span v-if="countdownTime.days > 0 || countdownTime.hours > 0" class="status-countdown-number">{{countdownTime.hours}}</span>
              <span v-if="countdownTime.days > 0 || countdownTime.hours > 0" class="status-countdown-separator">时</span>
              <span class="status-countdown-number">{{countdownTime.minutes}}</span>
              <span class="status-countdown-separator">分</span>
              <span class="status-countdown-number">{{countdownTime.seconds}}</span>
              <span class="status-countdown-separator">秒</span>
            </div>
          </div>
          <!-- 进行中状态 -->
          <div class="status-text status-live" v-show="currentStatus === 'playing'">直播进行中</div>
          <!-- 回放状态 -->
          <div class="status-text status-ended" v-show="currentStatus === 'replay'">直播已结束</div>
          <!-- 已结束状态 -->
          <div class="status-text status-ended" v-show="currentStatus === 'ended'">直播已结束</div>
          <!-- 无直播地址状态 -->
          <div class="status-text status-ended" v-show="currentStatus === 'no-stream'">暂无配置直播地址</div>
        </div>
      </div>

      <!-- 标签栏 -->
      <div class="tabs">
        <div class="tab" :class="{active: activeTab === 'details'}" @click="switchTab('details')">直播详情</div>
        <div class="tab" :class="{active: activeTab === 'interaction'}" @click="switchTab('interaction')">互动</div>
      </div>

      <!-- 内容区域 -->
      <!-- 直播详情内容 -->
      <div class="content" v-show="activeTab === 'details'">
        <h2 class="live-title">{{details.theme || '直播标题'}}</h2>
        <div class="live-time">时间：{{details.startTime ? formatFullDateTime(details.startTime) : ''}} - {{details.endTime ? formatFullDateTime(details.endTime) : ''}}</div>
        <div class="live-description">{{details.liveDescribes || '暂无描述'}}</div>
      </div>

      <!-- 互动内容 -->
      <div class="interaction-content" v-show="activeTab === 'interaction'">
        <div class="comment-list" ref="commentList">
          <div class="comment-item" v-for="comment in comments" :key="comment.id">
            <div class="comment-user">
              <span class="user-name">{{comment.commentUserName}}</span>
              <span class="comment-time">{{formatFullDateTime(comment.createDate)}}</span>
            </div>
            <div class="comment-text">{{comment.commentContent}}</div>
            <div class="comment-actions">
              <span class="action-btn" @click="replyComment(comment)">跟帖互动</span>
              <span class="action-btn" @click="likeComment(comment)">
                <i class="like-icon" :class="comment.hasClickPraises ? 'liked' : 'unliked'"></i>
                点赞 ({{comment.praisesCount || 0}})
              </span>
            </div>

            <!-- 回复输入框 -->
            <div class="reply-input-area" v-if="replyingTo && replyingTo.id === comment.id">
              <div class="reply-input-header">
                <span class="reply-to-text">回复 {{comment.commentUserName}}:</span>
                <span class="reply-close-btn" @click="cancelReply">×</span>
              </div>
              <div class="reply-input-container">
                <input type="text" class="reply-input" placeholder="说点什么..." v-model="replyContent" @keyup.enter="sendReply" maxlength="200" ref="replyInput" />
                <button class="reply-send-btn" @click="sendReply" :disabled="!replyContent.trim()">发送</button>
              </div>
            </div>

            <!-- 二级评论 -->
            <div class="reply-list" v-if="comment.children && comment.children.length > 0">
              <div class="reply-item" v-for="reply in comment.children" :key="reply.id">
                <div class="reply-user">
                  <span class="user-name">{{reply.commentUserName}}</span>
                  <span class="comment-time">{{formatFullDateTime(reply.createDate)}}</span>
                </div>
                <div class="reply-text">{{reply.commentContent }}</div>
                <div class="reply-actions">
                  <span class="action-btn" @click="likeComment(reply)">
                    <i class="like-icon" :class="reply.hasClickPraises ? 'liked' : 'unliked'"></i>
                    点赞 ({{reply.praisesCount || 0}})
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="no-comments" v-if="comments.length === 0">
            <div class="no-comments-text">暂无评论，快来抢沙发吧~</div>
          </div>
        </div>
        <div class="comment-input-area">
          <div class="input-container">
            <input type="text" class="comment-input" placeholder="说点什么..." v-model="newComment" @keyup.enter="sendComment" maxlength="200" />
            <button class="send-btn" @click="sendComment" :disabled="!newComment.trim()">发送</button>
          </div>
        </div>
      </div>

      <!-- 分享弹窗 -->
      <div class="share-modal" v-show="showShareModal" @click="closeShareModal">
        <div class="share-content" @click.stop>
          <div class="share-title">分享直播</div>
          <div class="share-url-container">
            <input type="text" class="share-url" :value="shareUrl" readonly ref="shareUrlInput" />
            <button class="copy-btn" :class="{ copied: copySuccess }" @click="copyShareUrl">{{ copySuccess ? '已复制' : '复制' }}</button>
          </div>
          <div class="share-actions">
            <button class="share-close-btn" @click="closeShareModal">关闭</button>
          </div>
        </div>
      </div>

      <!-- 手机验证弹窗 -->
      <div class="verify-modal" v-show="showVerifyModal" @click="closeVerifyModal">
        <div class="verify-content" @click.stop>
          <div class="verify-close" @click="closeVerifyModal">×</div>
          <div class="verify-title">手机号验证</div>
          <div class="verify-form">
            <input type="text" class="verify-input" placeholder="请输入姓名" v-model="verifyForm.name" maxlength="20" />
            <input type="tel" class="verify-input" placeholder="请输入手机号码" v-model="verifyForm.phone" maxlength="11" />
            <div class="verify-code-container">
              <input type="text" class="verify-input verify-code-input" placeholder="请输入验证码" v-model="verifyForm.code" maxlength="6" />
              <button class="send-code-btn" @click="sendVerifyCode" :disabled="!canSendCode || codeCountdown > 0">{{ codeCountdown > 0 ? codeCountdown + 's' : '发送验证码' }}</button>
            </div>
            <button class="verify-submit-btn" @click="submitVerify" :disabled="!canSubmitVerify">验证</button>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script type="text/javascript" src="./script/vue.min.js"></script>
  <script type="text/javascript" src="./script/axios.min.js"></script>
  <script type="text/javascript" src="./script/url.js"></script>
  <script type="text/javascript" src="./script/hls.js"></script>
  <script type="text/javascript">
    //接收页面参数
    var datas = {}
    var param = location.href.split('?')
    if (param.length > 1) {
      param = decodeURIComponent(param[1])
      var params = param.split('&')
      if (params) {
        for (var i = 0; i < params.length; i++) {
          if (params[i]) {
            var data_key = params[i].substring(0, params[i].indexOf('='))
            var data_value = params[i].substring(params[i].indexOf('=') + 1)
            if (data_value.indexOf('{') == 0 || data_value.indexOf('[') == 0) {
              data_value = JSON.parse(data_value)
            }
            datas[data_key] = data_value
          }
        }
      }
    }
    var vmData = {
      sysType: '',
      appUrl: appUrl,
      downErr: '请稍候', //异常提示
      details: {},
      currentStatus: 'countdown', // countdown, playing, ended, no-stream
      showVideoPlayer: false,
      countdownTime: {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0
      },
      countdownTimer: null,
      hls: null,
      // 标签切换相关
      activeTab: 'details', // details, interaction
      // 互动相关
      comments: [],
      newComment: '',
      commentTimer: null, // 评论定时器
      // 回复相关
      replyingTo: null, // 正在回复的评论
      replyContent: '', // 回复内容
      // 分享相关
      showShareModal: false, // 是否显示分享弹窗
      shareUrl: '', // 分享链接
      copySuccess: false, // 复制成功状态
      // 验证相关
      showVerifyModal: false, // 是否显示验证弹窗
      verifyForm: {
        name: '',
        phone: '',
        code: ''
      },
      codeCountdown: 0, // 验证码倒计时
      codeTimer: null, // 验证码定时器
      localToken: '', // 本地存储的token
      isSharedLink: false, // 是否为分享链接
      showShareButton: true // 是否显示分享按钮
    }
    var methods = {
      init: function () {
        var that = this
        var userAgent = navigator.userAgent.toLowerCase()
        if (userAgent.indexOf('android') !== -1) {
          that.sysType = 'android'
        } else if (userAgent.indexOf('iphone') !== -1 || userAgent.indexOf('ipad') !== -1 || userAgent.indexOf('ipod') !== -1) {
          that.sysType = 'ios'
        } else {
          that.sysType = 'pc'
        }

        // 检查是否为分享链接
        that.isSharedLink = datas.share === '1'

        // 检查本地token
        that.checkLocalToken()

        this.getInfo()
        this.generateShareUrl()
      },
      getUser: function () {},
      getInfo: function () {
        var that = this
        var token = that.getValidToken()
        axios
          .post(`${that.appUrl}videoConnection/info`, { detailId: datas.id }, { headers: { Authorization: token } })
          .catch(function (err) {
            that.downErr = err
            alert(that.downErr)
          })
          .then(function (ret) {
            ret = ret.data || {}
            if (ret.code == 200) {
              that.details = ret.data || {}
              // 设置分享按钮显示逻辑
              that.showShareButton = that.details.isPublic == 1
              // 只有有token时才调用getLiveRead
              if (token) {
                that.getLiveRead()
              }
              that.updateVideoStatus()
            } else {
              that.downErr = ret.message || ret.data
              alert(that.downErr)
            }
          })
      },
      // 增加阅读请求
      getLiveRead() {
        var that = this
        var token = that.getValidToken()
        if (!token) return

        axios
          .post(`${that.appUrl}liveWatchCount/add`, { form: { dataId: datas.id } }, { headers: { Authorization: token } })
          .catch(function (err) {
            that.downErr = err
            alert(that.downErr)
          })
          .then(function (ret) {
            ret = ret.data || {}
            console.log('ret===>', ret)
          })
      },
      // 更新视频状态
      updateVideoStatus: function () {
        var that = this
        var meetingStatus = that.details.meetingStatus
        if (meetingStatus === '已结束') {
          that.currentStatus = 'ended'
          that.showVideoPlayer = false
          that.stopCountdown()
        } else if (meetingStatus === '未开始') {
          that.currentStatus = 'countdown'
          that.showVideoPlayer = false
          that.startCountdown()
        } else if (meetingStatus === '进行中') {
          that.stopCountdown()
          if (that.details.liveUrl) {
            that.currentStatus = 'playing'
            that.showVideoPlayer = true
            that.$nextTick(function () {
              that.initVideoPlayer()
            })
          } else {
            that.currentStatus = 'no-stream'
            that.showVideoPlayer = false
          }
        }
      },
      // 开始倒计时
      startCountdown: function () {
        var that = this
        that.stopCountdown() // 先停止之前的倒计时

        that.countdownTimer = setInterval(function () {
          var now = new Date().getTime()
          var startTime = new Date(that.details.startTime).getTime()
          var distance = startTime - now

          if (distance > 0) {
            that.countdownTime.days = Math.floor(distance / (1000 * 60 * 60 * 24))
            that.countdownTime.hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
            that.countdownTime.minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
            that.countdownTime.seconds = Math.floor((distance % (1000 * 60)) / 1000)
          } else {
            // 倒计时结束，检查是否有推流地址
            that.stopCountdown()
            if (that.details.liveUrl) {
              that.currentStatus = 'playing'
              that.showVideoPlayer = true
              that.$nextTick(function () {
                that.initVideoPlayer()
              })
            } else {
              that.currentStatus = 'no-stream'
              that.showVideoPlayer = false
            }
          }
        }, 1000)
      },
      // 停止倒计时
      stopCountdown: function () {
        if (this.countdownTimer) {
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      },
      // 初始化视频播放器
      initVideoPlayer: function () {
        var that = this
        var video = that.$refs.videoElement
        var liveUrl = that.details.liveUrl
        if (!video) {
          console.error('视频元素未找到')
          return
        }

        if (!liveUrl) {
          console.error('播放地址为空')
          return
        }
        // 销毁之前的hls实例
        if (that.hls) {
          console.log('销毁之前的HLS实例')
          that.hls.destroy()
          that.hls = null
        }

        // 设置移动端全屏相关属性
        that.setupMobileFullscreen(video)

        // 直播状态下保持控制栏
        video.controls = true
        video.classList.add('live-video')
        if (Hls.isSupported()) {
          console.log('使用HLS.js播放')
          that.hls = new Hls()
          that.hls.loadSource(liveUrl)
          that.hls.attachMedia(video)
          that.hls.on(Hls.Events.MANIFEST_PARSED, function () {
            console.log('HLS manifest解析完成，开始播放')
            video
              .play()
              .then(function () {
                console.log('视频播放成功')
              })
              .catch(function (error) {
                console.error('视频播放失败:', error)
              })
          })

          that.hls.on(Hls.Events.ERROR, function (event, data) {
            console.error('HLS error:', data)
            if (data.fatal) {
              console.error('HLS致命错误，停止播放')
              that.currentStatus = 'no-stream'
              that.showVideoPlayer = false
            }
          })

          that.hls.on(Hls.Events.MEDIA_ATTACHED, function () {
            console.log('HLS媒体已附加')
          })
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // Safari原生支持HLS
          console.log('使用Safari原生HLS播放')
          video.src = liveUrl
          video.addEventListener('loadedmetadata', function () {
            console.log('视频元数据加载完成，开始播放')
            video
              .play()
              .then(function () {
                console.log('视频播放成功')
              })
              .catch(function (error) {
                console.error('视频播放失败:', error)
              })
          })
          video.addEventListener('error', function (e) {
            console.error('视频播放错误:', e)
          })
        } else {
          console.error('浏览器不支持HLS播放')
          alert('您的浏览器不支持视频播放，请使用Chrome、Safari等现代浏览器')
          that.currentStatus = 'no-stream'
          that.showVideoPlayer = false
        }
      },
      // 初始化回放播放器 (MP4)
      initReplayPlayer: function () {
        var that = this
        var video = that.$refs.videoElement
        var replayUrl = that.details.liveReplayUrl

        if (!video) {
          console.error('视频元素未找到')
          alert('视频播放器初始化失败')
          return
        }

        if (!replayUrl) {
          console.error('回放地址为空')
          alert('回放地址无效')
          return
        }

        // 销毁之前的hls实例（如果存在）
        if (that.hls) {
          console.log('销毁HLS实例，切换到MP4播放')
          that.hls.destroy()
          that.hls = null
        }

        // 直接设置MP4视频源
        console.log('设置MP4视频源:', replayUrl)
        video.src = replayUrl

        // 添加事件监听
        video.addEventListener('loadedmetadata', function () {
          console.log('回放视频元数据加载完成')
        })

        video.addEventListener('canplay', function () {
          console.log('回放视频可以播放，开始播放')
          video
            .play()
            .then(function () {
              console.log('回放视频播放成功')
            })
            .catch(function (error) {
              console.error('回放视频播放失败:', error)
              alert('视频播放失败: ' + error.message)
            })
        })

        video.addEventListener('error', function (e) {
          console.error('回放视频播放错误:', e)
          console.error('错误详情:', video.error)
          alert('视频播放出错，请检查网络连接或联系管理员')
        })

        video.addEventListener('ended', function () {
          console.log('回放视频播放结束')
        })

        // 设置移动端全屏相关属性
        that.setupMobileFullscreen(video)

        // 设置视频属性
        video.controls = true
        video.preload = 'metadata'
        // 回放状态下移除live-video类，确保显示进度条
        video.classList.remove('live-video')
        console.log('回放播放器初始化完成')
      },
      // 观看回放
      watchReplay: function () {
        var that = this
        // 如果是分享链接，不允许看回放
        if (that.isSharedLink) {
          alert('分享链接不支持观看回放')
          return
        }

        if (that.details.liveReplayUrl) {
          // 回放使用MP4播放，不需要HLS
          that.currentStatus = 'replay'
          that.showVideoPlayer = true
          that.$nextTick(function () {
            console.log('开始初始化回放播放器')
            that.initReplayPlayer()
          })
        } else {
          console.error('回放地址为空:', that.details.liveReplayUrl)
          alert('暂无回放地址')
        }
      },
      // 格式化日期时间
      formatDateTime: function (dateTimeStr) {
        if (!dateTimeStr) return ''
        var date = new Date(dateTimeStr)
        var month = this.padZero(date.getMonth() + 1)
        var day = this.padZero(date.getDate())
        var hours = this.padZero(date.getHours())
        var minutes = this.padZero(date.getMinutes())
        return month + '/' + day + ' ' + hours + ':' + minutes
      },
      // 格式化完整日期时间
      formatFullDateTime: function (dateTimeStr) {
        if (!dateTimeStr) return ''
        var date = new Date(dateTimeStr)
        var year = date.getFullYear()
        var month = this.padZero(date.getMonth() + 1)
        var day = this.padZero(date.getDate())
        var hours = this.padZero(date.getHours())
        var minutes = this.padZero(date.getMinutes())
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes
      },
      // 补零函数
      padZero: function (num) {
        return num < 10 ? '0' + num : num.toString()
      },
      // 标签切换
      switchTab: function (tab) {
        var that = this
        if (tab === 'interaction') {
          // 检查是否有token
          var token = that.getValidToken()
          if (!token) {
            // 没有token，显示验证弹窗
            that.showVerifyModal = true
            return
          }
        }

        that.activeTab = tab
        if (tab === 'interaction') {
          // 切换到互动标签时强制滚动到底部
          that.loadComments(true)
          that.startCommentTimer()
        } else {
          that.stopCommentTimer()
        }
      },
      // 启动评论定时器
      startCommentTimer: function () {
        var that = this
        that.stopCommentTimer() // 先清除之前的定时器
        that.commentTimer = setInterval(function () {
          that.loadComments()
        }, 3000) // 每3秒调用一次
      },
      // 停止评论定时器
      stopCommentTimer: function () {
        var that = this
        if (that.commentTimer) {
          clearInterval(that.commentTimer)
          that.commentTimer = null
        }
      },
      // 加载评论
      loadComments: function (shouldScrollToBottom) {
        var that = this
        var token = that.getValidToken()
        if (!token) return

        // 记录当前评论数量，用于检测是否有新留言
        var oldCommentsLength = that.comments.length

        axios
          .post(
            `${that.appUrl}comment/twoLevelTree`,
            {
              businessCode: 'liveBroadcast',
              businessId: datas.id,
              isAscByTime: 1,
              pageNo: 1,
              pageSize: 200
            },
            { headers: { Authorization: token } }
          )
          .then(function (ret) {
            ret = ret.data || {}
            if (ret.code == 200) {
              var newComments = ret.data || []
              var newCommentsLength = newComments.length

              // 更新评论列表
              that.comments = newComments

              // 如果强制滚动到底部，或者有新留言，则滚动到底部
              if (shouldScrollToBottom || newCommentsLength > oldCommentsLength) {
                that.$nextTick(function () {
                  that.scrollToBottom()
                })
              }
            } else {
              that.downErr = ret.message || ret.data
              alert(that.downErr)
            }
          })
      },
      // 滚动到评论列表底部
      scrollToBottom: function () {
        var that = this
        if (that.$refs.commentList) {
          that.$refs.commentList.scrollTop = that.$refs.commentList.scrollHeight
        }
      },
      // 点赞评论
      likeComment: function (comment) {
        var that = this
        if (comment.hasClickPraises) {
          // 取消点赞
          that.cancelLike(comment)
        } else {
          // 点赞
          that.addLike(comment)
        }
      },
      // 添加点赞
      addLike: function (comment) {
        var that = this
        var token = that.getValidToken()
        if (!token) return

        axios
          .post(
            `${that.appUrl}praises/add`,
            {
              form: { businessCode: 'comment', businessId: comment.id }
            },
            { headers: { Authorization: token } }
          )
          .then(function (ret) {
            ret = ret.data || {}
            if (ret.code == 200) {
              comment.hasClickPraises = true
              comment.praisesCount = (comment.praisesCount || 0) + 1
            } else {
              alert(ret.message || '点赞失败')
            }
          })
          .catch(function (err) {
            console.error('点赞失败:', err)
            alert('点赞失败，请稍后重试')
          })
      },
      // 取消点赞
      cancelLike: function (comment) {
        var that = this
        var token = that.getValidToken()
        if (!token) return

        axios
          .post(
            `${that.appUrl}praises/dels`,
            {
              businessCode: 'comment',
              businessId: comment.id
            },
            { headers: { Authorization: token } }
          )
          .then(function (ret) {
            ret = ret.data || {}
            if (ret.code == 200) {
              comment.hasClickPraises = false
              comment.praisesCount = Math.max(0, (comment.praisesCount || 0) - 1)
            } else {
              alert(ret.message || '取消点赞失败')
            }
          })
          .catch(function (err) {
            console.error('取消点赞失败:', err)
            alert('取消点赞失败，请稍后重试')
          })
      },
      // 回复评论
      replyComment: function (comment) {
        var that = this
        that.replyingTo = comment
        that.replyContent = ''

        // 聚焦到输入框
        that.$nextTick(function () {
          if (that.$refs.replyInput && that.$refs.replyInput.length > 0) {
            // 找到对应的输入框
            var inputs = that.$refs.replyInput
            if (inputs.length) {
              inputs[0].focus()
            }
          } else if (that.$refs.replyInput) {
            that.$refs.replyInput.focus()
          }
        })
      },
      // 取消回复
      cancelReply: function () {
        this.replyingTo = null
        this.replyContent = ''
      },
      // 发送回复
      sendReply: function () {
        var that = this
        if (!that.replyContent.trim() || !that.replyingTo) return

        var token = that.getValidToken()
        if (!token) return

        axios
          .post(
            `${that.appUrl}comment/add`,
            {
              form: {
                businessCode: 'liveBroadcast',
                businessId: datas.id,
                checkedStatus: 1,
                commentContent: that.replyContent.trim(),
                parentId: that.replyingTo.id,
                terminalName: 'PC'
              }
            },
            { headers: { Authorization: token } }
          )
          .then(function (ret) {
            ret = ret.data || {}
            if (ret.code == 200) {
              // 记录当前滚动位置
              var currentScrollTop = that.$refs.commentList ? that.$refs.commentList.scrollTop : 0
              // 发送成功，取消回复状态
              that.cancelReply()
              // 重新加载评论列表
              that.loadComments()
              // 保持当前滚动位置
              that.$nextTick(function () {
                if (that.$refs.commentList) {
                  that.$refs.commentList.scrollTop = currentScrollTop
                }
              })
            } else {
              alert(ret.message || '发送回复失败')
            }
          })
          .catch(function (err) {
            console.error('发送回复失败:', err)
            alert('发送回复失败，请稍后重试')
          })
      },
      // 发送评论
      sendComment: function () {
        var that = this
        if (!that.newComment.trim()) return

        var token = that.getValidToken()
        if (!token) return

        axios
          .post(
            `${that.appUrl}comment/add`,
            {
              form: {
                businessCode: 'liveBroadcast',
                businessId: datas.id,
                checkedStatus: 1,
                commentContent: that.newComment.trim(),
                parentId: '',
                terminalName: 'PC'
              }
            },
            { headers: { Authorization: token } }
          )
          .then(function (ret) {
            ret = ret.data || {}
            if (ret.code == 200) {
              // 发送成功，清空输入框
              that.newComment = ''
              // 重新加载评论列表并强制滚动到底部
              that.loadComments(true)
            } else {
              alert(ret.message || '发送评论失败')
            }
          })
          .catch(function (err) {
            console.error('发送评论失败:', err)
            alert('发送评论失败，请稍后重试')
          })
      },
      // 生成分享链接
      generateShareUrl: function () {
        var that = this
        // 获取当前页面的基础URL和参数
        var currentUrl = window.location.href
        var baseUrl = currentUrl.split('?')[0]
        // 解析当前URL参数
        var urlParams = new URLSearchParams(window.location.search)
        // 创建新的参数对象，排除token
        var shareParams = new URLSearchParams()
        // 只保留必要的参数，排除token
        urlParams.forEach(function (value, key) {
          if (key !== 'token') {
            shareParams.append(key, value)
          }
        })

        // 添加分享标识
        shareParams.append('share', '1')

        // 生成不包含token的分享链接
        var shareParamsString = shareParams.toString()
        that.shareUrl = baseUrl + (shareParamsString ? '?' + shareParamsString : '')
      },
      // 显示分享弹窗
      shareStream: function () {
        var that = this
        that.showShareModal = true
        that.copySuccess = false
      },
      // 关闭分享弹窗
      closeShareModal: function () {
        var that = this
        that.showShareModal = false
        that.copySuccess = false
      },
      // 复制分享链接
      copyShareUrl: function () {
        var that = this
        try {
          // 选中输入框中的文本
          that.$refs.shareUrlInput.select()
          that.$refs.shareUrlInput.setSelectionRange(0, 99999) // 兼容移动设备

          // 尝试使用现代API复制
          if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard
              .writeText(that.shareUrl)
              .then(function () {
                that.copySuccess = true
                setTimeout(function () {
                  that.copySuccess = false
                }, 2000)
              })
              .catch(function (err) {
                console.error('复制失败:', err)
                // 降级到传统方法
                that.fallbackCopy()
              })
          } else {
            // 使用传统方法复制
            that.fallbackCopy()
          }
        } catch (err) {
          console.error('复制失败:', err)
          alert('复制失败，请手动复制链接')
        }
      },
      // 降级复制方法
      fallbackCopy: function () {
        var that = this
        try {
          var successful = document.execCommand('copy')
          if (successful) {
            that.copySuccess = true
            setTimeout(function () {
              that.copySuccess = false
            }, 2000)
          } else {
            alert('复制失败，请手动复制链接')
          }
        } catch (err) {
          console.error('降级复制也失败:', err)
          alert('复制失败，请手动复制链接')
        }
      },
      // 检查本地token
      checkLocalToken: function () {
        var that = this
        that.localToken = localStorage.getItem('liveToken') || ''
      },
      // 获取有效token
      getValidToken: function () {
        var that = this
        return datas.token || that.localToken || ''
      },
      // 显示验证弹窗
      showVerify: function () {
        var that = this
        that.showVerifyModal = true
        that.resetVerifyForm()
      },
      // 关闭验证弹窗
      closeVerifyModal: function () {
        var that = this
        that.showVerifyModal = false
        that.resetVerifyForm()
        that.stopCodeCountdown()
      },
      // 重置验证表单
      resetVerifyForm: function () {
        var that = this
        that.verifyForm = {
          name: '',
          phone: '',
          code: ''
        }
      },
      // 发送验证码
      sendVerifyCode: function () {
        var that = this
        if (!that.canSendCode) return

        axios
          .post(`${that.appUrl}open_api/verifyCode/send`, {
            mobile: that.verifyForm.phone,
            sendType: 'no_login'
          })
          .then(function (ret) {
            ret = ret.data || {}
            if (ret.code == 200) {
              that.verifyCodeId = ret.data
              that.startCodeCountdown()
              alert('验证码已发送')
            } else {
              alert(ret.message || '发送验证码失败')
            }
          })
          .catch(function (err) {
            console.error('发送验证码失败:', err)
            alert('发送验证码失败，请稍后重试')
          })
      },
      // 开始验证码倒计时
      startCodeCountdown: function () {
        var that = this
        that.codeCountdown = 60
        that.codeTimer = setInterval(function () {
          that.codeCountdown--
          if (that.codeCountdown <= 0) {
            that.stopCodeCountdown()
          }
        }, 1000)
      },
      // 停止验证码倒计时
      stopCodeCountdown: function () {
        var that = this
        if (that.codeTimer) {
          clearInterval(that.codeTimer)
          that.codeTimer = null
        }
        that.codeCountdown = 0
      },
      // 提交验证
      submitVerify: function () {
        var that = this
        if (!that.canSubmitVerify) return
        var params = {
          grant_type: 'anonymous',
          userName: that.verifyForm.name,
          mobile: that.verifyForm.phone,
          verifyCode: that.verifyForm.code,
          verifyCodeId: that.verifyCodeId
        }
        axios
          .post(`${that.appUrl}oauth/token`, JSON.stringify(params), { headers: { Authorization: 'basic enlzb2Z0Onp5c29mdCo2MDc5' } })
          .then(function (ret) {
            ret = ret.data || {}
            if (ret.code == 200) {
              // 验证成功，保存token到本地
              var token = ret.data.token || ret.token
              if (token) {
                localStorage.setItem('liveToken', token)
                that.localToken = token
                that.closeVerifyModal()
                // 切换到互动标签
                that.activeTab = 'interaction'
                that.loadComments(true)
                that.startCommentTimer()
                alert('验证成功')
              } else {
                alert('验证成功但未获取到token')
              }
            } else {
              alert(ret.message || '验证失败')
            }
          })
          .catch(function (err) {
            console.error('验证失败:', err)
            alert('验证失败，请稍后重试')
          })
      },
      // 设置移动端全屏功能
      setupMobileFullscreen: function (video) {
        var that = this

        // 移动端全屏处理
        if (that.sysType === 'android' || that.sysType === 'ios') {
          // 设置视频属性以支持移动端全屏和横屏
          video.setAttribute('webkit-playsinline', 'true')
          video.setAttribute('playsinline', 'true')
          video.setAttribute('x5-video-player-type', 'h5')
          video.setAttribute('x5-video-player-fullscreen', 'true')
          video.setAttribute('x5-video-orientation', 'landscape')
          video.setAttribute('x5-video-player-orientation', 'landscape')

          // 强制启用全屏控制
          video.setAttribute('controlslist', 'nodownload')
          video.removeAttribute('disablePictureInPicture')

          // 监听全屏事件
          video.addEventListener('x5videoenterfullscreen', function () {
            console.log('进入X5全屏模式')
            that.lockScreenOrientation('landscape')
          })

          video.addEventListener('x5videoexitfullscreen', function () {
            console.log('退出X5全屏模式')
            that.unlockScreenOrientation()
          })

          // iOS Safari 全屏处理
          if (that.sysType === 'ios') {
            video.addEventListener('webkitbeginfullscreen', function () {
              console.log('iOS进入全屏')
              that.lockScreenOrientation('landscape')
            })

            video.addEventListener('webkitendfullscreen', function () {
              console.log('iOS退出全屏')
              that.unlockScreenOrientation()
            })
          }
        }
      },
      // 处理全屏变化事件
      handleFullscreenChange: function () {
        var that = this
        var video = that.$refs.videoElement

        // 检查是否进入全屏
        var isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement || video.webkitDisplayingFullscreen)

        if (isFullscreen) {
          // 进入全屏
          console.log('进入全屏模式')
          that.lockScreenOrientation('landscape')

          // 确保视频在全屏时正确显示
          setTimeout(function () {
            if (video) {
              video.style.objectFit = 'contain'
            }
          }, 100)
        } else {
          // 退出全屏
          console.log('退出全屏模式')
          that.unlockScreenOrientation()

          // 恢复视频样式
          if (video) {
            video.style.objectFit = 'cover'
          }
        }
      },
      // 锁定屏幕方向为横屏
      lockScreenOrientation: function (orientation) {
        var that = this
        if (that.sysType === 'android' || that.sysType === 'ios') {
          try {
            // 现代浏览器API
            if (screen.orientation && screen.orientation.lock) {
              screen.orientation.lock(orientation || 'landscape').catch(function (err) {
                console.log('屏幕方向锁定失败:', err)
              })
            }
            // 旧版API
            else if (screen.lockOrientation) {
              screen.lockOrientation(orientation || 'landscape')
            }
            // webkit前缀
            else if (screen.webkitLockOrientation) {
              screen.webkitLockOrientation(orientation || 'landscape')
            }
            // moz前缀
            else if (screen.mozLockOrientation) {
              screen.mozLockOrientation(orientation || 'landscape')
            }
          } catch (err) {
            console.log('屏幕方向锁定异常:', err)
          }
        }
      },
      // 解锁屏幕方向
      unlockScreenOrientation: function () {
        var that = this
        if (that.sysType === 'android' || that.sysType === 'ios') {
          try {
            // 现代浏览器API
            if (screen.orientation && screen.orientation.unlock) {
              screen.orientation.unlock()
            }
            // 旧版API
            else if (screen.unlockOrientation) {
              screen.unlockOrientation()
            }
            // webkit前缀
            else if (screen.webkitUnlockOrientation) {
              screen.webkitUnlockOrientation()
            }
            // moz前缀
            else if (screen.mozUnlockOrientation) {
              screen.mozUnlockOrientation()
            }
          } catch (err) {
            console.log('屏幕方向解锁异常:', err)
          }
        }
      }
    }

    // 计算属性
    var computed = {
      // 是否可以发送验证码
      canSendCode: function () {
        var phone = this.verifyForm.phone
        return phone && /^1[3-9]\d{9}$/.test(phone)
      },
      // 是否可以提交验证
      canSubmitVerify: function () {
        var form = this.verifyForm
        return form.name.trim() && this.canSendCode && form.code.trim()
      }
    }

    window.onload = function () {
      showVue()
    }

    // 页面卸载时清除定时器
    window.onbeforeunload = function () {
      if (window.vm && window.vm.stopCommentTimer) {
        window.vm.stopCommentTimer()
      }
    }

    function showVue() {
      window.vm = new Vue({
        el: '#app',
        data: vmData,
        computed: computed,
        mounted: function () {
          this.init()
          // 如果默认是互动标签页，初始化时滚动到底部
          var that = this
          that.$nextTick(function () {
            if (that.activeTab === 'interaction' && that.getValidToken()) {
              that.loadComments(true)
            }
          })
        },
        beforeDestroy: function () {
          this.stopCountdown()
          this.stopCodeCountdown()
          if (this.hls) {
            this.hls.destroy()
            this.hls = null
          }
        },
        methods: methods
      })
    }
  </script>
</html>
